﻿一级功能模块,二级功能模块,三级功能模块,功能描述,预估工作量（人天）,功能用户,触发事件,功能过程,子过程描述,数据移动类型,数据组,数据属性,CFP
客户端密钥协同模块,服务端部分签名,客户端密钥协同新增租户,nan,4,发起者：用户 接收者：密码服务平台,新增租户请求,租户密钥资源新增,输入租户密钥资源新增信息,E,租户密钥资源新增信息,租户ID、密钥数量、证书数量、资源类型,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同新增租户,nan,4,发起者：用户 接收者：密码服务平台,新增租户请求,租户密钥资源新增,校验租户密钥资源唯一性,R,租户密钥资源校验信息,租户ID、资源类型、密钥数量上限,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同新增租户,nan,4,发起者：用户 接收者：密码服务平台,新增租户请求,租户密钥资源新增,保存租户密钥资源配置,W,租户密钥资源配置,租户ID、密钥数量、证书数量、资源类型、配置时间,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同新增租户,nan,4,发起者：用户 接收者：密码服务平台,新增租户请求,租户密钥资源新增,返回租户密钥资源新增结果,X,租户密钥资源新增结果,租户ID、资源类型、配置状态、配置时间,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同查询租户信息,nan,4,发起者：用户 接收者：密码服务平台,发起租户信息查询,租户信息查询,输入租户查询请求信息,E,租户查询请求,租户标识、租户名称、机构名称、审核状态,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同查询租户信息,nan,4,发起者：用户 接收者：密码服务平台,发起租户信息查询,租户信息查询,读取租户审核记录信息,R,租户审核记录,租户标识、租户名称、机构名称、业务类型、申请时间、审核人、审核意见、完整性校验结果、审核状态,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同查询租户信息,nan,4,发起者：用户 接收者：密码服务平台,发起租户信息查询,租户信息查询,返回租户查询结果展示,X,租户查询结果,租户标识、租户名称、机构名称、审核状态、审核时间、操作详情,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同查询租户信息,nan,4,发起者：用户 接收者：密码服务平台,发起租户信息查询,租户信息查询,保存租户查询操作日志,W,租户查询日志,操作人ID、操作时间、查询条件、查询结果数量、系统时间戳,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同获取租户配额信息,nan,4,发起者：用户 接收者：密码服务平台,获取租户配额信息,客户端密钥协同获取租户配额信息,输入租户配额查询请求,E,租户配额查询请求,租户ID、服务标识、配额键,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同获取租户配额信息,nan,4,发起者：用户 接收者：密码服务平台,获取租户配额信息,客户端密钥协同获取租户配额信息,读取租户配额信息,R,租户配额信息,租户ID、服务标识、配额键、配额值,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同获取租户配额信息,nan,4,发起者：用户 接收者：密码服务平台,获取租户配额信息,客户端密钥协同获取租户配额信息,返回租户配额结果展示信息,X,租户配额结果展示信息,租户ID、服务标识、配额键、配额值,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同获取租户配额信息,nan,4,发起者：用户 接收者：密码服务平台,获取租户配额信息,客户端密钥协同获取租户配额信息,记录租户配额查询日志,W,租户配额查询日志,操作员ID、查询时间、查询结果,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同查询用户列表,nan,4,发起者：用户 接收者：密码服务平台,查询用户密钥信息,用户密钥信息查询,输入用户密钥查询条件,E,用户密钥查询请求,密钥算法、租户标识、服务ID,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同查询用户列表,nan,4,发起者：用户 接收者：密码服务平台,查询用户密钥信息,用户密钥信息查询,读取密钥统计信息,R,密钥统计信息,密钥状态、密钥算法、租户标识、服务ID,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同查询用户列表,nan,4,发起者：用户 接收者：密码服务平台,查询用户密钥信息,用户密钥信息查询,返回用户密钥查询结果,X,用户密钥查询结果,密钥算法、密钥状态、租户标识、服务组ID,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同查询用户列表,nan,4,发起者：用户 接收者：密码服务平台,查询用户密钥信息,用户密钥信息查询,记录用户密钥查询日志,W,用户密钥查询日志,操作人ID、查询时间、密钥算法、查询结果,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同修改用户状态,nan,4,发起者：用户 接收者：密码服务平台,修改用户状态请求,修改用户状态,输入用户状态修改请求,E,用户状态修改请求,用户ID、目标状态、操作员ID,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同修改用户状态,nan,4,发起者：用户 接收者：密码服务平台,修改用户状态请求,修改用户状态,读取用户当前状态信息,R,用户当前状态信息,用户ID、当前状态、租户标识、最后活跃时间,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同修改用户状态,nan,4,发起者：用户 接收者：密码服务平台,修改用户状态请求,修改用户状态,更新用户状态并保存,W,用户状态修改结果,用户ID、新状态、修改时间、操作员ID,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同修改用户状态,nan,4,发起者：用户 接收者：密码服务平台,修改用户状态请求,修改用户状态,返回用户状态修改结果,X,用户状态修改结果展示信息,用户ID、新状态、操作结果、修改时间,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同新增用户,nan,4,发起者：用户 接收者：密码服务平台,用户提交新增用户请求,新增协同签名用户,输入新增用户信息,E,新增用户信息,用户ID、用户名、密钥算法(SM2)、租户标识、服务ID,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同新增用户,nan,4,发起者：用户 接收者：密码服务平台,用户提交新增用户请求,新增协同签名用户,校验用户是否存在,R,用户校验信息,用户名、租户标识、服务ID、密钥状态,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同新增用户,nan,4,发起者：用户 接收者：密码服务平台,用户提交新增用户请求,新增协同签名用户,保存新增用户记录,W,新增用户记录,用户ID、用户名、密钥算法、租户标识、服务ID、创建时间,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同新增用户,nan,4,发起者：用户 接收者：密码服务平台,用户提交新增用户请求,新增协同签名用户,返回新增用户结果,X,新增用户结果,用户名、操作结果(成功/失败)、错误信息,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同导入用户,nan,4,发起者：用户 接收者：密码服务平台,客户端密钥协同导入用户请求,客户端密钥协同导入用户,输入客户端密钥协同导入请求,E,客户端密钥导入请求,租户ID、密钥文件、用户ID、导入时间,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同导入用户,nan,4,发起者：用户 接收者：密码服务平台,客户端密钥协同导入用户请求,客户端密钥协同导入用户,读取租户密钥资源配额信息,R,租户密钥资源信息,租户ID、密钥数量、证书数量、资源配额状态,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同导入用户,nan,4,发起者：用户 接收者：密码服务平台,客户端密钥协同导入用户请求,客户端密钥协同导入用户,保存客户端密钥协同导入结果,W,客户端密钥导入结果,密钥状态、密钥算法、租户标识、服务ID,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同导入用户,nan,4,发起者：用户 接收者：密码服务平台,客户端密钥协同导入用户请求,客户端密钥协同导入用户,返回客户端密钥导入结果展示,X,客户端密钥导入结果展示,导入状态、密钥ID、错误信息、操作时间,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同导出用户,nan,4,发起者：用户 接收者：密码服务平台,用户请求导出密钥,客户端密钥协同导出,输入密钥导出请求信息,E,密钥导出请求,用户ID、密钥ID、导出时间、导出类型,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同导出用户,nan,4,发起者：用户 接收者：密码服务平台,用户请求导出密钥,客户端密钥协同导出,验证用户权限及密钥状态,R,用户权限信息,用户角色、操作权限、密钥状态、租户ID,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同导出用户,nan,4,发起者：用户 接收者：密码服务平台,用户请求导出密钥,客户端密钥协同导出,生成并加密密钥导出文件,W,加密密钥文件,密钥内容、加密算法、文件路径、生成时间,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同导出用户,nan,4,发起者：用户 接收者：密码服务平台,用户请求导出密钥,客户端密钥协同导出,返回密钥导出结果,X,导出结果信息,文件下载链接、导出状态、错误代码、操作日志ID,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同删除用户,nan,4,发起者：用户 接收者：密码服务平台,用户请求删除协同密钥,客户端密钥协同删除,输入客户端密钥删除请求,E,密钥删除请求信息,用户ID、密钥ID、删除时间戳,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同删除用户,nan,4,发起者：用户 接收者：密码服务平台,用户请求删除协同密钥,客户端密钥协同删除,验证用户删除权限,R,用户权限信息,用户ID、权限级别、操作类型,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同删除用户,nan,4,发起者：用户 接收者：密码服务平台,用户请求删除协同密钥,客户端密钥协同删除,执行密钥协同删除操作,W,密钥状态信息,密钥ID、删除状态、操作记录,1
客户端密钥协同模块,服务端部分签名,客户端密钥协同删除用户,nan,4,发起者：用户 接收者：密码服务平台,用户请求删除协同密钥,客户端密钥协同删除,返回密钥删除结果,X,删除结果反馈信息,操作结果、密钥ID、删除时间,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同注册用户,nan,4,发起者：用户 接收者：密码服务平台,用户注册,用户注册,输入用户注册信息,E,用户注册信息,用户名称、用户类型、租户ID、密码,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同注册用户,nan,4,发起者：用户 接收者：密码服务平台,用户注册,用户注册,校验用户是否存在,R,用户校验信息,用户名称、租户ID、用户状态,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同注册用户,nan,4,发起者：用户 接收者：密码服务平台,用户注册,用户注册,保存用户注册信息,W,用户注册数据,用户名称、用户类型、租户ID、密码、注册时间,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同注册用户,nan,4,发起者：用户 接收者：密码服务平台,用户注册,用户注册,返回注册结果,X,用户注册结果,用户名称、注册状态、错误代码,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同注册用户,nan,4,发起者：用户 接收者：密码服务平台,用户审核,用户审核,输入用户审核请求,E,用户审核请求,用户ID、审核状态、操作员ID,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同注册用户,nan,4,发起者：用户 接收者：密码服务平台,用户审核,用户审核,读取用户审核信息,R,用户审核数据,用户ID、用户名称、租户ID、当前状态,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同注册用户,nan,4,发起者：用户 接收者：密码服务平台,用户审核,用户审核,更新用户审核状态,W,用户审核结果,用户ID、审核状态、操作员ID、审核时间,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同注册用户,nan,4,发起者：用户 接收者：密码服务平台,用户审核,用户审核,返回审核结果,X,用户审核反馈,用户名称、审核结果、操作员ID,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同注册用户,nan,4,发起者：用户 接收者：密码服务平台,用户信息查询,用户信息查询,输入用户查询条件,E,用户查询请求,用户名称、租户ID、查询类型,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同注册用户,nan,4,发起者：用户 接收者：密码服务平台,用户信息查询,用户信息查询,读取用户信息,R,用户信息数据,用户ID、用户名称、用户类型、租户ID、注册时间,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同注册用户,nan,4,发起者：用户 接收者：密码服务平台,用户信息查询,用户信息查询,返回用户信息结果,X,用户查询结果,用户ID、用户名称、用户类型、租户ID、注册时间,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同注册用户,nan,4,发起者：用户 接收者：密码服务平台,用户信息查询,用户信息查询,记录查询日志,W,用户查询日志,操作员ID、查询时间、用户ID、查询条件,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同用户是否注册,nan,4,密码发起者：用户 接收者：密码服务平台,用户注册检查,用户注册状态检查,输入用户注册信息,E,用户注册信息,用户ID、用户名、租户ID、请求时间,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同用户是否注册,nan,4,密码发起者：用户 接收者：密码服务平台,用户注册检查,用户注册状态检查,读取租户注册用户信息,R,租户注册用户信息,用户ID、租户ID、用户类型、注册状态、注册时间,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同用户是否注册,nan,4,密码发起者：用户 接收者：密码服务平台,用户注册检查,用户注册状态检查,输出用户注册状态结果,X,用户注册状态结果,用户ID、注册状态、注册时间、租户ID,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同用户是否注册,nan,4,密码发起者：用户 接收者：密码服务平台,用户注册检查,用户注册状态检查,记录用户注册检查日志,W,用户注册检查日志,操作员ID、检查时间、用户ID、检查结果,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同获取用户信息,nan,4,发起者：用户 接收者：密码服务平台,用户密钥信息查询,获取用户密钥详情,输入用户密钥查询条件,E,用户密钥查询请求,密钥算法类型、租户标识、服务ID,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同获取用户信息,nan,4,发起者：用户 接收者：密码服务平台,用户密钥信息查询,获取用户密钥详情,读取密钥统计信息,R,密钥统计记录,密钥状态、密钥算法、租户代码、服务组ID,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同获取用户信息,nan,4,发起者：用户 接收者：密码服务平台,用户密钥信息查询,获取用户密钥详情,验证租户密钥资源配额,R,租户密钥资源,租户ID、密钥数量、证书数量、资源配额状态,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同获取用户信息,nan,4,发起者：用户 接收者：密码服务平台,用户密钥信息查询,获取用户密钥详情,返回用户密钥详情结果,X,用户密钥详情数据,密钥算法、密钥状态、租户标识、服务ID、密钥创建时间,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同获取用户信息,nan,4,发起者：用户 接收者：密码服务平台,用户密钥信息查询,获取用户密钥详情,记录密钥查询操作日志,W,密钥操作日志,操作类型、操作时间、操作用户ID、查询条件摘要,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2生密钥,nan,4,发起者：用户 接收者：密码服务平台,用户请求生成SM2密钥对,SM2密钥对生成,输入SM2密钥生成请求,E,SM2密钥生成请求,用户ID、算法类型(SM2)、密钥长度、生成时间戳,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2生密钥,nan,4,发起者：用户 接收者：密码服务平台,用户请求生成SM2密钥对,SM2密钥对生成,调用WRP_KEY_CTX_new接口初始化密钥上下文,R,密钥上下文配置,算法参数、安全级别、上下文标识符,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2生密钥,nan,4,发起者：用户 接收者：密码服务平台,用户请求生成SM2密钥对,SM2密钥对生成,执行SM2密钥对生成算法,W,SM2密钥对数据,公钥值、私钥值、生成时间、算法标识,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2生密钥,nan,4,发起者：用户 接收者：密码服务平台,用户请求生成SM2密钥对,SM2密钥对生成,返回SM2密钥对生成结果,X,密钥生成响应,公钥数据、私钥数据、生成状态、错误代码,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2生密钥,nan,4,发起者：用户 接收者：密码服务平台,用户请求生成SM2密钥对,SM2密钥对生成,记录密钥生成操作日志,W,密钥操作日志,操作用户ID、操作类型、密钥ID、操作时间,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2协同签名,nan,4,发起者：用户 接收者：密码服务平台,发起SM2协同签名请求,SM2协同签名处理,输入SM2协同签名请求信息,E,SM2协同签名请求,用户ID、密钥分量、签名参数、请求时间,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2协同签名,nan,4,发起者：用户 接收者：密码服务平台,发起SM2协同签名请求,SM2协同签名处理,读取用户权限及密钥状态信息,R,密钥状态信息,密钥状态、密钥算法、租户标识、服务ID,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2协同签名,nan,4,发起者：用户 接收者：密码服务平台,发起SM2协同签名请求,SM2协同签名处理,生成SM2协同签名结果,W,协同签名结果,签名结果、签名时间、签名状态、密钥分量ID,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2协同签名,nan,4,发起者：用户 接收者：密码服务平台,发起SM2协同签名请求,SM2协同签名处理,返回SM2协同签名结果,X,签名结果展示信息,签名结果、签名时间、签名状态、用户ID,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2验签,nan,4,发起者：用户 接收者：密码服务平台,发起SM2验签请求,客户端密钥协同SM2验签,输入SM2验签请求参数,E,SM2验签请求参数,密钥ID、签名数据、签名算法类型、请求时间戳,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2验签,nan,4,发起者：用户 接收者：密码服务平台,发起SM2验签请求,客户端密钥协同SM2验签,读取密钥状态与算法信息,R,密钥状态信息,密钥ID、密钥状态、密钥算法、租户标识,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2验签,nan,4,发起者：用户 接收者：密码服务平台,发起SM2验签请求,客户端密钥协同SM2验签,执行SM2验签逻辑并记录结果,W,验签结果日志,验签状态、验签时间、密钥ID、操作员ID,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2验签,nan,4,发起者：用户 接收者：密码服务平台,发起SM2验签请求,客户端密钥协同SM2验签,返回验签结果给客户端,X,验签结果响应,验签状态、错误代码、详细描述、响应时间,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2加密,nan,4,发起者：用户 接收者：密码服务平台,发起SM2加密请求,客户端密钥协同SM2加密处理,输入SM2加密请求参数,E,SM2加密请求参数,加密算法类型、明文数据、用户ID,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2加密,nan,4,发起者：用户 接收者：密码服务平台,发起SM2加密请求,客户端密钥协同SM2加密处理,读取用户SM2密钥对,R,SM2密钥对信息,公钥、私钥、密钥ID,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2加密,nan,4,发起者：用户 接收者：密码服务平台,发起SM2加密请求,客户端密钥协同SM2加密处理,执行SM2加密算法运算,W,SM2加密运算参数,加密算法参数、加密时间戳、加密状态,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2加密,nan,4,发起者：用户 接收者：密码服务平台,发起SM2加密请求,客户端密钥协同SM2加密处理,输出SM2加密结果,X,SM2加密响应数据,密文数据、加密状态码、加密时间戳,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2协同解密,nan,4,发起者：用户 接收者：密码服务平台,发起SM2协同解密请求,SM2协同解密处理,输入SM2协同解密请求信息,E,SM2解密请求信息,加密数据、密钥ID、用户ID、解密算法类型,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2协同解密,nan,4,发起者：用户 接收者：密码服务平台,发起SM2协同解密请求,SM2协同解密处理,读取SM2密钥及用户权限信息,R,SM2密钥验证信息,密钥状态、用户权限、密钥算法类型、密钥有效期,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2协同解密,nan,4,发起者：用户 接收者：密码服务平台,发起SM2协同解密请求,SM2协同解密处理,执行SM2协同解密操作并返回结果,X,SM2解密结果,解密数据、解密状态码、解密时间戳,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同SM2协同解密,nan,4,发起者：用户 接收者：密码服务平台,发起SM2协同解密请求,SM2协同解密处理,记录SM2协同解密操作日志,W,SM2解密日志,操作员ID、解密时间、解密结果、密钥ID,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同修改PIN码,nan,4,发起者：用户 接收者：密码服务平台,修改PIN码请求,客户端密钥协同修改PIN码,输入修改PIN码信息,E,PIN码修改请求,旧PIN码、新PIN码、确认PIN码、用户ID、设备标识,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同修改PIN码,nan,4,发起者：用户 接收者：密码服务平台,修改PIN码请求,客户端密钥协同修改PIN码,校验旧PIN码有效性,R,用户密钥信息,存储的PIN码、密钥状态、用户ID、设备标识,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同修改PIN码,nan,4,发起者：用户 接收者：密码服务平台,修改PIN码请求,客户端密钥协同修改PIN码,更新并保存新PIN码,W,更新后的密钥信息,新PIN码、更新时间、用户ID、设备标识,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同修改PIN码,nan,4,发起者：用户 接收者：密码服务平台,修改PIN码请求,客户端密钥协同修改PIN码,返回PIN码修改结果,X,PIN码修改响应,操作状态、错误代码、用户ID、修改时间,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同导入P10,nan,4,发起者：用户 接收者：密码服务平台,导入密钥文件,密钥文件导入,输入密钥文件导入信息,E,密钥文件导入请求,文件名、所属租户ID、上传时间戳,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同导入P10,nan,4,发起者：用户 接收者：密码服务平台,导入密钥文件,密钥文件导入,验证租户权限并读取密钥文件,R,租户权限信息,租户ID、权限状态、操作员ID,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同导入P10,nan,4,发起者：用户 接收者：密码服务平台,导入密钥文件,密钥文件导入,执行密钥文件导入并写入存储,W,密钥文件导入结果,文件名、导入状态、密钥ID,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同导入P10,nan,4,发起者：用户 接收者：密码服务平台,导入密钥文件,密钥文件导入,返回密钥导入结果展示,X,密钥导入结果展示,文件名、导入状态、操作时间,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同导出P10,nan,4,发起者：用户 接收者：密码服务平台,导出P10密钥请求,导出P10密钥,输入P10密钥导出请求信息,E,P10导出请求信息,用户ID、密钥别名、导出格式(P10)、时间戳,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同导出P10,nan,4,发起者：用户 接收者：密码服务平台,导出P10密钥请求,导出P10密钥,读取用户权限信息并验证密钥存在性,R,用户权限信息,用户ID、权限级别、密钥别名、密钥状态,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同导出P10,nan,4,发起者：用户 接收者：密码服务平台,导出P10密钥请求,导出P10密钥,生成P10格式密钥文件并写入存储,W,P10文件,文件内容(PKCS#10格式)、文件名、生成时间,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同导出P10,nan,4,发起者：用户 接收者：密码服务平台,导出P10密钥请求,导出P10密钥,返回P10密钥文件下载链接,X,P10导出结果,文件名、生成时间、下载链接、校验码,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同检测PIN码,nan,4,发起者：用户 接收者：密码服务平台,用户输入PIN码进行检测,检测PIN码,输入PIN码及用户身份信息,E,PIN码输入信息,用户ID、输入PIN码、请求时间戳,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同检测PIN码,nan,4,发起者：用户 接收者：密码服务平台,用户输入PIN码进行检测,检测PIN码,读取用户存储的PIN码信息,R,用户PIN码信息,用户ID、加密存储PIN码、密钥算法类型、密钥状态,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同检测PIN码,nan,4,发起者：用户 接收者：密码服务平台,用户输入PIN码进行检测,检测PIN码,执行PIN码验证逻辑,W,PIN码验证日志,用户ID、验证结果、验证时间、操作类型,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同检测PIN码,nan,4,发起者：用户 接收者：密码服务平台,用户输入PIN码进行检测,检测PIN码,返回PIN码验证结果,X,PIN码验证结果,验证状态、错误代码、密钥可用状态,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同导入证书,nan,4,发起者：用户 接收者：密码服务平台,用户导入证书请求,证书导入处理,输入证书导入请求信息,E,证书导入请求,证书文件、租户ID、证书类型、证书有效期,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同导入证书,nan,4,发起者：用户 接收者：密码服务平台,用户导入证书请求,证书导入处理,验证租户密钥资源配额,R,租户密钥资源信息,租户ID、密钥数量、证书数量、配额上限,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同导入证书,nan,4,发起者：用户 接收者：密码服务平台,用户导入证书请求,证书导入处理,校验证书格式与合法性,R,证书校验规则,证书算法类型、签名验证规则、有效期范围,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同导入证书,nan,4,发起者：用户 接收者：密码服务平台,用户导入证书请求,证书导入处理,写入加密判定密钥表,W,加密判定密钥信息,密钥ID、证书文件、租户ID、证书类型,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同导入证书,nan,4,发起者：用户 接收者：密码服务平台,用户导入证书请求,证书导入处理,返回证书导入结果,X,证书导入结果,证书ID、导入状态、错误代码、操作时间,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同导入证书,nan,4,发起者：用户 接收者：密码服务平台,用户导入证书请求,证书导入处理,记录证书导入操作日志,W,证书导入日志,操作员ID、证书ID、操作类型、操作时间,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同获取证书,nan,4,密码发起者：用户 接收者：密码服务平台,用户请求下载证书,证书下载处理,输入证书下载请求,E,证书下载请求,证书ID、用户ID、请求时间,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同获取证书,nan,4,密码发起者：用户 接收者：密码服务平台,用户请求下载证书,证书下载处理,读取证书信息,R,证书信息,证书类型、序列号、签名算法、密钥算法、使用者、开始日期、结束日期,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同获取证书,nan,4,密码发起者：用户 接收者：密码服务平台,用户请求下载证书,证书下载处理,验证证书下载权限,R,租户密钥资源,租户ID、密钥数量、证书配额、使用状态,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同获取证书,nan,4,密码发起者：用户 接收者：密码服务平台,用户请求下载证书,证书下载处理,返回证书下载内容,X,证书下载响应,证书文件、下载链接、有效期、校验码,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同获取证书详情信息,nan,4,发起者：用户 接收者：密码服务平台,获取证书详情信息,获取证书详情信息,输入证书查询请求参数,E,证书查询请求,证书ID、查询条件、操作员ID,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同获取证书详情信息,nan,4,发起者：用户 接收者：密码服务平台,获取证书详情信息,获取证书详情信息,读取证书基本信息及关联密钥状态,R,证书详情信息,证书ID、颁发者、使用者、起止时间、密钥算法、密钥状态,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同获取证书详情信息,nan,4,发起者：用户 接收者：密码服务平台,获取证书详情信息,获取证书详情信息,返回证书详情展示信息,X,证书详情展示信息,证书ID、颁发者、使用者、起止时间、密钥算法、密钥状态,1
客户端密钥协同模块,客户端密钥协同业务管理,客户端密钥协同获取证书详情信息,nan,4,发起者：用户 接收者：密码服务平台,获取证书详情信息,获取证书详情信息,记录证书查询操作日志,W,证书查询日志,操作员ID、查询时间、证书ID、查询结果状态,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同查看密钥,nan,4,发起者：用户 接收者：密码服务平台,请求查看密钥信息,客户端密钥协同查看密钥,输入密钥查询条件,E,密钥查询条件,密钥算法、租户标识、服务ID,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同查看密钥,nan,4,发起者：用户 接收者：密码服务平台,请求查看密钥信息,客户端密钥协同查看密钥,读取密钥统计信息,R,密钥统计信息,密钥状态、密钥算法、租户标识、服务ID,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同查看密钥,nan,4,发起者：用户 接收者：密码服务平台,请求查看密钥信息,客户端密钥协同查看密钥,返回密钥查询结果,X,密钥查询结果,密钥名称、密钥算法、密钥状态、租户标识,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同查看密钥,nan,4,发起者：用户 接收者：密码服务平台,请求查看密钥信息,客户端密钥协同查看密钥,记录密钥查询日志,W,密钥查询日志,操作员ID、查询时间、密钥算法、租户标识,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥解冻,nan,4,发起者：用户 接收者：密码服务平台,提交解冻申请,证书解冻申请,输入证书解冻申请信息,E,证书解冻申请信息,证书序列号、操作员ID、解冻原因、申请时间,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥解冻,nan,4,发起者：用户 接收者：密码服务平台,提交解冻申请,证书解冻申请,验证证书冻结状态,R,证书状态信息,证书状态、冻结原因、冻结时间、证书有效期,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥解冻,nan,4,发起者：用户 接收者：密码服务平台,提交解冻申请,证书解冻申请,保存解冻申请记录,W,解冻申请记录,证书序列号、申请操作员、申请时间、解冻原因,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥解冻,nan,4,发起者：用户 接收者：密码服务平台,提交解冻申请,证书解冻申请,返回解冻申请结果,X,解冻申请反馈,申请状态、证书序列号、处理时间、操作结果,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥解冻,nan,4,发起者：用户 接收者：密码服务平台,审核解冻申请,证书解冻审核,输入解冻审核指令,E,解冻审核指令,证书序列号、审核操作员ID、审核意见,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥解冻,nan,4,发起者：用户 接收者：密码服务平台,审核解冻申请,证书解冻审核,更新证书状态为有效,W,证书状态更新,证书序列号、新状态、解冻时间、审核操作员,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥解冻,nan,4,发起者：用户 接收者：密码服务平台,审核解冻申请,证书解冻审核,记录解冻审核日志,W,解冻审核日志,证书序列号、审核结果、操作员ID、审核时间,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥解冻,nan,4,发起者：用户 接收者：密码服务平台,审核解冻申请,证书解冻审核,返回解冻审核结果,X,解冻审核反馈,证书序列号、审核状态、操作结果、反馈时间,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥解绑,nan,4,发起者：用户 接收者：密码服务平台,客户端密钥协同密钥解绑请求,客户端密钥协同密钥解绑,输入客户端密钥解绑请求信息,E,客户端密钥解绑请求,密钥ID、租户标识、解绑原因,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥解绑,nan,4,发起者：用户 接收者：密码服务平台,客户端密钥协同密钥解绑请求,客户端密钥协同密钥解绑,验证用户解绑权限并读取密钥状态,R,密钥状态验证信息,密钥ID、租户标识、密钥算法、密钥状态,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥解绑,nan,4,发起者：用户 接收者：密码服务平台,客户端密钥协同密钥解绑请求,客户端密钥协同密钥解绑,执行密钥解绑操作并更新密钥状态,W,密钥解绑更新记录,密钥ID、解绑时间、操作员ID、密钥状态变更,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥解绑,nan,4,发起者：用户 接收者：密码服务平台,客户端密钥协同密钥解绑请求,客户端密钥协同密钥解绑,返回密钥解绑结果信息,X,密钥解绑结果,密钥ID、解绑状态、操作时间、操作员标识,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同校验完整性,nan,4,发起者：用户 接收者：密码服务平台,密钥完整性校验请求,密钥信息查询与完整性校验,输入密钥查询条件,E,密钥查询请求,密钥ID、算法类型、查询时间,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同校验完整性,nan,4,发起者：用户 接收者：密码服务平台,密钥完整性校验请求,密钥信息查询与完整性校验,读取密钥元数据,R,密钥元数据,密钥ID、算法类型、创建时间、密钥状态、密钥长度,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同校验完整性,nan,4,发起者：用户 接收者：密码服务平台,密钥完整性校验请求,密钥信息查询与完整性校验,返回密钥校验结果,X,密钥校验结果,校验状态、错误信息、校验时间,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同校验完整性,nan,4,发起者：用户 接收者：密码服务平台,密钥完整性校验请求,密钥信息查询与完整性校验,记录密钥校验日志,W,密钥校验日志,操作员ID、操作时间、校验结果、密钥ID,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥归档,nan,4,密码发起者：用户 接收者：密码服务平台,主密钥备份,主密钥备份,输入主密钥备份请求信息,E,主密钥备份请求,租户标识、服务ID、密钥算法、备份类型,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥归档,nan,4,密码发起者：用户 接收者：密码服务平台,主密钥备份,主密钥备份,读取当前密钥状态信息,R,密钥状态信息,密钥状态、密钥算法、租户标识、服务组ID,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥归档,nan,4,密码发起者：用户 接收者：密码服务平台,主密钥备份,主密钥备份,生成主密钥备份文件,W,主密钥备份文件,备份文件名、备份时间、加密算法、租户标识,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥归档,nan,4,密码发起者：用户 接收者：密码服务平台,主密钥备份,主密钥备份,返回主密钥备份结果,X,主密钥备份结果,备份状态、备份文件ID、操作时间、租户标识,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥归档,nan,4,密码发起者：用户 接收者：密码服务平台,备份文件下载,备份文件下载,输入备份文件下载请求,E,备份文件下载请求,备份文件ID、租户标识、下载时间,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥归档,nan,4,密码发起者：用户 接收者：密码服务平台,备份文件下载,备份文件下载,读取备份文件元数据,R,备份文件元数据,备份文件名、加密算法、文件大小、创建时间,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥归档,nan,4,密码发起者：用户 接收者：密码服务平台,备份文件下载,备份文件下载,生成备份文件下载链接,W,备份文件下载链接,临时URL、过期时间、文件哈希值、下载权限,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同密钥归档,nan,4,密码发起者：用户 接收者：密码服务平台,备份文件下载,备份文件下载,输出备份文件下载响应,X,备份文件下载响应,下载链接、文件名、下载状态、操作时间,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同导出P10,nan,4,发起者：用户 接收者：密码服务平台,导出密钥为P10格式,密钥P10格式导出,输入密钥导出请求信息,E,密钥导出请求信息,密钥ID、导出格式(P10)、用户权限标识、导出时间戳,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同导出P10,nan,4,发起者：用户 接收者：密码服务平台,导出密钥为P10格式,密钥P10格式导出,验证用户导出权限并读取密钥结构体,R,密钥结构体数据,密钥算法类型、密钥长度、密钥状态、加密模式,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同导出P10,nan,4,发起者：用户 接收者：密码服务平台,导出密钥为P10格式,密钥P10格式导出,生成P10格式密钥文件,W,P10文件生成信息,P10文件内容、文件签名算法、证书链信息、导出时间,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同导出P10,nan,4,发起者：用户 接收者：密码服务平台,导出密钥为P10格式,密钥P10格式导出,返回P10文件下载链接及校验结果,X,密钥导出响应信息,文件下载URL、文件哈希值、导出状态、操作日志ID,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同查看证书,nan,4,发起者：用户 接收者：密码服务平台,用户请求查看证书信息,证书信息查看,输入证书查询请求,E,证书查询请求,证书ID、租户ID、查询时间,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同查看证书,nan,4,发起者：用户 接收者：密码服务平台,用户请求查看证书信息,证书信息查看,读取证书基础信息,R,证书基础信息,证书ID、证书名称、签发机构、有效期、公钥算法,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同查看证书,nan,4,发起者：用户 接收者：密码服务平台,用户请求查看证书信息,证书信息查看,返回证书详细信息展示,X,证书展示信息,证书名称、签发机构、有效期、公钥内容、证书状态,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同查看证书,nan,4,发起者：用户 接收者：密码服务平台,用户请求查看证书信息,证书信息查看,记录证书查询日志,W,证书查询日志,操作员ID、证书ID、查询时间、查询结果状态,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同查看证书,nan,4,发起者：用户 接收者：密码服务平台,用户请求验证证书链,证书链验证,输入证书链验证请求,E,证书链验证请求,证书ID、验证时间,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同查看证书,nan,4,发起者：用户 接收者：密码服务平台,用户请求验证证书链,证书链验证,读取证书链信息,R,证书链数据,根证书ID、中间证书ID、目标证书ID、证书状态,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同查看证书,nan,4,发起者：用户 接收者：密码服务平台,用户请求验证证书链,证书链验证,输出证书链验证结果,X,证书链验证结果,验证状态、验证时间、证书路径、异常信息,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同查看证书,nan,4,发起者：用户 接收者：密码服务平台,用户请求验证证书链,证书链验证,保存证书链验证记录,W,证书链验证日志,操作员ID、证书ID、验证结果、验证时间,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同导入证书,nan,4,发起者：用户 接收者：密码服务平台,导入客户端密钥证书,客户端密钥证书导入,输入客户端密钥证书导入信息,E,客户端密钥导入信息,密钥ID、证书内容、密钥算法类型、证书有效期,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同导入证书,nan,4,发起者：用户 接收者：密码服务平台,导入客户端密钥证书,客户端密钥证书导入,验证密钥证书格式与有效性,R,密钥验证规则,密钥算法白名单、证书格式规范、有效期阈值,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同导入证书,nan,4,发起者：用户 接收者：密码服务平台,导入客户端密钥证书,客户端密钥证书导入,写入加密判定密钥表,W,加密判定密钥,密钥ID、密钥类型、发送方区域ID、密钥数据,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同导入证书,nan,4,发起者：用户 接收者：密码服务平台,导入客户端密钥证书,客户端密钥证书导入,返回密钥导入结果,X,密钥导入响应,操作状态、密钥ID、错误代码、导入时间,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同业务日志查看列表,nan,4,密码发起者：用户 接收者：密码服务平台,请求业务日志查询,业务日志查询,输入业务日志查询条件,E,业务日志查询条件,查询时间范围、密钥算法类型、租户标识,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同业务日志查看列表,nan,4,密码发起者：用户 接收者：密码服务平台,请求业务日志查询,业务日志查询,读取密钥协同业务日志,R,密钥协同业务日志,日志ID、操作时间、操作类型、密钥状态,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同业务日志查看列表,nan,4,密码发起者：用户 接收者：密码服务平台,请求业务日志查询,业务日志查询,返回业务日志查询结果,X,业务日志结果集,日志详情、操作人、操作结果、密钥算法,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同业务日志查看列表,nan,4,密码发起者：用户 接收者：密码服务平台,请求业务日志查询,业务日志查询,记录日志查询操作记录,W,日志查询记录,查询时间、操作员ID、查询条件、查询结果,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同业务日志审计,nan,4,发起者：用户 接收者：密码服务平台,日志查询请求,业务日志查询,输入业务日志查询条件,E,业务日志查询请求,操作时间范围、操作类型、操作人ID,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同业务日志审计,nan,4,发起者：用户 接收者：密码服务平台,日志查询请求,业务日志查询,验证用户权限并读取日志数据,R,业务日志数据,操作人ID、操作类型、操作详情、操作时间、HMAC校验状态,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同业务日志审计,nan,4,发起者：用户 接收者：密码服务平台,日志查询请求,业务日志查询,返回业务日志查询结果,X,业务日志展示数据,操作人姓名、操作模块、操作状态、操作时间戳,1
客户端密钥协同模块,服务端密钥生成,客户端密钥协同业务日志审计,nan,4,发起者：用户 接收者：密码服务平台,日志查询请求,业务日志查询,记录日志查询操作记录,W,日志审计操作记录,查询人ID、查询条件、查询时间、查询结果数量,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务镜像管理,nan,4,发起者：用户 接收者：密码服务平台,镜像上传,镜像上传,输入镜像上传信息,E,镜像上传信息,镜像名称、服务类型、上传时间,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务镜像管理,nan,4,发起者：用户 接收者：密码服务平台,镜像上传,镜像上传,校验镜像信息,R,镜像校验信息,镜像名称、服务类型、状态,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务镜像管理,nan,4,发起者：用户 接收者：密码服务平台,镜像上传,镜像上传,存储镜像,W,镜像存储信息,镜像ID、服务类型、上传时间,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务镜像管理,nan,4,发起者：用户 接收者：密码服务平台,镜像上传,镜像上传,返回上传结果,X,镜像上传结果,镜像ID、状态、上传时间,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务组管理,nan,4,发起者：用户 接收者：密码服务平台,服务组新增,服务组新增,输入服务组新增信息,E,服务组新增信息,服务组名称、租户标识、服务ID、备份策略ID,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务组管理,nan,4,发起者：用户 接收者：密码服务平台,服务组新增,服务组新增,服务组名称重复性校验,R,服务组校验信息,服务组名称、租户标识、服务ID、主键约束,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务组管理,nan,4,发起者：用户 接收者：密码服务平台,服务组新增,服务组新增,服务组信息持久化存储,W,服务组存储信息,服务组ID、服务组名称、租户标识、服务ID,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务组管理,nan,4,发起者：用户 接收者：密码服务平台,服务组新增,服务组新增,返回服务组新增结果,X,服务组新增结果,服务组ID、服务组名称、操作结果状态,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务组管理,nan,4,发起者：用户 接收者：密码服务平台,服务组修改,服务组修改,输入服务组修改参数,E,服务组修改条件,服务组ID、修改字段、新值,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务组管理,nan,4,发起者：用户 接收者：密码服务平台,服务组修改,服务组修改,读取原服务组配置,R,服务组原始信息,服务组ID、服务组名称、租户标识、服务ID,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务组管理,nan,4,发起者：用户 接收者：密码服务平台,服务组修改,服务组修改,更新服务组配置,W,服务组更新信息,服务组ID、修改字段、新值、修改时间,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务组管理,nan,4,发起者：用户 接收者：密码服务平台,服务组修改,服务组修改,返回服务组修改结果,X,服务组修改结果,服务组ID、修改字段、操作结果状态,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务组管理,nan,4,发起者：用户 接收者：密码服务平台,服务组删除,服务组删除,发起服务组删除请求,E,服务组删除参数,服务组ID、删除原因,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务组管理,nan,4,发起者：用户 接收者：密码服务平台,服务组删除,服务组删除,校验服务组关联关系,R,服务组关联信息,服务组ID、关联密钥数量、备份策略引用,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务组管理,nan,4,发起者：用户 接收者：密码服务平台,服务组删除,服务组删除,执行服务组删除,W,服务组删除记录,服务组ID、删除时间、操作员ID,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务组管理,nan,4,发起者：用户 接收者：密码服务平台,服务组删除,服务组删除,返回服务组删除结果,X,服务组删除结果,服务组ID、删除状态、关联数据状态,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务组管理,nan,4,发起者：用户 接收者：密码服务平台,服务组查询,服务组查询,输入服务组查询条件,E,服务组查询请求,租户标识、服务ID、查询字段,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务组管理,nan,4,发起者：用户 接收者：密码服务平台,服务组查询,服务组查询,读取服务组配置数据,R,服务组查询结果,服务组ID、服务组名称、租户标识、服务ID,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务组管理,nan,4,发起者：用户 接收者：密码服务平台,服务组查询,服务组查询,返回服务组查询结果,X,服务组查询展示信息,服务组ID、服务组名称、租户标识、服务ID,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务组管理,nan,4,发起者：用户 接收者：密码服务平台,服务组查询,服务组查询,记录服务组查询日志,W,服务组查询日志,查询时间、操作员ID、查询条件,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务生命周期管理,nan,4,发起者：用户 接收者：密码服务平台,配置密钥生命周期属性,密钥生命周期配置,输入密钥生命周期配置信息,E,密钥生命周期配置信息,密钥类型标识、生命周期阶段、生效时间、失效时间,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务生命周期管理,nan,4,发起者：用户 接收者：密码服务平台,配置密钥生命周期属性,密钥生命周期配置,读取现有密钥生命周期属性字典,R,密钥生命周期属性字典,ID、D_NAME(名称)、D_VALUE(值)、DEFAULT_FLAG,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务生命周期管理,nan,4,发起者：用户 接收者：密码服务平台,配置密钥生命周期属性,密钥生命周期配置,校验并更新密钥生命周期配置,W,密钥生命周期配置结果,密钥ID、配置状态、更新时间、操作员ID,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务生命周期管理,nan,4,发起者：用户 接收者：密码服务平台,配置密钥生命周期属性,密钥生命周期配置,返回密钥生命周期配置结果,X,密钥生命周期配置反馈,配置状态、密钥类型、生效时间、操作结果,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务数据库管理,nan,4,发起者：用户 接收者：密码服务平台,同步数据库密码请求,同步数据库密码,输入同步数据库密码请求参数,E,数据库密码同步请求,服务ID、租户标识、目标数据库地址、同步时间戳,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务数据库管理,nan,4,发起者：用户 接收者：密码服务平台,同步数据库密码请求,同步数据库密码,读取当前密钥状态信息,R,密钥状态数据,密钥状态、密钥算法、服务ID、租户标识,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务数据库管理,nan,4,发起者：用户 接收者：密码服务平台,同步数据库密码请求,同步数据库密码,验证数据库连接有效性,R,数据库连接验证结果,连接状态、错误代码、验证时间,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务数据库管理,nan,4,发起者：用户 接收者：密码服务平台,同步数据库密码请求,同步数据库密码,更新数据库密码配置,W,数据库密码更新记录,服务ID、新密码哈希值、更新时间、操作员ID,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务数据库管理,nan,4,发起者：用户 接收者：密码服务平台,同步数据库密码请求,同步数据库密码,返回同步结果状态,X,密码同步响应,同步状态、错误信息、操作时间、服务ID,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务数据库管理,nan,4,发起者：用户 接收者：密码服务平台,密钥资源查询请求,租户密钥资源管理,输入租户密钥资源查询条件,E,密钥资源查询请求,租户ID、查询时间范围、资源类型,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务数据库管理,nan,4,发起者：用户 接收者：密码服务平台,密钥资源查询请求,租户密钥资源管理,读取租户密钥资源配额,R,租户密钥资源数据,租户ID、密钥数量、证书数量、配额上限,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务数据库管理,nan,4,发起者：用户 接收者：密码服务平台,密钥资源查询请求,租户密钥资源管理,计算资源使用率,R,资源使用统计,已用密钥数、剩余配额、使用率百分比,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务数据库管理,nan,4,发起者：用户 接收者：密码服务平台,密钥资源查询请求,租户密钥资源管理,输出资源管理视图,X,密钥资源展示数据,租户名称、密钥配额、使用状态、更新时间,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务数据库管理,nan,4,发起者：用户 接收者：密码服务平台,密钥资源查询请求,租户密钥资源管理,记录资源查询日志,W,资源查询操作日志,操作员ID、查询时间、租户ID、查询参数,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务日志管理,nan,4,发起者：用户 接收者：密码服务平台,日志查询,日志查询,输入日志查询条件,E,日志查询请求,查询时间范围、操作类型、用户ID,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务日志管理,nan,4,发起者：用户 接收者：密码服务平台,日志查询,日志查询,读取日志数据,R,日志数据,日志ID、操作时间、操作用户、操作类型、详细信息,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务日志管理,nan,4,发起者：用户 接收者：密码服务平台,日志查询,日志查询,返回日志查询结果,X,日志查询结果,日志列表、总条数、查询时间,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务日志管理,nan,4,发起者：用户 接收者：密码服务平台,日志查询,日志查询,记录日志查询操作,W,日志查询记录,操作用户ID、查询时间、查询条件摘要,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务日志管理,nan,4,发起者：用户 接收者：密码服务平台,日志删除,日志删除,输入日志删除条件,E,日志删除请求,日志ID列表、删除原因,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务日志管理,nan,4,发起者：用户 接收者：密码服务平台,日志删除,日志删除,验证删除权限,R,权限验证信息,用户ID、权限类型、服务ID,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务日志管理,nan,4,发起者：用户 接收者：密码服务平台,日志删除,日志删除,执行日志删除操作,W,日志删除结果,删除状态、删除数量、日志ID列表,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务日志管理,nan,4,发起者：用户 接收者：密码服务平台,日志删除,日志删除,返回日志删除反馈,X,日志删除反馈,删除状态、删除数量、操作时间,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务监控,nan,4,发起者：用户 接收者：密码服务平台,请求密钥状态监控,密钥状态监控,输入密钥监控请求,E,密钥监控请求,密钥算法类型、租户标识、服务组ID,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务监控,nan,4,发起者：用户 接收者：密码服务平台,请求密钥状态监控,密钥状态监控,读取密钥状态信息,R,密钥状态信息,密钥状态、密钥算法、租户标识、服务ID,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务监控,nan,4,发起者：用户 接收者：密码服务平台,请求密钥状态监控,密钥状态监控,生成密钥监控报告,X,监控报告,密钥状态分布、异常密钥数量、租户统计,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务监控,nan,4,发起者：用户 接收者：密码服务平台,请求密钥状态监控,密钥状态监控,保存监控记录,W,监控日志,监控时间、操作员ID、监控结果摘要,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务告警,nan,4,发起者：用户 接收者：密码服务平台,触发密钥协同服务告警,密钥协同服务告警处理,输入密钥协同服务告警信息,E,密钥协同服务告警信息,告警类型、触发时间、服务ID、密钥状态,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务告警,nan,4,发起者：用户 接收者：密码服务平台,触发密钥协同服务告警,密钥协同服务告警处理,读取密钥监控指标数据,R,密钥监控指标数据,密钥算法、密钥状态、租户标识、服务组ID,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务告警,nan,4,发起者：用户 接收者：密码服务平台,触发密钥协同服务告警,密钥协同服务告警处理,生成密钥协同服务告警记录,W,密钥协同服务告警记录,告警内容、触发时间、服务ID、处理状态,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务告警,nan,4,发起者：用户 接收者：密码服务平台,触发密钥协同服务告警,密钥协同服务告警处理,输出密钥协同服务告警结果,X,密钥协同服务告警展示信息,告警类型、触发时间、服务ID、处理建议,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务统计分析,nan,4,发起者：用户 接收者：密码服务平台,请求密钥统计分析,密钥统计分析,输入密钥统计请求参数,E,密钥统计请求,租户标识、服务ID、统计时间范围,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务统计分析,nan,4,发起者：用户 接收者：密码服务平台,请求密钥统计分析,密钥统计分析,读取密钥统计基础数据,R,密钥统计数据,密钥状态、密钥算法、租户标识、服务ID、服务组ID,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务统计分析,nan,4,发起者：用户 接收者：密码服务平台,请求密钥统计分析,密钥统计分析,生成密钥统计结果,W,密钥统计结果,总密钥数量、各状态密钥数量、加解密调用次数,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务统计分析,nan,4,发起者：用户 接收者：密码服务平台,请求密钥统计分析,密钥统计分析,输出密钥统计展示数据,X,密钥统计展示信息,统计维度、密钥数量、调用次数、时间范围,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务状态,nan,4,发起者：用户 接收者：密码服务平台,查询客户端密钥协同服务状态,客户端密钥协同服务状态查询,输入客户端密钥协同服务状态查询条件,E,客户端密钥协同服务状态查询条件,服务ID、租户标识、密钥算法类型,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务状态,nan,4,发起者：用户 接收者：密码服务平台,查询客户端密钥协同服务状态,客户端密钥协同服务状态查询,读取密钥状态统计信息,R,密钥状态统计信息,密钥状态、密钥算法、租户标识、服务ID,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务状态,nan,4,发起者：用户 接收者：密码服务平台,查询客户端密钥协同服务状态,客户端密钥协同服务状态查询,返回客户端密钥协同服务状态查询结果,X,客户端密钥协同服务状态查询结果,服务ID、密钥状态、密钥算法、统计时间,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务状态,nan,4,发起者：用户 接收者：密码服务平台,查询客户端密钥协同服务状态,客户端密钥协同服务状态查询,记录客户端密钥协同服务状态查询日志,W,客户端密钥协同服务状态查询日志,操作员ID、查询时间、查询条件、查询结果,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务状态,nan,4,发起者：用户 接收者：密码服务平台,更新客户端密钥协同服务状态,客户端密钥协同服务状态更新,输入客户端密钥协同服务状态更新参数,E,客户端密钥协同服务状态更新参数,服务ID、目标密钥状态、操作员ID,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务状态,nan,4,发起者：用户 接收者：密码服务平台,更新客户端密钥协同服务状态,客户端密钥协同服务状态更新,验证操作员权限和服务状态有效性,R,服务状态权限验证信息,操作员权限、服务当前状态、密钥状态字典,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务状态,nan,4,发起者：用户 接收者：密码服务平台,更新客户端密钥协同服务状态,客户端密钥协同服务状态更新,更新客户端密钥协同服务状态,W,客户端密钥协同服务状态更新记录,服务ID、原密钥状态、新密钥状态、更新时间,1
客户端密钥协同模块,密钥联合和恢复,客户端密钥协同服务状态,nan,4,发起者：用户 接收者：密码服务平台,更新客户端密钥协同服务状态,客户端密钥协同服务状态更新,返回客户端密钥协同服务状态更新结果,X,客户端密钥协同服务状态更新结果,服务ID、更新状态、更新时间、操作员ID,1
客户端密钥协同模块,证书下发,证书协同证书生成,nan,4,密码发起者：用户 接收者：密码服务平台,生成证书协同证书,证书协同证书生成,输入证书生成请求参数,E,证书生成请求信息,证书类型、序列号、签名算法、密钥算法,1
客户端密钥协同模块,证书下发,证书协同证书生成,nan,4,密码发起者：用户 接收者：密码服务平台,生成证书协同证书,证书协同证书生成,校验证书生成参数有效性,R,证书生成参数校验信息,证书类型约束、序列号格式、签名算法匹配、密钥算法兼容性,1
客户端密钥协同模块,证书下发,证书协同证书生成,nan,4,密码发起者：用户 接收者：密码服务平台,生成证书协同证书,证书协同证书生成,生成协同证书并写入存储,W,协同证书生成结果,证书内容、生成时间戳、证书状态、存储路径,1
客户端密钥协同模块,证书下发,证书协同证书生成,nan,4,密码发起者：用户 接收者：密码服务平台,生成证书协同证书,证书协同证书生成,返回证书生成结果信息,X,证书生成响应数据,证书ID、生成状态、下载链接、操作时间,1
客户端密钥协同模块,证书下发,证书协同证书更新,nan,4,发起者：用户 接收者：密码服务平台,证书更新申请提交,证书更新申请处理,输入证书更新申请信息,E,证书更新申请信息,证书ID、操作员ID、更新原因、更新时间,1
客户端密钥协同模块,证书下发,证书协同证书更新,nan,4,发起者：用户 接收者：密码服务平台,证书更新申请提交,证书更新申请处理,验证原证书有效性,R,原证书状态信息,证书ID、证书状态、签发机构、有效期,1
客户端密钥协同模块,证书下发,证书协同证书更新,nan,4,发起者：用户 接收者：密码服务平台,证书更新申请提交,证书更新申请处理,注销旧证书并生成新证书,W,证书更新操作结果,旧证书ID、新证书ID、更新时间、操作员ID,1
客户端密钥协同模块,证书下发,证书协同证书更新,nan,4,发起者：用户 接收者：密码服务平台,证书更新申请提交,证书更新申请处理,返回证书更新结果,X,证书更新反馈信息,更新状态、新证书ID、更新时间、操作员ID,1
客户端密钥协同模块,证书下发,证书协同证书更新,nan,4,发起者：用户 接收者：密码服务平台,证书更新申请提交,证书更新申请处理,记录证书更新操作日志,W,证书更新日志,操作员ID、操作时间、证书ID、更新状态,1
客户端密钥协同模块,证书下发,证书协同证书注销,nan,4,发起者：用户 接收者：密码服务平台,证书注销申请,证书注销处理,输入证书注销请求信息,E,证书注销请求,证书序列号、操作员ID、注销原因,1
客户端密钥协同模块,证书下发,证书协同证书注销,nan,4,发起者：用户 接收者：密码服务平台,证书注销申请,证书注销处理,验证操作员权限及证书有效性,R,证书权限验证信息,操作员权限等级、证书状态、证书颁发者,1
客户端密钥协同模块,证书下发,证书协同证书注销,nan,4,发起者：用户 接收者：密码服务平台,证书注销申请,证书注销处理,更新证书状态为已注销,W,证书状态更新,证书序列号、注销时间、操作员ID,1
客户端密钥协同模块,证书下发,证书协同证书注销,nan,4,发起者：用户 接收者：密码服务平台,证书注销申请,证书注销处理,返回证书注销结果,X,证书注销结果,证书序列号、注销状态、操作时间,1
客户端密钥协同模块,证书下发,证书协同证书注销,nan,4,发起者：用户 接收者：密码服务平台,证书注销申请,证书注销处理,记录证书注销操作日志,W,证书注销日志,操作员ID、证书序列号、注销原因、操作时间,1
客户端密钥协同模块,证书下发,证书协同证书查询,nan,4,发起者：用户 接收者：密码服务平台,证书查询请求,证书协同证书查询,输入证书查询条件,E,证书查询条件,证书ID、颁发者名称、有效期起始时间、查询类型（单个/批量）,1
客户端密钥协同模块,证书下发,证书协同证书查询,nan,4,发起者：用户 接收者：密码服务平台,证书查询请求,证书协同证书查询,读取证书信息及验证状态,R,证书信息,证书ID、颁发者DN、有效期、证书状态（有效/过期/吊销）、公钥算法,1
客户端密钥协同模块,证书下发,证书协同证书查询,nan,4,发起者：用户 接收者：密码服务平台,证书查询请求,证书协同证书查询,返回证书查询结果,X,证书查询结果,证书ID、颁发者名称、有效期、状态、验证结果（链验证状态）、TLS解析状态,1
客户端密钥协同模块,证书下发,证书协同证书查询,nan,4,发起者：用户 接收者：密码服务平台,证书查询请求,证书协同证书查询,记录证书查询操作日志,W,证书查询日志,操作员ID、查询时间、查询条件、返回记录数、操作结果（成功/失败）,1
