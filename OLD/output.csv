﻿一级功能模块,二级功能模块,三级功能模块,功能描述,预估工作量（人天）,功能用户,触发事件,功能过程,子过程描述,数据移动类型,数据组,数据属性,CFP
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：用户，接收者：总部平台HTTPS对接系统,配置上报路径,总部平台上报路径配置,输入上报路径配置信息,E,上报路径配置信息,IP地址、端口、协议类型（HTTPS）、AKSK凭证、操作员账号,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：用户，接收者：总部平台HTTPS对接系统,配置上报路径,总部平台上报路径配置,校验IP地址格式（IPv4/IPv6）,R,IP地址校验规则,IPv4格式规则、IPv6格式规则,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：用户，接收者：总部平台HTTPS对接系统,配置上报路径,总部平台上报路径配置,保存上报路径配置,W,上报路径配置信息,IP地址、端口、协议类型、AKSK凭证、操作员账号,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：用户，接收者：总部平台HTTPS对接系统,配置上报路径,总部平台上报路径配置,返回配置结果,X,配置结果信息,配置状态、错误信息,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：用户，接收者：总部平台HTTPS对接系统,配置上报路径,总部平台上报路径配置,记录配置操作日志,W,配置操作日志,操作时间、操作员ID、配置内容,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：用户，接收者：总部平台HTTPS对接系统,查看上报路径,总部平台上报路径查看,输入查询条件,E,上报路径查询条件,查询字段（IP地址/端口/协议类型）,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：用户，接收者：总部平台HTTPS对接系统,查看上报路径,总部平台上报路径查看,读取上报路径配置,R,上报路径配置信息,IP地址、端口、协议类型、AKSK凭证,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：用户，接收者：总部平台HTTPS对接系统,查看上报路径,总部平台上报路径查看,返回查询结果,X,上报路径查询结果,IP地址、端口、协议类型、配置时间,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：用户，接收者：总部平台HTTPS对接系统,配置HTTPS通道,总部平台HTTPS通道对接,输入HTTPS通道配置信息,E,HTTPS通道配置信息,TLS版本、加密套件列表、证书路径,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：用户，接收者：总部平台HTTPS对接系统,配置HTTPS通道,总部平台HTTPS通道对接,校验TLS协议兼容性,R,TLS协议校验规则,支持的TLS版本列表,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：用户，接收者：总部平台HTTPS对接系统,配置HTTPS通道,总部平台HTTPS通道对接,建立HTTPS连接,W,HTTPS连接参数,IP地址、端口、TLS配置、证书信息,1
系统管理,总部一级平台对接,总部平台HTTPS对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3,发起者：用户，接收者：总部平台HTTPS对接系统,配置HTTPS通道,总部平台HTTPS通道对接,返回通道状态,X,HTTPS通道状态,连接状态、错误信息,1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：平台操作员，接收者：密码服务管理平台,配置总部平台访问凭证,总部平台访问凭证配置,输入总部平台AKSK配置信息,E,总部平台AKSK配置信息,"platform_id, accesskey_id, secret_key, status",1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：平台操作员，接收者：密码服务管理平台,配置总部平台访问凭证,总部平台访问凭证配置,保存AKSK配置到数据库,W,总部平台AKSK配置信息,"platform_id, accesskey_id, secret_key, status",1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：平台操作员，接收者：密码服务管理平台,配置总部平台访问凭证,总部平台访问凭证配置,返回配置成功提示,X,配置结果信息,"操作状态, 操作时间, 操作员ID",1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：平台操作员，接收者：密码服务管理平台,查看访问凭证,访问凭证查看,查询总部平台AKSK配置,R,总部平台AKSK配置信息,"platform_id, accesskey_id, secret_key, status",1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：平台操作员，接收者：密码服务管理平台,查看访问凭证,访问凭证查看,返回AKSK展示信息,X,总部平台AKSK展示信息,"accesskey_id, status, 配置时间",1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：密码服务管理平台，接收者：集团平台,执行AKSK认证,AKSK认证,读取配置的AKSK信息,R,总部平台AKSK配置信息,"accesskey_id, secret_key, platform_id",1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：密码服务管理平台，接收者：集团平台,执行AKSK认证,AKSK认证,生成密码算法保护的认证请求,X,认证请求信息,"加密后的AKSK, 时间戳, 随机数",1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：密码服务管理平台，接收者：集团平台,执行AKSK认证,AKSK认证,接收集团平台认证响应,E,认证结果信息,"认证状态, 错误代码, 会话令牌",1
系统管理,总部一级平台对接,总部平台AKSK认证对接,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5,发起者：密码服务管理平台，接收者：集团平台,执行AKSK认证,AKSK认证,保存认证日志记录,W,认证日志信息,"操作员ID, 认证时间, 认证状态, IP地址",1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：用户，接收者：系统,用户注册信息列表查询,用户注册信息列表查询,输入用户注册信息查询条件,E,用户注册信息查询条件,查询条件、分页参数,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：用户，接收者：系统,用户注册信息列表查询,用户注册信息列表查询,读取用户注册信息列表,R,用户注册信息列表,序号、账号名、姓名、申请人、申请时间、备注、审核状态、审核时间、审核人、审核意见,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：用户，接收者：系统,用户注册信息列表查询,用户注册信息列表查询,输出用户注册信息列表结果,X,用户注册信息列表展示,序号、账号名、姓名、申请人、申请时间、备注、审核状态、审核时间、审核人、审核意见,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：用户，接收者：系统,注册用户信息录入,注册用户信息,输入注册用户基本信息,E,用户注册信息,账户名、姓名、用户类型、备注,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：用户，接收者：系统,注册用户信息录入,注册用户信息,校验用户注册信息唯一性,R,用户注册校验信息,账户名、用户类型,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：用户，接收者：系统,注册用户信息录入,注册用户信息,保存用户注册信息,W,用户注册信息,账户名、姓名、用户类型、备注,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：用户，接收者：系统,编辑用户信息,编辑用户信息,输入用户信息编辑请求,E,用户信息编辑请求,用户ID、新用户名称,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：用户，接收者：系统,编辑用户信息,编辑用户信息,读取原用户信息,R,用户信息,用户ID、原用户名称,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：用户，接收者：系统,编辑用户信息,编辑用户信息,更新用户信息,W,用户信息,用户ID、新用户名称,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：用户，接收者：系统,编辑用户信息,编辑用户信息,输出用户信息编辑结果,X,用户信息编辑结果,用户ID、新用户名称、编辑状态,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：用户，接收者：系统,删除注册记录,删除注册记录,输入删除注册记录请求,E,用户注册删除请求,用户ID,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：用户，接收者：系统,删除注册记录,删除注册记录,删除用户注册记录,W,用户注册信息,用户ID,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：用户，接收者：系统,用户注册审核,用户注册审核,输入用户注册审核信息,E,用户注册审核信息,审核状态、所属角色、审核意见,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：用户，接收者：系统,用户注册审核,用户注册审核,更新用户注册审核状态,W,用户注册信息,审核状态、所属角色、审核意见,1
系统管理,用户认证管理,用户注册审核,审核通过/拒绝、所属角色、审批意见,3,发起者：用户，接收者：系统,用户注册审核,用户注册审核,记录用户注册审核日志,W,用户注册审核日志,审核人ID、审核时间、审核状态、审核意见,1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：用户，接收者：系统,用户发起查询请求,用户信息列表查询,输入用户信息查询条件,E,用户信息查询条件,账号名、姓名、角色、租户、状态,1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：用户，接收者：系统,用户发起查询请求,用户信息列表查询,读取用户信息列表数据,R,用户信息列表,"USER_ID, USER_CODE, USER_NAME, 角色ID, 租户ID, 账号有效期, 完整性状态, 备注, 创建时间, USER_STATUS",1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：用户，接收者：系统,用户发起查询请求,用户信息列表查询,输出用户信息列表展示,X,用户信息列表展示,"序号, 账号名, 姓名, 角色名称, 租户名称, 账号有效期, 完整性状态, 备注, 创建时间, 状态描述",1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：用户，接收者：系统,用户启用操作,启用用户,输入用户启用请求,E,用户启用请求,"用户ID, 操作员ID",1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：用户，接收者：系统,用户启用操作,启用用户,更新用户状态为启用,W,用户状态信息,"USER_ID, USER_STATUS, 操作时间, 操作员ID",1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：用户，接收者：系统,用户禁用操作,禁用用户,输入用户禁用请求,E,用户禁用请求,"用户ID, 操作员ID",1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：用户，接收者：系统,用户禁用操作,禁用用户,更新用户状态为禁用,W,用户状态信息,"USER_ID, USER_STATUS, 操作时间, 操作员ID",1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：用户，接收者：系统,用户密码重置操作,重置用户密码,输入用户密码重置请求,E,用户密码重置请求,"用户ID, 默认密码",1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：用户，接收者：系统,用户密码重置操作,重置用户密码,更新用户密码信息,W,用户密码信息,"USER_ID, AUTH_CODE, CREATE_TYPE, 操作时间",1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：用户，接收者：系统,用户解锁操作,解锁用户锁定,输入用户解锁请求,E,用户解锁请求,"用户ID, 操作员ID",1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：用户，接收者：系统,用户解锁操作,解锁用户锁定,更新用户锁定状态,W,用户锁定信息,"USER_ID, 锁定状态, 操作时间, 操作员ID",1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：用户，接收者：系统,设置密码有效期,设置用户的口令有效期,输入密码有效期设置参数,E,密码有效期设置,"用户ID, 有效期天数",1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：用户，接收者：系统,设置密码有效期,设置用户的口令有效期,更新用户密码有效期配置,W,用户密码有效期配置,"USER_ID, 密码有效期, 更新时间",1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：用户，接收者：系统,设置密码有效期,设置用户的口令有效期,输出密码有效期设置结果,X,密码有效期设置结果,"用户ID, 有效期天数, 设置状态",1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：用户，接收者：系统,用户删除操作,删除用户,输入用户删除请求,E,用户删除请求,"用户ID, 操作员ID",1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：用户，接收者：系统,用户删除操作,删除用户,验证用户删除权限,R,用户权限信息,"操作员ID, 用户ID, 删除权限",1
系统管理,用户认证管理,用户信息管理,删除用户,2,发起者：用户，接收者：系统,用户删除操作,删除用户,执行用户数据删除,W,用户信息,"USER_ID, 删除标记, 操作时间",1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,用户提交登录请求,口令登录,输入用户登录信息,E,用户登录信息,用户名、口令、登录时间,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,用户提交登录请求,口令登录,验证用户口令有效性,R,用户认证信息,用户ID、加密口令、账户状态,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,用户提交登录请求,口令登录,返回登录结果,X,登录响应信息,登录状态、会话ID、错误代码,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,Ukey插入并提交登录,Ukey登录,读取Ukey设备信息,R,Ukey设备信息,Ukey序列号、数字证书、有效期,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,Ukey插入并提交登录,Ukey登录,验证Ukey合法性,R,Ukey认证信息,用户绑定状态、证书状态,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,Ukey插入并提交登录,Ukey登录,生成临时会话密钥,W,会话密钥信息,加密算法、密钥值、有效期,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,Ukey插入并提交登录,Ukey登录,返回Ukey登录结果,X,Ukey登录响应,登录状态、会话ID、安全令牌,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,Ukey插入并提交登录,Ukey登录,记录Ukey登录日志,W,安全日志信息,登录时间、用户ID、Ukey序列号,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,查询口令黑名单,口令黑名单列表查询,输入黑名单查询条件,E,黑名单查询请求,查询关键字、分页参数,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,查询口令黑名单,口令黑名单列表查询,读取口令黑名单数据,R,口令黑名单数据,明文口令、密文口令、禁用状态,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,查询口令黑名单,口令黑名单列表查询,返回黑名单查询结果,X,黑名单查询结果,口令列表、记录总数、分页信息,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,新增口令黑名单,新建口令黑名单,输入黑名单新增信息,E,黑名单新增信息,明文口令、禁用原因,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,新增口令黑名单,新建口令黑名单,校验口令黑名单唯一性,R,黑名单校验数据,明文口令、密文口令,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,新增口令黑名单,新建口令黑名单,保存黑名单记录,W,黑名单存储数据,明文口令、密文口令、创建时间,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,修改口令黑名单,编辑口令黑名单,输入黑名单修改信息,E,黑名单修改信息,口令ID、更新后的禁用原因,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,修改口令黑名单,编辑口令黑名单,读取原始黑名单记录,R,黑名单原始数据,明文口令、密文口令、修改时间,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,修改口令黑名单,编辑口令黑名单,更新黑名单记录,W,黑名单更新数据,明文口令、密文口令、更新时间,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,删除口令黑名单,删除口令黑名单,输入黑名单删除请求,E,黑名单删除请求,口令ID、删除原因,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,删除口令黑名单,删除口令黑名单,验证删除权限,R,权限验证数据,用户角色、操作权限,1
系统管理,访问控制管理,用户口令管理,删除用户登录口令黑名单,2,发起者：用户，接收者：系统,删除口令黑名单,删除口令黑名单,执行黑名单删除,W,黑名单删除记录,口令ID、删除时间、操作员ID,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,请求查看智能密码钥匙列表,智能密码钥匙列表展示,输入智能密码钥匙查询条件,E,智能密码钥匙查询条件,序列号、类型、所属账号名、所属用户名,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,请求查看智能密码钥匙列表,智能密码钥匙列表展示,读取智能密码钥匙数据,R,智能密码钥匙信息,序列号、类型、所属账号ID、所属用户ID、状态,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,请求查看智能密码钥匙列表,智能密码钥匙列表展示,返回智能密码钥匙列表结果,X,智能密码钥匙列表展示信息,序列号、类型、所属账号名、所属用户名、状态,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,请求查看智能密码钥匙列表,智能密码钥匙列表展示,记录智能密码钥匙查询日志,W,智能密码钥匙查询日志,操作员ID、查询时间、查询条件,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,提交智能密码钥匙新增请求,智能密码钥匙新增,输入智能密码钥匙新增信息,E,智能密码钥匙新增信息,UKey类型、UKey口令、绑定用户ID,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,提交智能密码钥匙新增请求,智能密码钥匙新增,校验UKey口令有效性,R,UKey口令校验信息,UKey口令、HMAC校验值,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,提交智能密码钥匙新增请求,智能密码钥匙新增,保存智能密码钥匙配置,W,智能密码钥匙配置信息,UKey序列号、绑定用户ID、UKey类型、状态,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,提交智能密码钥匙新增请求,智能密码钥匙新增,返回智能密码钥匙新增结果,X,智能密码钥匙新增结果,序列号、绑定用户名、操作结果,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,提交智能密码钥匙启用请求,智能密码钥匙启用,输入智能密码钥匙启用请求,E,智能密码钥匙启用请求,UKey序列号、操作员ID,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,提交智能密码钥匙启用请求,智能密码钥匙启用,验证操作员权限,R,权限验证信息,操作员ID、权限等级,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,提交智能密码钥匙启用请求,智能密码钥匙启用,更新智能密码钥匙状态为启用,W,智能密码钥匙状态信息,UKey序列号、状态、更新时间,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,提交智能密码钥匙启用请求,智能密码钥匙启用,返回智能密码钥匙启用结果,X,智能密码钥匙启用结果,UKey序列号、操作结果,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,提交智能密码钥匙禁用请求,智能密码钥匙禁用,输入智能密码钥匙禁用请求,E,智能密码钥匙禁用请求,UKey序列号、操作员ID,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,提交智能密码钥匙禁用请求,智能密码钥匙禁用,验证操作员权限,R,权限验证信息,操作员ID、权限等级,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,提交智能密码钥匙禁用请求,智能密码钥匙禁用,更新智能密码钥匙状态为禁用,W,智能密码钥匙状态信息,UKey序列号、状态、更新时间,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,提交智能密码钥匙禁用请求,智能密码钥匙禁用,返回智能密码钥匙禁用结果,X,智能密码钥匙禁用结果,UKey序列号、操作结果,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,提交智能密码钥匙删除请求,智能密码钥匙删除,输入智能密码钥匙删除请求,E,智能密码钥匙删除请求,UKey序列号、操作员ID,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,提交智能密码钥匙删除请求,智能密码钥匙删除,验证操作员权限,R,权限验证信息,操作员ID、权限等级,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,提交智能密码钥匙删除请求,智能密码钥匙删除,删除智能密码钥匙配置,W,智能密码钥匙删除信息,UKey序列号、删除时间,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,提交智能密码钥匙删除请求,智能密码钥匙删除,返回智能密码钥匙删除结果,X,智能密码钥匙删除结果,UKey序列号、操作结果,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,配置是否开启口令登录,是否开启口令登录,输入口令登录配置请求,E,口令登录配置请求,开启状态、操作员ID,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,配置是否开启口令登录,是否开启口令登录,更新口令登录配置,W,口令登录配置信息,开启状态、更新时间,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,配置是否开启口令登录,是否开启口令登录,返回口令登录配置结果,X,口令登录配置结果,当前开启状态、操作结果,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,配置是否开启UKey登录,是否开启UKey登录,输入UKey登录配置请求,E,UKey登录配置请求,开启状态、操作员ID,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,配置是否开启UKey登录,是否开启UKey登录,更新UKey登录配置,W,UKey登录配置信息,开启状态、更新时间,1
系统管理,访问控制管理,用户ukey策略管理,是否开启UKey登录，允许用户使用Ukey进行登录,2,发起者：用户，接收者：系统,配置是否开启UKey登录,是否开启UKey登录,返回UKey登录配置结果,X,UKey登录配置结果,当前开启状态、操作结果,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：系统,设置用户默认口令,系统管理员设置用户默认口令,输入用户默认口令配置信息,E,用户默认口令配置,默认口令值、生效时间、配置描述,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：系统,设置用户默认口令,系统管理员设置用户默认口令,保存用户默认口令配置,W,用户默认口令配置,默认口令值、生效时间、配置描述,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：系统,配置历史口令限制次数,系统管理员配置历史口令限制次数,输入历史口令限制配置信息,E,历史口令限制配置,限制次数、生效时间、配置描述,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：系统,配置历史口令限制次数,系统管理员配置历史口令限制次数,保存历史口令限制配置,W,历史口令限制配置,限制次数、生效时间、配置描述,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：系统,配置长时间未登录禁用账户天数,系统管理员配置长时间未登录禁用账户天数,输入未登录禁用配置信息,E,未登录禁用配置,禁用天数、生效时间、配置描述,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：系统,配置长时间未登录禁用账户天数,系统管理员配置长时间未登录禁用账户天数,保存未登录禁用配置,W,未登录禁用配置,禁用天数、生效时间、配置描述,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：系统,配置口令有效期天数,系统管理员配置口令有效期天数,输入口令有效期配置信息,E,口令有效期配置,有效期天数、生效时间、配置描述,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：系统,配置口令有效期天数,系统管理员配置口令有效期天数,保存口令有效期配置,W,口令有效期配置,有效期天数、生效时间、配置描述,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：系统,配置口令有效期告警天数,系统管理员配置口令有效期告警天数,输入口令告警配置信息,E,口令告警配置,告警天数、生效时间、配置描述,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：系统,配置口令有效期告警天数,系统管理员配置口令有效期告警天数,保存口令告警配置,W,口令告警配置,告警天数、生效时间、配置描述,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：系统,配置登录失败次数限制,系统管理员配置登录失败次数限制,输入登录失败限制配置信息,E,登录失败限制配置,失败次数、锁定状态、生效时间,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：系统,配置登录失败次数限制,系统管理员配置登录失败次数限制,保存登录失败限制配置,W,登录失败限制配置,失败次数、锁定状态、生效时间,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：系统,配置登录失败锁定时长,系统管理员配置登录失败锁定时长,输入登录失败锁定时长配置信息,E,登录失败锁定配置,锁定时长、生效时间、配置描述,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：系统,配置登录失败锁定时长,系统管理员配置登录失败锁定时长,保存登录失败锁定时长配置,W,登录失败锁定配置,锁定时长、生效时间、配置描述,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：系统,配置是否强制修改默认口令,系统管理员配置是否强制修改默认口令,输入强制修改默认口令配置信息,E,强制修改口令配置,强制修改标志、生效时间、配置描述,1
系统管理,访问控制管理,用户口令策略管理,是否强制修改默认口令,2,发起者：系统管理员，接收者：系统,配置是否强制修改默认口令,系统管理员配置是否强制修改默认口令,保存强制修改默认口令配置,W,强制修改口令配置,强制修改标志、生效时间、配置描述,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：用户，接收者：系统,请求查看上报内容列表,上报内容列表,输入上报内容查询条件,E,上报配置查询请求,分页参数、过滤条件（上报内容标识、启用状态）,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：用户，接收者：系统,请求查看上报内容列表,上报内容列表,读取上报配置数据,R,上报配置列表数据,上报内容标识、上报内容名称、上报频率ID、启用状态、最后上报时间,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：用户，接收者：系统,请求查看上报内容列表,上报内容列表,处理分页信息,R,分页信息,当前页码、每页数量、总记录数,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：用户，接收者：系统,请求查看上报内容列表,上报内容列表,输出上报内容列表,X,上报配置列表展示数据,上报内容名称、频率名称、启用状态、最后上报时间、分页信息,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：用户，接收者：系统,请求查看上报内容列表,上报内容列表,记录上报内容查询日志,W,上报内容查询日志,操作员ID、查询时间、查询条件、返回记录数,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：用户，接收者：系统,修改上报内容启用状态,上报内容配置,输入上报内容配置信息,E,上报内容配置信息,上报内容标识、启用状态,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：用户，接收者：系统,修改上报内容启用状态,上报内容配置,读取当前上报配置,R,上报内容当前配置,上报内容标识、启用状态、上报频率ID,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：用户，接收者：系统,修改上报内容启用状态,上报内容配置,更新上报启用状态,W,上报内容配置结果,上报内容标识、新启用状态、更新时间,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：用户，接收者：系统,修改上报内容启用状态,上报内容配置,输出配置结果,X,上报内容配置反馈,操作结果（成功/失败）、更新后状态,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：用户，接收者：系统,修改上报内容启用状态,上报内容配置,记录上报配置变更日志,W,上报配置变更日志,操作员ID、操作时间、变更前状态、变更后状态,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：用户，接收者：系统,修改上报频率配置,上报频率配置,输入上报频率配置信息,E,上报频率配置信息,上报内容标识、频率ID、cron表达式,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：用户，接收者：系统,修改上报频率配置,上报频率配置,读取当前频率配置,R,当前频率配置,上报内容标识、当前频率ID、当前cron表达式,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：用户，接收者：系统,修改上报频率配置,上报频率配置,校验频率有效性,R,频率字典信息,频率ID、cron表达式、频率名称,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：用户，接收者：系统,修改上报频率配置,上报频率配置,更新上报频率配置,W,上报频率配置结果,上报内容标识、新频率ID、新cron表达式、更新时间,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：用户，接收者：系统,修改上报频率配置,上报频率配置,输出频率配置结果,X,上报频率配置反馈,操作结果、更新后频率名称、cron表达式,1
系统管理,上报周期管理,上报周期及频率管理,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7,发起者：用户，接收者：系统,修改上报频率配置,上报频率配置,记录频率配置变更日志,W,频率配置变更日志,操作员ID、操作时间、变更前频率、变更后频率,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：用户，接收者：系统,查询登录日志,查询登录日志,输入登录日志查询条件,E,登录日志查询条件,操作时间范围、用户ID、操作结果（0-成功，1-失败）、分页参数（页码/每页条数）,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：用户，接收者：系统,查询登录日志,查询登录日志,读取登录日志数据,R,登录日志信息,ID、租户ID、应用ID、操作时间、操作用户、操作结果、IP地址、操作详情,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：用户，接收者：系统,查询登录日志,查询登录日志,返回登录日志查询结果,X,登录日志查询结果,操作时间、用户ID、操作结果、IP地址、分页信息（总条数/当前页）,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：用户，接收者：系统,查询登录日志,查询登录日志,保存登录日志查询记录,W,登录日志查询记录,查询时间、查询用户ID、查询条件摘要、查询结果条数,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：用户，接收者：系统,批量审计登录日志,批量审计,输入批量审计请求,E,批量审计请求,待审计日志ID列表、审计意见、操作员ID,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：用户，接收者：系统,批量审计登录日志,批量审计,读取待审计日志信息,R,待审计日志信息,ID、操作时间、操作用户、原始操作结果、IP地址,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：用户，接收者：系统,批量审计登录日志,批量审计,更新日志审计状态,W,日志审计结果,ID、审计状态（已审计/未审计）、审计时间、审计意见,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：用户，接收者：系统,批量审计登录日志,批量审计,返回批量审计结果,X,批量审计反馈,成功审计条数、失败条数、审计完成时间,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：用户，接收者：系统,导出登录日志,日志导出,输入日志导出参数,E,日志导出请求,导出时间范围、用户ID、操作结果、最大导出条数（5000）,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：用户，接收者：系统,导出登录日志,日志导出,读取待导出日志数据,R,待导出日志数据,ID、操作时间、用户ID、操作结果、IP地址、操作详情,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：用户，接收者：系统,导出登录日志,日志导出,生成日志导出文件,X,日志导出文件,文件格式（CSV/Excel）、文件大小、导出时间戳,1
系统管理,日志管理/统计分析,登录日志管理,导出登录日志,4,发起者：用户，接收者：系统,导出登录日志,日志导出,记录日志导出操作,W,日志导出记录,导出时间、导出用户ID、导出条数、文件存储路径,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：用户，接收者：系统,输入查询条件,操作日志查询,输入操作日志查询条件,E,操作日志查询请求,功能模块、操作人、操作时间范围、操作结果、租户ID,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：用户，接收者：系统,输入查询条件,操作日志查询,读取操作日志数据,R,操作日志,ID、TENANT_ID、APP_ID、OPER_TYPE、RESULT、OPER_TIME、OPER_USER,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：用户，接收者：系统,输入查询条件,操作日志查询,返回操作日志查询结果,X,操作日志查询结果,ID、功能模块、操作人、操作时间、操作结果、租户名称,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：用户，接收者：系统,输入查询条件,操作日志查询,保存操作日志查询记录,W,操作日志查询记录,查询条件、查询时间、操作人ID,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：用户，接收者：系统,点击批量审批按钮,批量审批,输入批量审批请求,E,批量审批请求,日志ID列表、审批状态、审批意见,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：用户，接收者：系统,点击批量审批按钮,批量审批,读取待审批操作日志,R,待审批操作日志,ID、AUDIT_STATUS、OPER_TYPE、OPER_TIME,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：用户，接收者：系统,点击批量审批按钮,批量审批,更新操作日志审批状态,W,操作日志审批结果,ID、AUDIT_STATUS、AUDIT_TIME、AUDIT_USER,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：用户，接收者：系统,点击批量审批按钮,批量审批,返回批量审批结果,X,批量审批反馈,成功数量、失败数量、失败原因列表,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：用户，接收者：系统,点击导出按钮,日志导出,输入日志导出请求,E,日志导出请求,导出格式、导出字段、分页参数,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：用户，接收者：系统,点击导出按钮,日志导出,读取待导出操作日志,R,操作日志,ID、OPER_TYPE、OPER_TIME、RESULT、OPER_USER,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：用户，接收者：系统,点击导出按钮,日志导出,生成操作日志导出文件,X,操作日志导出数据,CSV/Excel格式文件、导出时间戳,1
系统管理,日志管理/统计分析,操作日志管理,导出操作日志,4,发起者：用户，接收者：系统,点击导出按钮,日志导出,保存日志导出记录,W,日志导出记录,导出时间、导出数量、导出用户ID,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：用户，接收者：密码服务管理平台,分页查询密码应用类型,密码应用类型分页列表查询,输入分页查询参数,E,密码应用类型分页请求,当前页码、每页数量,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：用户，接收者：密码服务管理平台,分页查询密码应用类型,密码应用类型分页列表查询,读取密码应用类型数据,R,密码应用类型数据,类型编码、类型名称、备注、创建时间,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：用户，接收者：密码服务管理平台,分页查询密码应用类型,密码应用类型分页列表查询,返回分页查询结果,X,密码应用类型分页结果,类型编码、类型名称、备注、创建时间、操作按钮,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：用户，接收者：密码服务管理平台,过滤查询密码应用类型,密码应用类型过滤查询,输入过滤条件,E,密码应用类型过滤条件,类型名称、类型编码,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：用户，接收者：密码服务管理平台,过滤查询密码应用类型,密码应用类型过滤查询,读取匹配的密码应用类型,R,密码应用类型数据,类型编码、类型名称、备注、创建时间,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：用户，接收者：密码服务管理平台,过滤查询密码应用类型,密码应用类型过滤查询,返回过滤查询结果,X,密码应用类型过滤结果,类型编码、类型名称、备注、创建时间、操作按钮,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：用户，接收者：密码服务管理平台,新增密码应用类型,新增密码应用类型,输入新增密码应用类型信息,E,密码应用类型新增信息,类型编码、类型名称、备注,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：用户，接收者：密码服务管理平台,新增密码应用类型,新增密码应用类型,校验类型编码唯一性,R,密码应用类型校验信息,类型编码、类型名称,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：用户，接收者：密码服务管理平台,新增密码应用类型,新增密码应用类型,保存新增密码应用类型,W,密码应用类型新增记录,类型编码、类型名称、备注、创建时间,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：用户，接收者：密码服务管理平台,新增密码应用类型,新增密码应用类型,返回新增结果,X,密码应用类型新增结果,类型编码、类型名称、操作状态,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：用户，接收者：密码服务管理平台,编辑密码应用类型,编辑密码应用类型,输入编辑密码应用类型信息,E,密码应用类型编辑信息,类型编码、类型名称、备注,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：用户，接收者：密码服务管理平台,编辑密码应用类型,编辑密码应用类型,读取原始密码应用类型数据,R,密码应用类型原始数据,类型编码、类型名称、备注,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：用户，接收者：密码服务管理平台,编辑密码应用类型,编辑密码应用类型,更新密码应用类型信息,W,密码应用类型更新记录,类型编码、类型名称、备注、修改时间,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：用户，接收者：密码服务管理平台,编辑密码应用类型,编辑密码应用类型,返回编辑结果,X,密码应用类型编辑结果,类型编码、类型名称、操作状态,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：用户，接收者：密码服务管理平台,删除密码应用类型,删除密码应用类型,输入删除密码应用类型请求,E,密码应用类型删除请求,类型编码,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：用户，接收者：密码服务管理平台,删除密码应用类型,删除密码应用类型,检查关联应用存在性,R,密码应用关联检查,类型编码、关联应用数量,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：用户，接收者：密码服务管理平台,删除密码应用类型,删除密码应用类型,删除密码应用类型,W,密码应用类型删除记录,类型编码、删除时间,1
密码应用数据管理,密码应用类型管理,密码应用类型管理,应用类型下无对应用时，允许删除密码应用类型,4,发起者：用户，接收者：密码服务管理平台,删除密码应用类型,删除密码应用类型,返回删除结果,X,密码应用类型删除结果,类型编码、操作状态,1
密码应用数据管理,密码应用类型管理,应用关联应用类型,统计展示平台中应用类型下包含的应用数量分布,4,发起者：用户，接收者：密码服务管理平台,创建应用或过滤查询,密码应用类型下拉选择,输入应用类型查询请求,E,应用类型查询请求,查询条件、分页参数,1
密码应用数据管理,密码应用类型管理,应用关联应用类型,统计展示平台中应用类型下包含的应用数量分布,4,发起者：用户，接收者：密码服务管理平台,创建应用或过滤查询,密码应用类型下拉选择,读取密码应用类型数据,R,密码应用类型数据,类型ID、类型名称、创建时间,1
密码应用数据管理,密码应用类型管理,应用关联应用类型,统计展示平台中应用类型下包含的应用数量分布,4,发起者：用户，接收者：密码服务管理平台,创建应用或过滤查询,密码应用类型下拉选择,输出应用类型下拉列表,X,应用类型下拉列表,类型ID、类型名称、排序字段,1
密码应用数据管理,密码应用类型管理,应用关联应用类型,统计展示平台中应用类型下包含的应用数量分布,4,发起者：用户，接收者：密码服务管理平台,请求应用类型数量分布,密码应用类型应用数量分布,输入数量分布统计请求,E,应用类型数量分布请求,统计维度、过滤条件,1
密码应用数据管理,密码应用类型管理,应用关联应用类型,统计展示平台中应用类型下包含的应用数量分布,4,发起者：用户，接收者：密码服务管理平台,请求应用类型数量分布,密码应用类型应用数量分布,读取密码应用关联数据,R,密码应用关联数据,应用ID、类型ID、关联状态,1
密码应用数据管理,密码应用类型管理,应用关联应用类型,统计展示平台中应用类型下包含的应用数量分布,4,发起者：用户，接收者：密码服务管理平台,请求应用类型数量分布,密码应用类型应用数量分布,计算类型数量分布,W,应用类型数量分布数据,类型ID、类型名称、应用数量,1
密码应用数据管理,密码应用类型管理,应用关联应用类型,统计展示平台中应用类型下包含的应用数量分布,4,发起者：用户，接收者：密码服务管理平台,请求应用类型数量分布,密码应用类型应用数量分布,输出数量分布统计结果,X,应用类型数量分布结果,类型ID、类型名称、应用数量、占比,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户发起分页列表查询请求,用户查询密码应用分页列表,输入分页查询条件,E,分页查询条件,页码、每页条数、排序字段、排序方式,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户发起分页列表查询请求,用户查询密码应用分页列表,读取密码应用分页数据,R,密码应用信息,应用标识、应用名称、所属单位、业务描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户发起分页列表查询请求,用户查询密码应用分页列表,返回分页查询结果,X,分页查询结果,应用标识、应用名称、所属单位、业务描述、总记录数,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户发起分页列表查询请求,用户查询密码应用分页列表,记录分页查询日志,W,操作日志,操作人ID、操作时间、查询条件、查询结果数量,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户发起过滤查询请求,用户过滤查询密码应用,输入过滤条件,E,过滤查询条件,应用标识、应用名称、简称,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户发起过滤查询请求,用户过滤查询密码应用,读取匹配的密码应用数据,R,密码应用信息,应用标识、应用名称、简称、所属单位,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户发起过滤查询请求,用户过滤查询密码应用,返回过滤查询结果,X,过滤查询结果,应用标识、应用名称、简称、所属单位、匹配记录数,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户发起过滤查询请求,用户过滤查询密码应用,记录过滤查询日志,W,操作日志,操作人ID、操作时间、过滤条件、匹配记录数,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户提交新增密码应用请求,用户新增密码应用,输入新增密码应用信息,E,密码应用新增信息,应用标识、应用名称、所属单位、业务类型、密码服务集群、认证方式、业务描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户提交新增密码应用请求,用户新增密码应用,校验应用标识唯一性,R,密码应用信息,应用标识,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户提交新增密码应用请求,用户新增密码应用,保存新增密码应用,W,密码应用信息,应用标识、应用名称、所属单位、业务类型、密码服务集群、认证方式、业务描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户提交新增密码应用请求,用户新增密码应用,返回新增结果,X,操作结果,操作状态、应用标识、应用名称,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户提交新增密码应用请求,用户新增密码应用,记录新增操作日志,W,操作日志,操作人ID、操作时间、新增内容、操作结果,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户提交新增密码应用请求,用户新增密码应用,同步更新密码服务调度关系,W,密码服务调度关系,应用标识、密码服务集群,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户提交编辑密码应用请求,用户编辑密码应用,输入编辑密码应用信息,E,密码应用编辑信息,应用标识、应用名称、所属单位、业务描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户提交编辑密码应用请求,用户编辑密码应用,读取原始密码应用数据,R,密码应用信息,应用标识、应用名称、所属单位、业务描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户提交编辑密码应用请求,用户编辑密码应用,保存编辑后的密码应用,W,密码应用信息,应用标识、应用名称、所属单位、业务描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户提交编辑密码应用请求,用户编辑密码应用,返回编辑结果,X,操作结果,操作状态、应用标识、应用名称,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户提交编辑密码应用请求,用户编辑密码应用,记录编辑操作日志,W,操作日志,操作人ID、操作时间、编辑内容、操作结果,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户提交删除密码应用请求,用户删除密码应用,输入删除密码应用标识,E,删除请求,应用标识,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户提交删除密码应用请求,用户删除密码应用,校验应用关联数据,R,密码应用关联数据,应用标识、密钥数量、证书数量,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户提交删除密码应用请求,用户删除密码应用,删除密码应用,W,密码应用信息,应用标识,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户提交删除密码应用请求,用户删除密码应用,同步删除认证方式和调度关系,W,密码服务调度关系,应用标识,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户提交删除密码应用请求,用户删除密码应用,返回删除结果,X,操作结果,操作状态、应用标识,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户提交删除密码应用请求,用户删除密码应用,记录删除操作日志,W,操作日志,操作人ID、操作时间、删除内容、操作结果,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户请求查看密码应用详情,用户查看密码应用详情,输入密码应用标识,E,详情请求,应用标识,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户请求查看密码应用详情,用户查看密码应用详情,读取密码应用详细信息,R,密码应用信息,应用标识、应用名称、所属单位、业务类型、密码服务集群、认证方式、业务描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户请求查看密码应用详情,用户查看密码应用详情,返回密码应用详情,X,密码应用详情,应用标识、应用名称、所属单位、业务类型、密码服务集群、认证方式、业务描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码服务管理平台,用户请求查看密码应用详情,用户查看密码应用详情,记录详情查看日志,W,操作日志,操作人ID、操作时间、查看的应用标识,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：系统，接收者：密码服务管理平台,系统定时校验密码应用数据完整性,系统校验密码应用数据完整性,读取所有密码应用数据,R,密码应用信息,应用标识、应用名称、所属单位、业务描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：系统，接收者：密码服务管理平台,系统定时校验密码应用数据完整性,系统校验密码应用数据完整性,校验数据完整性,R,数据校验规则,必填字段列表、字段格式规则,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：系统，接收者：密码服务管理平台,系统定时校验密码应用数据完整性,系统校验密码应用数据完整性,生成校验结果,W,数据校验结果,应用标识、校验状态、异常字段、异常描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：系统，接收者：密码服务管理平台,系统定时校验密码应用数据完整性,系统校验密码应用数据完整性,输出校验异常信息,X,数据校验异常,应用标识、异常字段、异常描述,1
密码应用数据管理,密码应用管理,密码应用管理,保障密码应用的数据完整性，如数据被篡改，显示异常,3,发起者：系统，接收者：密码服务管理平台,系统定时校验密码应用数据完整性,系统校验密码应用数据完整性,记录校验日志,W,操作日志,操作时间、校验结果数量、异常数量,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户发起认证凭证列表查询,应用认证凭证列表查询,输入认证凭证列表查询请求,E,认证凭证列表查询请求,分页参数、排序方式,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户发起认证凭证列表查询,应用认证凭证列表查询,读取认证凭证基础数据,R,认证凭证基础数据,凭证ID、凭证名称、认证方式、创建时间、状态,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户发起认证凭证列表查询,应用认证凭证列表查询,返回认证凭证列表展示数据,X,认证凭证列表展示数据,凭证ID、凭证名称、认证方式、创建时间、状态、分页信息,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户发起认证凭证过滤查询,应用认证凭证过滤查询,输入认证凭证过滤查询条件,E,认证凭证过滤查询条件,密钥ID、描述关键字,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户发起认证凭证过滤查询,应用认证凭证过滤查询,读取认证凭证基础数据,R,认证凭证基础数据,凭证ID、凭证名称、认证方式、密钥ID、描述、创建时间、状态,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户发起认证凭证过滤查询,应用认证凭证过滤查询,返回认证凭证过滤结果,X,认证凭证过滤结果,凭证ID、凭证名称、认证方式、密钥ID、描述、创建时间、状态,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户新增认证凭证,新增应用认证凭证,输入认证凭证新增信息,E,认证凭证新增信息,凭证名称、认证方式、密钥ID、描述,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户新增认证凭证,新增应用认证凭证,校验认证凭证唯一性,R,认证凭证校验信息,凭证名称、密钥ID,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户新增认证凭证,新增应用认证凭证,生成SK文件并保存,W,认证凭证SK文件信息,SK文件内容、文件路径、生成时间,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户新增认证凭证,新增应用认证凭证,写入认证凭证基础数据,W,认证凭证基础数据,凭证ID、凭证名称、认证方式、密钥ID、描述、SK文件路径、创建时间,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户新增认证凭证,新增应用认证凭证,通知认证中心同步凭证,X,认证中心同步请求,凭证ID、认证方式、密钥ID、SK文件路径,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户编辑认证凭证,编辑应用认证凭证,输入认证凭证编辑信息,E,认证凭证编辑信息,凭证ID、新描述,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户编辑认证凭证,编辑应用认证凭证,读取原始认证凭证数据,R,认证凭证基础数据,凭证ID、凭证名称、认证方式、密钥ID、描述、创建时间,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户编辑认证凭证,编辑应用认证凭证,更新认证凭证描述信息,W,认证凭证基础数据,凭证ID、新描述,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户编辑认证凭证,编辑应用认证凭证,返回认证凭证更新结果,X,认证凭证更新结果,凭证ID、更新状态、更新时间,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户启用认证凭证,启用应用认证凭证,输入认证凭证启用请求,E,认证凭证启用请求,凭证ID,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户启用认证凭证,启用应用认证凭证,读取认证凭证状态,R,认证凭证状态信息,凭证ID、当前状态,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户启用认证凭证,启用应用认证凭证,更新认证凭证状态为启用,W,认证凭证状态信息,凭证ID、新状态,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户启用认证凭证,启用应用认证凭证,通知认证中心启用凭证,X,认证中心启用请求,凭证ID,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户停用认证凭证,停用应用认证凭证,输入认证凭证停用请求,E,认证凭证停用请求,凭证ID,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户停用认证凭证,停用应用认证凭证,读取认证凭证状态,R,认证凭证状态信息,凭证ID、当前状态,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户停用认证凭证,停用应用认证凭证,更新认证凭证状态为停用,W,认证凭证状态信息,凭证ID、新状态,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户停用认证凭证,停用应用认证凭证,通知认证中心停用凭证,X,认证中心停用请求,凭证ID,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户删除认证凭证,删除应用认证凭证,输入认证凭证删除请求,E,认证凭证删除请求,凭证ID,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户删除认证凭证,删除应用认证凭证,读取认证凭证关联数据,R,认证凭证关联数据,凭证ID、SK文件路径,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户删除认证凭证,删除应用认证凭证,删除认证凭证基础数据,W,认证凭证基础数据,凭证ID,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：用户，接收者：密码应用数据管理系统,用户删除认证凭证,删除应用认证凭证,清除认证中心凭证数据,X,认证中心清除请求,凭证ID,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：系统，接收者：密码应用数据管理系统,定时校验认证凭证完整性,应用认证凭证完整性校验,读取认证凭证基础数据,R,认证凭证基础数据,凭证ID、SK文件路径、校验哈希值,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：系统，接收者：密码应用数据管理系统,定时校验认证凭证完整性,应用认证凭证完整性校验,校验SK文件完整性,R,SK文件完整性校验,文件路径、实际哈希值,1
密码应用数据管理,密码应用管理,密码应用认证凭证管理,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3,发起者：系统，接收者：密码应用数据管理系统,定时校验认证凭证完整性,应用认证凭证完整性校验,返回完整性校验结果,X,认证凭证完整性校验结果,凭证ID、校验状态、异常信息,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：用户，接收者：密码服务管理平台,请求密码应用业务功能列表,密码应用业务功能列表,输入密码应用业务列表查询条件,E,密码应用业务列表查询条件,服务类型、分页参数,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：用户，接收者：密码服务管理平台,请求密码应用业务功能列表,密码应用业务功能列表,读取密码应用业务绑定关系数据,R,密码应用业务绑定关系,业务类型代码、密码服务集群ID、绑定状态,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：用户，接收者：密码服务管理平台,请求密码应用业务功能列表,密码应用业务功能列表,输出密码应用业务列表展示数据,X,密码应用业务列表数据,应用业务类型名称、密码服务集群名称、绑定时间,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：用户，接收者：密码服务管理平台,新增密码应用业务功能,新增密码应用业务功能,输入密码应用业务新增信息,E,密码应用业务新增信息,业务类型代码、密码服务集群ID,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：用户，接收者：密码服务管理平台,新增密码应用业务功能,新增密码应用业务功能,校验密码服务集群可用性,R,密码服务集群状态,集群ID、服务状态、最大绑定数,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：用户，接收者：密码服务管理平台,新增密码应用业务功能,新增密码应用业务功能,保存密码应用业务绑定关系,W,密码应用业务绑定关系,业务类型代码、密码服务集群ID、绑定时间,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：用户，接收者：密码服务管理平台,新增密码应用业务功能,新增密码应用业务功能,输出密码应用业务新增结果,X,密码应用业务新增结果,绑定状态、错误代码、操作时间,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：用户，接收者：密码服务管理平台,删除密码应用业务功能,删除密码应用业务功能,输入密码应用业务删除请求,E,密码应用业务删除请求,业务绑定ID,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：用户，接收者：密码服务管理平台,删除密码应用业务功能,删除密码应用业务功能,验证删除操作权限,R,操作员权限信息,用户ID、删除权限标志,1
密码应用数据管理,密码应用管理,密码应用业务管理,删除应用业务和密码服务集群的绑定关系,3,发起者：用户，接收者：密码服务管理平台,删除密码应用业务功能,删除密码应用业务功能,删除密码应用业务绑定关系,W,密码应用业务绑定关系,业务绑定ID,1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,分页查询密码应用场景,密码应用场景分页列表查询,输入分页查询条件,E,密码应用场景查询条件,页码、每页数量、业务系统名称、所属应用、所在城市、是否主OMC、是否接入系统、算法,1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,分页查询密码应用场景,密码应用场景分页列表查询,读取密码应用场景数据,R,密码应用场景数据,"业务系统ID, 业务系统名称, 所属应用, 所在城市, 是否主OMC, 是否接入系统, 算法, 备注, 创建时间",1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,分页查询密码应用场景,密码应用场景分页列表查询,处理分页逻辑,R,分页参数,"总记录数, 当前页码, 每页数量",1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,分页查询密码应用场景,密码应用场景分页列表查询,返回分页查询结果,X,密码应用场景分页结果,"序号, 业务系统名称, 所属应用, 所在城市, 是否主OMC, 是否接入系统, 算法, 备注, 创建时间, 总页数",1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,新建密码应用场景,新建密码应用场景,输入新建密码应用场景信息,E,密码应用场景新建信息,"业务系统名称, 应用, 所在城市, 是否主OMC, 是否接入系统, 算法, 备注",1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,新建密码应用场景,新建密码应用场景,校验业务系统名称唯一性,R,业务系统名称校验数据,"业务系统名称, 是否存在",1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,新建密码应用场景,新建密码应用场景,保存密码应用场景数据,W,密码应用场景数据,"业务系统ID, 业务系统名称, 应用, 所在城市, 是否主OMC, 是否接入系统, 算法, 备注, 创建时间",1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,新建密码应用场景,新建密码应用场景,返回新建结果,X,密码应用场景新建结果,"业务系统ID, 业务系统名称, 创建时间, 操作结果",1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,新建密码应用场景,新建密码应用场景,记录新建操作日志,W,密码应用场景操作日志,"操作类型, 操作人ID, 操作时间, 操作内容, 操作结果",1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,编辑密码应用场景,编辑密码应用场景,输入编辑密码应用场景信息,E,密码应用场景编辑信息,"业务系统ID, 业务系统名称, 应用, 所在城市, 是否主OMC, 是否接入系统, 算法, 备注",1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,编辑密码应用场景,编辑密码应用场景,读取原始密码应用场景数据,R,密码应用场景原始数据,"业务系统ID, 业务系统名称, 应用, 所在城市, 是否主OMC, 是否接入系统, 算法, 备注, 创建时间",1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,编辑密码应用场景,编辑密码应用场景,校验业务系统名称唯一性,R,业务系统名称校验数据,"业务系统名称, 是否存在",1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,编辑密码应用场景,编辑密码应用场景,更新密码应用场景数据,W,密码应用场景数据,"业务系统ID, 业务系统名称, 应用, 所在城市, 是否主OMC, 是否接入系统, 算法, 备注, 修改时间",1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,编辑密码应用场景,编辑密码应用场景,返回编辑结果,X,密码应用场景编辑结果,"业务系统ID, 业务系统名称, 修改时间, 操作结果",1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,编辑密码应用场景,编辑密码应用场景,记录编辑操作日志,W,密码应用场景操作日志,"操作类型, 操作人ID, 操作时间, 操作内容, 操作结果",1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,删除密码应用场景,删除密码应用场景,输入删除密码应用场景请求,E,密码应用场景删除请求,业务系统ID,1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,删除密码应用场景,删除密码应用场景,验证删除权限,R,用户权限数据,"用户ID, 操作权限",1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,删除密码应用场景,删除密码应用场景,删除密码应用场景数据,W,密码应用场景数据,业务系统ID,1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,删除密码应用场景,删除密码应用场景,返回删除结果,X,密码应用场景删除结果,"业务系统ID, 操作结果",1
密码应用数据管理,密码应用场景管理,密码应用场景管理,删除密码应用场景,3,发起者：用户，接收者：密码服务管理平台,删除密码应用场景,删除密码应用场景,记录删除操作日志,W,密码应用场景操作日志,"操作类型, 操作人ID, 操作时间, 操作内容, 操作结果",1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：用户，接收者：密码应用数据管理系统,分页查询密码应用改造厂商,密码应用改造厂商分页列表查询,输入密码应用改造厂商分页查询条件,E,密码应用改造厂商分页查询条件,页码、每页数量、厂商名称、状态,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：用户，接收者：密码应用数据管理系统,分页查询密码应用改造厂商,密码应用改造厂商分页列表查询,读取密码应用改造厂商分页数据,R,密码应用改造厂商分页数据,厂商ID、厂商名称、联系方式、地址、状态、创建时间,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：用户，接收者：密码应用数据管理系统,分页查询密码应用改造厂商,密码应用改造厂商分页列表查询,返回密码应用改造厂商分页查询结果,X,密码应用改造厂商分页查询结果,厂商ID、厂商名称、联系方式、地址、状态、创建时间、总记录数,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：用户，接收者：密码应用数据管理系统,分页查询密码应用改造厂商,密码应用改造厂商分页列表查询,记录密码应用改造厂商分页查询日志,W,密码应用改造厂商查询日志,操作员ID、查询时间、查询条件、查询结果数量,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：用户，接收者：密码应用数据管理系统,新增密码应用改造厂商,新增密码应用改造厂商,输入密码应用改造厂商新增信息,E,密码应用改造厂商新增信息,厂商名称、联系方式、地址、负责人、状态,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：用户，接收者：密码应用数据管理系统,新增密码应用改造厂商,新增密码应用改造厂商,校验密码应用改造厂商唯一性,R,密码应用改造厂商校验信息,厂商名称、联系方式,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：用户，接收者：密码应用数据管理系统,新增密码应用改造厂商,新增密码应用改造厂商,保存密码应用改造厂商新增数据,W,密码应用改造厂商新增数据,厂商ID、厂商名称、联系方式、地址、负责人、状态、创建时间,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：用户，接收者：密码应用数据管理系统,新增密码应用改造厂商,新增密码应用改造厂商,返回密码应用改造厂商新增结果,X,密码应用改造厂商新增结果,厂商ID、厂商名称、新增状态、错误信息,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：用户，接收者：密码应用数据管理系统,编辑密码应用改造厂商,编辑密码应用改造厂商,输入密码应用改造厂商修改信息,E,密码应用改造厂商修改信息,厂商ID、厂商名称、联系方式、地址、负责人、状态,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：用户，接收者：密码应用数据管理系统,编辑密码应用改造厂商,编辑密码应用改造厂商,读取密码应用改造厂商原始数据,R,密码应用改造厂商原始数据,厂商ID、厂商名称、联系方式、地址、负责人、状态,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：用户，接收者：密码应用数据管理系统,编辑密码应用改造厂商,编辑密码应用改造厂商,保存密码应用改造厂商修改数据,W,密码应用改造厂商修改数据,厂商ID、厂商名称、联系方式、地址、负责人、状态、修改时间,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：用户，接收者：密码应用数据管理系统,编辑密码应用改造厂商,编辑密码应用改造厂商,返回密码应用改造厂商修改结果,X,密码应用改造厂商修改结果,厂商ID、厂商名称、修改状态、错误信息,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：用户，接收者：密码应用数据管理系统,删除密码应用改造厂商,删除密码应用改造厂商,输入密码应用改造厂商删除请求,E,密码应用改造厂商删除请求,厂商ID、删除原因,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：用户，接收者：密码应用数据管理系统,删除密码应用改造厂商,删除密码应用改造厂商,读取密码应用改造厂商删除权限,R,密码应用改造厂商删除权限,厂商ID、操作员权限、关联业务系统,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：用户，接收者：密码应用数据管理系统,删除密码应用改造厂商,删除密码应用改造厂商,执行密码应用改造厂商删除操作,W,密码应用改造厂商删除数据,厂商ID、删除时间、删除操作员,1
密码应用数据管理,密码应用改造厂商管理,密码应用改造厂商管理,删除密码应用改造的厂商信息,3,发起者：用户，接收者：密码应用数据管理系统,删除密码应用改造厂商,删除密码应用改造厂商,返回密码应用改造厂商删除结果,X,密码应用改造厂商删除结果,厂商ID、删除状态、错误信息,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求密码服务列表,密码服务列表,输入密码服务列表查询条件,E,密码服务列表查询条件,服务名称、服务类型、IP地址、端口,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求密码服务列表,密码服务列表,读取密码服务数据,R,密码服务信息,服务名称、服务类型、所属租户、管理IP、业务IP、管理端口、业务端口、管控IP、管控端口、CPU核数、内存、服务集群、设备集群、镜像名称、镜像版本、容器名称、备注、创建时间、运行状态,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求密码服务列表,密码服务列表,处理密码服务列表数据,W,密码服务列表处理结果,服务名称、服务类型、IP地址、端口、运行状态,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求密码服务列表,密码服务列表,输出密码服务列表展示,X,密码服务列表展示信息,服务名称、服务类型、所属租户、管理IP、业务IP、管理端口、业务端口、管控IP、管控端口、CPU核数、内存、服务集群、设备集群、镜像名称、镜像版本、容器名称、备注、创建时间、运行状态,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交密码服务查询条件,密码服务查询,输入密码服务查询条件,E,密码服务查询请求,服务名称、服务类型、IP地址、端口,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交密码服务查询条件,密码服务查询,读取密码服务数据,R,密码服务信息,服务名称、服务类型、所属租户、管理IP、业务IP、管理端口、业务端口、管控IP、管控端口、CPU核数、内存、服务集群、设备集群、镜像名称、镜像版本、容器名称、备注、创建时间、运行状态,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交密码服务查询条件,密码服务查询,处理密码服务查询结果,W,密码服务查询处理结果,服务名称、服务类型、IP地址、端口、运行状态,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交密码服务查询条件,密码服务查询,输出密码服务查询结果,X,密码服务查询结果展示,服务名称、服务类型、所属租户、管理IP、业务IP、管理端口、业务端口、管控IP、管控端口、CPU核数、内存、服务集群、设备集群、镜像名称、镜像版本、容器名称、备注、创建时间、运行状态,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：系统定时任务，接收者：密码资产数据管理系统,定时检测密码服务状态,密码服务状态检测,读取密码服务状态检测配置,R,密码服务状态检测配置,检测频率、检测接口URL、超时阈值,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：系统定时任务，接收者：密码资产数据管理系统,定时检测密码服务状态,密码服务状态检测,调用REST接口检测密码服务状态,E,密码服务状态检测请求,服务IP、服务端口、检测接口路径,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：系统定时任务，接收者：密码资产数据管理系统,定时检测密码服务状态,密码服务状态检测,接收密码服务状态检测响应,X,密码服务状态检测响应,服务状态、响应时间、错误信息,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：系统定时任务，接收者：密码资产数据管理系统,定时检测密码服务状态,密码服务状态检测,更新密码服务状态记录,W,密码服务状态更新信息,服务名称、服务IP、服务端口、最新状态、检测时间,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交新建密码服务请求,新建密码服务,输入新建密码服务信息,E,密码服务新建请求,服务名称、区域、服务类型、服务集群、设备集群、服务规格/IP端口、备注、创建数量,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交新建密码服务请求,新建密码服务,校验服务名称唯一性,R,密码服务校验信息,服务名称、服务类型、区域,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交新建密码服务请求,新建密码服务,生成密码服务实例,W,密码服务实例信息,服务名称、区域、服务类型、服务集群、设备集群、服务规格/IP端口、备注、创建数量、实例ID,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交新建密码服务请求,新建密码服务,记录密码服务创建日志,W,密码服务创建日志,操作员ID、操作时间、服务名称、创建结果,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交新建密码服务请求,新建密码服务,输出密码服务创建结果,X,密码服务创建结果,服务名称、创建状态、实例ID、错误信息,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交编辑密码服务请求,编辑密码服务,输入密码服务编辑信息,E,密码服务编辑请求,服务名称、备注,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交编辑密码服务请求,编辑密码服务,读取原始密码服务信息,R,密码服务原始信息,服务名称、区域、服务类型、服务集群、设备集群、服务规格/IP端口、备注,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交编辑密码服务请求,编辑密码服务,更新密码服务信息,W,密码服务更新信息,服务名称、备注、更新时间,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交编辑密码服务请求,编辑密码服务,记录密码服务编辑日志,W,密码服务编辑日志,操作员ID、操作时间、服务名称、修改内容,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交编辑密码服务请求,编辑密码服务,输出密码服务编辑结果,X,密码服务编辑结果,服务名称、编辑状态、错误信息,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求重启密码服务,重启密码服务,输入密码服务重启请求,E,密码服务重启请求,服务名称、实例ID,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求重启密码服务,重启密码服务,验证服务状态,R,密码服务状态信息,服务名称、运行状态,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求重启密码服务,重启密码服务,执行密码服务重启操作,W,密码服务重启指令,服务名称、实例ID、重启时间,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求重启密码服务,重启密码服务,记录密码服务重启日志,W,密码服务重启日志,操作员ID、操作时间、服务名称、重启结果,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求重启密码服务,重启密码服务,输出密码服务重启结果,X,密码服务重启结果,服务名称、重启状态、错误信息,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求启动密码服务,启动密码服务,输入密码服务启动请求,E,密码服务启动请求,服务名称、实例ID,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求启动密码服务,启动密码服务,验证服务状态,R,密码服务状态信息,服务名称、运行状态,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求启动密码服务,启动密码服务,执行密码服务启动操作,W,密码服务启动指令,服务名称、实例ID、启动时间,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求启动密码服务,启动密码服务,记录密码服务启动日志,W,密码服务启动日志,操作员ID、操作时间、服务名称、启动结果,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求启动密码服务,启动密码服务,输出密码服务启动结果,X,密码服务启动结果,服务名称、启动状态、错误信息,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求停止密码服务,停止密码服务,输入密码服务停止请求,E,密码服务停止请求,服务名称、实例ID,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求停止密码服务,停止密码服务,验证服务状态,R,密码服务状态信息,服务名称、运行状态,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求停止密码服务,停止密码服务,执行密码服务停止操作,W,密码服务停止指令,服务名称、实例ID、停止时间,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求停止密码服务,停止密码服务,记录密码服务停止日志,W,密码服务停止日志,操作员ID、操作时间、服务名称、停止结果,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,请求停止密码服务,停止密码服务,输出密码服务停止结果,X,密码服务停止结果,服务名称、停止状态、错误信息,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交密码服务规格更新请求,更新密码服务规格,输入密码服务规格更新信息,E,密码服务规格更新请求,服务名称、CPU核数、内存、镜像版本,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交密码服务规格更新请求,更新密码服务规格,读取原始密码服务规格,R,密码服务原始规格,服务名称、CPU核数、内存、镜像版本,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交密码服务规格更新请求,更新密码服务规格,执行密码服务规格更新,W,密码服务规格更新指令,服务名称、新CPU核数、新内存、新镜像版本、更新时间,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交密码服务规格更新请求,更新密码服务规格,记录密码服务规格更新日志,W,密码服务规格更新日志,操作员ID、操作时间、服务名称、更新内容,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交密码服务规格更新请求,更新密码服务规格,输出密码服务规格更新结果,X,密码服务规格更新结果,服务名称、更新状态、错误信息,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交密码服务删除请求,删除密码服务,输入密码服务删除请求,E,密码服务删除请求,服务名称、实例ID,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交密码服务删除请求,删除密码服务,验证服务状态,R,密码服务状态信息,服务名称、运行状态,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交密码服务删除请求,删除密码服务,执行密码服务删除操作,W,密码服务删除指令,服务名称、实例ID、删除时间,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交密码服务删除请求,删除密码服务,记录密码服务删除日志,W,密码服务删除日志,操作员ID、操作时间、服务名称、删除结果,1
密码资产数据管理,密码资产名称管理,密码服务管理,删除停止的密码服务,4,发起者：用户，接收者：密码资产数据管理系统,提交密码服务删除请求,删除密码服务,输出密码服务删除结果,X,密码服务删除结果,服务名称、删除状态、错误信息,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：用户，接收者：密码资产数据管理系统,新增服务组,密码服务服务组新增,输入服务组新增信息,E,服务组新增信息,服务组ID、服务组名称、业务类型、服务数量、备注,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：用户，接收者：密码资产数据管理系统,新增服务组,密码服务服务组新增,校验服务组唯一性,R,服务组校验信息,服务组ID、服务组名称、业务类型,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：用户，接收者：密码资产数据管理系统,新增服务组,密码服务服务组新增,保存服务组配置信息,W,服务组配置信息,服务组ID、服务组名称、业务类型、服务数量、备注、创建时间,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：用户，接收者：密码资产数据管理系统,新增服务组,密码服务服务组新增,返回服务组新增结果,X,服务组新增结果,服务组ID、服务组名称、业务类型、操作结果,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：用户，接收者：密码资产数据管理系统,查询服务组列表,密码服务服务组列表,输入服务组查询条件,E,服务组查询条件,服务组ID、服务组名称、业务类型,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：用户，接收者：密码资产数据管理系统,查询服务组列表,密码服务服务组列表,读取服务组信息,R,服务组列表数据,服务组ID、服务组名称、业务类型、服务数量、创建时间,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：用户，接收者：密码资产数据管理系统,查询服务组列表,密码服务服务组列表,返回服务组列表展示数据,X,服务组展示数据,服务组ID、服务组名称、业务类型、服务数量、创建时间,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：用户，接收者：密码资产数据管理系统,查询服务组列表,密码服务服务组列表,记录服务组查询日志,W,服务组查询日志,操作员ID、查询时间、查询条件,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：用户，接收者：密码资产数据管理系统,编辑服务组名称,密码服务服务组编辑,输入服务组编辑信息,E,服务组编辑信息,服务组ID、新服务组名称,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：用户，接收者：密码资产数据管理系统,编辑服务组名称,密码服务服务组编辑,更新服务组名称,W,服务组更新信息,服务组ID、服务组名称、修改时间,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：用户，接收者：密码资产数据管理系统,编辑服务组名称,密码服务服务组编辑,返回服务组编辑结果,X,服务组编辑结果,服务组ID、服务组名称、操作结果,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：用户，接收者：密码资产数据管理系统,查看服务组内服务,密码服务管理列表,输入服务组服务查询请求,E,服务组服务查询条件,服务组ID,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：用户，接收者：密码资产数据管理系统,查看服务组内服务,密码服务管理列表,读取服务组服务信息,R,服务组服务列表,服务ID、服务名称、服务类型、服务状态,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：用户，接收者：密码资产数据管理系统,查看服务组内服务,密码服务管理列表,返回服务组服务展示数据,X,服务组服务展示数据,服务ID、服务名称、服务类型、服务状态,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：用户，接收者：密码资产数据管理系统,释放服务组服务,密码服务释放,输入服务释放请求,E,服务释放请求,服务组ID、服务ID,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：用户，接收者：密码资产数据管理系统,释放服务组服务,密码服务释放,验证服务释放权限,R,服务释放权限信息,服务组ID、服务ID、操作员权限,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：用户，接收者：密码资产数据管理系统,释放服务组服务,密码服务释放,更新服务组服务关联,W,服务组服务更新信息,服务组ID、服务ID、更新时间,1
密码资产数据管理,密码资产名称管理,密码服务组管理,从服务组释放密码服务，,3,发起者：用户，接收者：密码资产数据管理系统,释放服务组服务,密码服务释放,返回服务释放结果,X,服务释放结果,服务组ID、服务ID、操作结果,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像列表展示,密码服务镜像列表,输入镜像查询条件,E,镜像查询条件,镜像名称、服务类型,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像列表展示,密码服务镜像列表,读取镜像信息,R,镜像信息,镜像ID、镜像名称、镜像版本、服务类型、文件大小、完整性、状态、备注,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像列表展示,密码服务镜像列表,返回镜像列表展示,X,镜像列表展示信息,镜像名称、服务类型、镜像版本、文件大小、完整性、状态、备注,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像列表展示,密码服务镜像列表,保存镜像查询记录,W,镜像查询记录,镜像名称、服务类型、查询时间、操作员ID,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像文件上传,密码服务镜像上传,接收镜像上传文件,E,镜像上传文件,文件名称、文件大小、文件摘要,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像文件上传,密码服务镜像上传,校验文件摘要,R,镜像校验规则,摘要算法类型、摘要长度、校验规则,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像文件上传,密码服务镜像上传,存储镜像文件,W,镜像存储信息,文件路径、存储位置、存储时间,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像文件上传,密码服务镜像上传,更新镜像元数据,W,镜像元数据,镜像名称、镜像版本、服务类型、文件大小、完整性、状态,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像文件上传,密码服务镜像上传,返回上传结果,X,镜像上传结果,上传状态、错误信息、镜像ID,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像备注编辑,密码服务镜像编辑,输入镜像编辑信息,E,镜像编辑信息,镜像ID、备注内容,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像备注编辑,密码服务镜像编辑,读取镜像原始信息,R,镜像原始信息,镜像ID、镜像名称、镜像版本、服务类型、备注,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像备注编辑,密码服务镜像编辑,更新镜像备注,W,镜像更新信息,镜像ID、更新时间、备注内容,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像备注编辑,密码服务镜像编辑,返回编辑结果,X,镜像编辑结果,镜像ID、更新状态、操作时间,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像条件查询,密码服务镜像查询,输入镜像查询条件,E,镜像查询条件,镜像名称、服务类型,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像条件查询,密码服务镜像查询,读取镜像信息,R,镜像信息,镜像ID、镜像名称、镜像版本、服务类型、文件大小、完整性、状态、备注,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像条件查询,密码服务镜像查询,返回查询结果,X,镜像查询结果,镜像名称、服务类型、镜像版本、文件大小、完整性、状态、备注,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像状态启用,密码服务镜像启用,输入镜像启用请求,E,镜像启用请求,镜像ID、操作员ID,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像状态启用,密码服务镜像启用,读取镜像当前状态,R,镜像状态信息,镜像ID、当前状态、启用条件,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像状态启用,密码服务镜像启用,更新镜像状态为启用,W,镜像状态更新,镜像ID、新状态、更新时间,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像状态启用,密码服务镜像启用,返回启用结果,X,镜像启用结果,镜像ID、启用状态、操作时间,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像状态禁用,密码服务镜像禁用,输入镜像禁用请求,E,镜像禁用请求,镜像ID、操作员ID,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像状态禁用,密码服务镜像禁用,读取镜像当前状态,R,镜像状态信息,镜像ID、当前状态、禁用条件,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像状态禁用,密码服务镜像禁用,更新镜像状态为禁用,W,镜像状态更新,镜像ID、新状态、更新时间,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像状态禁用,密码服务镜像禁用,返回禁用结果,X,镜像禁用结果,镜像ID、禁用状态、操作时间,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像删除操作,密码服务镜像删除,输入镜像删除请求,E,镜像删除请求,镜像ID、操作员ID,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像删除操作,密码服务镜像删除,读取镜像状态并校验删除权限,R,镜像删除校验,镜像ID、当前状态、操作员权限,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像删除操作,密码服务镜像删除,删除镜像文件,W,镜像文件删除,文件路径、删除时间,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像删除操作,密码服务镜像删除,删除镜像元数据,W,镜像元数据删除,镜像ID、删除时间,1
密码资产数据管理,密码资产名称管理,密码服务镜像管理,删除密码服务镜像,4,发起者：用户，接收者：密码资产数据管理系统,镜像删除操作,密码服务镜像删除,返回删除结果,X,镜像删除结果,镜像ID、删除状态、操作时间,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：用户，接收者：密码服务平台,新增数据库,密码服务数据库新增,输入数据库新增信息,E,数据库新增信息,数据库名称、数据库类型、IP地址、端口号、管理员账号、管理员密码,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：用户，接收者：密码服务平台,新增数据库,密码服务数据库新增,校验数据库类型有效性,R,数据库类型信息,设备类型ID、设备类型名称,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：用户，接收者：密码服务平台,新增数据库,密码服务数据库新增,保存数据库配置信息,W,数据库配置信息,数据库名称、数据库类型、IP地址、端口号、管理员账号、管理员密码、创建时间,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：用户，接收者：密码服务平台,新增数据库,密码服务数据库新增,返回数据库新增结果,X,数据库操作结果,操作状态、数据库ID、数据库名称,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：用户，接收者：密码服务平台,编辑数据库信息,密码服务数据库编辑,输入数据库编辑信息,E,数据库编辑信息,数据库ID、数据库名称、数据库备注,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：用户，接收者：密码服务平台,编辑数据库信息,密码服务数据库编辑,读取原始数据库信息,R,数据库基础信息,数据库ID、数据库名称、数据库类型、IP地址、端口号,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：用户，接收者：密码服务平台,编辑数据库信息,密码服务数据库编辑,更新数据库信息,W,数据库更新信息,数据库ID、数据库名称、数据库备注、更新时间,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：用户，接收者：密码服务平台,编辑数据库信息,密码服务数据库编辑,返回数据库编辑结果,X,数据库操作结果,操作状态、数据库ID、数据库名称,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：用户，接收者：密码服务平台,删除数据库,密码服务数据库删除,输入数据库删除请求,E,数据库删除请求,数据库ID、操作员ID,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：用户，接收者：密码服务平台,删除数据库,密码服务数据库删除,校验数据库删除权限,R,数据库权限信息,数据库ID、操作员ID、操作权限状态,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：用户，接收者：密码服务平台,删除数据库,密码服务数据库删除,执行数据库删除操作,W,数据库删除信息,数据库ID、删除时间、操作员ID,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：用户，接收者：密码服务平台,删除数据库,密码服务数据库删除,返回数据库删除结果,X,数据库操作结果,操作状态、数据库ID,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：用户，接收者：密码服务平台,查询数据库列表,密码服务数据库列表,输入数据库查询条件,E,数据库查询条件,数据库名称、数据库类型、IP地址,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：用户，接收者：密码服务平台,查询数据库列表,密码服务数据库列表,读取数据库列表信息,R,数据库列表信息,数据库ID、数据库名称、数据库类型、IP地址、端口号、完整性校验状态,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：用户，接收者：密码服务平台,查询数据库列表,密码服务数据库列表,返回数据库列表结果,X,数据库列表展示信息,数据库ID、数据库名称、数据库类型、IP地址、端口号、完整性校验状态,1
密码资产数据管理,密码资产数据管理,密码服务数据库管理,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4,发起者：用户，接收者：密码服务平台,查询数据库列表,密码服务数据库列表,保存数据库查询记录,W,数据库查询记录,查询时间、操作员ID、查询条件,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：用户，接收者：密码服务平台,请求展示数据库模式列表,密码服务数据库模式列表,接收数据库模式列表查询请求,E,数据库模式列表查询条件,分页参数（页码、每页数量）,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：用户，接收者：密码服务平台,请求展示数据库模式列表,密码服务数据库模式列表,读取数据库模式数据,R,数据库模式信息,模式ID、模式名称、创建时间、更新时间、服务类型,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：用户，接收者：密码服务平台,请求展示数据库模式列表,密码服务数据库模式列表,返回数据库模式列表结果,X,数据库模式列表展示信息,模式ID、模式名称、创建时间、更新时间、服务类型、分页信息,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：用户，接收者：密码服务平台,删除数据库模式,密码服务数据库模式删除,接收数据库模式删除请求,E,数据库模式删除请求,模式ID,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：用户，接收者：密码服务平台,删除数据库模式,密码服务数据库模式删除,验证删除权限并执行删除,W,数据库模式删除操作,模式ID、操作结果,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：用户，接收者：密码服务平台,删除数据库模式,密码服务数据库模式删除,返回删除结果,X,数据库模式删除结果,模式ID、删除状态,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：用户，接收者：密码服务平台,查询数据库模式,密码服务数据库模式查询,接收数据库模式查询请求,E,数据库模式查询条件,查询字段（如模式名称、服务类型）,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：用户，接收者：密码服务平台,查询数据库模式,密码服务数据库模式查询,读取匹配的数据库模式数据,R,数据库模式信息,模式ID、模式名称、创建时间、更新时间、服务类型,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：用户，接收者：密码服务平台,查询数据库模式,密码服务数据库模式查询,返回查询结果,X,数据库模式查询结果,模式ID、模式名称、创建时间、更新时间、服务类型,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：用户，接收者：密码服务平台,新增数据库模式,密码服务数据库模式新增,接收数据库模式新增信息,E,数据库模式新增信息,模式名称、服务类型、描述,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：用户，接收者：密码服务平台,新增数据库模式,密码服务数据库模式新增,校验模式名称唯一性,R,数据库模式校验信息,模式名称、服务类型,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：用户，接收者：密码服务平台,新增数据库模式,密码服务数据库模式新增,保存新增数据库模式,W,数据库模式新增记录,模式ID、模式名称、服务类型、创建时间,1
密码资产数据管理,密码资产数据管理,密码服务数据库模式管理,新增数据库模式,2,发起者：用户，接收者：密码服务平台,新增数据库模式,密码服务数据库模式新增,返回新增结果,X,数据库模式新增结果,模式ID、模式名称、创建时间,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,请求查看API网关列表,API网关列表,输入网关查询条件,E,网关查询条件,所属区域、网关类型、IP地址,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,请求查看API网关列表,API网关列表,读取网关基础信息,R,网关基础信息,"网关ID, 网关名称, 所属区域, 网关标识, 网关类型, IP地址, 业务端口, 管理端口",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,请求查看API网关列表,API网关列表,返回网关列表展示数据,X,网关列表展示数据,"网关名称, 所属区域, 网关标识, 网关类型, IP地址, 业务端口, 管理端口",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,请求查看API网关列表,API网关列表,保存网关查询记录,W,网关查询记录,"查询时间, 操作用户ID, 查询条件",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：系统部署组件，接收者：密码服务平台,平台部署完成触发网关初始化,API网关初始化,读取平台部署配置,R,平台部署信息,"部署区域ID, 网关组件标识, 网关类型, 管理端口",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：系统部署组件，接收者：密码服务平台,平台部署完成触发网关初始化,API网关初始化,写入初始网关配置,W,网关初始化配置,"网关ID, 网关名称, 网关标识, 网关类型, 管理端口",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：系统部署组件，接收者：密码服务平台,平台部署完成触发网关初始化,API网关初始化,返回初始化结果,X,网关初始化结果,"初始化状态, 网关ID, 错误信息",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,点击新建网关按钮,API网关新增,输入网关新增信息,E,网关新增信息,"网关名称, 所属需求, 网关标识, 网关类型, 管理端口",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,点击新建网关按钮,API网关新增,校验网关标识唯一性,R,网关标识校验,"网关标识, 网关类型",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,点击新建网关按钮,API网关新增,写入新网关配置,W,网关新增配置,"网关ID, 网关名称, 网关标识, 网关类型, 管理端口",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,点击新建网关按钮,API网关新增,返回新增结果,X,网关新增结果,"网关ID, 新增状态, 错误信息",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,点击新建网关按钮,API网关新增,记录网关新增日志,W,网关操作日志,"操作类型, 操作用户ID, 操作时间, 操作内容",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,点击网关编辑按钮,API网关编辑,输入网关编辑信息,E,网关编辑信息,"网关ID, 网关名称, 管理端口",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,点击网关编辑按钮,API网关编辑,读取当前网关配置,R,网关当前配置,"网关ID, 网关名称, 管理端口",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,点击网关编辑按钮,API网关编辑,更新网关配置,W,网关更新配置,"网关ID, 网关名称, 管理端口",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,点击网关编辑按钮,API网关编辑,返回编辑结果,X,网关编辑结果,"网关ID, 编辑状态, 错误信息",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,点击网关编辑按钮,API网关编辑,记录网关编辑日志,W,网关操作日志,"操作类型, 操作用户ID, 操作时间, 操作内容",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,点击网关删除按钮,API网关删除,输入网关删除请求,E,网关删除请求,"网关ID, 操作用户ID",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,点击网关删除按钮,API网关删除,验证删除权限,R,用户权限信息,"用户ID, 操作权限",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,点击网关删除按钮,API网关删除,删除网关配置,W,网关删除配置,网关ID,1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,点击网关删除按钮,API网关删除,返回删除结果,X,网关删除结果,"网关ID, 删除状态, 错误信息",1
密码资产数据管理,密码资产数据管理,API网关管理,删除网关信息,3,发起者：用户，接收者：密码服务平台,点击网关删除按钮,API网关删除,记录网关删除日志,W,网关操作日志,"操作类型, 操作用户ID, 操作时间, 操作内容",1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：用户，接收者：网关管理系统,请求路由列表展示,路由管理列表,输入路由查询条件,E,路由查询条件,路由名称、服务类型、所属应用、所属服务组、URL路径、分页参数,1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：用户，接收者：网关管理系统,请求路由列表展示,路由管理列表,读取路由基础信息,R,路由信息,路由ID、路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径,1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：用户，接收者：网关管理系统,请求路由列表展示,路由管理列表,读取路由高级配置,R,路由配置信息,上游配置、匹配条件、超时时间,1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：用户，接收者：网关管理系统,请求路由列表展示,路由管理列表,返回路由列表展示数据,X,路由列表展示信息,路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间,1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：用户，接收者：网关管理系统,请求路由列表展示,路由管理列表,保存路由查询记录,W,路由查询记录,查询条件、查询时间、操作员ID,1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：用户，接收者：网关管理系统,请求路由详情展示,路由管理详情,输入路由详情请求,E,路由详情请求,路由ID,1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：用户，接收者：网关管理系统,请求路由详情展示,路由管理详情,读取路由基础详情,R,路由基础信息,路由ID、路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径,1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：用户，接收者：网关管理系统,请求路由详情展示,路由管理详情,读取服务列表信息,R,服务列表信息,服务ID、服务名称、服务地址、服务状态,1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：用户，接收者：网关管理系统,请求路由详情展示,路由管理详情,读取应用关联信息,R,应用信息,应用ID、应用名称、应用描述、所属区域,1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：用户，接收者：网关管理系统,请求路由详情展示,路由管理详情,返回路由详情展示数据,X,路由详情展示信息,路由名称、服务列表、应用信息、路由配置详情,1
密码资产数据管理,密码资产数据管理,网关路由管理,展示路由管理详情，包含服务列表信息、应用信息,4,发起者：用户，接收者：网关管理系统,请求路由详情展示,路由管理详情,保存路由详情访问记录,W,路由详情访问记录,路由ID、访问时间、操作员ID,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型展示,设备类型展示,读取设备类型基础信息,R,设备类型基础信息,设备类型ID、设备类型名称、所属厂商、设备类型分类（云/物理/虚拟）、管理接口协议、管理端口,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型展示,设备类型展示,读取设备类型监控配置,R,设备类型监控配置,监控类型、监控URL、监控端口、监控协议,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型展示,设备类型展示,输出设备类型展示数据,X,设备类型展示数据,设备类型名称、厂商、分类、接口协议、端口、监控配置,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型展示,设备类型展示,记录设备类型展示日志,W,设备类型展示日志,操作用户、展示时间、展示内容,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：系统，接收者：密码资产数据管理系统,平台部署初始化,设备类型初始化,读取平台默认设备类型配置,R,平台默认设备类型配置,设备类型名称、厂商、分类、接口协议、端口,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：系统，接收者：密码资产数据管理系统,平台部署初始化,设备类型初始化,写入初始化设备类型数据,W,初始化设备类型数据,设备类型ID、名称、厂商、分类、接口协议、端口,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：系统，接收者：密码资产数据管理系统,平台部署初始化,设备类型初始化,输出初始化结果,X,初始化结果,初始化状态、成功/失败原因,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：系统，接收者：密码资产数据管理系统,平台部署初始化,设备类型初始化,记录初始化操作日志,W,初始化操作日志,操作时间、操作类型、操作结果,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型新增,设备类型新增,输入设备类型新增信息,E,设备类型新增信息,设备类型名称、厂商、分类、接口协议、管理端口,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型新增,设备类型新增,校验设备类型唯一性,R,设备类型校验信息,设备类型名称、厂商、分类,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型新增,设备类型新增,写入设备类型数据,W,设备类型数据,设备类型ID、名称、厂商、分类、接口协议、端口,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型新增,设备类型新增,输出新增结果,X,新增结果,设备类型ID、新增状态、错误信息,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型编辑,设备类型编辑,输入设备类型编辑信息,E,设备类型编辑信息,设备类型ID、名称、厂商、分类、接口协议、端口,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型编辑,设备类型编辑,读取原始设备类型数据,R,原始设备类型数据,设备类型ID、名称、厂商、分类、接口协议、端口,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型编辑,设备类型编辑,更新设备类型数据,W,更新设备类型数据,设备类型ID、名称、厂商、分类、接口协议、端口,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型编辑,设备类型编辑,输出编辑结果,X,编辑结果,设备类型ID、编辑状态、错误信息,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型停用,设备类型停用,输入设备类型停用请求,E,设备类型停用请求,设备类型ID,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型停用,设备类型停用,校验设备类型可用性,R,设备类型状态信息,设备类型ID、当前状态、关联设备数量,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型停用,设备类型停用,更新设备类型状态,W,设备类型状态更新,设备类型ID、新状态（停用）,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型停用,设备类型停用,输出停用结果,X,停用结果,设备类型ID、停用状态、错误信息,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型启用,设备类型启用,输入设备类型启用请求,E,设备类型启用请求,设备类型ID,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型启用,设备类型启用,校验设备类型停用状态,R,设备类型状态信息,设备类型ID、当前状态,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型启用,设备类型启用,更新设备类型状态,W,设备类型状态更新,设备类型ID、新状态（启用）,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型启用,设备类型启用,输出启用结果,X,启用结果,设备类型ID、启用状态、错误信息,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型删除,设备类型删除,输入设备类型删除请求,E,设备类型删除请求,设备类型ID,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型删除,设备类型删除,校验设备类型关联设备,R,设备类型关联信息,设备类型ID、关联设备数量,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型删除,设备类型删除,删除设备类型数据,W,设备类型删除数据,设备类型ID,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,设备类型删除,设备类型删除,输出删除结果,X,删除结果,设备类型ID、删除状态、错误信息,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,监控信息配置查看,监控信息配置查看,输入监控信息查询条件,E,监控信息查询条件,设备类型ID、监控方式（SNMP/Rest/组件/探针）,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,监控信息配置查看,监控信息配置查看,读取监控配置数据,R,监控配置数据,监控方式、监控URL、监控端口、监控协议,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,监控信息配置查看,监控信息配置查看,输出监控配置结果,X,监控配置结果,监控方式、配置详情、配置时间,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,监控信息配置查看,监控信息配置查看,记录监控查询日志,W,监控查询日志,操作用户、查询时间、查询内容,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,监控信息配置,监控信息配置,输入监控信息配置,E,监控信息配置,设备类型ID、监控方式、监控URL、监控端口、监控协议,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,监控信息配置,监控信息配置,校验监控配置有效性,R,监控配置校验,监控方式、URL格式、端口有效性,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,监控信息配置,监控信息配置,写入监控配置数据,W,监控配置数据,设备类型ID、监控方式、监控URL、监控端口、监控协议,1
密码资产数据管理,密码资产数据管理,设备类型管理,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3,发起者：用户，接收者：密码资产数据管理系统,监控信息配置,监控信息配置,输出配置结果,X,监控配置结果,配置状态、错误信息,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,请求查询密码设备集群列表,密码设备集群列表,输入密码设备集群查询条件,E,密码设备集群查询条件,名称、设备类型、所属区域,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,请求查询密码设备集群列表,密码设备集群列表,读取密码设备集群数据,R,密码设备集群信息,名称、设备类型、所属区域、设备数量、描述,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,请求查询密码设备集群列表,密码设备集群列表,处理分页参数,E,分页参数,页码、每页数量,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,请求查询密码设备集群列表,密码设备集群列表,返回密码设备集群列表结果,X,密码设备集群列表结果,名称、设备类型、所属区域、设备数量、描述,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,提交密码设备集群新增请求,密码设备集群新增,输入密码设备集群新增信息,E,密码设备集群新增信息,名称、设备类型、所属区域、描述,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,提交密码设备集群新增请求,密码设备集群新增,校验密码设备集群名称唯一性,R,密码设备集群校验信息,名称、设备类型,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,提交密码设备集群新增请求,密码设备集群新增,保存密码设备集群数据,W,密码设备集群信息,名称、设备类型、所属区域、描述,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,提交密码设备集群编辑请求,密码设备集群编辑,输入密码设备集群编辑信息,E,密码设备集群编辑信息,名称、描述,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,提交密码设备集群编辑请求,密码设备集群编辑,读取原始密码设备集群数据,R,密码设备集群信息,名称、设备类型、所属区域、描述,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,提交密码设备集群编辑请求,密码设备集群编辑,更新密码设备集群数据,W,密码设备集群信息,名称、描述,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,提交密码设备集群删除请求,密码设备集群删除,输入密码设备集群删除请求,E,密码设备集群删除请求,集群ID,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,提交密码设备集群删除请求,密码设备集群删除,校验密码设备集群使用状态,R,密码设备集群校验信息,集群ID、关联服务状态,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,提交密码设备集群删除请求,密码设备集群删除,删除密码设备集群数据,W,密码设备集群信息,集群ID,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,提交密码设备绑定请求,绑定密码设备,输入密码设备绑定信息,E,密码设备绑定信息,集群ID、设备ID、设备类型,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,提交密码设备绑定请求,绑定密码设备,校验密码设备类型兼容性,R,密码机服务类型,设备类型代码、设备类型名称,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,提交密码设备绑定请求,绑定密码设备,执行密码设备绑定操作,W,密码设备绑定关系,集群ID、设备ID、绑定时间,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,提交密码设备绑定请求,绑定密码设备,生成保护密钥配置,W,保护密钥配置,集群ID、密钥算法、密钥长度,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,提交密码设备释放请求,释放密码设备,输入密码设备释放信息,E,密码设备释放信息,集群ID、设备ID,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,提交密码设备释放请求,释放密码设备,校验密码设备集群使用状态,R,密码设备集群校验信息,集群ID、关联服务状态,1
密码资产数据管理,密码资产数据管理,密码设备集群管理,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3,发起者：用户，接收者：密码资产数据管理系统,提交密码设备释放请求,释放密码设备,解除密码设备绑定关系,W,密码设备绑定关系,集群ID、设备ID,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机列表查询,云密码机列表,输入云密码机查询条件,E,云密码机查询请求,名称、管理IP,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机列表查询,云密码机列表,读取云密码机信息,R,云密码机信息,ID、名称、管理IP、状态、创建时间,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机列表查询,云密码机列表,返回云密码机查询结果,X,云密码机查询结果,ID、名称、管理IP、状态、创建时间,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机列表查询,云密码机列表,保存云密码机查询记录,W,云密码机查询记录,查询条件、查询时间、操作员ID,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机新增,云密码机新建,输入云密码机新增信息,E,云密码机新增信息,名称、管理IP、管理端口、设备类型、备注,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机新增,云密码机新建,校验云密码机唯一性,R,云密码机校验信息,名称、管理IP、设备类型,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机新增,云密码机新建,保存云密码机信息,W,云密码机新增信息,名称、管理IP、管理端口、设备类型、备注、创建时间,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机新增,云密码机新建,返回云密码机新增结果,X,云密码机新增结果,ID、名称、管理IP、操作结果,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机新增,云密码机新建,记录云密码机新增日志,W,云密码机操作日志,操作类型、操作员ID、操作时间、操作内容,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机编辑,云密码机编辑,输入云密码机修改信息,E,云密码机修改信息,ID、名称、备注,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机编辑,云密码机编辑,读取原始云密码机信息,R,云密码机原始信息,ID、名称、管理IP、管理端口、设备类型、备注,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机编辑,云密码机编辑,更新云密码机信息,W,云密码机修改信息,ID、名称、备注、修改时间,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机编辑,云密码机编辑,返回云密码机修改结果,X,云密码机修改结果,ID、名称、修改时间、操作结果,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机编辑,云密码机编辑,记录云密码机修改日志,W,云密码机操作日志,操作类型、操作员ID、操作时间、操作内容,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机删除,云密码机删除,发起云密码机删除请求,E,云密码机删除请求,ID,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机删除,云密码机删除,校验云密码机删除权限,R,云密码机删除权限,ID、关联虚拟机数量、操作员权限,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机删除,云密码机删除,执行云密码机删除,W,云密码机删除信息,ID、删除时间、删除操作员,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机详情查看,云密码机详情,输入云密码机详情请求,E,云密码机详情请求,ID,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机详情查看,云密码机详情,读取云密码机详细信息,R,云密码机详情信息,ID、名称、管理IP、管理端口、设备类型、状态、创建时间、备注,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机详情查看,云密码机详情,返回云密码机详情展示,X,云密码机详情展示信息,ID、名称、管理IP、管理端口、设备类型、状态、创建时间、备注,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机详情查看,云密码机详情,记录云密码机详情访问日志,W,云密码机访问日志,访问时间、操作员ID、访问对象ID,1
密码资产数据管理,密码资产数据管理,云密码机管理,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5,发起者：用户，接收者：云密码机管理系统,云密码机详情查看,云密码机详情,校验云密码机关联状态,R,云密码机关联状态,ID、关联虚拟机数量,1
密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6,发起者：用户，接收者：云密码机管理系统,查询网络配置,网络配置列表,输入网络配置查询条件,E,网络配置查询条件,网络名称、网卡名称、主机序列号,1
密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6,发起者：用户，接收者：云密码机管理系统,查询网络配置,网络配置列表,读取网络配置数据,R,网络配置列表信息,网络配置ID、网络名称、网卡名称、主机序列号、IP范围,1
密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6,发起者：用户，接收者：云密码机管理系统,查询网络配置,网络配置列表,返回网络配置查询结果,X,网络配置查询结果,网络配置ID、网络名称、网卡名称、IP范围、创建时间,1
密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6,发起者：用户，接收者：云密码机管理系统,新增虚拟机网络配置,新增虚拟机网络配置,输入新增网络配置信息,E,新增网络配置信息,网络名称、网卡名称、IP范围、主机序列号,1
密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6,发起者：用户，接收者：云密码机管理系统,新增虚拟机网络配置,新增虚拟机网络配置,校验网络配置唯一性,R,网络配置校验信息,网络名称、网卡名称、IP范围,1
密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6,发起者：用户，接收者：云密码机管理系统,新增虚拟机网络配置,新增虚拟机网络配置,保存网络配置数据,W,网络配置保存信息,网络配置ID、网络名称、网卡名称、IP范围、主机序列号,1
密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6,发起者：用户，接收者：云密码机管理系统,新增虚拟机网络配置,新增虚拟机网络配置,返回新增网络配置结果,X,网络配置新增结果,网络配置ID、网络名称、操作结果,1
密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6,发起者：用户，接收者：云密码机管理系统,新增虚拟机网络配置,新增虚拟机网络配置,记录网络配置操作日志,W,网络配置操作日志,操作类型、操作时间、操作用户、网络配置ID,1
密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6,发起者：用户，接收者：云密码机管理系统,新增虚拟机网络配置,新增虚拟机网络配置,校验IP地址有效性,R,IP地址校验信息,IP范围、子网掩码、网关,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起批量创建虚拟机请求,批量创建虚拟机,输入批量创建虚拟机参数,E,虚拟密码机批量创建参数,设备类型、虚拟机网络、资源配置、创建数量、管理端口、服务端口、连接密码,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起批量创建虚拟机请求,批量创建虚拟机,校验虚拟机网络配置,R,虚拟机网络配置信息,网络名称、网卡名称、主机序列号,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起批量创建虚拟机请求,批量创建虚拟机,校验资源配置参数,R,资源配置校验信息,CPU核数、内存大小、存储容量,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起批量创建虚拟机请求,批量创建虚拟机,调用云密码机0088标准创建接口,W,虚拟密码机创建请求,设备类型、网络配置、资源配置、创建数量,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起批量创建虚拟机请求,批量创建虚拟机,异步创建虚拟机状态监控,R,虚拟密码机状态信息,状态ID、状态名、创建进度,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起批量创建虚拟机请求,批量创建虚拟机,自动配置虚拟机网络,W,虚拟机网络配置,网卡名称、IP地址、子网掩码,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起批量创建虚拟机请求,批量创建虚拟机,生成虚拟机唯一标识,W,虚拟机唯一标识,虚拟机ID、创建时间、序列号,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起批量创建虚拟机请求,批量创建虚拟机,记录虚拟机创建日志,W,虚拟机操作日志,操作类型、操作时间、操作用户、虚拟机ID,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起批量创建虚拟机请求,批量创建虚拟机,返回虚拟机创建结果,X,虚拟机创建结果,创建状态、虚拟机ID列表、错误信息,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起批量创建虚拟机请求,批量创建虚拟机,更新虚拟机状态表,W,虚拟密码机状态,状态代码、状态名称、更新时间,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户请求虚拟密码机列表,虚拟密码机列表,读取虚拟密码机基础信息,R,虚拟密码机基础信息,虚拟机ID、设备类型、创建时间,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户请求虚拟密码机列表,虚拟密码机列表,读取虚拟机网络配置,R,虚拟机网络配置,管理IP、服务IP、网卡名称,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户请求虚拟密码机列表,虚拟密码机列表,读取虚拟机状态信息,R,虚拟密码机状态,状态代码、状态名称、更新时间,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户请求虚拟密码机列表,虚拟密码机列表,整合虚拟机列表数据,R,虚拟密码机列表,虚拟机ID、设备类型、管理IP、状态、创建时间,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户请求虚拟密码机列表,虚拟密码机列表,返回虚拟机列表展示,X,虚拟密码机列表展示,虚拟机ID、设备类型、管理IP、状态、操作按钮,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户输入查询条件,虚拟密码机列表查询,输入虚拟机查询条件,E,虚拟机查询请求,名称、主机、管理IP、服务IP、设备类型,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户输入查询条件,虚拟密码机列表查询,读取虚拟机基础信息,R,虚拟密码机基础信息,虚拟机ID、设备类型、管理IP,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户输入查询条件,虚拟密码机列表查询,读取虚拟机网络配置,R,虚拟机网络配置,管理IP、服务IP、网卡名称,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户输入查询条件,虚拟密码机列表查询,读取虚拟机状态信息,R,虚拟密码机状态,状态代码、状态名称,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户输入查询条件,虚拟密码机列表查询,过滤匹配查询条件的虚拟机,R,虚拟机查询结果,虚拟机ID、设备类型、管理IP、状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户输入查询条件,虚拟密码机列表查询,返回查询结果展示,X,虚拟机查询结果展示,虚拟机ID、设备类型、管理IP、状态、匹配条件,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起创建虚拟机请求,创建虚拟密码机,选择云密码机模板,E,云密码机选择信息,模板ID、模板名称,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起创建虚拟机请求,创建虚拟密码机,输入虚拟机创建参数,E,虚拟密码机创建参数,设备类型、资源配置、管理端口,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起创建虚拟机请求,创建虚拟密码机,校验云密码机可用性,R,云密码机状态,可用状态、剩余资源,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起创建虚拟机请求,创建虚拟密码机,调用云密码机创建接口,W,虚拟密码机创建请求,模板ID、资源配置、创建数量,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起创建虚拟机请求,创建虚拟密码机,生成虚拟机唯一标识,W,虚拟机唯一标识,虚拟机ID、创建时间,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起创建虚拟机请求,创建虚拟密码机,记录创建操作日志,W,虚拟机操作日志,操作类型、操作时间、操作用户,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起创建虚拟机请求,创建虚拟密码机,返回创建结果,X,虚拟机创建结果,创建状态、虚拟机ID、错误信息,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户请求虚拟机详情,虚拟密码机详情,输入虚拟机详情请求,E,虚拟机详情请求,虚拟机ID,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户请求虚拟机详情,虚拟密码机详情,读取虚拟机基础信息,R,虚拟密码机基础信息,虚拟机ID、设备类型、创建时间,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户请求虚拟机详情,虚拟密码机详情,读取虚拟机网络配置,R,虚拟机网络配置,管理IP、服务IP、网卡名称,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户请求虚拟机详情,虚拟密码机详情,读取虚拟机状态信息,R,虚拟密码机状态,状态代码、状态名称、更新时间,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户请求虚拟机详情,虚拟密码机详情,读取虚拟机资源配置,R,虚拟机资源配置,CPU核数、内存大小、存储容量,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户请求虚拟机详情,虚拟密码机详情,整合虚拟机详情数据,R,虚拟密码机详情,虚拟机ID、设备类型、管理IP、状态、资源配置,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户请求虚拟机详情,虚拟密码机详情,返回虚拟机详情展示,X,虚拟密码机详情展示,虚拟机ID、设备类型、管理IP、状态、资源配置,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起编辑虚拟机请求,编辑虚拟密码机,输入虚拟机编辑参数,E,虚拟密码机编辑参数,虚拟机ID、新名称、新连接密码,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起编辑虚拟机请求,编辑虚拟密码机,校验虚拟机存在性,R,虚拟密码机基础信息,虚拟机ID、当前状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起编辑虚拟机请求,编辑虚拟密码机,更新虚拟机名称,W,虚拟密码机基础信息,虚拟机ID、新名称,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起编辑虚拟机请求,编辑虚拟密码机,更新连接密码,W,虚拟密码机连接信息,虚拟机ID、新密码,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起编辑虚拟机请求,编辑虚拟密码机,动态下发配置到密码服务,W,密码服务配置,虚拟机ID、新名称、新密码,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起编辑虚拟机请求,编辑虚拟密码机,记录编辑操作日志,W,虚拟机操作日志,操作类型、操作时间、操作用户,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起编辑虚拟机请求,编辑虚拟密码机,返回编辑结果,X,虚拟机编辑结果,编辑状态、虚拟机ID、错误信息,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起删除虚拟机请求,删除虚拟密码机,输入虚拟机删除请求,E,虚拟机删除请求,虚拟机ID,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起删除虚拟机请求,删除虚拟密码机,校验虚拟机存在性,R,虚拟密码机基础信息,虚拟机ID、当前状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起删除虚拟机请求,删除虚拟密码机,校验删除权限,R,用户权限信息,用户ID、删除权限,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起删除虚拟机请求,删除虚拟密码机,调用云密码机删除接口,W,虚拟密码机删除请求,虚拟机ID,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起删除虚拟机请求,删除虚拟密码机,更新虚拟机状态,W,虚拟密码机状态,虚拟机ID、新状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起删除虚拟机请求,删除虚拟密码机,记录删除操作日志,W,虚拟机操作日志,操作类型、操作时间、操作用户,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起删除虚拟机请求,删除虚拟密码机,返回删除结果,X,虚拟机删除结果,删除状态、虚拟机ID、错误信息,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起启动虚拟机请求,启动虚拟密码机,输入虚拟机启动请求,E,虚拟机启动请求,虚拟机ID,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起启动虚拟机请求,启动虚拟密码机,校验虚拟机存在性,R,虚拟密码机基础信息,虚拟机ID、当前状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起启动虚拟机请求,启动虚拟密码机,校验启动权限,R,用户权限信息,用户ID、启动权限,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起启动虚拟机请求,启动虚拟密码机,调用云密码机启动接口,W,虚拟密码机启动请求,虚拟机ID,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起启动虚拟机请求,启动虚拟密码机,更新虚拟机状态,W,虚拟密码机状态,虚拟机ID、新状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起启动虚拟机请求,启动虚拟密码机,记录启动操作日志,W,虚拟机操作日志,操作类型、操作时间、操作用户,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起启动虚拟机请求,启动虚拟密码机,返回启动结果,X,虚拟机启动结果,启动状态、虚拟机ID、错误信息,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起停止虚拟机请求,停止虚拟密码机,输入虚拟机停止请求,E,虚拟机停止请求,虚拟机ID,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起停止虚拟机请求,停止虚拟密码机,校验虚拟机存在性,R,虚拟密码机基础信息,虚拟机ID、当前状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起停止虚拟机请求,停止虚拟密码机,校验停止权限,R,用户权限信息,用户ID、停止权限,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起停止虚拟机请求,停止虚拟密码机,调用云密码机停止接口,W,虚拟密码机停止请求,虚拟机ID,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起停止虚拟机请求,停止虚拟密码机,更新虚拟机状态,W,虚拟密码机状态,虚拟机ID、新状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起停止虚拟机请求,停止虚拟密码机,记录停止操作日志,W,虚拟机操作日志,操作类型、操作时间、操作用户,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起停止虚拟机请求,停止虚拟密码机,返回停止结果,X,虚拟机停止结果,停止状态、虚拟机ID、错误信息,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起重启虚拟机请求,重启虚拟密码机,输入虚拟机重启请求,E,虚拟机重启请求,虚拟机ID,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起重启虚拟机请求,重启虚拟密码机,校验虚拟机存在性,R,虚拟密码机基础信息,虚拟机ID、当前状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起重启虚拟机请求,重启虚拟密码机,校验重启权限,R,用户权限信息,用户ID、重启权限,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起重启虚拟机请求,重启虚拟密码机,调用云密码机重启接口,W,虚拟密码机重启请求,虚拟机ID,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起重启虚拟机请求,重启虚拟密码机,更新虚拟机状态,W,虚拟密码机状态,虚拟机ID、新状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起重启虚拟机请求,重启虚拟密码机,记录重启操作日志,W,虚拟机操作日志,操作类型、操作时间、操作用户,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起重启虚拟机请求,重启虚拟密码机,返回重启结果,X,虚拟机重启结果,重启状态、虚拟机ID、错误信息,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起强制删除虚拟机请求,强制删除虚拟密码机,输入强制删除请求,E,虚拟机强制删除请求,虚拟机ID,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起强制删除虚拟机请求,强制删除虚拟密码机,校验虚拟机存在性,R,虚拟密码机基础信息,虚拟机ID、当前状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起强制删除虚拟机请求,强制删除虚拟密码机,校验强制删除权限,R,用户权限信息,用户ID、强制删除权限,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起强制删除虚拟机请求,强制删除虚拟密码机,调用云密码机强制删除接口,W,虚拟密码机强制删除请求,虚拟机ID,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起强制删除虚拟机请求,强制删除虚拟密码机,清理残留数据,W,虚拟机残留数据,虚拟机ID、残留配置,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起强制删除虚拟机请求,强制删除虚拟密码机,更新虚拟机状态,W,虚拟密码机状态,虚拟机ID、新状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起强制删除虚拟机请求,强制删除虚拟密码机,记录强制删除日志,W,虚拟机操作日志,操作类型、操作时间、操作用户,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起强制删除虚拟机请求,强制删除虚拟密码机,返回强制删除结果,X,虚拟机强制删除结果,删除状态、虚拟机ID、错误信息,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起生成虚机影像请求,生成虚机影像,输入生成影像请求,E,虚机影像生成请求,虚拟机ID、影像名称,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起生成虚机影像请求,生成虚机影像,校验虚拟机存在性,R,虚拟密码机基础信息,虚拟机ID、当前状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起生成虚机影像请求,生成虚机影像,校验生成权限,R,用户权限信息,用户ID、生成权限,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起生成虚机影像请求,生成虚机影像,调用影像生成接口,W,虚机影像生成请求,虚拟机ID、影像名称,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起生成虚机影像请求,生成虚机影像,监控影像生成进度,R,虚机影像生成状态,生成状态、进度百分比,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起生成虚机影像请求,生成虚机影像,记录生成操作日志,W,虚拟机操作日志,操作类型、操作时间、操作用户,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起生成虚机影像请求,生成虚机影像,返回生成结果,X,虚机影像生成结果,生成状态、影像ID、错误信息,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起下载虚机影像请求,下载虚机影像,输入下载影像请求,E,虚机影像下载请求,影像ID、下载路径,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起下载虚机影像请求,下载虚机影像,校验影像存在性,R,虚机影像信息,影像ID、存储路径,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起下载虚机影像请求,下载虚机影像,校验下载权限,R,用户权限信息,用户ID、下载权限,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起下载虚机影像请求,下载虚机影像,调用影像下载接口,W,虚机影像下载请求,影像ID、下载路径,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起下载虚机影像请求,下载虚机影像,监控下载进度,R,虚机影像下载状态,下载状态、进度百分比,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起下载虚机影像请求,下载虚机影像,记录下载操作日志,W,虚拟机操作日志,操作类型、操作时间、操作用户,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起下载虚机影像请求,下载虚机影像,返回下载结果,X,虚机影像下载结果,下载状态、文件路径、错误信息,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起导入虚机影像请求,导入虚机影像,输入导入影像请求,E,虚机影像导入请求,文件路径、目标虚拟机ID,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起导入虚机影像请求,导入虚机影像,校验目标虚拟机存在性,R,虚拟密码机基础信息,虚拟机ID、当前状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起导入虚机影像请求,导入虚机影像,校验导入权限,R,用户权限信息,用户ID、导入权限,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起导入虚机影像请求,导入虚机影像,调用影像导入接口,W,虚机影像导入请求,文件路径、目标虚拟机ID,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起导入虚机影像请求,导入虚机影像,监控导入进度,R,虚机影像导入状态,导入状态、进度百分比,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起导入虚机影像请求,导入虚机影像,更新虚拟机状态,W,虚拟密码机状态,虚拟机ID、新状态,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起导入虚机影像请求,导入虚机影像,记录导入操作日志,W,虚拟机操作日志,操作类型、操作时间、操作用户,1
密码资产数据管理,密码资产数据管理,虚拟密码机管理,导入虚机影像，还原虚机影像,4,发起者：用户，接收者：网商用密码管理系统,用户发起导入虚机影像请求,导入虚机影像,返回导入结果,X,虚机影像导入结果,导入状态、虚拟机ID、错误信息,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,查询物理密码机列表,物理密码机列表展示,输入物理密码机查询条件,E,物理密码机查询条件,设备类型、厂商、管理IP、版本、序列号,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,查询物理密码机列表,物理密码机列表展示,读取物理密码机信息,R,物理密码机信息,密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,查询物理密码机列表,物理密码机列表展示,返回物理密码机列表展示,X,物理密码机列表展示信息,密码机名称、所属厂商、设备类型、管理IP、版本、序列号、操作按钮,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,查询物理密码机列表,物理密码机列表展示,保存物理密码机查询记录,W,物理密码机查询记录,查询时间、操作人ID、查询条件,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,新建物理密码机,物理密码机注册,输入物理密码机新建信息,E,物理密码机新建信息,密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、连接密码、备注,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,新建物理密码机,物理密码机注册,校验物理密码机唯一性,R,物理密码机校验信息,管理IP、序列号、设备类型,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,新建物理密码机,物理密码机注册,保存物理密码机信息,W,物理密码机信息,密码机名称、所属厂商、设备类型、管理IP、管理端口、版本、序列号、连接密码、备注,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,新建物理密码机,物理密码机注册,返回物理密码机新建结果,X,物理密码机新建结果,操作结果、密码机ID、错误信息,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,新建物理密码机,物理密码机注册,记录物理密码机新建日志,W,物理密码机操作日志,操作人ID、操作时间、操作类型、操作结果,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,编辑物理密码机,物理密码机信息修改,输入物理密码机编辑信息,E,物理密码机编辑信息,密码机ID、修改后的名称、备注、连接密码,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,编辑物理密码机,物理密码机信息修改,读取原始物理密码机信息,R,物理密码机信息,密码机ID、原始名称、所属厂商、设备类型、管理IP、管理端口、版本、序列号,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,编辑物理密码机,物理密码机信息修改,更新物理密码机信息,W,物理密码机信息,密码机ID、修改后的名称、备注、连接密码,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,编辑物理密码机,物理密码机信息修改,返回物理密码机编辑结果,X,物理密码机编辑结果,操作结果、密码机ID、错误信息,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,编辑物理密码机,物理密码机信息修改,记录物理密码机编辑日志,W,物理密码机操作日志,操作人ID、操作时间、操作类型、操作结果,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,删除物理密码机,物理密码机删除,发起物理密码机删除请求,E,物理密码机删除请求,密码机ID、操作人ID,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,删除物理密码机,物理密码机删除,读取物理密码机状态,R,物理密码机状态信息,密码机ID、管理状态、运行状态,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,删除物理密码机,物理密码机删除,执行物理密码机删除,W,物理密码机信息,密码机ID、删除状态,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,删除物理密码机,物理密码机删除,返回物理密码机删除结果,X,物理密码机删除结果,操作结果、密码机ID、错误信息,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,删除物理密码机,物理密码机删除,记录物理密码机删除日志,W,物理密码机操作日志,操作人ID、操作时间、操作类型、操作结果,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,查看物理密码机详情,物理密码机详情展示,发起物理密码机详情请求,E,物理密码机详情请求,密码机ID,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,查看物理密码机详情,物理密码机详情展示,读取物理密码机完整信息,R,物理密码机信息,密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注、运行状态、管理状态,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,查看物理密码机详情,物理密码机详情展示,返回物理密码机详情展示,X,物理密码机详情展示信息,密码机名称、所属厂商、设备类型、管理IP、版本、序列号、运行状态、管理状态、完整性校验结果,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,查看物理密码机详情,物理密码机详情展示,记录物理密码机详情访问日志,W,物理密码机操作日志,操作人ID、操作时间、操作类型,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,强制删除物理密码机,物理密码机强制删除,发起物理密码机强制删除请求,E,物理密码机强制删除请求,密码机ID、操作人ID,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,强制删除物理密码机,物理密码机强制删除,验证强制删除条件,R,物理密码机状态信息,密码机ID、管理状态、运行状态,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,强制删除物理密码机,物理密码机强制删除,执行物理密码机强制删除,W,物理密码机信息,密码机ID、删除状态,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,强制删除物理密码机,物理密码机强制删除,返回物理密码机强制删除结果,X,物理密码机强制删除结果,操作结果、密码机ID、错误信息,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,强制删除物理密码机,物理密码机强制删除,记录物理密码机强制删除日志,W,物理密码机操作日志,操作人ID、操作时间、操作类型、操作结果,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,跳转物理机管理系统,管理页面跳转,获取物理密码机管理地址,R,物理密码机管理地址,管理IP、管理端口,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,跳转物理机管理系统,管理页面跳转,生成管理页面跳转链接,E,管理页面跳转请求,管理IP、管理端口、跳转参数,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,跳转物理机管理系统,管理页面跳转,返回管理页面跳转响应,X,管理页面跳转响应,跳转URL、状态码,1
密码资产数据管理,密码资产数据管理,物理密码机管理,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3,发起者：用户，接收者：密码资产数据管理系统,跳转物理机管理系统,管理页面跳转,记录管理页面跳转日志,W,物理密码机操作日志,操作人ID、操作时间、操作类型、目标地址,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥创建请求,保护主密钥创建,输入主密钥创建参数,E,主密钥创建信息,租户ID、设备类型（服务器密码机/虚拟服务器密码机/签名验签服务器）、密钥算法类型、密钥长度,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥创建请求,保护主密钥创建,校验租户权限和设备状态,R,租户设备校验信息,租户ID、设备在线状态、可用密钥槽位数,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥创建请求,保护主密钥创建,生成主密钥并加密存储,W,主密钥生成结果,主密钥ID、加密密钥值、生成时间、存储位置,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥创建请求,保护主密钥创建,返回主密钥创建结果,X,主密钥创建响应,主密钥ID、生成状态、错误代码,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥创建请求,保护主密钥创建,记录主密钥创建日志,W,主密钥操作日志,操作员ID、操作时间、操作类型、主密钥ID,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥同步请求,保护主密钥同步,输入主密钥同步参数,E,主密钥同步信息,源设备ID、目标设备ID、同步密钥ID,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥同步请求,保护主密钥同步,验证设备连接状态,R,设备状态信息,源设备在线状态、目标设备可用存储空间,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥同步请求,保护主密钥同步,执行主密钥同步操作,W,主密钥同步结果,同步状态、同步时间、错误代码,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥同步请求,保护主密钥同步,返回同步结果,X,主密钥同步响应,同步状态、目标设备密钥ID,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥同步请求,保护主密钥同步,记录主密钥同步日志,W,主密钥操作日志,操作员ID、操作时间、源设备ID、目标设备ID,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥备份请求,保护主密钥备份,输入主密钥备份参数,E,主密钥备份请求,租户ID、备份类型（口令/UKey）、加密算法,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥备份请求,保护主密钥备份,验证备份权限,R,备份权限信息,操作员权限等级、租户ID、设备主密钥状态,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥备份请求,保护主密钥备份,执行主密钥加密备份,W,主密钥备份结果,备份文件ID、加密密钥值、备份时间,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥备份请求,保护主密钥备份,返回备份文件信息,X,主密钥备份响应,备份文件ID、下载链接、备份时间,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥备份请求,保护主密钥备份,记录备份操作日志,W,主密钥操作日志,操作员ID、备份文件ID、操作时间,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥还原请求,保护主密钥还原,输入主密钥还原参数,E,主密钥还原请求,备份文件ID、还原设备ID、验证口令,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥还原请求,保护主密钥还原,验证备份文件有效性,R,备份文件信息,备份文件ID、加密算法、备份时间,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥还原请求,保护主密钥还原,执行主密钥还原操作,W,主密钥还原结果,还原状态、还原时间、错误代码,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥还原请求,保护主密钥还原,返回还原结果,X,主密钥还原响应,还原状态、主密钥ID,1
密码资产数据管理,密码资产数据管理,保护主密钥管理,还原设备内保护主密钥,3,发起者：系统操作员，接收者：密码资产管理系统,主密钥还原请求,保护主密钥还原,记录还原操作日志,W,主密钥操作日志,操作员ID、备份文件ID、还原设备ID,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书导入,用户证书导入,输入用户证书导入信息,E,用户证书导入信息,证书类型（签名/加密）、证书内容、证书序列号、用户ID,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书导入,用户证书导入,校验用户是否存在,R,用户信息,用户ID、用户状态,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书导入,用户证书导入,校验证书格式和有效性,R,证书校验规则,证书格式要求、有效期范围,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书导入,用户证书导入,保存用户证书到数据库,W,用户证书数据,证书类型、证书内容、证书序列号、用户ID、导入时间,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书导入,用户证书导入,返回证书导入结果,X,证书导入结果,证书类型、证书序列号、导入状态、错误信息,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书导入,用户证书导入,记录证书导入操作日志,W,操作日志,操作类型、操作时间、操作用户、证书序列号、操作结果,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书列表查询,用户证书列表,输入证书列表查询条件,E,证书查询条件,用户ID、证书类型、分页参数（页码/每页数量）,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书列表查询,用户证书列表,读取用户证书数据,R,用户证书数据,证书类型、证书序列号、证书状态、创建时间、用户ID,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书列表查询,用户证书列表,返回分页证书列表,X,证书列表结果,证书类型、证书序列号、证书状态、创建时间、分页信息,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书停用,用户证书停用,输入证书停用请求,E,证书停用请求,证书序列号、用户ID,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书停用,用户证书停用,读取证书当前状态,R,用户证书数据,证书序列号、证书状态,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书停用,用户证书停用,更新证书状态为停用,W,用户证书数据,证书序列号、证书状态、更新时间,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书停用,用户证书停用,返回停用结果,X,证书操作结果,证书序列号、操作结果、错误信息,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书启用,用户证书启用,输入证书启用请求,E,证书启用请求,证书序列号、用户ID,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书启用,用户证书启用,读取证书当前状态,R,用户证书数据,证书序列号、证书状态,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书启用,用户证书启用,更新证书状态为启用,W,用户证书数据,证书序列号、证书状态、更新时间,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书启用,用户证书启用,返回启用结果,X,证书操作结果,证书序列号、操作结果、错误信息,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书删除,用户证书删除,输入证书删除请求,E,证书删除请求,证书序列号、用户ID,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书删除,用户证书删除,校验删除权限,R,用户权限信息,用户ID、操作权限,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书删除,用户证书删除,删除证书记录,W,用户证书数据,证书序列号、删除时间,1
密码资产数据管理,密码产品证书及编号管理,用户证书管理,删除用户证书,3,发起者：用户，接收者：密码服务平台,用户证书删除,用户证书删除,返回删除结果,X,证书操作结果,证书序列号、操作结果、错误信息,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,创建应用证书,应用证书创建,输入应用证书创建信息,E,应用证书创建信息,应用ID、证书名称、证书内容、证书类型、有效期,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,创建应用证书,应用证书创建,校验证书格式和内容,R,证书校验规则,证书格式规范、有效期范围、证书类型约束,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,创建应用证书,应用证书创建,保存应用证书到数据库,W,应用证书信息,应用ID、证书名称、证书内容、证书类型、创建时间,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,创建应用证书,应用证书创建,返回证书创建结果,X,证书创建结果,证书ID、证书名称、创建状态、错误信息,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,创建应用证书,应用证书创建,记录证书创建操作日志,W,操作日志,操作用户、操作时间、操作类型、证书ID,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,下载证书请求文件,下载应用证书证书请求,请求下载证书请求文件,E,证书请求下载请求,证书请求ID、下载格式（PEM/CRT）,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,下载证书请求文件,下载应用证书证书请求,读取证书请求文件内容,R,证书请求文件,证书请求内容、文件格式、生成时间,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,下载证书请求文件,下载应用证书证书请求,生成证书请求文件下载流,X,证书请求文件下载,文件内容、文件类型、下载时间,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,导入应用证书,应用证书导入,上传证书请求文件,E,证书请求文件,文件内容、文件类型、上传时间,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,导入应用证书,应用证书导入,解析证书请求文件,R,证书请求解析结果,证书主题、公钥信息、签名算法,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,导入应用证书,应用证书导入,保存解析后的证书信息,W,应用证书信息,应用ID、证书名称、证书内容、证书类型,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,导入应用证书,应用证书导入,返回证书导入结果,X,证书导入结果,证书ID、导入状态、错误信息,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,导入应用证书和密钥,导入应用证书和密钥,上传证书和密钥文件,E,证书密钥文件,签名证书内容、加密证书内容、私钥内容、口令,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,导入应用证书和密钥,导入应用证书和密钥,验证证书和密钥匹配性,R,证书密钥校验规则,证书公钥与私钥匹配规则、口令校验规则,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,导入应用证书和密钥,导入应用证书和密钥,保存证书和密钥信息,W,应用证书密钥信息,应用ID、签名证书、加密证书、私钥、口令,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,导入应用证书和密钥,导入应用证书和密钥,返回导入结果,X,证书密钥导入结果,证书ID、密钥ID、导入状态、错误信息,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,查询应用证书列表,应用证书列表查询,输入证书查询条件,E,证书查询条件,证书名称、证书类型、状态、分页参数,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,查询应用证书列表,应用证书列表查询,读取证书列表数据,R,应用证书列表,证书ID、证书名称、证书类型、状态、创建时间,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,查询应用证书列表,应用证书列表查询,返回分页查询结果,X,证书查询结果,证书列表、总记录数、分页信息,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,停用应用证书,应用证书停用,请求停用证书,E,证书停用请求,证书ID、操作用户ID,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,停用应用证书,应用证书停用,更新证书状态为停用,W,应用证书状态,证书ID、状态、更新时间,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,停用应用证书,应用证书停用,返回停用结果,X,证书停用结果,证书ID、操作状态、更新时间,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,启用应用证书,应用证书启用,请求启用证书,E,证书启用请求,证书ID、操作用户ID,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,启用应用证书,应用证书启用,更新证书状态为启用,W,应用证书状态,证书ID、状态、更新时间,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,启用应用证书,应用证书启用,返回启用结果,X,证书启用结果,证书ID、操作状态、更新时间,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,删除应用证书,应用证书删除,请求删除证书,E,证书删除请求,证书ID、操作用户ID,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,删除应用证书,应用证书删除,校验证书删除权限,R,用户权限信息,用户ID、证书ID、删除权限,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,删除应用证书,应用证书删除,删除证书记录,W,应用证书信息,证书ID、删除时间,1
密码资产数据管理,密码产品证书及编号管理,应用证书管理,删除应用证书,3,发起者：用户，接收者：密码服务管理平台,删除应用证书,应用证书删除,返回删除结果,X,证书删除结果,证书ID、删除状态、错误信息,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户新增密钥,新增密钥,输入密钥新增信息,E,密钥新增信息,密钥ID、密钥名称、密钥类型、密钥算法、密钥长度、密钥用途、是否可导出,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户新增密钥,新增密钥,校验密钥ID唯一性,R,密钥校验信息,密钥ID、密钥名称、密钥类型,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户新增密钥,新增密钥,校验密钥算法合法性,R,密钥算法字典,算法类型、算法参数,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户新增密钥,新增密钥,生成密钥生命周期属性,R,密钥生命周期属性,密钥状态、创建时间、更新时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户新增密钥,新增密钥,保存密钥基本信息,W,密钥基本信息,密钥ID、密钥名称、密钥类型、密钥算法、密钥长度,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户新增密钥,新增密钥,保存密钥生命周期信息,W,密钥生命周期信息,密钥状态、创建时间、更新时间、操作员ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户新增密钥,新增密钥,返回密钥新增结果,X,密钥新增结果,密钥ID、密钥名称、操作结果,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户新增密钥,新增密钥,记录密钥新增日志,W,密钥操作日志,操作类型、操作时间、操作员ID、密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户新增密钥,新增密钥,生成密钥摘要值,W,密钥摘要信息,密钥摘要值、摘要算法,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户新增密钥,新增密钥,更新密钥状态为激活,W,密钥状态信息,密钥状态、更新时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户查询密钥列表,密钥信息列表,输入分页查询条件,E,密钥查询条件,应用名称、密钥ID、密钥名称、页码、每页数量,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户查询密钥列表,密钥信息列表,读取密钥基本信息,R,密钥基本信息,密钥ID、密钥名称、密钥类型、密钥算法、密钥长度,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户查询密钥列表,密钥信息列表,读取密钥生命周期信息,R,密钥生命周期信息,密钥状态、创建时间、更新时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户查询密钥列表,密钥信息列表,合并密钥信息并分页返回,X,密钥列表信息,应用名称、密钥ID、密钥名称、密钥类型、密钥状态、密钥算法、密钥长度、分页信息,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户查询密钥,密钥查询,输入密钥查询条件,E,密钥查询条件,应用名称、密钥ID、密钥名称,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户查询密钥,密钥查询,读取匹配的密钥信息,R,密钥查询结果,密钥ID、密钥名称、密钥类型、密钥算法、密钥长度,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户查询密钥,密钥查询,返回密钥查询结果,X,密钥查询结果,密钥ID、密钥名称、密钥类型、密钥算法、密钥长度,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户查看密钥详情,密钥详情,输入密钥ID,E,密钥详情请求,密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户查看密钥详情,密钥详情,读取密钥基本信息,R,密钥基本信息,密钥ID、密钥名称、密钥类型、密钥算法、密钥长度,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户查看密钥详情,密钥详情,读取密钥生命周期信息,R,密钥生命周期信息,密钥状态、创建时间、更新时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户查看密钥详情,密钥详情,读取密钥摘要信息,R,密钥摘要信息,密钥摘要值、摘要算法,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户查看密钥详情,密钥详情,返回密钥详情信息,X,密钥详情信息,密钥ID、密钥名称、密钥类型、密钥算法、密钥长度、密钥状态、创建时间、更新时间、密钥摘要值、是否可导出,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户查看密钥链接,密钥链接,输入密钥ID,E,密钥链接请求,密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户查看密钥链接,密钥链接,读取密钥链接信息,R,密钥链接信息,关联密钥ID、关联密钥名称、链接类型,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户查看密钥链接,密钥链接,返回密钥链接信息,X,密钥链接信息,关联密钥ID、关联密钥名称、链接类型,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户查看密钥历史版本,密钥历史版本,输入密钥ID,E,密钥历史版本请求,密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户查看密钥历史版本,密钥历史版本,读取密钥历史版本信息,R,密钥历史版本信息,版本号、修改时间、修改内容,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户查看密钥历史版本,密钥历史版本,返回密钥历史版本信息,X,密钥历史版本信息,版本号、修改时间、修改内容,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户翻新密钥,密钥翻新,输入密钥翻新请求,E,密钥翻新请求,密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户翻新密钥,密钥翻新,读取当前密钥信息,R,密钥基本信息,密钥ID、密钥类型、密钥算法,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户翻新密钥,密钥翻新,生成新密钥,W,新密钥信息,新密钥ID、新密钥值、生成时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户翻新密钥,密钥翻新,更新密钥生命周期信息,W,密钥生命周期信息,密钥状态、更新时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户翻新密钥,密钥翻新,记录密钥翻新日志,W,密钥操作日志,操作类型、操作时间、操作员ID、密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户翻新密钥,密钥翻新,返回密钥翻新结果,X,密钥翻新结果,新密钥ID、操作结果,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：系统，接收者：系统,系统触发自动翻新,密钥自动翻新,读取自动翻新策略,R,自动翻新策略,翻新周期、密钥类型、算法类型,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：系统，接收者：系统,系统触发自动翻新,密钥自动翻新,筛选待翻新密钥,R,待翻新密钥列表,密钥ID、密钥类型、密钥算法,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：系统，接收者：系统,系统触发自动翻新,密钥自动翻新,生成新密钥,W,新密钥信息,新密钥ID、新密钥值、生成时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：系统，接收者：系统,系统触发自动翻新,密钥自动翻新,更新密钥生命周期信息,W,密钥生命周期信息,密钥状态、更新时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：系统，接收者：系统,系统触发自动翻新,密钥自动翻新,记录自动翻新日志,W,密钥操作日志,操作类型、操作时间、操作员ID、密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户归档密钥,密钥归档,输入密钥归档请求,E,密钥归档请求,密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户归档密钥,密钥归档,读取密钥当前状态,R,密钥状态信息,密钥ID、密钥状态,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户归档密钥,密钥归档,更新密钥状态为归档,W,密钥状态信息,密钥状态、更新时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户归档密钥,密钥归档,记录密钥归档日志,W,密钥操作日志,操作类型、操作时间、操作员ID、密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户归档密钥,密钥归档,返回密钥归档结果,X,密钥归档结果,密钥ID、操作结果,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户恢复密钥,密钥恢复,输入密钥恢复请求,E,密钥恢复请求,密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户恢复密钥,密钥恢复,读取归档密钥信息,R,归档密钥信息,密钥ID、密钥值、归档时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户恢复密钥,密钥恢复,更新密钥状态为激活,W,密钥状态信息,密钥状态、更新时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户恢复密钥,密钥恢复,记录密钥恢复日志,W,密钥操作日志,操作类型、操作时间、操作员ID、密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户恢复密钥,密钥恢复,返回密钥恢复结果,X,密钥恢复结果,密钥ID、操作结果,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户注销密钥,密钥注销,输入密钥注销请求,E,密钥注销请求,密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户注销密钥,密钥注销,读取密钥当前状态,R,密钥状态信息,密钥ID、密钥状态,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户注销密钥,密钥注销,更新密钥状态为注销,W,密钥状态信息,密钥状态、更新时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户注销密钥,密钥注销,记录密钥注销日志,W,密钥操作日志,操作类型、操作时间、操作员ID、密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户注销密钥,密钥注销,返回密钥注销结果,X,密钥注销结果,密钥ID、操作结果,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户销毁密钥,密钥销毁,输入密钥销毁请求,E,密钥销毁请求,密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户销毁密钥,密钥销毁,读取密钥当前状态,R,密钥状态信息,密钥ID、密钥状态,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户销毁密钥,密钥销毁,执行密钥销毁操作,W,密钥销毁信息,密钥ID、销毁时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户销毁密钥,密钥销毁,记录密钥销毁日志,W,密钥操作日志,操作类型、操作时间、操作员ID、密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户销毁密钥,密钥销毁,返回密钥销毁结果,X,密钥销毁结果,密钥ID、操作结果,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户删除密钥,密钥删除,输入密钥删除请求,E,密钥删除请求,密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户删除密钥,密钥删除,读取密钥当前状态,R,密钥状态信息,密钥ID、密钥状态,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户删除密钥,密钥删除,执行密钥删除操作,W,密钥删除信息,密钥ID、删除时间,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户删除密钥,密钥删除,记录密钥删除日志,W,密钥操作日志,操作类型、操作时间、操作员ID、密钥ID,1
密码资产数据管理,密钥信息管理,密钥及生命周期管理,将密钥做删除处理,3,发起者：用户，接收者：系统,用户删除密钥,密钥删除,返回密钥删除结果,X,密钥删除结果,密钥ID、操作结果,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,上传文件添加知识库记录,添加密码知识库数据,输入密码知识库新增信息,E,密码知识库新增信息,知识库名称、知识库分类、文件类型、文件大小、备注、租户访问标志,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,上传文件添加知识库记录,添加密码知识库数据,读取文件存储路径信息,R,文件存储路径信息,存储路径、文件名称、文件类型,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,上传文件添加知识库记录,添加密码知识库数据,保存密码知识库记录,W,密码知识库记录,ID、知识库名称、知识库分类、文件路径、文件大小、备注、租户访问标志、创建时间,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,上传文件添加知识库记录,添加密码知识库数据,返回知识库新增结果,X,知识库新增结果,知识库名称、操作结果、错误信息,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,上传文件添加知识库记录,添加密码知识库数据,记录知识库新增操作日志,W,知识库操作日志,操作员ID、操作类型、操作时间、操作内容、操作结果,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,编辑知识库记录,编辑密码知识库数据,输入密码知识库编辑信息,E,密码知识库编辑信息,知识库ID、知识库名称、知识库分类、备注、租户访问标志,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,编辑知识库记录,编辑密码知识库数据,读取原知识库记录,R,密码知识库原信息,ID、知识库名称、知识库分类、文件路径、备注、租户访问标志,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,编辑知识库记录,编辑密码知识库数据,更新密码知识库记录,W,密码知识库更新信息,ID、知识库名称、知识库分类、备注、租户访问标志、修改时间,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,编辑知识库记录,编辑密码知识库数据,返回知识库编辑结果,X,知识库编辑结果,知识库名称、操作结果、错误信息,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,编辑知识库记录,编辑密码知识库数据,记录知识库编辑操作日志,W,知识库操作日志,操作员ID、操作类型、操作时间、操作内容、操作结果,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,删除知识库记录,删除密码知识库数据,输入密码知识库删除请求,E,密码知识库删除请求,知识库ID、操作员ID,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,删除知识库记录,删除密码知识库数据,验证操作员删除权限,R,操作员权限信息,操作员ID、操作权限、知识库ID,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,删除知识库记录,删除密码知识库数据,删除密码知识库记录,W,密码知识库删除信息,ID、删除时间、操作员ID,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,删除知识库记录,删除密码知识库数据,删除关联文件存储记录,W,文件存储删除信息,文件路径、删除时间、操作员ID,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,删除知识库记录,删除密码知识库数据,返回知识库删除结果,X,知识库删除结果,知识库ID、操作结果、错误信息,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,删除知识库记录,删除密码知识库数据,记录知识库删除操作日志,W,知识库操作日志,操作员ID、操作类型、操作时间、操作内容、操作结果,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,查询知识库记录,查询密码知识库数据,输入密码知识库查询条件,E,密码知识库查询条件,知识库名称、知识库分类、文件类型、租户访问标志,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,查询知识库记录,查询密码知识库数据,读取密码知识库记录,R,密码知识库记录,ID、知识库名称、知识库分类、文件类型、文件大小、备注、租户访问标志、创建时间,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,查询知识库记录,查询密码知识库数据,返回知识库查询结果,X,密码知识库查询结果,ID、知识库名称、知识库分类、文件类型、文件大小、备注、租户访问标志、创建时间,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,查询知识库记录,查询密码知识库数据,记录知识库查询操作日志,W,知识库操作日志,操作员ID、操作类型、操作时间、查询条件,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,配置知识库显示状态,显示/隐藏知识库信息,输入知识库显示配置信息,E,知识库显示配置信息,知识库ID、租户显示标志,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,配置知识库显示状态,显示/隐藏知识库信息,读取原知识库记录,R,密码知识库原信息,ID、租户访问标志,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,配置知识库显示状态,显示/隐藏知识库信息,更新知识库显示状态,W,知识库显示更新信息,ID、租户访问标志、修改时间,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,配置知识库显示状态,显示/隐藏知识库信息,返回显示状态更新结果,X,显示状态更新结果,知识库ID、租户显示标志、操作结果,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,配置知识库显示状态,显示/隐藏知识库信息,记录显示状态配置日志,W,知识库操作日志,操作员ID、操作类型、操作时间、配置内容,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,预览知识库文件,预览知识库信息,输入知识库预览请求,E,知识库预览请求,知识库ID、文件类型,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,预览知识库文件,预览知识库信息,读取知识库文件存储信息,R,文件存储信息,存储路径、文件名称、文件类型,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,预览知识库文件,预览知识库信息,返回文件预览内容,X,文件预览内容,文件路径、文件类型、文件大小、预览格式,1
密码资产数据管理,密码文档信息管理,密码文档信息管理,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3,发起者：平台操作员，接收者：密码资产数据管理系统,预览知识库文件,预览知识库信息,记录知识库预览操作日志,W,知识库操作日志,操作员ID、操作类型、操作时间、文件类型,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,分页请求,密码应用测评改造阶段分页列表,输入分页参数,E,测评阶段分页请求,页码、每页数量,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,分页请求,密码应用测评改造阶段分页列表,查询测评阶段信息,R,测评阶段信息,阶段ID、阶段名称、阶段编码、创建时间,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,分页请求,密码应用测评改造阶段分页列表,返回分页结果,X,测评阶段分页结果,总记录数、阶段列表、当前页码,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,过滤查询请求,密码应用测评改造阶段过滤查询,输入过滤条件,E,测评阶段过滤条件,阶段编码、阶段名称,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,过滤查询请求,密码应用测评改造阶段过滤查询,查询匹配的测评阶段,R,测评阶段信息,阶段ID、阶段名称、阶段编码,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,过滤查询请求,密码应用测评改造阶段过滤查询,返回过滤结果,X,测评阶段过滤结果,阶段列表、匹配数量,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,新增阶段请求,新增密码应用测评改造阶段,输入新增阶段信息,E,测评阶段新增信息,阶段名称、阶段编码、描述,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,新增阶段请求,新增密码应用测评改造阶段,校验阶段编码唯一性,R,测评阶段校验信息,阶段编码、阶段ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,新增阶段请求,新增密码应用测评改造阶段,保存新增阶段数据,W,测评阶段新增数据,阶段ID、阶段名称、阶段编码,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,新增阶段请求,新增密码应用测评改造阶段,返回新增结果,X,测评阶段新增结果,阶段ID、操作结果,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,编辑阶段请求,编辑密码应用测评改造阶段,输入编辑参数,E,测评阶段编辑参数,阶段ID、新阶段名称、新阶段编码,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,编辑阶段请求,编辑密码应用测评改造阶段,查询原阶段信息,R,测评阶段信息,阶段ID、阶段名称、阶段编码,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,编辑阶段请求,编辑密码应用测评改造阶段,更新阶段数据,W,测评阶段更新数据,阶段ID、阶段名称、阶段编码,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,编辑阶段请求,编辑密码应用测评改造阶段,返回编辑结果,X,测评阶段编辑结果,阶段ID、操作结果,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,删除阶段请求,删除密码应用测评改造阶段,输入删除参数,E,测评阶段删除参数,阶段ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,删除阶段请求,删除密码应用测评改造阶段,查询关联应用,R,关联应用信息,阶段ID、应用ID列表,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,删除阶段请求,删除密码应用测评改造阶段,删除阶段数据,W,测评阶段删除数据,阶段ID、删除时间,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,删除阶段请求,删除密码应用测评改造阶段,返回删除结果,X,测评阶段删除结果,阶段ID、操作结果,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,设置阶段请求,密码应用设置测评改造阶段,输入设置参数,E,应用阶段设置参数,应用ID、阶段ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,设置阶段请求,密码应用设置测评改造阶段,查询应用当前阶段,R,应用当前阶段信息,应用ID、当前阶段ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,设置阶段请求,密码应用设置测评改造阶段,更新应用阶段,W,应用阶段更新数据,应用ID、阶段ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,设置阶段请求,密码应用设置测评改造阶段,返回设置结果,X,应用阶段设置结果,应用ID、操作结果,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,修改阶段请求,密码应用修改测评改造阶段,输入修改参数,E,应用阶段修改参数,应用ID、新阶段ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,修改阶段请求,密码应用修改测评改造阶段,查询应用当前阶段,R,应用当前阶段信息,应用ID、当前阶段ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,修改阶段请求,密码应用修改测评改造阶段,更新应用阶段,W,应用阶段更新数据,应用ID、阶段ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,修改阶段请求,密码应用修改测评改造阶段,返回修改结果,X,应用阶段修改结果,应用ID、操作结果,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,查询阶段请求,查询密码应用测评改造阶段,输入查询参数,E,应用阶段查询参数,应用ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,查询阶段请求,查询密码应用测评改造阶段,查询应用当前阶段,R,应用当前阶段信息,应用ID、阶段ID、阶段名称,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,查询阶段请求,查询密码应用测评改造阶段,返回查询结果,X,应用阶段查询结果,应用ID、阶段ID、阶段名称,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,查询阶段请求,查询密码应用测评改造阶段,记录查询日志,W,应用阶段查询日志,应用ID、查询时间、操作员ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,阶段分布查询,测评改造阶段的应用分布,输入阶段ID,E,阶段分布查询参数,阶段ID,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,阶段分布查询,测评改造阶段的应用分布,查询关联应用,R,关联应用信息,阶段ID、应用ID列表,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,阶段分布查询,测评改造阶段的应用分布,统计应用数量,R,应用数量统计,阶段ID、应用数量,1
密码应用测评管理,改造阶段管理,密码应用测评管理,根据测评改造阶段查询包含的应用数量,5,发起者：用户，接收者：密码服务平台,阶段分布查询,测评改造阶段的应用分布,返回分布结果,X,阶段应用分布结果,阶段ID、应用数量,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：密码应用测评系统,分页查询测评报告,应用测评报告分页列表查询,输入测评报告分页查询条件,E,测评报告分页查询条件,页码、每页数量、报告名称、所属应用、报告格式,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：密码应用测评系统,分页查询测评报告,应用测评报告分页列表查询,读取测评报告基础信息,R,测评报告基础信息,报告ID、报告名称、所属应用ID、报告格式、测评分数、上报时间,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：密码应用测评系统,分页查询测评报告,应用测评报告分页列表查询,处理分页逻辑并返回结果,X,测评报告分页结果,总记录数、当前页数据列表、分页状态,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：密码应用测评系统,新增测评报告对象,新增应用测评报告对象,输入测评报告新增信息,E,测评报告新增信息,报告名称、所属应用ID、报告格式、测评分数,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：密码应用测评系统,新增测评报告对象,新增应用测评报告对象,校验所属应用有效性,R,应用信息,应用ID、应用名称、所属单位ID,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：密码应用测评系统,新增测评报告对象,新增应用测评报告对象,保存测评报告对象,W,测评报告对象,报告ID、报告名称、所属应用ID、报告格式、测评分数、创建时间,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：文件服务,上传测评报告文件,应用测评报告文件上传,接收测评报告文件数据,E,测评报告文件数据,文件名、文件内容、文件类型、文件大小,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：文件服务,上传测评报告文件,应用测评报告文件上传,调用文件服务上传接口,X,文件上传请求,文件流、文件元数据、上传路径,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：文件服务,上传测评报告文件,应用测评报告文件上传,关联文件与测评报告对象,W,测评报告文件关联,报告ID、文件存储路径、文件哈希值,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：文件服务,预览测评报告文件,应用测评报告文件预览,获取文件预览请求,E,文件预览请求,报告ID、文件标识,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：文件服务,预览测评报告文件,应用测评报告文件预览,读取文件存储路径信息,R,文件存储信息,文件路径、文件类型、文件大小,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：文件服务,预览测评报告文件,应用测评报告文件预览,返回文件预览流,X,文件预览内容,文件流、文件元数据、预览格式,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：文件服务,下载测评报告文件,应用测评报告文件下载,选择测评报告下载对象,E,测评报告下载请求,报告ID、下载格式,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：文件服务,下载测评报告文件,应用测评报告文件下载,读取文件存储路径信息,R,文件存储信息,文件路径、文件类型、文件大小,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：文件服务,下载测评报告文件,应用测评报告文件下载,生成文件下载链接,X,文件下载响应,下载URL、文件名、文件类型,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：密码应用测评系统,编辑测评报告对象,编辑应用测评报告对象,输入测评报告修改信息,E,测评报告修改信息,报告ID、新报告名称、新描述,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：密码应用测评系统,编辑测评报告对象,编辑应用测评报告对象,读取原测评报告信息,R,测评报告基础信息,报告ID、报告名称、所属应用ID、报告格式,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：密码应用测评系统,编辑测评报告对象,编辑应用测评报告对象,更新测评报告对象,W,测评报告更新信息,报告ID、新报告名称、修改时间,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：密码应用测评系统,删除测评报告对象,删除应用测评报告对象,发起测评报告删除请求,E,测评报告删除请求,报告ID、删除原因,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：密码应用测评系统,删除测评报告对象,删除应用测评报告对象,读取关联文件信息,R,文件存储信息,文件路径、文件哈希值,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：密码应用测评系统,删除测评报告对象,删除应用测评报告对象,删除测评报告对象,W,测评报告删除记录,报告ID、删除时间、操作员ID,1
密码应用测评管理,应用测评报告、测评分数管理,应用测评管理,删除测评报告数据和对应文件,4,发起者：用户，接收者：密码应用测评系统,删除测评报告对象,删除应用测评报告对象,调用文件服务删除文件,X,文件删除请求,文件路径、删除标识,1
密码应用测评管理,密码应用方案管理,应用测评方案管理,"为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。
",4,发起者：用户，接收者：密码服务平台,分页查询测评方案,密码应用测评方案分页列表,输入测评方案分页查询条件,E,测评方案分页查询条件,页码、每页数量、应用名称、等保等级,1
密码应用测评管理,密码应用方案管理,应用测评方案管理,"为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。
",4,发起者：用户，接收者：密码服务平台,分页查询测评方案,密码应用测评方案分页列表,读取测评方案基础信息,R,测评方案基础信息,测评方案ID、应用ID、等保等级、创建时间,1
密码应用测评管理,密码应用方案管理,应用测评方案管理,"为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。
",4,发起者：用户，接收者：密码服务平台,分页查询测评方案,密码应用测评方案分页列表,返回测评方案分页结果,X,测评方案分页结果,总记录数、当前页数据列表（含测评方案ID、应用名称、等保状态）,1
密码应用测评管理,密码应用方案管理,应用测评方案管理,"为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。
",4,发起者：用户，接收者：密码服务平台,提交测评方案新增请求,新建密码应用测评方案,输入测评方案新增信息,E,测评方案新增信息,应用ID、等保等级、等保状态、密评进度模板ID、密评要求模板ID、密评交付模板ID,1
密码应用测评管理,密码应用方案管理,应用测评方案管理,"为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。
",4,发起者：用户，接收者：密码服务平台,提交测评方案新增请求,新建密码应用测评方案,校验应用有效性,R,应用信息,应用ID、应用名称、业务系统类型,1
密码应用测评管理,密码应用方案管理,应用测评方案管理,"为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。
",4,发起者：用户，接收者：密码服务平台,提交测评方案新增请求,新建密码应用测评方案,生成测评方案记录,W,测评方案记录,测评方案ID、应用ID、等保等级、创建时间、更新时间,1
密码应用测评管理,密码应用方案管理,应用测评方案管理,"为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。
",4,发起者：用户，接收者：密码服务平台,提交测评方案新增请求,新建密码应用测评方案,关联模板配置,W,模板关联信息,测评方案ID、密评进度模板ID、密评要求模板ID、密评交付模板ID,1
密码应用测评管理,密码应用方案管理,应用测评方案管理,"为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。
",4,发起者：用户，接收者：密码服务平台,提交测评方案新增请求,新建密码应用测评方案,返回测评方案新增结果,X,测评方案新增结果,测评方案ID、操作结果状态、错误信息（如有）,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,绑定测评进度模板,绑定密码应用测评进度模板,输入测评进度模板绑定信息,E,测评进度模板绑定信息,密码应用ID、测评进度模板ID、绑定时间,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,绑定测评进度模板,绑定密码应用测评进度模板,校验测评进度模板有效性,R,测评进度模板信息,模板ID、模板名称、模板状态,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,绑定测评进度模板,绑定密码应用测评进度模板,保存测评进度模板绑定关系,W,测评进度模板绑定信息,密码应用ID、测评进度模板ID、绑定时间,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,绑定测评进度模板,绑定密码应用测评进度模板,返回绑定结果,X,测评进度模板绑定结果,绑定状态、错误信息,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,绑定测评进度模板,绑定密码应用测评进度模板,记录测评进度模板绑定日志,W,测评进度模板绑定日志,操作员ID、绑定时间、绑定结果,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,绑定测评要求模板,绑定密码应用测评要求模板,输入测评要求模板绑定信息,E,测评要求模板绑定信息,密码应用ID、测评要求模板ID、绑定时间,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,绑定测评要求模板,绑定密码应用测评要求模板,校验测评要求模板有效性,R,测评要求模板信息,模板ID、模板名称、模板状态,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,绑定测评要求模板,绑定密码应用测评要求模板,保存测评要求模板绑定关系,W,测评要求模板绑定信息,密码应用ID、测评要求模板ID、绑定时间,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,绑定测评要求模板,绑定密码应用测评要求模板,返回绑定结果,X,测评要求模板绑定结果,绑定状态、错误信息,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,绑定测评要求模板,绑定密码应用测评要求模板,记录测评要求模板绑定日志,W,测评要求模板绑定日志,操作员ID、绑定时间、绑定结果,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,编辑测评进度模板,密码应用测评进度模板编辑,输入测评进度模板编辑信息,E,测评进度模板编辑信息,原模板ID、新模板ID、密码应用ID,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,编辑测评进度模板,密码应用测评进度模板编辑,读取原测评进度模板绑定信息,R,测评进度模板绑定信息,密码应用ID、原模板ID,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,编辑测评进度模板,密码应用测评进度模板编辑,更新测评进度模板绑定关系,W,测评进度模板绑定信息,密码应用ID、新模板ID,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,编辑测评进度模板,密码应用测评进度模板编辑,返回编辑结果,X,测评进度模板编辑结果,编辑状态、错误信息,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,编辑测评进度模板,密码应用测评进度模板编辑,记录测评进度模板编辑日志,W,测评进度模板编辑日志,操作员ID、编辑时间、编辑结果,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,编辑测评要求模板,密码应用测评要求模板编辑,输入测评要求模板编辑信息,E,测评要求模板编辑信息,原模板ID、新模板ID、密码应用ID,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,编辑测评要求模板,密码应用测评要求模板编辑,读取原测评要求模板绑定信息,R,测评要求模板绑定信息,密码应用ID、原模板ID,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,编辑测评要求模板,密码应用测评要求模板编辑,更新测评要求模板绑定关系,W,测评要求模板绑定信息,密码应用ID、新模板ID,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,编辑测评要求模板,密码应用测评要求模板编辑,返回编辑结果,X,测评要求模板编辑结果,编辑状态、错误信息,1
密码应用测评管理,密码应用方案管理,应用测评模板管理,编辑密码应用测评方案对象绑定的测评要求模板,3,发起者：用户，接收者：密码应用测评管理系统,编辑测评要求模板,密码应用测评要求模板编辑,记录测评要求模板编辑日志,W,测评要求模板编辑日志,操作员ID、编辑时间、编辑结果,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：用户，接收者：密码应用测评系统,测评要求推进,密码应用测评要求推进,输入测评要求输入信息,E,测评要求输入信息,测评要求ID、测评要求状态（满足/不满足）、测评调研选项ID、测评改进建议ID,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：用户，接收者：密码应用测评系统,测评要求推进,密码应用测评要求推进,读取测评模板条目信息,R,测评模板条目,模板条目ID、模板条目名称、测评要求描述、风险等级、调研选项列表、改进建议列表,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：用户，接收者：密码应用测评系统,测评要求推进,密码应用测评要求推进,校验测评要求状态与模板关联性,R,测评要求关联校验,测评要求ID、模板条目ID、关联状态,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：用户，接收者：密码应用测评系统,测评要求推进,密码应用测评要求推进,保存测评要求状态信息,W,测评要求状态,测评要求ID、测评状态、更新时间、操作员ID,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：用户，接收者：密码应用测评系统,测评要求推进,密码应用测评要求推进,加载测评调研选项内容,R,测评调研选项,调研选项ID、选项内容、关联模板条目ID,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：用户，接收者：密码应用测评系统,测评要求推进,密码应用测评要求推进,加载测评改进建议内容,R,测评改进建议,改进建议ID、建议内容、关联模板条目ID,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：用户，接收者：密码应用测评系统,测评要求推进,密码应用测评要求推进,输出测评要求推进结果,X,测评要求推进结果,测评要求ID、测评状态、调研选项内容、改进建议内容、操作结果,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：用户，接收者：密码应用测评系统,测评要求推进,密码应用测评要求推进,记录测评要求操作日志,W,测评操作日志,操作类型、操作时间、操作员ID、测评要求ID、操作详情,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：用户，接收者：密码应用测评系统,测评进度推进,密码应用测评进度推进,输入测评进度更新信息,E,测评进度输入,测评进度ID、当前阶段、整改状态、预计完成时间,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：用户，接收者：密码应用测评系统,测评进度推进,密码应用测评进度推进,读取当前测评进度信息,R,当前测评进度,测评进度ID、当前阶段、整改状态、历史记录,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：用户，接收者：密码应用测评系统,测评进度推进,密码应用测评进度推进,校验测评进度更新权限,R,测评权限校验,操作员ID、测评对象ID、操作权限,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：用户，接收者：密码应用测评系统,测评进度推进,密码应用测评进度推进,更新测评进度状态,W,测评进度更新,测评进度ID、新阶段、整改状态、更新时间,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：用户，接收者：密码应用测评系统,测评进度推进,密码应用测评进度推进,输出测评进度更新结果,X,测评进度结果,测评进度ID、当前阶段、整改状态、更新时间、操作结果,1
密码应用测评管理,密码应用方案管理,密评进度推进管理,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5,发起者：用户，接收者：密码应用测评系统,测评进度推进,密码应用测评进度推进,记录测评进度操作日志,W,测评操作日志,操作类型、操作时间、操作员ID、测评进度ID、操作详情,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：用户，接收者：密码服务平台,生成并展示密评进度跟踪报告,密码应用测评进度跟踪报告展示,输入密评对象ID和模板ID,E,密评进度展示请求,密评对象ID、模板ID,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：用户，接收者：密码服务平台,生成并展示密评进度跟踪报告,密码应用测评进度跟踪报告展示,读取密评进度模板数据,R,密评进度模板信息,模板ID、阶段名称、阶段描述、关联对象ID,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：用户，接收者：密码服务平台,生成并展示密评进度跟踪报告,密码应用测评进度跟踪报告展示,读取密评对象当前进度数据,R,密评进度跟踪报告信息,进度ID、阶段名称、预估完成时间、当前进度百分比、备注,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：用户，接收者：密码服务平台,生成并展示密评进度跟踪报告,密码应用测评进度跟踪报告展示,生成并展示进度列表,X,密评进度展示结果,阶段名称、预估完成时间、当前进度百分比、备注、风险项,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：用户，接收者：密码服务平台,生成并展示密评进度跟踪报告,密码应用测评进度跟踪报告展示,保存编辑后的进度数据,W,密评进度更新信息,进度ID、预估完成时间、当前进度百分比、备注,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：用户，接收者：密码服务平台,修改密评进度模板展示要素,密码应用测评进度跟踪报告编辑,输入模板修改请求,E,模板修改请求,模板ID、字段显示状态、字段排序,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：用户，接收者：密码服务平台,修改密评进度模板展示要素,密码应用测评进度跟踪报告编辑,读取当前模板配置,R,密评进度模板信息,模板ID、字段列表、显示状态、排序规则,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：用户，接收者：密码服务平台,修改密评进度模板展示要素,密码应用测评进度跟踪报告编辑,更新模板配置,W,密评进度模板更新,模板ID、字段显示状态、字段排序,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：用户，接收者：密码服务平台,修改密评进度模板展示要素,密码应用测评进度跟踪报告编辑,输出模板更新结果,X,模板更新确认信息,模板ID、更新时间、操作结果,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：用户，接收者：密码服务平台,下载密评进度跟踪报告,密码应用测评进度跟踪报告下载,输入下载请求参数,E,报告下载请求,报告ID、导出格式,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：用户，接收者：密码服务平台,下载密评进度跟踪报告,密码应用测评进度跟踪报告下载,读取报告数据,R,密评进度跟踪报告信息,报告ID、阶段名称、预估完成时间、当前进度百分比、备注,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：用户，接收者：密码服务平台,下载密评进度跟踪报告,密码应用测评进度跟踪报告下载,生成报告文件,W,生成的报告文件,文件ID、文件类型、生成时间,1
密码应用测评管理,密码应用方案管理,密评进度跟踪报告管理,下载密码应用测评进度的跟踪报告文件,4,发起者：用户，接收者：密码服务平台,下载密评进度跟踪报告,密码应用测评进度跟踪报告下载,输出下载链接,X,报告下载链接,文件ID、下载地址、有效期,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,请求展示差距分析内容,密码应用测评差距分析内容展示,输入差距分析查询条件,E,差距分析查询条件,测评项目ID、业务系统ID、查询时间范围,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,请求展示差距分析内容,密码应用测评差距分析内容展示,读取差距分析数据,R,差距分析数据,测评项名称、差距描述、整改建议、风险等级、测评状态,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,请求展示差距分析内容,密码应用测评差距分析内容展示,生成差距分析展示视图,X,差距分析展示数据,测评项列表、差距详情、整改进度、关联文档,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,请求展示差距分析内容,密码应用测评差距分析内容展示,加载关联密码产品信息,R,密码产品关联信息,产品名称、服务类型、部署位置、合规状态,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,请求展示差距分析内容,密码应用测评差距分析内容展示,记录展示操作日志,W,差距分析展示日志,操作用户ID、展示时间、查询参数、展示结果摘要,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,提交差距分析内容编辑,密码应用测评差距分析内容编辑,输入差距分析编辑信息,E,差距分析编辑信息,测评项ID、修改字段、新值、修改说明,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,提交差距分析内容编辑,密码应用测评差距分析内容编辑,读取原差距分析数据,R,原差距分析数据,测评项ID、原始差距描述、原始整改建议,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,提交差距分析内容编辑,密码应用测评差距分析内容编辑,执行差距分析数据更新,W,差距分析更新数据,测评项ID、更新后差距描述、更新后整改建议、修改时间,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,提交差距分析内容编辑,密码应用测评差距分析内容编辑,返回编辑结果确认信息,X,编辑结果确认信息,操作结果状态、修改字段摘要、版本号,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,提交差距分析内容编辑,密码应用测评差距分析内容编辑,记录编辑操作日志,W,差距分析编辑日志,操作用户ID、修改时间、修改字段、旧值/新值,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,请求生成差距分析报告,密码应用测评差距分析内容报告生成,输入报告生成参数,E,报告生成参数,报告类型、包含字段、格式选项、时间范围,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,请求生成差距分析报告,密码应用测评差距分析内容报告生成,读取完整差距分析数据,R,完整差距分析数据,测评项列表、差距详情、整改建议、风险等级、关联文档,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,请求生成差距分析报告,密码应用测评差距分析内容报告生成,生成结构化报告文件,W,生成的报告文件,报告标题、目录结构、内容章节、附件列表、生成时间,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,请求生成差距分析报告,密码应用测评差距分析内容报告生成,执行报告格式校验,R,报告校验规则,格式规范、字段完整性、合规性要求,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,请求生成差距分析报告,密码应用测评差距分析内容报告生成,返回报告生成结果,X,报告生成结果,报告文件路径、生成状态、校验结果摘要,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,请求导出差距分析报告,密码应用测评差距分析内容报告导出,输入导出参数,E,导出参数,导出格式、包含字段、文件命名规则,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,请求导出差距分析报告,密码应用测评差距分析内容报告导出,读取已生成报告文件,R,已生成报告文件,报告内容、元数据、生成时间、版本号,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,请求导出差距分析报告,密码应用测评差距分析内容报告导出,执行文件导出操作,W,导出文件,文件内容、文件名、文件格式、下载链接,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,请求导出差距分析报告,密码应用测评差距分析内容报告导出,生成下载链接,X,下载链接信息,临时URL、有效期限、文件大小,1
密码应用测评管理,密码应用方案管理,密码应用测评差距管理,导出下载密码应用测评过程中当前的差距分析报告文件,4,发起者：用户，接收者：密码服务管理平台,请求导出差距分析报告,密码应用测评差距分析内容报告导出,记录导出操作日志,W,导出操作日志,操作用户ID、导出时间、文件名、导出格式,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,分页查询密评机构,密评机构分页列表查询,输入分页查询条件,E,密评机构分页查询请求,页码、每页数量、机构名称、地址、联系人姓名,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,分页查询密评机构,密评机构分页列表查询,读取密评机构基础信息,R,密评机构基础信息,机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,分页查询密评机构,密评机构分页列表查询,生成分页查询结果,X,密评机构分页列表,机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱、总记录数,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,分页查询密评机构,密评机构分页列表查询,记录查询操作日志,W,密评机构查询日志,操作员ID、查询时间、查询条件、结果数量,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,新增密评机构,新增密评机构,输入密评机构新增信息,E,密评机构新增信息,机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,新增密评机构,新增密评机构,校验机构名称唯一性,R,密评机构名称校验,机构名称、主键ID,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,新增密评机构,新增密评机构,保存密评机构信息,W,密评机构基础信息,机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,新增密评机构,新增密评机构,返回新增结果,X,密评机构新增结果,机构ID、机构名称、操作结果,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,新增密评机构,新增密评机构,记录新增操作日志,W,密评机构操作日志,操作员ID、操作时间、操作类型、机构名称,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,编辑密评机构,编辑密评机构,输入密评机构修改信息,E,密评机构修改信息,机构ID、机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,编辑密评机构,编辑密评机构,读取原始机构信息,R,密评机构基础信息,机构ID、机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,编辑密评机构,编辑密评机构,更新密评机构信息,W,密评机构基础信息,机构ID、机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,编辑密评机构,编辑密评机构,返回修改结果,X,密评机构修改结果,机构ID、机构名称、操作结果,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,编辑密评机构,编辑密评机构,记录修改操作日志,W,密评机构操作日志,操作员ID、操作时间、操作类型、机构名称,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,删除密评机构,删除密评机构,输入密评机构删除请求,E,密评机构删除请求,机构ID,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,删除密评机构,删除密评机构,校验删除权限,R,密评机构权限信息,机构ID、操作员权限等级,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,删除密评机构,删除密评机构,删除密评机构信息,W,密评机构基础信息,机构ID,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,删除密评机构,删除密评机构,返回删除结果,X,密评机构删除结果,机构ID、操作结果,1
密码应用测评管理,密评机构管理,密评机构管理,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4,发起者：用户，接收者：密码服务平台,删除密评机构,删除密评机构,记录删除操作日志,W,密评机构操作日志,操作员ID、操作时间、操作类型、机构ID,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,分页查询请求,密码漏洞/安全事件类型分页列表展示,输入分页查询条件,E,安全漏洞事件类型查询条件,页码、每页数量、过滤条件（事件类型名称、状态等）,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,分页查询请求,密码漏洞/安全事件类型分页列表展示,读取安全漏洞事件类型数据,R,安全漏洞事件类型列表,安全漏洞事件ID、事件类型名称、描述、状态、创建时间,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,分页查询请求,密码漏洞/安全事件类型分页列表展示,返回分页查询结果,X,安全漏洞事件类型分页结果,总记录数、当前页数据（事件类型ID、名称、描述、状态）,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,新增事件类型,新增密码漏洞/安全事件类型,输入新增事件类型信息,E,安全漏洞事件类型新增信息,事件类型名称、描述、状态、关联漏洞类型,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,新增事件类型,新增密码漏洞/安全事件类型,校验事件类型唯一性,R,安全漏洞事件类型校验信息,事件类型名称、状态,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,新增事件类型,新增密码漏洞/安全事件类型,保存新增事件类型,W,安全漏洞事件类型新增记录,事件类型ID、名称、描述、状态、创建时间,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,新增事件类型,新增密码漏洞/安全事件类型,返回新增结果,X,安全漏洞事件类型新增结果,事件类型ID、名称、操作结果,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,编辑事件类型,编辑密码漏洞/安全事件类型,输入编辑事件类型信息,E,安全漏洞事件类型编辑信息,事件类型ID、名称、描述、状态,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,编辑事件类型,编辑密码漏洞/安全事件类型,读取原始事件类型数据,R,安全漏洞事件类型原始数据,事件类型ID、名称、描述、状态,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,编辑事件类型,编辑密码漏洞/安全事件类型,更新事件类型信息,W,安全漏洞事件类型更新记录,事件类型ID、名称、描述、状态、更新时间,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,编辑事件类型,编辑密码漏洞/安全事件类型,返回编辑结果,X,安全漏洞事件类型编辑结果,事件类型ID、名称、操作结果,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,删除事件类型,删除密码漏洞/安全事件类型,输入删除事件类型请求,E,安全漏洞事件类型删除请求,事件类型ID、删除原因,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,删除事件类型,删除密码漏洞/安全事件类型,读取待删除事件类型数据,R,安全漏洞事件类型删除数据,事件类型ID、名称、状态,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,删除事件类型,删除密码漏洞/安全事件类型,执行删除操作,W,安全漏洞事件类型删除记录,事件类型ID、删除时间、操作员ID,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,删除事件类型,删除密码漏洞/安全事件类型,返回删除结果,X,安全漏洞事件类型删除结果,事件类型ID、删除状态,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：系统，接收者：密码应用漏洞/安全事件管理系统,系统初始化,初始化密码漏洞/安全事件类型,读取默认配置文件,R,安全漏洞事件类型初始化配置,预设事件类型列表、初始状态,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：系统，接收者：密码应用漏洞/安全事件管理系统,系统初始化,初始化密码漏洞/安全事件类型,校验初始化数据唯一性,R,安全漏洞事件类型校验信息,事件类型名称、状态,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：系统，接收者：密码应用漏洞/安全事件管理系统,系统初始化,初始化密码漏洞/安全事件类型,批量写入初始化数据,W,安全漏洞事件类型初始化记录,事件类型ID、名称、描述、状态、初始化时间,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：系统，接收者：密码应用漏洞/安全事件管理系统,系统初始化,初始化密码漏洞/安全事件类型,记录初始化日志,W,安全漏洞事件类型初始化日志,初始化时间、操作类型、操作结果,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型管理,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5,发起者：系统，接收者：密码应用漏洞/安全事件管理系统,系统初始化,初始化密码漏洞/安全事件类型,返回初始化结果,X,安全漏洞事件类型初始化结果,成功记录数、失败记录数、详细日志,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,分页查询漏洞/安全事件告警,漏洞/安全事件告警分页列表展示,输入漏洞/安全事件分页查询条件,E,漏洞/安全事件分页查询条件,页码、每页数量、租户ID、区域ID、告警状态、告警类型,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,分页查询漏洞/安全事件告警,漏洞/安全事件告警分页列表展示,读取漏洞/安全事件告警数据,R,漏洞/安全事件告警数据,ID、租户ID、区域ID、告警标识、告警源ID、告警状态、创建时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,分页查询漏洞/安全事件告警,漏洞/安全事件告警分页列表展示,处理分页逻辑并组装结果,X,漏洞/安全事件分页结果,总记录数、当前页数据、页码、每页数量,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,分页查询漏洞/安全事件告警,漏洞/安全事件告警分页列表展示,记录漏洞/安全事件查询日志,W,漏洞/安全事件查询日志,操作员ID、查询条件、查询时间、查询结果数量,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,新增漏洞/安全事件告警,新增漏洞/安全事件告警,输入漏洞/安全事件新增信息,E,漏洞/安全事件新增信息,告警标识、告警名称、告警类型、告警描述、告警级别,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,新增漏洞/安全事件告警,新增漏洞/安全事件告警,校验漏洞/安全事件唯一性,R,漏洞/安全事件校验信息,告警标识、告警名称、告警类型,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,新增漏洞/安全事件告警,新增漏洞/安全事件告警,保存漏洞/安全事件告警配置,W,漏洞/安全事件告警配置,ID、告警标识、告警名称、告警类型、告警描述、告警级别、创建时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,新增漏洞/安全事件告警,新增漏洞/安全事件告警,返回漏洞/安全事件新增结果,X,漏洞/安全事件新增结果,ID、告警标识、告警名称、操作结果,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,新增漏洞/安全事件告警,新增漏洞/安全事件告警,记录漏洞/安全事件新增日志,W,漏洞/安全事件新增日志,操作员ID、告警标识、告警名称、操作时间、操作结果,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,启用漏洞/安全事件告警,漏洞/安全事件告警启用,输入漏洞/安全事件启用请求,E,漏洞/安全事件启用请求,告警ID、操作员ID,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,启用漏洞/安全事件告警,漏洞/安全事件告警启用,读取漏洞/安全事件当前状态,R,漏洞/安全事件状态信息,ID、告警状态,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,启用漏洞/安全事件告警,漏洞/安全事件告警启用,更新漏洞/安全事件为启用状态,W,漏洞/安全事件状态更新,ID、告警状态、更新时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,启用漏洞/安全事件告警,漏洞/安全事件告警启用,返回漏洞/安全事件启用结果,X,漏洞/安全事件启用结果,ID、告警状态、操作结果,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,禁用漏洞/安全事件告警,漏洞/安全事件告警禁用,输入漏洞/安全事件禁用请求,E,漏洞/安全事件禁用请求,告警ID、操作员ID,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,禁用漏洞/安全事件告警,漏洞/安全事件告警禁用,读取漏洞/安全事件当前状态,R,漏洞/安全事件状态信息,ID、告警状态,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,禁用漏洞/安全事件告警,漏洞/安全事件告警禁用,更新漏洞/安全事件为禁用状态,W,漏洞/安全事件状态更新,ID、告警状态、更新时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,禁用漏洞/安全事件告警,漏洞/安全事件告警禁用,返回漏洞/安全事件禁用结果,X,漏洞/安全事件禁用结果,ID、告警状态、操作结果,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,删除漏洞/安全事件告警,删除告警漏洞/安全事件,输入漏洞/安全事件删除请求,E,漏洞/安全事件删除请求,告警ID、操作员ID,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,删除漏洞/安全事件告警,删除告警漏洞/安全事件,校验漏洞/安全事件删除权限,R,漏洞/安全事件权限信息,ID、操作员权限,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,删除漏洞/安全事件告警,删除告警漏洞/安全事件,删除漏洞/安全事件告警配置,W,漏洞/安全事件删除记录,ID、删除时间、操作员ID,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,删除漏洞/安全事件告警,删除告警漏洞/安全事件,返回漏洞/安全事件删除结果,X,漏洞/安全事件删除结果,ID、操作结果,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件管理,删除平台监控的漏洞/安全事件,4,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,删除漏洞/安全事件告警,删除告警漏洞/安全事件,记录漏洞/安全事件删除日志,W,漏洞/安全事件删除日志,操作员ID、告警ID、删除时间、操作结果,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,查询漏洞/安全事件告警通知人列表,漏洞/安全事件告警通知人列表展示,输入告警通知人查询条件,E,告警通知人查询条件,查询条件、分页参数,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,查询漏洞/安全事件告警通知人列表,漏洞/安全事件告警通知人列表展示,读取告警通知人列表数据,R,告警通知人列表,通知人ID、姓名、联系方式、通知方式、绑定状态,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,查询漏洞/安全事件告警通知人列表,漏洞/安全事件告警通知人列表展示,返回告警通知人列表展示信息,X,告警通知人列表展示信息,通知人ID、姓名、联系方式、通知方式、绑定状态、分页信息,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,查询漏洞/安全事件告警通知人列表,漏洞/安全事件告警通知人列表展示,保存告警通知人查询记录,W,告警通知人查询记录,操作人ID、查询时间、查询条件,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,绑定漏洞/安全事件告警通知人,绑定漏洞/安全事件告警通知人,输入告警通知人绑定信息,E,告警通知人绑定信息,漏洞/安全事件ID、通知人ID列表,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,绑定漏洞/安全事件告警通知人,绑定漏洞/安全事件告警通知人,读取漏洞/安全事件关联通知人,R,漏洞/安全事件关联通知人,漏洞/安全事件ID、当前绑定通知人ID,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,绑定漏洞/安全事件告警通知人,绑定漏洞/安全事件告警通知人,保存告警通知人绑定关系,W,告警通知人绑定关系,漏洞/安全事件ID、通知人ID、绑定时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,绑定漏洞/安全事件告警通知人,绑定漏洞/安全事件告警通知人,返回告警通知人绑定结果,X,告警通知人绑定结果,绑定状态、绑定通知人ID列表,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,绑定漏洞/安全事件告警通知人,绑定漏洞/安全事件告警通知人,记录告警通知人绑定日志,W,告警通知人绑定日志,操作人ID、绑定时间、漏洞/安全事件ID、绑定通知人ID列表,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,新增漏洞/安全事件告警通知人,新增漏洞/安全事件告警通知人,输入告警通知人新增信息,E,告警通知人新增信息,姓名、联系方式、通知方式、备注,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,新增漏洞/安全事件告警通知人,新增漏洞/安全事件告警通知人,校验告警通知人唯一性,R,告警通知人校验信息,姓名、联系方式,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,新增漏洞/安全事件告警通知人,新增漏洞/安全事件告警通知人,保存告警通知人新增数据,W,告警通知人新增数据,通知人ID、姓名、联系方式、通知方式、创建时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,新增漏洞/安全事件告警通知人,新增漏洞/安全事件告警通知人,返回告警通知人新增结果,X,告警通知人新增结果,通知人ID、姓名、新增状态,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,新增漏洞/安全事件告警通知人,新增漏洞/安全事件告警通知人,记录告警通知人新增日志,W,告警通知人新增日志,操作人ID、新增时间、通知人ID、姓名,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,删除漏洞/安全事件告警通知人,删除漏洞/安全事件告警通知人,输入告警通知人删除请求,E,告警通知人删除请求,通知人ID、漏洞/安全事件ID,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,删除漏洞/安全事件告警通知人,删除漏洞/安全事件告警通知人,校验告警通知人删除权限,R,告警通知人删除权限,操作人ID、通知人ID、漏洞/安全事件ID,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,删除漏洞/安全事件告警通知人,删除漏洞/安全事件告警通知人,执行告警通知人删除操作,W,告警通知人删除数据,通知人ID、漏洞/安全事件ID、删除时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,删除漏洞/安全事件告警通知人,删除漏洞/安全事件告警通知人,返回告警通知人删除结果,X,告警通知人删除结果,删除状态、通知人ID,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,漏洞/安全事件通知人管理,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4,发起者：用户，接收者：密码服务平台,删除漏洞/安全事件告警通知人,删除漏洞/安全事件告警通知人,记录告警通知人删除日志,W,告警通知人删除日志,操作人ID、删除时间、通知人ID、漏洞/安全事件ID,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,提交邮箱服务器配置,告警邮箱服务器配置提交,输入告警邮箱服务器配置信息,E,告警邮箱配置信息,SMTP服务器地址、端口、用户名、密码、加密方式、发件人邮箱,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,提交邮箱服务器配置,告警邮箱服务器配置提交,校验邮箱配置格式有效性,R,邮箱配置校验规则,SMTP地址格式、端口范围、加密方式类型,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,提交邮箱服务器配置,告警邮箱服务器配置提交,保存邮箱配置到数据库,W,告警邮箱配置信息,SMTP服务器地址、端口、用户名、密码、加密方式、发件人邮箱,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,提交邮箱服务器配置,告警邮箱服务器配置提交,返回配置提交结果,X,告警邮箱配置提交结果,配置状态、错误信息、提交时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,查询邮箱服务器配置,告警邮箱服务器配置查询,输入邮箱配置查询条件,E,邮箱配置查询请求,查询字段、过滤条件,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,查询邮箱服务器配置,告警邮箱服务器配置查询,读取邮箱配置数据,R,告警邮箱配置信息,SMTP服务器地址、端口、用户名、加密方式、发件人邮箱,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,查询邮箱服务器配置,告警邮箱服务器配置查询,返回邮箱配置查询结果,X,告警邮箱配置查询结果,SMTP服务器地址、端口、加密方式、配置时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,重置邮箱服务器配置,告警邮箱服务器配置重置,发起邮箱配置重置请求,E,邮箱配置重置请求,重置标识、操作员ID,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,重置邮箱服务器配置,告警邮箱服务器配置重置,读取当前邮箱配置,R,告警邮箱配置信息,SMTP服务器地址、端口、用户名,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,重置邮箱服务器配置,告警邮箱服务器配置重置,清空邮箱配置数据,W,告警邮箱配置信息,SMTP服务器地址、端口、用户名、密码,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,发送验证邮件,告警验证邮件发送,输入邮件发送测试请求,E,邮件发送测试请求,测试邮件接收地址、测试标识,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,发送验证邮件,告警验证邮件发送,读取当前邮箱配置,R,告警邮箱配置信息,SMTP服务器地址、端口、用户名、密码,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,发送验证邮件,告警验证邮件发送,生成测试邮件内容,运算,测试邮件内容,邮件主题、正文、发送时间,0
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,发送验证邮件,告警验证邮件发送,执行邮件发送操作,X,邮件发送请求,SMTP服务器地址、端口、用户名、密码、邮件内容,1
密码应用漏洞/安全事件管理,漏洞/安全事件级别管理,告警邮箱配置管理,测试配置的邮箱服务器是否正常，发送测试邮件,5,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,发送验证邮件,告警验证邮件发送,记录邮件发送结果,W,邮件发送日志,发送状态、错误信息、发送时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,请求展示漏洞/安全事件基本信息,漏洞/安全事件基本信息展示,输入漏洞/安全事件查询条件,E,漏洞/安全事件查询条件,漏洞/安全事件ID、查询时间范围,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,请求展示漏洞/安全事件基本信息,漏洞/安全事件基本信息展示,读取漏洞/安全事件基本信息,R,漏洞/安全事件基本信息,漏洞/安全事件ID、名称、描述、发现时间、严重等级、关联资产,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,请求展示漏洞/安全事件基本信息,漏洞/安全事件基本信息展示,输出漏洞/安全事件展示信息,X,漏洞/安全事件展示信息,漏洞/安全事件ID、名称、描述、发现时间、严重等级、关联资产、展示状态,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,提交漏洞/安全事件信息修改,漏洞/安全事件信息编辑,输入漏洞/安全事件修改信息,E,漏洞/安全事件修改信息,漏洞/安全事件ID、修改字段、新值,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,提交漏洞/安全事件信息修改,漏洞/安全事件信息编辑,读取漏洞/安全事件原始信息,R,漏洞/安全事件原始信息,漏洞/安全事件ID、名称、描述、发现时间、严重等级、关联资产,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,提交漏洞/安全事件信息修改,漏洞/安全事件信息编辑,更新漏洞/安全事件信息,W,漏洞/安全事件更新信息,漏洞/安全事件ID、修改字段、新值、修改时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,提交漏洞/安全事件信息修改,漏洞/安全事件信息编辑,输出漏洞/安全事件修改结果,X,漏洞/安全事件修改结果,漏洞/安全事件ID、修改字段、新值、操作结果,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,配置漏洞/安全事件告警阈值,漏洞/安全事件告警阈值配置,输入告警阈值配置信息,E,告警阈值配置信息,漏洞/安全事件ID、阈值类型、阈值数值、触发条件,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,配置漏洞/安全事件告警阈值,漏洞/安全事件告警阈值配置,校验告警阈值有效性,R,告警阈值校验规则,阈值类型、数值范围、触发条件格式,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,配置漏洞/安全事件告警阈值,漏洞/安全事件告警阈值配置,保存告警阈值配置,W,告警阈值配置记录,漏洞/安全事件ID、阈值类型、阈值数值、触发条件、配置时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,配置漏洞/安全事件告警阈值,漏洞/安全事件告警阈值配置,输出告警阈值配置结果,X,告警阈值配置结果,漏洞/安全事件ID、阈值类型、阈值数值、配置状态,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,配置漏洞/安全事件告警阈值,漏洞/安全事件告警阈值配置,记录告警阈值配置日志,W,告警阈值配置日志,操作员ID、漏洞/安全事件ID、阈值类型、配置内容、操作时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,设置漏洞/安全事件告警标签,漏洞/安全事件标签配置,输入告警标签配置信息,E,告警标签配置信息,漏洞/安全事件ID、标签类型、标签值,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,设置漏洞/安全事件告警标签,漏洞/安全事件标签配置,校验告警标签有效性,R,告警标签校验规则,标签类型、标签值格式,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,设置漏洞/安全事件告警标签,漏洞/安全事件标签配置,保存告警标签配置,W,告警标签配置记录,漏洞/安全事件ID、标签类型、标签值、配置时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,设置漏洞/安全事件告警标签,漏洞/安全事件标签配置,输出告警标签配置结果,X,告警标签配置结果,漏洞/安全事件ID、标签类型、标签值、配置状态,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,配置漏洞/安全事件组合告警阈值,漏洞/安全事件告警组合阈值配置,输入组合告警阈值配置信息,E,组合告警阈值配置信息,漏洞/安全事件ID、组合逻辑、子阈值列表,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,配置漏洞/安全事件组合告警阈值,漏洞/安全事件告警组合阈值配置,校验组合逻辑有效性,R,组合逻辑校验规则,逻辑运算符、子阈值关联性,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,配置漏洞/安全事件组合告警阈值,漏洞/安全事件告警组合阈值配置,保存组合告警阈值配置,W,组合告警阈值配置记录,漏洞/安全事件ID、组合逻辑、子阈值列表、配置时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,配置漏洞/安全事件组合告警阈值,漏洞/安全事件告警组合阈值配置,输出组合告警阈值配置结果,X,组合告警阈值配置结果,漏洞/安全事件ID、组合逻辑、配置状态,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,配置漏洞/安全事件组合告警阈值,漏洞/安全事件告警组合阈值配置,记录组合告警阈值配置日志,W,组合告警阈值配置日志,操作员ID、漏洞/安全事件ID、组合逻辑、配置内容、操作时间,1
密码应用漏洞/安全事件管理,漏洞/安全事件详情管理,漏洞/安全事件详情管理,配置漏洞/安全事件告警阈值的组合判定方式,7,发起者：用户，接收者：密码应用漏洞/安全事件管理系统,配置漏洞/安全事件组合告警阈值,漏洞/安全事件告警组合阈值配置,生成组合告警规则文档,W,组合告警规则文档,漏洞/安全事件ID、组合逻辑、子阈值描述、生成时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看密码产品监控列表,密码产品监控列表分布展示,输入密码产品监控列表查询条件,E,密码产品列表查询条件,产品名称、管理IP、状态,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看密码产品监控列表,密码产品监控列表分布展示,读取密码产品监控列表数据,R,密码产品列表信息,ID、产品名称、管理IP、产品类型、状态、创建时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看密码产品监控列表,密码产品监控列表分布展示,输出密码产品监控列表展示数据,X,密码产品列表展示信息,产品名称、管理IP、产品类型、状态、操作按钮,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看当前监控数据,密码产品当前监控数据列表展示,输入当前监控数据查询条件,E,当前监控数据查询条件,产品ID、查询时间范围,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看当前监控数据,密码产品当前监控数据列表展示,读取实时监控数据,R,密码产品当前监控数据,CPU使用率、内存使用率、磁盘使用率、采集时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看当前监控数据,密码产品当前监控数据列表展示,处理实时监控数据格式,W,密码产品当前监控数据,CPU使用率、内存使用率、磁盘使用率、采集时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看当前监控数据,密码产品当前监控数据列表展示,输出当前监控数据展示列表,X,密码产品当前监控数据展示,产品名称、CPU使用率、内存使用率、磁盘使用率、采集时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看历史监控数据,密码产品监控历史数据列表展示,输入历史监控数据查询条件,E,历史监控数据查询条件,产品ID、开始时间、结束时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看历史监控数据,密码产品监控历史数据列表展示,读取历史监控数据,R,密码产品历史监控数据,CPU使用率、内存使用率、磁盘使用率、采集时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看历史监控数据,密码产品监控历史数据列表展示,处理历史监控数据格式,W,密码产品历史监控数据,CPU使用率、内存使用率、磁盘使用率、采集时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看历史监控数据,密码产品监控历史数据列表展示,输出历史监控数据展示列表,X,密码产品历史监控数据展示,产品名称、CPU使用率、内存使用率、磁盘使用率、采集时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看历史监控数据,密码产品监控历史数据列表展示,保存历史监控数据查询记录,W,监控数据查询记录,产品ID、查询时间、查询用户,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看当前监控折线图,密码产品当前监控数据折线图,输入折线图生成参数,E,折线图生成参数,产品ID、时间粒度（分钟/小时）,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看当前监控折线图,密码产品当前监控数据折线图,读取实时监控数据,R,密码产品当前监控数据,CPU使用率、内存使用率、磁盘使用率、采集时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看当前监控折线图,密码产品当前监控数据折线图,生成折线图数据格式,W,密码产品当前监控折线图数据,时间序列、CPU曲线、内存曲线、磁盘曲线,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看当前监控折线图,密码产品当前监控数据折线图,输出折线图展示数据,X,密码产品当前监控折线图展示,产品名称、折线图数据、时间范围,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看历史监控折线图,密码产品监控历史数据折线图,输入历史折线图生成参数,E,历史折线图生成参数,产品ID、开始时间、结束时间、时间粒度,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看历史监控折线图,密码产品监控历史数据折线图,读取历史监控数据,R,密码产品历史监控数据,CPU使用率、内存使用率、磁盘使用率、采集时间,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看历史监控折线图,密码产品监控历史数据折线图,生成历史折线图数据格式,W,密码产品历史监控折线图数据,时间序列、CPU曲线、内存曲线、磁盘曲线,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看历史监控折线图,密码产品监控历史数据折线图,输出历史折线图展示数据,X,密码产品历史监控折线图展示,产品名称、折线图数据、时间范围,1
密码应用漏洞/安全事件管理,密码产品监控范围管理,密码产品监控范围管理,以折线图信息显示监控的密码产品对象的监控历史数据,5,发起者：用户，接收者：密码服务平台,请求查看历史监控折线图,密码产品监控历史数据折线图,保存历史折线图查询记录,W,监控折线图查询记录,产品ID、查询时间、查询用户,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：用户，接收者：密码服务管理平台,密码应用数据采集请求,密码应用上报数据采集,输入密码应用采集条件,E,密码应用采集条件,业务系统ID、业务系统名称、密码业务类型,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：用户，接收者：密码服务管理平台,密码应用数据采集请求,密码应用上报数据采集,查询密码应用数据,R,密码应用信息,业务系统ID、业务系统名称、密码业务类型、密码产品类型,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：用户，接收者：密码服务管理平台,密码应用数据采集请求,密码应用上报数据采集,验证密码应用数据完整性,R,密码应用校验规则,必填字段校验规则、格式校验规则,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：用户，接收者：密码服务管理平台,密码应用数据采集请求,密码应用上报数据采集,保存密码应用采集结果,W,密码应用采集数据,业务系统ID、采集时间、采集状态,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：用户，接收者：密码服务管理平台,密码应用数据采集请求,密码应用上报数据采集,返回密码应用采集结果,X,密码应用采集结果,采集成功数量、失败数量、异常信息,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统管理员，接收者：密码服务管理平台,接口配置提交,密码应用数据上报接口对接,输入接口配置参数,E,接口配置信息,接口URL、认证方式、数据格式,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统管理员，接收者：密码服务管理平台,接口配置提交,密码应用数据上报接口对接,验证接口配置有效性,R,接口校验规则,URL格式校验、认证方式校验,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统管理员，接收者：密码服务管理平台,接口配置提交,密码应用数据上报接口对接,保存接口配置信息,W,接口配置信息,接口URL、认证方式、数据格式,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统管理员，接收者：密码服务管理平台,接口配置提交,密码应用数据上报接口对接,执行接口连接测试,X,接口测试请求,测试数据包、认证凭证,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统管理员，接收者：密码服务管理平台,接口配置提交,密码应用数据上报接口对接,返回接口测试结果,R,接口测试结果,响应状态码、响应时间、错误信息,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：用户，接收者：密码服务管理平台,密码应用列表查询,密码应用上报数据列表展示,输入列表查询条件,E,密码应用列表查询条件,分页参数、排序字段、过滤条件,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：用户，接收者：密码服务管理平台,密码应用列表查询,密码应用上报数据列表展示,读取密码应用数据,R,密码应用信息,业务系统ID、业务系统名称、密码业务类型,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：用户，接收者：密码服务管理平台,密码应用列表查询,密码应用上报数据列表展示,处理分页逻辑,R,分页配置,每页记录数、当前页码,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：用户，接收者：密码服务管理平台,密码应用列表查询,密码应用上报数据列表展示,返回密码应用列表数据,X,密码应用列表数据,业务系统ID、业务系统名称、密码业务类型、上报状态,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统，接收者：密码服务管理平台,定时任务触发,密码应用数据定时上报更新,读取上报配置信息,R,上报配置信息,上报频率、上次上报时间,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统，接收者：密码服务管理平台,定时任务触发,密码应用数据定时上报更新,触发定时上报任务,R,定时任务配置,任务名称、执行时间,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统，接收者：密码服务管理平台,定时任务触发,密码应用数据定时上报更新,处理待上报数据,R,待上报数据,业务系统ID、更新时间、数据变更标记,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统，接收者：密码服务管理平台,定时任务触发,密码应用数据定时上报更新,发送上报数据到集团平台,X,上报数据包,业务系统ID、密码业务类型、上报时间,1
数据上报接口,密码应用数据上报类接口,密码应用数据上报管理,根据配置的上报频率，定时上报更新密码应用数据,4,发起者：系统，接收者：密码服务管理平台,定时任务触发,密码应用数据定时上报更新,记录上报日志,W,上报日志,上报时间、上报结果、异常信息,1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：用户，接收者：密码产品管理系统,密码产品信息采集,密码产品信息上报数据采集,输入密码产品基础信息,E,密码产品基础信息,"product_id, product_name, product_type, service_type, data_type",1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：用户，接收者：密码产品管理系统,密码产品信息采集,密码产品信息上报数据采集,校验密码产品唯一性,R,密码产品校验信息,"product_id, product_name, service_type",1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：用户，接收者：密码产品管理系统,密码产品信息采集,密码产品信息上报数据采集,保存密码产品信息,W,密码产品存储信息,"product_id, product_name, product_type, service_type, data_type, create_time",1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：用户，接收者：密码产品管理系统,密码产品信息采集,密码产品信息上报数据采集,返回采集结果,X,密码产品采集反馈,"product_id, product_name, status, message",1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：密码产品管理系统，接收者：集团平台,接口调用,密码产品信息上报接口对接,读取接口配置参数,R,接口配置信息,"api_url, auth_token, retry_count, timeout",1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：密码产品管理系统，接收者：集团平台,接口调用,密码产品信息上报接口对接,构建上报数据包,E,密码产品上报数据,"product_id, product_name, product_type, service_type, data_type, attachment_ids",1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：密码产品管理系统，接收者：集团平台,接口调用,密码产品信息上报接口对接,发送数据到集团平台,X,密码产品上报请求,"data_package, signature, timestamp",1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：密码产品管理系统，接收者：集团平台,接口调用,密码产品信息上报接口对接,接收接口响应,R,接口响应信息,"status_code, response_data, error_message",1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：密码产品管理系统，接收者：集团平台,接口调用,密码产品信息上报接口对接,记录接口日志,W,接口操作日志,"request_time, response_time, status, data_size",1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：用户，接收者：密码产品管理系统,列表查询,密码产品信息上报数据列表展示,接收分页查询请求,E,分页查询条件,"page_num, page_size, filter_type, status",1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：用户，接收者：密码产品管理系统,列表查询,密码产品信息上报数据列表展示,读取密码产品数据,R,密码产品列表数据,"product_id, product_name, product_type, service_type, data_type, create_time",1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：用户，接收者：密码产品管理系统,列表查询,密码产品信息上报数据列表展示,返回分页结果,X,分页展示数据,"total_count, current_page, items, page_size",1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：定时任务调度器，接收者：密码产品管理系统,定时触发,密码产品信息数据定时上报更新,读取定时配置,R,定时任务配置,"cron_expression, enable_flag, last_run_time",1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：定时任务调度器，接收者：密码产品管理系统,定时触发,密码产品信息数据定时上报更新,获取待上报数据,R,待上报密码产品,"product_id, product_name, update_time, status",1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：定时任务调度器，接收者：密码产品管理系统,定时触发,密码产品信息数据定时上报更新,执行批量上报,X,批量上报数据,"data_list, batch_id, timestamp",1
数据上报接口,密码资产数据上报类接口,密码产品信息上报,根据配置的上报频率，定时上报更新密码产品信息数据,4,发起者：定时任务调度器，接收者：密码产品管理系统,定时触发,密码产品信息数据定时上报更新,更新上报状态,W,上报状态记录,"product_id, last_report_time, report_status, error_info",1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：密码服务平台，接收者：集省一体服务,密钥信息采集请求,密钥信息上报数据采集,接收密钥信息采集请求,E,密钥信息采集请求,采集时间、采集范围、采集频率,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：密码服务平台，接收者：集省一体服务,密钥信息采集请求,密钥信息上报数据采集,读取密钥信息配置参数,R,上报配置信息,"report_code, report_name, frequency_id",1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：密码服务平台，接收者：集省一体服务,密钥信息采集请求,密钥信息上报数据采集,从密码服务平台拉取密钥数据,R,密钥信息,"key_id, key_code, province_code, key_status",1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：密码服务平台，接收者：集省一体服务,密钥信息采集请求,密钥信息上报数据采集,生成密钥信息采集结果,W,密钥信息采集结果,"key_id, key_code, province_code, key_status, 采集时间",1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：密码服务平台，接收者：集省一体服务,密钥信息采集请求,密钥信息上报数据采集,返回密钥信息采集状态,X,密钥信息采集状态,采集结果、异常信息、采集时间,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：集省一体服务，接收者：集团平台,密钥信息上报接口调用,密钥信息上报接口对接,构造密钥信息上报请求,E,密钥信息上报请求,上报时间、密钥数据、省份编码,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：集省一体服务，接收者：集团平台,密钥信息上报接口调用,密钥信息上报接口对接,验证接口连接状态,R,接口状态信息,接口地址、认证令牌、连接状态,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：集省一体服务，接收者：集团平台,密钥信息上报接口调用,密钥信息上报接口对接,发送密钥信息到集团平台,X,密钥信息上报数据,"key_id, key_code, province_code, key_status",1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：集省一体服务，接收者：集团平台,密钥信息上报接口调用,密钥信息上报接口对接,接收集团平台响应结果,R,接口响应信息,响应状态码、错误信息、处理时间,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：集省一体服务，接收者：集团平台,密钥信息上报接口调用,密钥信息上报接口对接,记录接口调用日志,W,接口调用日志,请求参数、响应结果、调用时间,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：集省一体服务，接收者：集团平台,密钥信息上报接口调用,密钥信息上报接口对接,生成接口调用报告,W,接口调用报告,成功/失败状态、处理耗时、异常详情,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：用户，接收者：集省一体服务,密钥信息列表查询,密钥信息上报数据列表展示,输入密钥信息查询条件,E,密钥信息查询请求,查询时间范围、省份编码、密钥状态,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：用户，接收者：集省一体服务,密钥信息列表查询,密钥信息上报数据列表展示,读取密钥信息数据,R,密钥信息列表,"key_id, key_code, province_code, key_status, 上报时间",1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：用户，接收者：集省一体服务,密钥信息列表查询,密钥信息上报数据列表展示,生成分页展示数据,W,密钥信息分页数据,当前页码、每页数量、总记录数,1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：用户，接收者：集省一体服务,密钥信息列表查询,密钥信息上报数据列表展示,返回密钥信息展示列表,X,密钥信息展示列表,"key_id, key_code, province_code, key_status, 上报时间",1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：定时任务，接收者：集省一体服务,定时任务触发,密钥信息数据定时上报更新,读取定时任务配置,R,定时任务配置,"frequency_id, 上报频率、上次执行时间",1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：定时任务，接收者：集省一体服务,定时任务触发,密钥信息数据定时上报更新,获取待更新密钥信息,R,密钥更新数据,"key_id, key_code, province_code, key_status, 更新时间",1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：定时任务，接收者：集省一体服务,定时任务触发,密钥信息数据定时上报更新,生成密钥信息更新包,W,密钥信息更新包,"key_id, key_code, province_code, key_status, 更新时间",1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：定时任务，接收者：集省一体服务,定时任务触发,密钥信息数据定时上报更新,执行密钥信息定时上报,X,密钥信息定时上报数据,"key_id, key_code, province_code, key_status, 上报时间",1
数据上报接口,密码资产数据上报类接口,密钥信息上报,根据配置的上报频率，定时上报更新密钥信息信息数据,4,发起者：定时任务，接收者：集省一体服务,定时任务触发,密钥信息数据定时上报更新,记录定时上报日志,W,定时上报日志,执行时间、上报数量、异常信息,1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：用户，接收者：密码服务平台,证书信息采集,证书信息上报数据采集,输入证书信息采集参数,E,证书信息采集参数,证书ID、证书名称、证书内容、省份编码,1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：用户，接收者：密码服务平台,证书信息采集,证书信息上报数据采集,读取本地证书信息,R,证书信息,证书ID、证书名称、证书内容、省份编码、证书状态,1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：用户，接收者：密码服务平台,证书信息采集,证书信息上报数据采集,校验证书信息完整性,R,证书校验规则,必填字段校验规则、格式校验规则,1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：用户，接收者：密码服务平台,证书信息采集,证书信息上报数据采集,生成标准化证书上报数据,W,证书上报数据,证书ID、证书名称、证书内容、省份编码、上报时间,1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：密码服务平台，接收者：集团平台,证书信息上报,证书信息上报接口对接,接收集团平台接口请求,E,接口请求参数,接口版本、认证令牌、请求时间戳,1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：密码服务平台，接收者：集团平台,证书信息上报,证书信息上报接口对接,验证接口请求合法性,R,接口鉴权信息,认证令牌有效期、IP白名单、接口权限,1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：密码服务平台，接收者：集团平台,证书信息上报,证书信息上报接口对接,构建证书上报数据包,R,证书上报数据,证书ID、证书名称、证书内容、省份编码、上报时间,1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：密码服务平台，接收者：集团平台,证书信息上报,证书信息上报接口对接,调用集团平台上报接口,X,证书上报请求,证书ID、证书名称、证书内容、省份编码、上报时间,1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：密码服务平台，接收者：集团平台,证书信息上报,证书信息上报接口对接,接收集团平台响应结果,E,接口响应数据,响应状态码、错误信息、响应时间,1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：密码服务平台，接收者：集团平台,证书信息上报,证书信息上报接口对接,记录接口调用日志,W,接口调用日志,请求时间、响应时间、状态码、操作人,1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：用户，接收者：密码服务平台,证书信息查询,证书信息上报数据列表展示,输入证书查询条件,E,证书查询请求,证书名称、省份编码、时间范围,1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：用户，接收者：密码服务平台,证书信息查询,证书信息上报数据列表展示,读取证书上报记录,R,证书上报记录,证书ID、证书名称、证书内容、省份编码、上报时间,1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：用户，接收者：密码服务平台,证书信息查询,证书信息上报数据列表展示,返回证书列表展示数据,X,证书列表展示数据,证书ID、证书名称、省份编码、上报时间、证书状态,1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：定时任务，接收者：密码服务平台,定时任务触发,证书信息数据定时上报更新,触发定时任务,E,定时任务参数,任务ID、执行时间、频率,1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：定时任务，接收者：密码服务平台,定时任务触发,证书信息数据定时上报更新,读取待更新证书信息,R,证书更新数据,证书ID、证书名称、证书内容、省份编码、更新时间,1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：定时任务，接收者：密码服务平台,定时任务触发,证书信息数据定时上报更新,生成证书更新数据包,W,证书更新数据,证书ID、证书名称、证书内容、省份编码、更新时间,1
数据上报接口,密码资产数据上报类接口,证书信息上报,根据配置的上报频率，定时上报更新证书信息信息数据,4,发起者：定时任务，接收者：密码服务平台,定时任务触发,证书信息数据定时上报更新,调用集团平台更新接口,X,证书更新请求,证书ID、证书名称、证书内容、省份编码、更新时间,1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：用户，接收者：集省一体服务,用户发起密码文档信息采集,密码文档信息上报数据采集,输入密码文档基础信息,E,密码文档信息,"document_id, document_name, province_code",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：用户，接收者：集省一体服务,用户发起密码文档信息采集,密码文档信息上报数据采集,校验文档信息完整性,R,密码文档校验规则,"非空校验字段, 格式校验规则",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：用户，接收者：集省一体服务,用户发起密码文档信息采集,密码文档信息上报数据采集,保存密码文档信息,W,密码文档信息,"document_id, document_name, province_code, create_time",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：用户，接收者：集省一体服务,用户发起密码文档信息采集,密码文档信息上报数据采集,返回采集结果,X,密码文档采集结果,"document_id, 采集状态, 错误信息",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：集省一体服务，接收者：集团平台,系统触发接口对接,密码文档信息上报接口对接,构建上报数据包,R,密码文档信息,"document_id, document_name, province_code",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：集省一体服务，接收者：集团平台,系统触发接口对接,密码文档信息上报接口对接,发送数据到集团平台,E,密码文档上报数据,"document_id, document_name, province_code, 上报时间",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：集省一体服务，接收者：集团平台,系统触发接口对接,密码文档信息上报接口对接,接收集团平台响应,R,接口响应信息,"响应状态码, 响应消息",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：集省一体服务，接收者：集团平台,系统触发接口对接,密码文档信息上报接口对接,记录接口日志,W,接口操作日志,"操作时间, 操作类型, 操作结果",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：用户，接收者：集省一体服务,用户请求查看文档列表,密码文档信息上报数据列表展示,输入查询条件,E,文档查询条件,"province_code, 查询时间范围",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：用户，接收者：集省一体服务,用户请求查看文档列表,密码文档信息上报数据列表展示,读取密码文档数据,R,密码文档信息,"document_id, document_name, province_code, create_time",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：用户，接收者：集省一体服务,用户请求查看文档列表,密码文档信息上报数据列表展示,返回分页展示数据,X,文档列表展示数据,"document_id, document_name, province_code, create_time, 分页信息",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：定时任务，接收者：集省一体服务,定时任务触发,密码文档信息数据定时上报更新,读取上报配置,R,定时任务配置,"执行频率, 上报类型",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：定时任务，接收者：集省一体服务,定时任务触发,密码文档信息数据定时上报更新,获取待上报文档数据,R,密码文档信息,"document_id, document_name, province_code, 最后修改时间",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：定时任务，接收者：集省一体服务,定时任务触发,密码文档信息数据定时上报更新,发送更新数据到集团平台,E,密码文档更新数据,"document_id, document_name, province_code, 更新时间",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：定时任务，接收者：集省一体服务,定时任务触发,密码文档信息数据定时上报更新,记录定时任务日志,W,定时任务日志,"任务名称, 执行时间, 执行结果",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：用户，接收者：集省一体服务,用户上传密码文档文件,密码文档文件上传,接收上传文件,E,上传文件信息,"文件名称, 文件大小, 文件类型",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：用户，接收者：集省一体服务,用户上传密码文档文件,密码文档文件上传,校验文件格式,R,文件校验规则,"允许文件类型, 最大文件大小",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：用户，接收者：集省一体服务,用户上传密码文档文件,密码文档文件上传,保存文件到存储系统,W,文件存储信息,"存储路径, 文件哈希值, 上传时间",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：用户，接收者：集省一体服务,用户上传密码文档文件,密码文档文件上传,关联文档与文件,W,密码文档信息,"document_id, 文件存储路径, 文件哈希值",1
数据上报接口,密码资产数据上报类接口,密码文档信息上报,上传密码文档文件,5,发起者：用户，接收者：集省一体服务,用户上传密码文档文件,密码文档文件上传,返回上传结果,X,文件上传结果,"文件名称, 上传状态, 错误信息",1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：用户，接收者：密码服务管理平台,用户发起密码应用测评数据采集,密码应用测评上报数据采集,输入密码应用测评数据采集条件,E,密码应用测评采集条件,上报时间范围、应用ID、省份编码,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：用户，接收者：密码服务管理平台,用户发起密码应用测评数据采集,密码应用测评上报数据采集,读取密码应用基础数据,R,密码应用数据,"app_id(业务系统ID), app_name(业务系统名称), app_type(业务系统类型)",1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：用户，接收者：密码服务管理平台,用户发起密码应用测评数据采集,密码应用测评上报数据采集,读取密码应用密评信息,R,密评信息,"assess_id(密评ID), province_code(省份编码), app_id(业务系统ID)",1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：用户，接收者：密码服务管理平台,用户发起密码应用测评数据采集,密码应用测评上报数据采集,整合密码应用测评数据,W,密码应用测评整合数据,"app_id, app_name, app_type, assess_id, province_code, 上报时间",1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：用户，接收者：密码服务管理平台,用户发起密码应用测评数据采集,密码应用测评上报数据采集,输出密码应用测评采集结果,X,密码应用测评采集结果,采集状态、数据条数、异常信息,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：密码服务管理平台，接收者：集团平台,系统触发密码应用测评数据上报,密码应用测评数据上报接口对接,读取接口配置参数,R,接口配置信息,接口地址、认证密钥、数据格式,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：密码服务管理平台，接收者：集团平台,系统触发密码应用测评数据上报,密码应用测评数据上报接口对接,构建密码应用测评上报数据包,W,接口请求数据,"app_id, app_name, assess_id, province_code, 上报时间",1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：密码服务管理平台，接收者：集团平台,系统触发密码应用测评数据上报,密码应用测评数据上报接口对接,发送密码应用测评数据到集团平台,X,接口请求数据,"app_id, app_name, assess_id, province_code, 上报时间",1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：密码服务管理平台，接收者：集团平台,系统触发密码应用测评数据上报,密码应用测评数据上报接口对接,接收集团平台接口响应,E,接口响应数据,响应状态码、响应消息、数据ID,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：密码服务管理平台，接收者：集团平台,系统触发密码应用测评数据上报,密码应用测评数据上报接口对接,记录接口调用日志,W,接口调用日志,调用时间、接口地址、响应状态码、操作员ID,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：用户，接收者：密码服务管理平台,用户查询密码应用测评上报数据,密码应用测评上报数据列表展示,输入密码应用测评数据查询条件,E,查询条件,时间范围、省份编码、应用名称,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：用户，接收者：密码服务管理平台,用户查询密码应用测评上报数据,密码应用测评上报数据列表展示,读取密码应用测评上报数据,R,密码应用测评数据,"app_id, app_name, assess_id, province_code, 上报时间",1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：用户，接收者：密码服务管理平台,用户查询密码应用测评上报数据,密码应用测评上报数据列表展示,格式化密码应用测评数据列表,W,格式化数据,分页信息、排序字段、列宽配置,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：用户，接收者：密码服务管理平台,用户查询密码应用测评上报数据,密码应用测评上报数据列表展示,输出密码应用测评数据列表,X,密码应用测评数据列表,"app_id, app_name, assess_id, province_code, 上报时间, 状态",1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统定时任务，接收者：密码服务管理平台,定时任务触发密码应用测评数据上报,密码应用测评数据定时上报更新,读取定时任务配置,R,定时配置,执行周期、启用状态、上次执行时间,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统定时任务，接收者：密码服务管理平台,定时任务触发密码应用测评数据上报,密码应用测评数据定时上报更新,触发密码应用测评数据采集,E,定时任务触发,触发时间、任务ID,1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统定时任务，接收者：密码服务管理平台,定时任务触发密码应用测评数据上报,密码应用测评数据定时上报更新,读取待上报密码应用测评数据,R,待上报数据,"app_id, assess_id, 上报时间, 状态",1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统定时任务，接收者：密码服务管理平台,定时任务触发密码应用测评数据上报,密码应用测评数据定时上报更新,发送密码应用测评数据到集团平台,X,接口请求数据,"app_id, assess_id, province_code, 上报时间",1
数据上报接口,密码应用测评数据上报类接口,应用测评信息上报,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4,发起者：系统定时任务，接收者：密码服务管理平台,定时任务触发密码应用测评数据上报,密码应用测评数据定时上报更新,更新密码应用测评数据上报状态,W,上报状态更新,"app_id, assess_id, 上报时间, 状态",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：用户，接收者：密码服务管理平台,提交密码应用漏洞/安全事件信息,密码应用漏洞/安全事件上报数据采集,输入安全漏洞事件基本信息,E,安全漏洞事件信息,"flaw_event_id, flaw_event_name, flaw_event_type, 发现时间, 影响范围",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：用户，接收者：密码服务管理平台,提交密码应用漏洞/安全事件信息,密码应用漏洞/安全事件上报数据采集,校验安全漏洞事件字段完整性,R,安全漏洞事件校验规则,"必填字段列表, 格式校验规则, 唯一性校验规则",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：用户，接收者：密码服务管理平台,提交密码应用漏洞/安全事件信息,密码应用漏洞/安全事件上报数据采集,存储安全漏洞事件数据,W,安全漏洞事件信息,"flaw_event_id, flaw_event_name, flaw_event_type, 发现时间, 影响范围",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：用户，接收者：密码服务管理平台,提交密码应用漏洞/安全事件信息,密码应用漏洞/安全事件上报数据采集,返回数据采集结果,X,数据采集反馈信息,"采集状态, 错误信息, 采集时间",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：密码服务管理平台，接收者：集团平台,接口调用请求,密码应用漏洞/安全事件上报接口对接,接收集团平台接口请求,E,上报接口请求数据,"report_code, report_name, 上报时间, 数据格式版本",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：密码服务管理平台，接收者：集团平台,接口调用请求,密码应用漏洞/安全事件上报接口对接,读取待上报的安全漏洞事件数据,R,安全漏洞事件信息,"flaw_event_id, flaw_event_name, flaw_event_type, 发现时间, 影响范围",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：密码服务管理平台，接收者：集团平台,接口调用请求,密码应用漏洞/安全事件上报接口对接,转换数据格式并加密传输,X,接口传输数据,"加密数据包, 数据摘要, 传输时间",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：密码服务管理平台，接收者：集团平台,接口调用请求,密码应用漏洞/安全事件上报接口对接,记录接口调用日志,W,接口调用日志,"调用时间, 调用方IP, 响应状态码, 传输数据量",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：用户，接收者：密码服务管理平台,发起安全漏洞事件查询,密码应用漏洞/安全事件上报数据列表展示,输入查询条件,E,安全漏洞事件查询条件,"查询时间范围, 事件类型, 影响范围",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：用户，接收者：密码服务管理平台,发起安全漏洞事件查询,密码应用漏洞/安全事件上报数据列表展示,读取安全漏洞事件数据,R,安全漏洞事件列表,"id, flaw_event_name, flaw_event_type, 发现时间, 影响范围, 上报状态",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：用户，接收者：密码服务管理平台,发起安全漏洞事件查询,密码应用漏洞/安全事件上报数据列表展示,返回分页展示数据,X,安全漏洞事件展示列表,"分页参数, 事件列表数据, 总记录数",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：定时任务，接收者：密码服务管理平台,定时任务触发,密码应用漏洞/安全事件定时上报更新,读取上报配置信息,R,定时上报配置,"frequency_id, 上报频率, 上报时间点",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：定时任务，接收者：密码服务管理平台,定时任务触发,密码应用漏洞/安全事件定时上报更新,收集待上报的安全漏洞事件,R,安全漏洞事件信息,"flaw_event_id, flaw_event_name, flaw_event_type, 发现时间, 影响范围",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：定时任务，接收者：密码服务管理平台,定时任务触发,密码应用漏洞/安全事件定时上报更新,执行接口上报操作,X,接口传输数据,"加密数据包, 数据摘要, 传输时间",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：定时任务，接收者：密码服务管理平台,定时任务触发,密码应用漏洞/安全事件定时上报更新,更新上报状态标记,W,安全漏洞事件信息,"上报状态, 上报时间",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：用户，接收者：密码服务管理平台,补充安全事件信息,密码应用漏洞/安全事件补充,输入补充事件信息,E,补充安全事件信息,"事件描述, 事件类型, 发现时间, 影响范围",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：用户，接收者：密码服务管理平台,补充安全事件信息,密码应用漏洞/安全事件补充,校验补充信息格式,R,安全事件补充规则,"字段校验规则, 格式校验规则",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：用户，接收者：密码服务管理平台,补充安全事件信息,密码应用漏洞/安全事件补充,存储补充事件数据,W,安全漏洞事件信息,"flaw_event_id, flaw_event_name, flaw_event_type, 发现时间, 影响范围",1
数据上报接口,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,4,发起者：用户，接收者：密码服务管理平台,补充安全事件信息,密码应用漏洞/安全事件补充,返回补充结果,X,补充操作反馈,"操作状态, 错误信息, 补充时间",1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台,提交漏洞事件,漏洞事件上报,输入漏洞事件上报信息,E,漏洞事件上报信息,漏洞事件ID、漏洞事件名称、漏洞描述、影响范围、上报时间,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台,提交漏洞事件,漏洞事件上报,关联密码应用信息,R,密码应用信息,应用ID、应用名称、应用类型、密评ID,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台,提交漏洞事件,漏洞事件上报,校验漏洞事件唯一性,R,漏洞事件校验信息,漏洞事件ID、漏洞事件名称、重复性校验,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台,提交漏洞事件,漏洞事件上报,保存漏洞事件数据,W,漏洞事件存储信息,漏洞事件ID、漏洞事件名称、漏洞描述、关联应用ID、上报时间,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台,提交漏洞事件,漏洞事件上报,生成漏洞事件上报确认,X,漏洞事件确认信息,漏洞事件ID、上报状态、确认时间,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台,提交漏洞事件,漏洞事件上报,记录漏洞事件操作日志,W,漏洞事件操作日志,操作员ID、操作时间、操作类型、漏洞事件ID,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台,报告安全事件,安全事件上报,输入安全事件上报信息,E,安全事件上报信息,安全事件ID、事件名称、事件类型、发生时间、影响等级,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台,报告安全事件,安全事件上报,关联密评信息,R,密评信息,密评ID、省份编码、应用ID、评估状态,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台,报告安全事件,安全事件上报,校验安全事件合规性,R,安全事件校验信息,事件类型、影响等级、合规性校验规则,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台,报告安全事件,安全事件上报,存储安全事件数据,W,安全事件存储信息,安全事件ID、事件名称、事件类型、关联密评ID、上报时间,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台,报告安全事件,安全事件上报,输出安全事件上报结果,X,安全事件确认信息,安全事件ID、上报状态、处理建议,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台,报告安全事件,安全事件上报,记录安全事件操作日志,W,安全事件操作日志,操作员ID、操作时间、事件ID、操作详情,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台,查询上报记录,上报记录查询,输入查询条件,E,上报记录查询条件,查询时间范围、事件类型、应用ID,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台,查询上报记录,上报记录查询,读取漏洞事件记录,R,漏洞事件记录,漏洞事件ID、事件名称、上报时间、处理状态,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台,查询上报记录,上报记录查询,读取安全事件记录,R,安全事件记录,安全事件ID、事件类型、影响等级、上报时间,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台,查询上报记录,上报记录查询,返回查询结果,X,上报记录查询结果,事件ID、事件名称、类型、上报时间、处理状态,1
合计,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报,nan,1053,发起者：平台管理员，接收者：密码服务平台,查询上报记录,上报记录查询,保存查询操作日志,W,查询操作日志,操作员ID、查询时间、查询条件、结果数量,1
