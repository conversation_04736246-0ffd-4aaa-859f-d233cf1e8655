#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COSMIC功能需求文档生成器 Web UI 演示脚本

展示完整的使用流程
"""

import os
import sys
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🌟 COSMIC功能需求文档生成器 Web UI 演示")
    print("=" * 60)
    print()

def print_features():
    """打印功能特性"""
    print("🎯 主要功能:")
    print("1. 📁 文件选择: 下拉框选择xlsx/csv文件")
    print("2. 📝 文档生成: 调用doc_generator.py生成markdown文档")
    print("3. 🎨 在线渲染: 支持Markdown和Mermaid时序图渲染")
    print("4. 🔄 重新生成: 支持重新生成已存在的文档")
    print("5. 💾 文件管理: 自动检查md文件状态，提供下载功能")
    print()

def print_new_structure():
    """打印新的文档结构"""
    print("📋 新的文档结构:")
    print("""
## 2.1 一级功能需求 （对应一级模块）
 (该一级模块的功能简介，描述)
### 2.1.1 关键时序图/业务逻辑图
 (顺序列出各三级模块名称及时序图)
（三级模块时序图：可结合功能过程、子过程、触发事件、数据组等相关信息，生成可渲染的Mermaid语法图）
### 2.1.2 功能需求描述
（详细描述该一级模块的功能）
#### 2.1.2.1 二级功能需求 （对应二级模块）
##### 2.1.2.1.1 三级功能需求 （对应三级模块）
{三级模块名称} 包含如下功能：
{顺序列出该三级模块所包含的各功能过程名称}
###### 2.1.2.1.1.1 功能过程 （对应功能过程）
***功能简介***
{功能过程名称}
***功能要求***
{顺序列出各子过程描述列的名称}
    """)
    print()

def print_improvements():
    """打印改进点"""
    print("✨ 主要改进:")
    print("1. 🎯 约束优化: 只为三级模块生成时序图，功能过程不生成时序图")
    print("2. 📊 结构调整: 从三级模块逐层向上组织文档内容")
    print("3. 🌐 Web界面: 提供友好的Web界面，支持文件选择和在线渲染")
    print("4. 🎨 Mermaid支持: 完整的时序图在线渲染支持")
    print("5. 🔄 重新生成: 支持重新生成已存在的markdown文档")
    print()

def print_usage():
    """打印使用说明"""
    print("🚀 使用方法:")
    print()
    print("方法1: 使用启动脚本（推荐）")
    print("  python start_web_ui.py")
    print()
    print("方法2: 手动启动")
    print("  1. pip install flask flask-cors markdown pygments")
    print("  2. python web_ui.py")
    print("  3. 访问 http://localhost:5000")
    print()
    print("方法3: 测试功能")
    print("  python debug/test_web_ui.py")
    print()

def print_workflow():
    """打印工作流程"""
    print("📝 使用流程:")
    print("1. 📁 准备数据: 将xlsx/csv文件放在项目根目录")
    print("2. 🚀 启动服务: 运行启动脚本或手动启动")
    print("3. 🌐 打开界面: 浏览器访问 http://localhost:5000")
    print("4. 📋 选择文件: 在下拉框中选择要处理的文件")
    print("5. ⚙️  生成文档: 点击'生成文档'按钮")
    print("6. 👁️  查看结果: 点击'查看文档'查看渲染结果")
    print("7. 💾 下载文档: 可选择下载生成的markdown文件")
    print()

def check_files():
    """检查项目文件"""
    print("🔍 检查项目文件:")
    
    required_files = [
        'web_ui.py',
        'doc_generator.py', 
        'config.py',
        'doc_prompt.md',
        'templates/index.html',
        'start_web_ui.py'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (缺失)")
            all_exist = False
    
    print()
    
    # 检查数据文件
    data_files = []
    for ext in ['.xlsx', '.csv']:
        import glob
        files = glob.glob(f'*{ext}')
        data_files.extend(files)
    
    if data_files:
        print(f"📊 找到 {len(data_files)} 个数据文件:")
        for file in data_files[:3]:
            print(f"   - {file}")
        if len(data_files) > 3:
            print(f"   ... 还有 {len(data_files) - 3} 个文件")
    else:
        print("⚠️  未找到数据文件，请添加xlsx或csv文件")
    
    print()
    return all_exist

def main():
    """主函数"""
    print_banner()
    print_features()
    print_new_structure()
    print_improvements()
    print_usage()
    print_workflow()
    
    # 检查文件
    if check_files():
        print("🎉 项目文件完整，可以开始使用！")
        print()
        print("💡 建议:")
        print("1. 运行 'python start_web_ui.py' 启动Web界面")
        print("2. 或运行 'python debug/test_web_ui.py' 测试功能")
        print("3. 查看 'WEB_UI_README.md' 了解详细说明")
    else:
        print("❌ 项目文件不完整，请检查缺失的文件")
    
    print()
    print("=" * 60)
    print("感谢使用 COSMIC功能需求文档生成器 Web UI！")
    print("=" * 60)

if __name__ == "__main__":
    main()
