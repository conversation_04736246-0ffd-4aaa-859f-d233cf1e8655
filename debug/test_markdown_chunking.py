#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试markdown切块逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from knowledge_base import KnowledgeBase

def test_markdown_chunking():
    """测试markdown切块功能"""
    
    # 创建知识库实例
    kb = KnowledgeBase()
    
    # 读取测试文件
    test_file = "debug/test_markdown.md"
    with open(test_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("原始内容:")
    print("=" * 50)
    print(content)
    print("=" * 50)
    
    # 测试新的切块逻辑
    print("\n测试新的层次结构切块逻辑:")
    print("-" * 50)
    
    chunks = kb._parse_markdown_hierarchy(content)
    
    print(f"总共生成了 {len(chunks)} 个切块:")
    print()
    
    for i, chunk in enumerate(chunks, 1):
        print(f"块{i}: {chunk['title']}")
        print(f"内容: {chunk['content'].strip()}")
        print("-" * 30)

if __name__ == "__main__":
    test_markdown_chunking()
