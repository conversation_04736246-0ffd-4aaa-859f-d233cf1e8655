#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的处理流程
"""

import pandas as pd
import json
from collections import OrderedDict
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_mock_processing():
    """使用模拟数据测试完整处理流程"""
    print("=== 测试完整处理流程（模拟数据）===")
    
    # 模拟大模型返回的结果
    mock_result = [
        {
            "总部平台HTTPS对接": [
                {
                    "功能用户": "发起者：管理员，接收者：系统管理模块",
                    "触发事件": "管理员点击总部平台配置菜单",
                    "功能过程": "配置总部平台上报路径",
                    "子过程": [
                        {"子过程描述": "输入上报路径信息", "数据移动类型": "E", "数据组": "平台配置信息", "数据属性": "上报路径、IP地址、端口", "CFP": 1},
                        {"子过程描述": "验证地址格式", "数据移动类型": "R", "数据组": "验证规则信息", "数据属性": "IP格式规则、端口范围", "CFP": 1},
                        {"子过程描述": "保存配置信息", "数据移动类型": "W", "数据组": "平台配置信息", "数据属性": "上报路径、IP地址、端口", "CFP": 1},
                        {"子过程描述": "返回配置结果", "数据移动类型": "X", "数据组": "操作结果信息", "数据属性": "操作状态、提示信息", "CFP": 1}
                    ]
                },
                {
                    "功能用户": "发起者：管理员，接收者：系统管理模块",
                    "触发事件": "管理员查看总部平台配置",
                    "功能过程": "查看总部平台上报路径",
                    "子过程": [
                        {"子过程描述": "读取配置信息", "数据移动类型": "R", "数据组": "平台配置信息", "数据属性": "上报路径、IP地址、端口", "CFP": 1},
                        {"子过程描述": "展示配置列表", "数据移动类型": "X", "数据组": "平台配置信息", "数据属性": "上报路径、IP地址、端口", "CFP": 1}
                    ]
                }
            ]
        }
    ]
    
    # 模拟当前模块信息
    current_level_1 = "系统管理"
    current_level_2 = "总部一级平台对接"
    current_level_3 = "总部平台HTTPS对接"
    current_function_description = "支持配置总部平台的上报路径"
    current_estimated_workload = "4"
    current_function_process_list = ["总部平台上报路径配置", "总部平台上报路径查看", "总部平台HTTPS通道对接"]
    
    # 列名定义
    level_1_name = "一级功能模块"
    level_2_name = "二级功能模块"
    level_3_name = "三级功能模块"
    func_process_name = "功能过程"
    function_description_name = "功能描述"
    estimated_workload_name = "预估工作量\n（人天）"
    
    all_results = []
    
    # 处理结果 - 模拟main.py中的逻辑
    if mock_result and isinstance(mock_result, list):
        for module_dict in mock_result:
            if isinstance(module_dict, dict):
                for module_name, function_processes in module_dict.items():
                    level_3 = current_level_3
                    func_desc = current_function_description
                    est_work = current_estimated_workload

                    if isinstance(function_processes, list):
                        for i, process in enumerate(function_processes):
                            ordered_item = OrderedDict()
                            ordered_item[level_1_name] = current_level_1
                            ordered_item[level_2_name] = current_level_2
                            ordered_item[level_3_name] = level_3
                            # 添加对应的功能过程信息
                            if i < len(current_function_process_list):
                                ordered_item[func_process_name] = current_function_process_list[i]
                            else:
                                ordered_item[func_process_name] = ""
                            ordered_item[function_description_name] = func_desc
                            ordered_item[estimated_workload_name] = est_work
                            # 添加功能过程的所有字段
                            for k, v in process.items():
                                ordered_item[k] = v
                            all_results.append(ordered_item)
    
    print(f"处理结果数量: {len(all_results)}")
    for i, result in enumerate(all_results):
        print(f"\n结果 {i+1}:")
        for k, v in result.items():
            print(f"  {k}: {v}")
    
    # 测试扁平化处理
    from main import flatten_results_with_subprocess
    flat_results = flatten_results_with_subprocess(all_results)
    print(f"\n扁平化结果数量: {len(flat_results)}")
    
    # 转换为DataFrame测试
    result_df = pd.DataFrame(flat_results)
    print(f"\nDataFrame列名: {result_df.columns.tolist()}")
    print(f"DataFrame形状: {result_df.shape}")
    
    # 保存测试结果
    result_df.to_csv('debug/test_output.csv', index=False, encoding='utf-8-sig')
    print("\n测试结果已保存到 debug/test_output.csv")
    
    return True

if __name__ == "__main__":
    print("开始测试完整处理流程...")
    
    # 切换到项目根目录
    os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    success = test_mock_processing()
    
    if success:
        print("\n✅ 完整流程测试通过！")
    else:
        print("\n❌ 完整流程测试失败！")
