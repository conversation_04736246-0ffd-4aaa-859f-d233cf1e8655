#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试知识库功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from knowledge_base import knowledge_base

def test_knowledge_base():
    """测试知识库基本功能"""
    print("=== 知识库功能测试 ===")
    
    # 测试知识库是否正常初始化
    print(f"知识库启用状态: {knowledge_base.enabled}")
    print(f"功能文档数量: {len(knowledge_base.function_docs)}")
    print(f"实体文档数量: {len(knowledge_base.entity_docs)}")
    
    if knowledge_base.function_docs:
        print("\n=== 功能文档示例 ===")
        for i, doc in enumerate(knowledge_base.function_docs[:3]):
            print(f"{i+1}. {doc['title']}")
            print(f"   内容: {doc['content'][:100]}...")
            print()
    
    if knowledge_base.entity_docs:
        print("\n=== 实体文档示例 ===")
        for i, doc in enumerate(knowledge_base.entity_docs[:3]):
            print(f"{i+1}. {doc['table_name']}")
            if 'fields' in doc:
                field_names = [f['name'] for f in doc['fields'][:5]]
                print(f"   字段: {', '.join(field_names)}")
            print()
    
    # 测试搜索功能
    print("\n=== 搜索功能测试 ===")
    test_queries = [
        "用户管理",
        "密码",
        "证书",
        "权限",
        "配置"
    ]
    
    for query in test_queries:
        print(f"\n搜索: '{query}'")
        results = knowledge_base.search_knowledge(query)
        print(f"找到 {len(results)} 个相关结果")
        for i, result in enumerate(results[:2]):
            print(f"  {i+1}. [{result['type']}] {result.get('title', result.get('table_name', 'Unknown'))} (相似度: {result['similarity']:.3f})")
    
    # 测试模块上下文获取
    print("\n=== 模块上下文测试 ===")
    context = knowledge_base.get_context_for_module(
        "用户管理", 
        "用户权限管理", 
        "角色权限分配",
        "管理员可以为不同角色分配相应的权限"
    )
    print("获取的上下文:")
    print(context)

if __name__ == "__main__":
    test_knowledge_base()
