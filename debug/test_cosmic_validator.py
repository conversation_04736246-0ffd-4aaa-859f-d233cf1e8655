#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COSMIC校验器测试脚本

用于测试cosmic_validator.py的各项功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cosmic_validator import CosmicValidator
import config
import json


def test_csv_parsing():
    """测试CSV解析功能"""
    print("=" * 60)
    print("测试CSV解析功能")
    print("=" * 60)
    
    validator = CosmicValidator(config)
    
    # 测试解析output-new.csv
    csv_file = "output-new.csv"
    if not os.path.exists(csv_file):
        print(f"错误: 找不到测试文件 {csv_file}")
        return False
    
    print(f"解析文件: {csv_file}")
    hierarchical_data = validator.parse_csv_to_hierarchical_json(csv_file)
    
    if not hierarchical_data:
        print("解析失败!")
        return False
    
    # 显示解析结果摘要
    summary = hierarchical_data.get('summary', {})
    print(f"解析成功!")
    print(f"  总记录数: {summary.get('total_records', 0)}")
    print(f"  一级模块数: {len(summary.get('level1_modules', []))}")
    print(f"  二级模块数: {len(summary.get('level2_modules', []))}")
    print(f"  三级模块数: {len(summary.get('level3_modules', []))}")
    print(f"  数据移动类型分布: {summary.get('data_movement_types', {})}")
    print(f"  CFP分布: {summary.get('cfp_distribution', {})}")
    
    # 保存解析结果用于调试
    debug_file = "debug/test_parsing_result.json"
    try:
        with open(debug_file, 'w', encoding='utf-8') as f:
            json.dump(hierarchical_data, f, ensure_ascii=False, indent=2)
        print(f"解析结果已保存到: {debug_file}")
    except Exception as e:
        print(f"保存解析结果失败: {e}")
    
    return True


def test_function_process_grouping():
    """测试功能过程分组功能"""
    print("\n" + "=" * 60)
    print("测试功能过程分组功能")
    print("=" * 60)
    
    validator = CosmicValidator(config)
    
    # 先解析CSV
    hierarchical_data = validator.parse_csv_to_hierarchical_json("output-new.csv")
    if not hierarchical_data:
        print("CSV解析失败，无法测试分组功能")
        return False
    
    # 测试分组功能
    function_processes = validator._group_by_function_process(hierarchical_data)
    
    print(f"功能过程分组完成!")
    print(f"  总功能过程数: {len(function_processes)}")
    
    # 显示前几个功能过程的详细信息
    count = 0
    for process_key, process_data in function_processes.items():
        if count >= 3:  # 只显示前3个
            break
        
        print(f"\n  功能过程 {count + 1}: {process_key}")
        print(f"    子过程数量: {process_data['subprocess_count']}")
        movements = process_data['data_movements']
        print(f"    数据移动: E={movements['movement_counts']['E']}, "
              f"R={movements['movement_counts']['R']}, "
              f"W={movements['movement_counts']['W']}, "
              f"X={movements['movement_counts']['X']}")
        print(f"    数据组数量: {movements['data_group_count']}")
        print(f"    总CFP: {movements['total_cfp']}")
        print(f"    是否完整: {movements['is_complete']}")
        
        count += 1
    
    if len(function_processes) > 3:
        print(f"  ... 还有 {len(function_processes) - 3} 个功能过程")
    
    # 保存分组结果
    debug_file = "debug/test_function_processes.json"
    try:
        with open(debug_file, 'w', encoding='utf-8') as f:
            json.dump(function_processes, f, ensure_ascii=False, indent=2)
        print(f"分组结果已保存到: {debug_file}")
    except Exception as e:
        print(f"保存分组结果失败: {e}")
    
    return True


def test_validation_data_preparation():
    """测试校验数据准备功能"""
    print("\n" + "=" * 60)
    print("测试校验数据准备功能")
    print("=" * 60)
    
    validator = CosmicValidator(config)
    
    # 解析CSV并准备校验数据
    hierarchical_data = validator.parse_csv_to_hierarchical_json("output-new.csv")
    if not hierarchical_data:
        print("CSV解析失败")
        return False
    
    function_processes = validator._group_by_function_process(hierarchical_data)
    
    # 构建校验数据
    validation_data = {
        "summary": hierarchical_data["summary"],
        "function_processes": function_processes,
        "validation_focus": {
            "completeness_check": "每个功能过程是否包含至少1个E和1个X",
            "data_group_aggregation": "是否将关联数据属性合并为最小单元",
            "storage_boundary": "R/W是否仅针对边界内持久存储",
            "no_duplicate_counting": "同一数据组在同一功能过程中是否被重复计数"
        }
    }
    
    print("校验数据准备完成!")
    print(f"  包含功能过程数: {len(function_processes)}")
    
    # 统计完整性
    complete_count = sum(1 for fp in function_processes.values() 
                        if fp["data_movements"]["is_complete"])
    incomplete_count = len(function_processes) - complete_count
    
    print(f"  完整功能过程: {complete_count}")
    print(f"  不完整功能过程: {incomplete_count}")
    
    if incomplete_count > 0:
        print("\n  不完整的功能过程示例:")
        count = 0
        for process_key, process_data in function_processes.items():
            if not process_data["data_movements"]["is_complete"] and count < 3:
                movements = process_data['data_movements']['movement_counts']
                print(f"    {process_key}: E={movements['E']}, X={movements['X']}")
                count += 1
    
    # 保存校验数据
    debug_file = "debug/test_validation_data.json"
    try:
        with open(debug_file, 'w', encoding='utf-8') as f:
            json.dump(validation_data, f, ensure_ascii=False, indent=2)
        print(f"校验数据已保存到: {debug_file}")
    except Exception as e:
        print(f"保存校验数据失败: {e}")
    
    return True


def test_prompt_loading():
    """测试提示词加载功能"""
    print("\n" + "=" * 60)
    print("测试提示词加载功能")
    print("=" * 60)
    
    prompt_file = "check_prompt.md"
    
    if not os.path.exists(prompt_file):
        print(f"错误: 找不到提示词文件 {prompt_file}")
        return False
    
    try:
        with open(prompt_file, 'r', encoding='utf-8') as f:
            prompt = f.read()
        
        print(f"提示词加载成功!")
        print(f"  文件: {prompt_file}")
        print(f"  长度: {len(prompt)} 字符")
        print(f"  前100字符: {prompt[:100]}...")
        
        return True
    except Exception as e:
        print(f"加载提示词失败: {e}")
        return False


def main():
    """主测试函数"""
    print("COSMIC校验器功能测试")
    print("=" * 80)
    
    # 确保debug目录存在
    os.makedirs("debug", exist_ok=True)
    
    tests = [
        ("CSV解析功能", test_csv_parsing),
        ("功能过程分组功能", test_function_process_grouping),
        ("校验数据准备功能", test_validation_data_preparation),
        ("提示词加载功能", test_prompt_loading),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n测试 '{test_name}' 出现异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果摘要
    print("\n" + "=" * 80)
    print("测试结果摘要")
    print("=" * 80)
    
    passed = 0
    for test_name, result in results:
        status = "通过" if result else "失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("所有测试通过！可以进行完整的校验测试。")
    else:
        print("部分测试失败，请检查相关功能。")


if __name__ == "__main__":
    main()
