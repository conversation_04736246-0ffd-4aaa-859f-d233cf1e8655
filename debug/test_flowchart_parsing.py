#!/usr/bin/env python3
"""
测试流程图解析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from web_ui import generate_flowchart_html

def test_sequence_parsing():
    """测试时序图解析"""
    
    # 测试用例1：简单的用户登录流程
    sequence1 = """
sequenceDiagram
    participant User as 用户
    participant System as 系统
    participant DB as 数据库

    User->>System: 提交登录请求
    System->>System: 输入参数验证(E)
    System->>DB: 验证登录信息(R)
    DB-->>System: 返回验证结果
    System->>DB: 保存登录信息(W)
    System->>User: 返回登录结果(X)
"""
    
    print("=== 测试用例1：用户登录流程 ===")
    html1 = generate_flowchart_html(sequence1)
    print("生成的HTML长度:", len(html1))
    print("包含participant-box:", "participant-box" in html1)
    print("包含interaction:", "interaction" in html1)
    print()
    
    # 测试用例2：复杂的数据查询流程
    sequence2 = """
sequenceDiagram
    participant User as 用户
    participant System as 系统
    participant Cache as 缓存
    participant DB as 数据库

    User->>System: 发起查询请求
    System->>Cache: 检查缓存数据(R)
    Cache-->>System: 返回缓存结果
    System->>DB: 查询数据库(R)
    DB-->>System: 返回查询结果
    System->>Cache: 更新缓存(W)
    System->>User: 返回查询结果(X)
"""
    
    print("=== 测试用例2：数据查询流程 ===")
    html2 = generate_flowchart_html(sequence2)
    print("生成的HTML长度:", len(html2))
    print("包含participant-box:", "participant-box" in html2)
    print("包含interaction:", "interaction" in html2)
    print()
    
    # 保存测试结果
    with open('debug/test_flowchart_result1.html', 'w', encoding='utf-8') as f:
        f.write(f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>流程图测试结果1</title>
    <style>
        .flowchart-container {{
            position: relative;
            min-height: 400px;
            background: #f9f9f9;
            padding: 20px;
            overflow: auto;
            border: 1px solid #ddd;
            margin: 20px;
        }}
        
        .participant-box {{
            position: absolute;
            top: 20px;
            width: 120px;
            height: 60px;
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border: 2px solid #2196f3;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #1976d2;
            text-align: center;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        
        .interaction {{
            position: absolute;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }}
        
        .arrow {{
            position: relative;
            width: 100%;
            height: 2px;
            background: #333;
        }}
        
        .arrow-right::after {{
            content: '';
            position: absolute;
            right: -8px;
            top: -4px;
            width: 0;
            height: 0;
            border-left: 8px solid #333;
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
        }}
        
        .arrow-left::after {{
            content: '';
            position: absolute;
            left: -8px;
            top: -4px;
            width: 0;
            height: 0;
            border-right: 8px solid #333;
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
        }}
        
        .dashed-arrow {{
            background: repeating-linear-gradient(
                to right,
                #333 0px,
                #333 5px,
                transparent 5px,
                transparent 10px
            );
        }}
        
        .message {{
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }}
    </style>
</head>
<body>
    <h1>流程图测试结果</h1>
    <h2>测试用例1：用户登录流程</h2>
    {html1}
    
    <h2>测试用例2：数据查询流程</h2>
    {html2}
</body>
</html>
        """)
    
    print("测试结果已保存到: debug/test_flowchart_result1.html")

if __name__ == "__main__":
    test_sequence_parsing()
