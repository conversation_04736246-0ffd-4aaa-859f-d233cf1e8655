# 知识库增强功能实现说明

## 功能概述

本次更新为COSMIC功能拆解系统增加了知识库增强功能，通过解析用户手册和SQL文件，为大模型提供更准确的上下文信息，从而提高功能拆解的质量和准确性。

## 主要功能

### 1. 配置管理
在 `config.py` 中新增了以下配置项：
- `MARKDOWN_MANUAL_PATH`: 用户手册文件路径（支持.md和.docx格式）
- `SQL_FILE_PATH`: SQL数据库文件路径
- `KNOWLEDGE_BASE_ENABLED`: 是否启用知识库功能
- `KNOWLEDGE_BASE_TOP_K`: 检索返回的最相关结果数量
- `KNOWLEDGE_BASE_SIMILARITY_THRESHOLD`: 相似度阈值
- `KNOWLEDGE_BASE_CACHE_DIR`: 知识库缓存目录

### 2. 文档解析功能
#### Word文档解析 (.docx)
- 支持多种标题格式识别：
  - 样式名以"Heading"开头的段落
  - 数字开头的标题（如"1.1 用户管理"）
  - 中文章节标题（如"第一章"）
  - 加粗的短文本段落
- 自动提取章节标题和内容
- 生成结构化的功能说明文档

#### SQL文件解析
- 解析CREATE TABLE语句
- 提取表名、字段名、字段类型
- 解析字段注释和表注释
- 生成结构化的数据实体信息

### 3. 知识库检索功能
- 使用TF-IDF向量化和余弦相似度进行文本检索
- 支持中文分词（使用jieba）
- 分别检索功能说明和数据实体
- 根据相似度阈值过滤结果
- 返回最相关的top-k结果

### 4. 上下文生成功能
- 根据模块信息（一级、二级、三级模块名称和功能描述）生成查询
- 检索相关的功能说明和数据实体
- 格式化生成知识库上下文
- 包含相关功能说明和数据实体的详细信息

### 5. 主程序集成
- 在调用大模型前先获取知识库上下文
- 将知识库上下文添加到系统提示词中
- 为每个批次的模块分别获取相关上下文
- 支持批量处理时的上下文聚合

### 6. 系统提示词优化
- 添加了知识库上下文使用说明
- 强调优先使用知识库中的数据实体信息
- 指导如何利用功能说明理解业务流程
- 确保拆解结果与实际数据库结构一致

## 技术实现

### 核心类：KnowledgeBase
```python
class KnowledgeBase:
    - _parse_user_manual(): 解析用户手册
    - _parse_sql_file(): 解析SQL文件
    - _vectorize_documents(): 文档向量化
    - search_knowledge(): 知识库检索
    - get_context_for_module(): 获取模块上下文
```

### 缓存机制
- 支持文档解析结果缓存
- 避免重复解析大文件
- 缓存文件存储在debug/knowledge_cache目录

### 依赖包
- `scikit-learn`: 用于TF-IDF向量化和相似度计算
- `jieba`: 中文分词
- `python-docx`: Word文档解析
- `numpy`: 数值计算

## 使用效果

### 解析结果统计
- **Word文档解析**：成功提取39个功能说明章节
- **SQL文件解析**：成功提取20个数据实体，包含完整的字段信息和注释
- **知识库检索**：能够根据模块信息准确检索相关内容

### 上下文示例
对于"密码设备管理"模块，系统能够自动检索到：
- 相关功能说明：密码设备管理、证书管理等
- 相关数据实体：up_product_cypher_type、up_app_cypher_type等表结构

## 配置说明

### 启用知识库功能
```python
KNOWLEDGE_BASE_ENABLED = True
```

### 调整检索参数
```python
KNOWLEDGE_BASE_TOP_K = 5  # 返回最相关的5个结果
KNOWLEDGE_BASE_SIMILARITY_THRESHOLD = 0.1  # 相似度阈值
```

### 指定文档路径
```python
MARKDOWN_MANUAL_PATH = "用户手册.docx"
SQL_FILE_PATH = "数据库结构.sql"
```

## 测试验证

### 测试脚本
- `debug/test_knowledge_base.py`: 基础功能测试
- `debug/test_integration.py`: 集成功能测试

### 测试结果
- 文档解析功能正常
- 知识库检索准确
- 上下文生成完整
- 主程序集成成功

## 优势

1. **提高准确性**：基于实际的用户手册和数据库结构进行功能拆解
2. **减少错误**：避免大模型臆测数据实体和字段信息
3. **提升效率**：自动化的知识库检索和上下文生成
4. **易于维护**：支持文档更新后的自动重新解析
5. **灵活配置**：可根据需要调整检索参数和启用状态

## 后续优化建议

1. 支持更多文档格式（PDF、HTML等）
2. 增加语义检索能力（使用预训练的embedding模型）
3. 支持知识库的增量更新
4. 添加知识库质量评估功能
5. 支持多语言文档解析
