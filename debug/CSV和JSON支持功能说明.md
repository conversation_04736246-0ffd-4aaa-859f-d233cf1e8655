# COSMIC校验器CSV和JSON支持功能说明

## 功能概述

已成功修改 `cosmic_validator.py`，使其支持同时处理CSV和JSON格式的COSMIC功能拆解数据输入。

## 主要修改内容

### 1. 文件格式检测
- 自动检测输入文件的扩展名（.csv 或 .json）
- 根据文件格式选择相应的处理逻辑
- 支持错误提示，对不支持的格式给出明确提示

### 2. CSV格式处理
- **直接输入模式**：CSV文件内容直接作为大模型输入，不进行JSON转换
- **数据过滤**：自动过滤掉配置中指定的不需要字段（如"预估工作量（人天）"）
- **统计信息生成**：生成基本的数据统计信息，包括：
  - 总记录数
  - 列名列表
  - 数据移动类型分布（E、R、W、X）
  - CFP分布
  - 各级功能模块统计

### 3. JSON格式处理
- **层次化转换**：将JSON数据转换为层次化结构
- **功能过程分析**：按功能过程重新组织数据，便于校验
- **数据简化**：当数据量过大时自动进行简化处理
- **完整性分析**：分析每个功能过程的完整性（是否包含E和X）

### 4. 统一的校验流程
- 两种格式都使用相同的大模型校验接口
- 统一的结果保存和报告生成
- 一致的错误处理和调试信息输出

## 文件结构变化

### 新增方法
1. `validate_cosmic_data(file_path, prompt_file_path)` - 主入口方法，支持文件格式检测
2. `_validate_csv_data(csv_file_path, prompt)` - CSV格式专用处理方法
3. `_validate_json_data(json_file_path, prompt)` - JSON格式专用处理方法
4. `_perform_validation(validation_data_str, prompt, summary_info)` - 统一的校验执行方法
5. `_generate_csv_summary(df)` - 生成CSV数据摘要信息
6. `_convert_json_to_hierarchical(json_data)` - 将JSON数据转换为层次化结构

### 修改的方法
- `main()` - 支持命令行参数，自动检测可用的测试文件

## 使用方式

### 命令行使用
```bash
# 使用默认文件（自动检测 output-sft.csv 或 output-sft.json）
python cosmic_validator.py

# 指定CSV文件
python cosmic_validator.py output-sft.csv

# 指定JSON文件
python cosmic_validator.py output-sft.json
```

### 程序调用
```python
from cosmic_validator import CosmicValidator
import config

validator = CosmicValidator(config)

# 校验CSV文件
result = validator.validate_cosmic_data("data.csv")

# 校验JSON文件
result = validator.validate_cosmic_data("data.json")
```

## 输出结果差异

### CSV格式输出
- `data_format`: "CSV"
- `csv_content`: 原始CSV内容（过滤后）
- `summary`: 基本统计信息
- 不包含 `function_process_summary`

### JSON格式输出
- `data_format`: "JSON"
- `function_processes`: 按功能过程组织的详细数据
- `summary`: 详细的层次化统计信息
- `function_process_summary`: 功能过程完整性统计

## 测试结果

### CSV格式测试
- 文件：output-sft.csv
- 总记录数：1177
- 数据移动类型分布：E(307), R(299), W(275), X(296)
- 处理方式：直接作为大模型输入
- 数据大小：171KB，预估token：257K
- 状态：✅ 成功处理，CSV内容直接包含在校验数据中

### JSON格式测试
- 文件：output-sft.json
- 总记录数：1177
- 功能过程：总数310，完整264，不完整46
- 处理方式：层次化分析后输入大模型
- 自动简化：原始626KB简化为31KB
- 校验结果：合规率75.2%，重大问题46个，轻微问题89个
- 状态：✅ 成功处理，包含详细的功能过程分析

### 格式检测测试
- 不支持的格式（.txt）：✅ 正确提示错误信息
- 文件不存在：✅ 正确处理文件路径错误

## 优势特点

1. **格式兼容性**：同时支持CSV和JSON两种常用格式
2. **处理效率**：CSV直接输入，避免不必要的转换开销
3. **数据完整性**：JSON格式保持原有的详细分析能力
4. **自动优化**：大数据量时自动简化，避免token限制
5. **统一接口**：对外提供一致的调用接口
6. **错误处理**：完善的错误提示和异常处理

## 调试支持

- 所有处理过程都会在 `debug/` 目录下生成调试文件
- `cosmic_validation_input.json`：发送给大模型的数据
- `cosmic_validation_result.json`：完整的校验结果
- `cosmic_validation_report.txt`：人类可读的校验报告

## 配置要求

需要在 `config.py` 中配置：
- 大模型API相关参数
- 限流参数（QPM、TPM）
- 可选的字段过滤列表

这个功能增强使得COSMIC校验器更加灵活和实用，能够适应不同的数据输入格式需求。
