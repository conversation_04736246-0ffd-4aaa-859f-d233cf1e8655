#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的COSMIC校验测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

print("开始测试...")

try:
    print("1. 导入配置模块...")
    import config
    print("   配置模块导入成功")
    
    print("2. 导入校验模块...")
    from cosmic_validator import CosmicValidator
    print("   校验模块导入成功")
    
    print("3. 初始化校验器...")
    validator = CosmicValidator(config)
    print("   校验器初始化成功")
    
    print("4. 检查输入文件...")
    csv_file = "output-new.csv"
    if os.path.exists(csv_file):
        print(f"   输入文件 {csv_file} 存在")
    else:
        print(f"   错误: 输入文件 {csv_file} 不存在")
        sys.exit(1)
    
    print("5. 检查提示词文件...")
    prompt_file = "check_prompt.md"
    if os.path.exists(prompt_file):
        print(f"   提示词文件 {prompt_file} 存在")
    else:
        print(f"   错误: 提示词文件 {prompt_file} 不存在")
        sys.exit(1)
    
    print("6. 测试CSV解析...")
    hierarchical_data = validator.parse_csv_to_hierarchical_json(csv_file)
    if hierarchical_data:
        summary = hierarchical_data.get('summary', {})
        print(f"   CSV解析成功，总记录数: {summary.get('total_records', 0)}")
    else:
        print("   CSV解析失败")
        sys.exit(1)
    
    print("7. 测试功能过程分组...")
    function_processes = validator._group_by_function_process(hierarchical_data)
    print(f"   功能过程分组成功，总数: {len(function_processes)}")
    
    print("8. 所有基础功能测试通过！")
    print("   可以进行完整的校验测试")
    
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("测试完成！")
