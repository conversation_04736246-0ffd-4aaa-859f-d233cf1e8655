# 流程图测试文档

## 测试时序图1 - 用户登录流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant System as 系统
    participant DB as 数据库

    User->>System: 提交登录请求
    System->>System: 输入参数验证(E)
    System->>DB: 验证登录信息(R)
    DB-->>System: 返回验证结果
    System->>DB: 保存登录信息(W)
    System->>User: 返回登录结果(X)
```

## 测试时序图2 - 数据查询流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant System as 系统
    participant Cache as 缓存
    participant DB as 数据库

    User->>System: 发起查询请求
    System->>Cache: 检查缓存数据(R)
    Cache-->>System: 返回缓存结果
    alt 缓存未命中
        System->>DB: 查询数据库(R)
        DB-->>System: 返回查询结果
        System->>Cache: 更新缓存(W)
    end
    System->>User: 返回查询结果(X)
```

## 测试时序图3 - 文件上传流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant System as 系统
    participant Storage as 存储系统

    User->>System: 上传文件请求
    System->>System: 文件格式验证
    System->>Storage: 保存文件数据(W)
    Storage-->>System: 返回保存结果
    System->>User: 返回上传结果
```

这个测试文档包含了三个不同复杂度的时序图，用于测试流程图备选渲染功能。
