{"data_format": "CSV", "batch_info": {"batch_index": 1, "total_batches": 6, "batch_size": 200, "data_range": "第1行到第200行"}, "summary": {"total_records": 1177, "columns": ["一级功能模块", "二级功能模块", "三级功能模块", "功能用户", "触发事件", "功能过程", "子过程描述", "数据移动类型", "数据组", "数据属性", "CFP", "功能点个数", "预估工作量", "功能过程ID", "功能过程名称"], "data_movement_types": {"E": 307, "R": 299, "X": 296, "W": 275}, "cfp_distribution": {"1": 1177}, "level1_modules": ["系统管理", "密码应用数据管理", "密码资产数据管理", "密码应用测评管理", "密码应用漏洞/安全事件管理", "数据上报接口", "合计"], "level2_modules": ["总部一级平台对接", "用户认证管理", "访问控制管理", "上报周期管理", "日志管理/统计分析", "密码应用类型管理", "密码应用管理", "密码应用场景管理", "密码应用改造厂商管理", "密码资产名称管理", "密码资产数据管理", "密码产品证书及编号管理", "密钥信息管理", "密码文档信息管理", "改造阶段管理", "应用测评报告、测评分数管理", "密码应用方案管理", "密评机构管理", "密码漏洞/安全事件类型管理", "漏洞/安全事件级别管理", "漏洞/安全事件详情管理", "密码产品监控范围管理", "密码应用数据上报类接口", "密码资产数据上报类接口", "密码应用测评数据上报类接口", "密码应用漏洞/安全事件上报类接口"], "level3_modules": ["总部平台HTTPS对接", "总部平台AKSK认证对接", "用户注册审核", "用户信息管理", "用户口令管理", "用户ukey策略管理", "用户口令策略管理", "上报周期及频率管理", "登录日志管理", "操作日志管理", "密码应用类型管理", "应用关联应用类型", "密码应用管理", "密码应用认证凭证管理", "密码应用业务管理", "密码应用场景管理", "密码应用改造厂商管理", "密码服务管理", "密码服务组管理", "密码服务镜像管理", "密码服务数据库管理", "密码服务数据库模式管理", "API网关管理", "网关路由管理", "设备类型管理", "密码设备集群管理", "云密码机管理", "云密码机虚机网络管理", "虚拟密码机管理", "物理密码机管理", "保护主密钥管理", "用户证书管理", "应用证书管理", "密钥及生命周期管理", "密码文档信息管理", "密码应用测评管理", "应用测评管理", "应用测评方案管理", "应用测评模板管理", "密评进度推进管理", "密评进度跟踪报告管理", "密码应用测评差距管理", "密评机构管理", "密码漏洞/安全事件类型管理", "漏洞/安全事件管理", "漏洞/安全事件通知人管理", "告警邮箱配置管理", "漏洞/安全事件详情管理", "密码产品监控范围管理", "密码应用数据上报管理", "密码产品信息上报", "密钥信息上报", "证书信息上报", "密码文档信息上报", "应用测评信息上报", "密码应用漏洞/安全事件上报"]}, "csv_content": "一级功能模块,二级功能模块,三级功能模块,功能用户,触发事件,功能过程,子过程描述,数据移动类型,数据组,数据属性,CFP,功能点个数,预估工作量,功能过程ID,功能过程名称\n系统管理,总部一级平台对接,总部平台HTTPS对接,发起者：用户，接收者：密服平台-集省对接模块,用户在上级平台管理页面点击上级平台地址配置按钮,总部平台上报路径配置,输入上级平台配置信息,E,上报路径信息,IP、端口、协议,1,,,,\n系统管理,总部一级平台对接,总部平台HTTPS对接,发起者：用户，接收者：密服平台-集省对接模块,用户在上级平台管理页面点击上级平台地址配置按钮,总部平台上报路径配置,验证配置信息格式,R,配置规则信息,IP格式规则、端口范围规则,1,,,,\n系统管理,总部一级平台对接,总部平台HTTPS对接,发起者：用户，接收者：密服平台-集省对接模块,用户在上级平台管理页面点击上级平台地址配置按钮,总部平台上报路径配置,保存上级平台配置,W,上报路径信息,IP、端口、协议,1,,,,\n系统管理,总部一级平台对接,总部平台HTTPS对接,发起者：用户，接收者：密服平台-集省对接模块,用户在上级平台管理页面点击上级平台地址配置按钮,总部平台上报路径配置,返回配置结果,X,操作结果信息,操作状态、提示信息,1,,,,\n系统管理,总部一级平台对接,总部平台HTTPS对接,发起者：用户，接收者：密服平台-集省对接模块,用户点击上级平台配置菜单,总部平台上报路径查看,读取上级平台配置信息,R,上报路径信息,IP、端口、协议,1,,,,\n系统管理,总部一级平台对接,总部平台HTTPS对接,发起者：用户，接收者：密服平台-集省对接模块,用户点击上级平台配置菜单,总部平台上报路径查看,展示平台配置列表,X,上报路径信息,IP、端口、协议,1,,,,\n系统管理,总部一级平台对接,总部平台HTTPS对接,发起者：用户，接收者：密服平台-集省对接模块,用户点击HTTPS通道对接,总部平台HTTPS通道对接,发起HTTPS请求,E,HTTPS请求信息,HTTPS请求,1,,,,\n系统管理,总部一级平台对接,总部平台HTTPS对接,发起者：用户，接收者：密服平台-集省对接模块,用户点击HTTPS通道对接,总部平台HTTPS通道对接,HTTPS通道对接,R,HTTPS通道信息,TLS协议、加密套件,1,,,,\n系统管理,总部一级平台对接,总部平台HTTPS对接,发起者：用户，接收者：密服平台-集省对接模块,用户点击HTTPS通道对接,总部平台HTTPS通道对接,返回HTTPS响应,X,HTTPS响应信息,HTTPS响应,1,,,,\n系统管理,总部一级平台对接,总部平台AKSK认证对接,发起者：用户，接收者：密服平台-集省对接模块,用户在总部平台访问凭证配置页面点击总部平台访问凭证配置按钮,总部平台访问凭证配置,输入总部平台访问凭证配置信息,E,总部平台访问凭证,总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量,1,3.0,,,\n系统管理,总部一级平台对接,总部平台AKSK认证对接,发起者：用户，接收者：密服平台-集省对接模块,用户在总部平台访问凭证配置页面点击总部平台访问凭证配置按钮,总部平台访问凭证配置,保存总部平台访问凭证配置信息,W,总部平台访问凭证,总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量,1,3.0,,,\n系统管理,总部一级平台对接,总部平台AKSK认证对接,发起者：用户，接收者：密服平台-集省对接模块,用户在总部平台访问凭证配置页面点击总部平台访问凭证配置按钮,总部平台访问凭证配置,返回展示总部平台访问凭证配置内容,X,总部平台访问凭证,总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量,1,3.0,,,\n系统管理,总部一级平台对接,总部平台AKSK认证对接,发起者：用户，接收者：密服平台-集省对接模块,用户在总部平台访问凭证列表页面点击总部平台访问凭证查询按钮,访问凭证查看,输入总部平台访问凭证查询条件,E,总部平台访问凭证,总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量,1,2.0,,,\n系统管理,总部一级平台对接,总部平台AKSK认证对接,发起者：用户，接收者：密服平台-集省对接模块,用户在总部平台访问凭证列表页面点击总部平台访问凭证查询按钮,访问凭证查看,返回展示总部平台访问凭证查询结果,X,总部平台访问凭证,总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量,1,2.0,,,\n系统管理,总部一级平台对接,总部平台AKSK认证对接,发起者：用户，接收者：密服平台-集省对接模块,用户在总部平台访问凭证列表页面点击总部平台访问凭证认证按钮,AKSK认证,获取总部平台访问凭证,R,总部平台访问凭证,总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量,1,5.0,,,\n系统管理,总部一级平台对接,总部平台AKSK认证对接,发起者：用户，接收者：密服平台-集省对接模块,用户在总部平台访问凭证列表页面点击总部平台访问凭证认证按钮,AKSK认证,生成总部平台访问凭证密钥,R,总部平台访问凭证,总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量,1,5.0,,,\n系统管理,总部一级平台对接,总部平台AKSK认证对接,发起者：用户，接收者：密服平台-集省对接模块,用户在总部平台访问凭证列表页面点击总部平台访问凭证认证按钮,AKSK认证,总部平台访问凭证加密,R,总部平台访问凭证,总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量,1,5.0,,,\n系统管理,总部一级平台对接,总部平台AKSK认证对接,发起者：用户，接收者：密服平台-集省对接模块,用户在总部平台访问凭证列表页面点击总部平台访问凭证认证按钮,AKSK认证,总部平台访问凭证认证,X,总部平台访问凭证,总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量,1,5.0,,,\n系统管理,总部一级平台对接,总部平台AKSK认证对接,发起者：用户，接收者：密服平台-集省对接模块,用户在总部平台访问凭证列表页面点击总部平台访问凭证认证按钮,AKSK认证,总部平台访问凭证认证结果展示,X,总部平台访问凭证,总部平台访问凭证名称、总部平台访问凭证类型、总部平台访问凭证ID、总部平台访问凭证数量,1,5.0,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击用户注册信息列表查询按钮,用户注册信息列表查询,输入用户注册信息列表查询条件,E,用户注册信息列表查询请求,用户注册信息列表查询条件、用户注册信息列表查询项,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击用户注册信息列表查询按钮,用户注册信息列表查询,获取用户注册信息列表,R,用户注册信息列表,用户注册信息列表名称、用户注册信息列表类型、用户注册信息列表ID、分页数、用户注册信息列表数量,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击用户注册信息列表查询按钮,用户注册信息列表查询,返回用户注册信息列表查询结果展示,X,用户注册信息列表查询结果,用户注册信息列表名称、用户注册信息列表类型、用户注册信息列表ID、分页数、用户注册信息列表数量,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击用户注册信息列表查询按钮,用户注册信息列表查询,保存用户注册信息列表查询记录,W,用户注册信息列表查询记录,用户注册信息列表名称、用户注册信息列表类型、用户注册信息列表ID、操作人、系统时间,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击注册用户信息,注册用户信息,输入注册用户信息,E,注册用户信息,注册用户信息ID、注册用户信息名称,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击注册用户信息,注册用户信息,注册用户信息重复性校验,R,注册用户信息,注册用户信息名称、主键、非空校验、判重、注册用户信息类型,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击注册用户信息,注册用户信息,注册用户信息保存,W,注册用户信息,注册用户信息名称、注册用户信息类型、注册用户信息ID、注册用户信息数量,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击注册用户信息,注册用户信息,返回展示注册用户信息,X,注册用户信息,注册用户信息名称、注册用户信息类型、注册用户信息ID、注册用户信息数量,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击编辑用户信息,编辑用户信息,输入编辑用户信息,E,编辑用户信息,编辑用户信息ID、编辑用户信息名称,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击编辑用户信息,编辑用户信息,获取编辑用户信息,R,编辑用户信息,编辑用户信息名称、编辑用户信息类型、编辑用户信息ID,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击编辑用户信息,编辑用户信息,编辑用户信息保存,W,编辑用户信息,编辑用户信息名称、编辑用户信息类型、编辑用户信息ID,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击编辑用户信息,编辑用户信息,返回展示编辑用户信息,X,编辑用户信息,编辑用户信息名称、编辑用户信息类型、编辑用户信息ID,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击删除注册记录,删除注册记录,发起删除注册记录请求,E,删除注册记录请求,删除注册记录指令,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击删除注册记录,删除注册记录,获取删除注册记录,R,删除注册记录,删除注册记录名称、删除注册记录类型、删除注册记录ID,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击删除注册记录,删除注册记录,删除注册记录,W,删除注册记录,删除注册记录名称、删除注册记录类型、删除注册记录ID,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击删除注册记录,删除注册记录,返回展示删除注册记录结果,X,删除注册记录结果,删除注册记录名称、删除注册记录类型、删除注册记录ID、删除结果,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击用户注册审核,用户注册审核,发起用户注册审核请求,E,用户注册审核请求,用户注册审核请求参数,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击用户注册审核,用户注册审核,获取用户注册审核信息,R,用户注册审核信息,用户注册审核名称、用户注册审核类型、用户注册审核ID,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击用户注册审核,用户注册审核,用户注册审核结果数据生成,W,用户注册审核生成信息,用户注册审核名称、用户注册审核类型、用户注册审核ID、生成时间,1,,,,\n系统管理,用户认证管理,用户注册审核,发起者：用户，接收者：用户注册审核模块,用户点击用户注册审核,用户注册审核,返回展示用户注册审核结果,X,用户注册审核结果,用户注册审核名称、用户注册审核类型、用户注册审核ID、分页数,1,,,,\n系统管理,用户认证管理,用户信息管理,发起者：用户，接收者：用户信息管理模块,用户点击用户信息列表,用户信息列表查询,输入用户信息列表查询条件,E,用户信息列表查询请求,页码、单页数量、用户ID、用户名称、用户类型,1,,,,\n系统管理,用户认证管理,用户信息管理,发起者：用户，接收者：用户信息管理模块,用户点击用户信息列表,用户信息列表查询,获取用户信息列表,R,用户信息列表,用户ID、用户名称、用户类型、用户状态、用户权限、创建时间,1,,,,\n系统管理,用户认证管理,用户信息管理,发起者：用户，接收者：用户信息管理模块,用户点击用户信息列表,用户信息列表查询,返回展示用户信息列表,X,用户信息列表查询结果,页码、单页数量、用户ID、用户名称、用户类型、用户状态、用户权限、创建时间,1,,,,\n系统管理,用户认证管理,用户信息管理,发起者：用户，接收者：用户信息管理模块,用户点击启用按钮,启用用户,发起启用用户请求,E,用户ID,用户ID,1,,,,\n系统管理,用户认证管理,用户信息管理,发起者：用户，接收者：用户信息管理模块,用户点击启用按钮,启用用户,更新用户状态为启用,W,用户信息,用户ID、用户名称、用户类型、用户状态,1,,,,\n系统管理,用户认证管理,用户信息管理,发起者：用户，接收者：用户信息管理模块,用户点击禁用按钮,禁用用户,发起禁用用户请求,E,用户ID,用户ID,1,,,,\n系统管理,用户认证管理,用户信息管理,发起者：用户，接收者：用户信息管理模块,用户点击禁用按钮,禁用用户,更新用户状态为禁用,W,用户信息,用户ID、用户名称、用户类型、用户状态,1,,,,\n系统管理,用户认证管理,用户信息管理,发起者：用户，接收者：用户信息管理模块,用户点击重置密码按钮,重置用户密码,发起重置用户密码请求,E,用户ID,用户ID,1,,,,\n系统管理,用户认证管理,用户信息管理,发起者：用户，接收者：用户信息管理模块,用户点击重置密码按钮,重置用户密码,更新用户密码,W,用户信息,用户ID、用户名称、用户类型、用户状态、密码,1,,,,\n系统管理,用户认证管理,用户信息管理,发起者：用户，接收者：用户信息管理模块,用户点击解锁按钮,解锁用户锁定,发起解锁用户请求,E,用户ID,用户ID,1,,,,\n系统管理,用户认证管理,用户信息管理,发起者：用户，接收者：用户信息管理模块,用户点击解锁按钮,解锁用户锁定,更新用户锁定状态,W,用户信息,用户ID、用户名称、用户类型、用户状态,1,,,,\n系统管理,用户认证管理,用户信息管理,发起者：用户，接收者：用户信息管理模块,用户点击设置用户口令有效期按钮,设置用户的口令有效期,输入用户口令有效期,E,用户口令有效期,用户口令有效期,1,,,,\n系统管理,用户认证管理,用户信息管理,发起者：用户，接收者：用户信息管理模块,用户点击设置用户口令有效期按钮,设置用户的口令有效期,获取用户口令有效期,R,用户口令有效期,用户口令有效期,1,,,,\n系统管理,用户认证管理,用户信息管理,发起者：用户，接收者：用户信息管理模块,用户点击设置用户口令有效期按钮,设置用户的口令有效期,保存用户口令有效期,W,用户口令有效期,用户口令有效期,1,,,,\n系统管理,用户认证管理,用户信息管理,发起者：用户，接收者：用户信息管理模块,用户点击删除按钮,删除用户,发起删除用户请求,E,用户ID,用户ID,1,,,,\n系统管理,用户认证管理,用户信息管理,发起者：用户，接收者：用户信息管理模块,用户点击删除按钮,删除用户,删除用户,W,用户信息,用户ID、用户名称、用户类型,1,,,,\n系统管理,访问控制管理,用户口令管理,发起者：用户，接收者：用户口令管理模块,用户输入口令登录,口令登录,输入口令,E,口令,口令,1,,3.0,,\n系统管理,访问控制管理,用户口令管理,发起者：用户，接收者：用户口令管理模块,用户输入口令登录,口令登录,验证口令,R,口令,口令,1,,3.0,,\n系统管理,访问控制管理,用户口令管理,发起者：用户，接收者：用户口令管理模块,用户输入口令登录,口令登录,口令登录,X,口令,口令,1,,3.0,,\n系统管理,访问控制管理,用户口令管理,发起者：用户，接收者：用户口令管理模块,用户使用Ukey登录,Ukey登录,Ukey插入,E,Ukey,Ukey,1,,5.0,,\n系统管理,访问控制管理,用户口令管理,发起者：用户，接收者：用户口令管理模块,用户使用Ukey登录,Ukey登录,Ukey验证,R,Ukey,Ukey,1,,5.0,,\n系统管理,访问控制管理,用户口令管理,发起者：用户，接收者：用户口令管理模块,用户使用Ukey登录,Ukey登录,Ukey登录,X,Ukey,Ukey,1,,5.0,,\n系统管理,访问控制管理,用户口令管理,发起者：用户，接收者：用户口令管理模块,用户使用Ukey登录,Ukey登录,Ukey口令验证,R,口令,口令,1,,5.0,,\n系统管理,访问控制管理,用户口令管理,发起者：用户，接收者：用户口令管理模块,用户使用Ukey登录,Ukey登录,Ukey登录结果,X,口令,口令,1,,5.0,,\n系统管理,访问控制管理,用户口令管理,发起者：用户，接收者：用户口令管理模块,用户点击口令黑名单列表,口令黑名单列表查询,输入口令黑名单列表查询条件,E,口令黑名单列表查询请求,口令黑名单列表查询条件、口令黑名单列表查询项,1,,2.0,,\n系统管理,访问控制管理,用户口令管理,发起者：用户，接收者：用户口令管理模块,用户点击口令黑名单列表,口令黑名单列表查询,获取口令黑名单列表,R,口令黑名单列表,口令黑名单列表名称、口令黑名单列表类型、口令黑名单列表ID,1,,2.0,,\n系统管理,访问控制管理,用户口令管理,发起者：用户，接收者：用户口令管理模块,用户点击口令黑名单列表,口令黑名单列表查询,返回展示口令黑名单列表查询结果,X,口令黑名单列表查询结果,口令黑名单列表名称、口令黑名单列表类型、口令黑名单列表ID、查询时间,1,,2.0,,\n系统管理,访问控制管理,用户口令管理,发起者：用户，接收者：用户口令管理模块,用户新建口令黑名单,新建口令黑名单,输入新建口令黑名单信息,E,口令黑名单新建信息,口令黑名单新建ID、口令黑名单新建名称,1,,3.0,,\n系统管理,访问控制管理,用户口令管理,发起者：用户，接收者：用户口令管理模块,用户新建口令黑名单,新建口令黑名单,口令黑名单重复性校验,R,口令黑名单校验信息,口令黑名单名称、主键、非空校验、判重、口令黑名单类型,1,,3.0,,\n系统管理,访问控制管理,用户口令管理,发起者：用户，接收者：用户口令管理模块,用户新建口令黑名单,新建口令黑名单,口令黑名单新建保存,W,口令黑名单新建结果,口令黑名单名称、口令黑名单类型、口令黑名单ID、新建结果,1,,3.0,,\n系统管理,访问控制管理,用户口令管理,发起者：用户，接收者：用户口令管理模块,用户编辑口令黑名单,编辑口令黑名单,输入编辑口令黑名单信息,E,口令黑名单编辑条件,口令黑名单ID,1,,2.0,,\n系统管理,访问控制管理,用户口令管理,发起者：用户，接收者：用户口令管理模块,用户编辑口令黑名单,编辑口令黑名单,口令黑名单编辑保存,W,口令黑名单编辑结果,编辑时间、口令黑名单名称、口令黑名单类型、口令黑名单ID,1,,2.0,,\n系统管理,访问控制管理,用户口令管理,发起者：用户，接收者：用户口令管理模块,用户删除口令黑名单,删除口令黑名单,发起口令黑名单删除请求,E,口令黑名单删除请求,口令黑名单删除参数,1,,2.0,,\n系统管理,访问控制管理,用户口令管理,发起者：用户，接收者：用户口令管理模块,用户删除口令黑名单,删除口令黑名单,口令黑名单删除保存,W,口令黑名单删除结果,口令黑名单名称、口令黑名单类型、口令黑名单ID、删除结果,1,,2.0,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙列表,智能密码钥匙列表,输入智能密码钥匙列表查询条件,E,智能密码钥匙列表查询请求,智能密码钥匙列表查询条件、智能密码钥匙列表查询项,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙列表,智能密码钥匙列表,读取智能密码钥匙列表,R,智能密码钥匙列表,智能密码钥匙列表名称、智能密码钥匙列表类型、智能密码钥匙列表ID,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙列表,智能密码钥匙列表,返回智能密码钥匙列表查询结果展示,X,智能密码钥匙列表查询结果,智能密码钥匙列表名称、智能密码钥匙列表类型、智能密码钥匙列表ID、查询时间,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙列表,智能密码钥匙列表,保存智能密码钥匙列表查询记录,W,智能密码钥匙列表查询记录,智能密码钥匙列表名称、智能密码钥匙列表类型、智能密码钥匙列表ID、操作人、系统时间,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙新增,智能密码钥匙新增,输入智能密码钥匙新增信息,E,智能密码钥匙新增信息,智能密码钥匙新增ID、智能密码钥匙新增名称,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙新增,智能密码钥匙新增,智能密码钥匙重复性校验,R,智能密码钥匙校验信息,智能密码钥匙名称、主键、非空校验、判重、智能密码钥匙类型,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙新增,智能密码钥匙新增,智能密码钥匙新增入库,W,智能密码钥匙新增内容,智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID、新增时间,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙新增,智能密码钥匙新增,返回展示智能密码钥匙新增内容,X,智能密码钥匙新增内容,智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID、新增结果,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙启用,智能密码钥匙启用,发起智能密码钥匙启用请求,E,智能密码钥匙启用请求,智能密码钥匙启用指令,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙启用,智能密码钥匙启用,获取智能密码钥匙,R,智能密码钥匙,智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙启用,智能密码钥匙启用,智能密码钥匙启用保存,W,智能密码钥匙启用结果,智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID、启用结果,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙启用,智能密码钥匙启用,返回展示智能密码钥匙启用结果,X,智能密码钥匙启用结果展示信息,智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID、启用内容,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙禁用,智能密码钥匙禁用,发起智能密码钥匙禁用请求,E,智能密码钥匙禁用请求,智能密码钥匙禁用指令,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙禁用,智能密码钥匙禁用,获取智能密码钥匙,R,智能密码钥匙,智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙禁用,智能密码钥匙禁用,智能密码钥匙禁用保存,W,智能密码钥匙禁用结果,智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID、禁用结果,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙禁用,智能密码钥匙禁用,返回展示智能密码钥匙禁用结果,X,智能密码钥匙禁用结果展示信息,智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID、禁用内容,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙删除,智能密码钥匙删除,发起智能密码钥匙删除请求,E,智能密码钥匙删除请求,智能密码钥匙删除指令,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙删除,智能密码钥匙删除,获取操作员权限并判断智能密码钥匙是否可删除,R,智能密码钥匙删除权限,智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID、操作员权限,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙删除,智能密码钥匙删除,智能密码钥匙删除保存,W,智能密码钥匙删除结果,智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID、删除结果,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击智能密码钥匙删除,智能密码钥匙删除,返回展示智能密码钥匙删除内容,X,智能密码钥匙删除内容,智能密码钥匙名称、智能密码钥匙类型、智能密码钥匙ID、删除内容,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击是否开启口令登录,是否开启口令登录,发起是否开启口令登录请求,E,是否开启口令登录请求,是否开启口令登录指令,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击是否开启口令登录,是否开启口令登录,获取是否开启口令登录,R,是否开启口令登录,是否开启口令登录名称、是否开启口令登录类型、是否开启口令登录ID,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击是否开启口令登录,是否开启口令登录,是否开启口令登录保存,W,是否开启口令登录结果,是否开启口令登录名称、是否开启口令登录类型、是否开启口令登录ID、保存内容,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击是否开启口令登录,是否开启口令登录,返回展示是否开启口令登录内容,X,是否开启口令登录内容,是否开启口令登录名称、是否开启口令登录类型、是否开启口令登录ID、展示内容,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击是否开启UKey登录,是否开启UKey登录,发起是否开启UKey登录请求,E,是否开启UKey登录请求,是否开启UKey登录指令,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击是否开启UKey登录,是否开启UKey登录,获取是否开启UKey登录,R,是否开启UKey登录,是否开启UKey登录名称、是否开启UKey登录类型、是否开启UKey登录ID,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击是否开启UKey登录,是否开启UKey登录,是否开启UKey登录保存,W,是否开启UKey登录结果,是否开启UKey登录名称、是否开启UKey登录类型、是否开启UKey登录ID、保存内容,1,,,,\n系统管理,访问控制管理,用户ukey策略管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击是否开启UKey登录,是否开启UKey登录,返回展示是否开启UKey登录内容,X,是否开启UKey登录内容,是否开启UKey登录名称、是否开启UKey登录类型、是否开启UKey登录ID、展示内容,1,,,,\n系统管理,访问控制管理,用户口令策略管理,发起者：用户，接收者：用户口令策略模块,用户在用户口令策略页面点击设置用户默认口令按钮,设置用户默认口令,输入用户默认口令,E,用户口令策略,用户默认口令、用户默认口令类型,1,,,,\n系统管理,访问控制管理,用户口令策略管理,发起者：用户，接收者：用户口令策略模块,用户在用户口令策略页面点击设置用户默认口令按钮,设置用户默认口令,保存用户默认口令,W,用户口令策略,用户默认口令、用户默认口令类型、保存时间,1,,,,\n系统管理,访问控制管理,用户口令策略管理,发起者：用户，接收者：用户口令策略模块,用户在用户口令策略页面点击历史口令限制次数按钮,历史口令限制次数,输入历史口令限制次数,E,用户口令策略,历史口令限制次数,1,,,,\n系统管理,访问控制管理,用户口令策略管理,发起者：用户，接收者：用户口令策略模块,用户在用户口令策略页面点击历史口令限制次数按钮,历史口令限制次数,保存历史口令限制次数,W,用户口令策略,历史口令限制次数、保存时间,1,,,,\n系统管理,访问控制管理,用户口令策略管理,发起者：用户，接收者：用户口令策略模块,用户在用户口令策略页面点击长时间未登录禁用账户天数按钮,长时间未登录禁用账户天数,输入长时间未登录禁用账户天数,E,用户口令策略,长时间未登录禁用账户天数,1,,,,\n系统管理,访问控制管理,用户口令策略管理,发起者：用户，接收者：用户口令策略模块,用户在用户口令策略页面点击长时间未登录禁用账户天数按钮,长时间未登录禁用账户天数,保存长时间未登录禁用账户天数,W,用户口令策略,长时间未登录禁用账户天数、保存时间,1,,,,\n系统管理,访问控制管理,用户口令策略管理,发起者：用户，接收者：用户口令策略模块,用户在用户口令策略页面点击口令有效期天数按钮,口令有效期天数,输入口令有效期天数,E,用户口令策略,口令有效期天数,1,,,,\n系统管理,访问控制管理,用户口令策略管理,发起者：用户，接收者：用户口令策略模块,用户在用户口令策略页面点击口令有效期天数按钮,口令有效期天数,保存口令有效期天数,W,用户口令策略,口令有效期天数、保存时间,1,,,,\n系统管理,访问控制管理,用户口令策略管理,发起者：用户，接收者：用户口令策略模块,用户在用户口令策略页面点击口令有效期告警天数按钮,口令有效期告警天数,输入口令有效期告警天数,E,用户口令策略,口令有效期告警天数,1,,,,\n系统管理,访问控制管理,用户口令策略管理,发起者：用户，接收者：用户口令策略模块,用户在用户口令策略页面点击口令有效期告警天数按钮,口令有效期告警天数,保存口令有效期告警天数,W,用户口令策略,口令有效期告警天数、保存时间,1,,,,\n系统管理,访问控制管理,用户口令策略管理,发起者：用户，接收者：用户口令策略模块,用户在用户口令策略页面点击登录失败次数限制次数按钮,登录失败次数限制次数,输入登录失败次数限制次数,E,用户口令策略,登录失败次数限制次数,1,,,,\n系统管理,访问控制管理,用户口令策略管理,发起者：用户，接收者：用户口令策略模块,用户在用户口令策略页面点击登录失败次数限制次数按钮,登录失败次数限制次数,保存登录失败次数限制次数,W,用户口令策略,登录失败次数限制次数、保存时间,1,,,,\n系统管理,访问控制管理,用户口令策略管理,发起者：用户，接收者：用户口令策略模块,用户在用户口令策略页面点击登录失败锁定时长(分钟)按钮,登录失败锁定时长(分钟),输入登录失败锁定时长,E,用户口令策略,登录失败锁定时长,1,,,,\n系统管理,访问控制管理,用户口令策略管理,发起者：用户，接收者：用户口令策略模块,用户在用户口令策略页面点击登录失败锁定时长(分钟)按钮,登录失败锁定时长(分钟),保存登录失败锁定时长,W,用户口令策略,登录失败锁定时长、保存时间,1,,,,\n系统管理,访问控制管理,用户口令策略管理,发起者：用户，接收者：用户口令策略模块,用户在用户口令策略页面点击是否强制修改默认口令按钮,是否强制修改默认口令,输入是否强制修改默认口令,E,用户口令策略,是否强制修改默认口令,1,,,,\n系统管理,访问控制管理,用户口令策略管理,发起者：用户，接收者：用户口令策略模块,用户在用户口令策略页面点击是否强制修改默认口令按钮,是否强制修改默认口令,保存是否强制修改默认口令,W,用户口令策略,是否强制修改默认口令、保存时间,1,,,,\n系统管理,上报周期管理,上报周期及频率管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击上报内容列表,上报内容列表,输入查询条件,E,查询条件信息,页码、单页数量、上报内容名称,1,,,,\n系统管理,上报周期管理,上报周期及频率管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击上报内容列表,上报内容列表,读取上报内容列表,R,上报内容信息,上报内容ID、上报内容名称、上报内容类型、上报内容数量,1,,,,\n系统管理,上报周期管理,上报周期及频率管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击上报内容列表,上报内容列表,返回展示上报内容列表,X,上报内容信息,上报内容ID、上报内容名称、上报内容类型、上报内容数量,1,,,,\n系统管理,上报周期管理,上报周期及频率管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击上报内容列表,上报内容列表,保存上报内容列表,W,上报内容信息,上报内容ID、上报内容名称、上报内容类型、上报内容数量,1,,,,\n系统管理,上报周期管理,上报周期及频率管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击上报内容配置,上报内容配置,输入上报内容配置信息,E,上报内容信息,上报内容ID、上报内容名称、是否开启,1,,,,\n系统管理,上报周期管理,上报周期及频率管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击上报内容配置,上报内容配置,读取上报内容配置信息,R,上报内容信息,上报内容ID、上报内容名称、是否开启,1,,,,\n系统管理,上报周期管理,上报周期及频率管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击上报内容配置,上报内容配置,上报内容配置保存,W,上报内容信息,上报内容ID、上报内容名称、是否开启,1,,,,\n系统管理,上报周期管理,上报周期及频率管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击上报内容配置,上报内容配置,返回展示上报内容配置结果,X,上报内容信息,上报内容ID、上报内容名称、是否开启,1,,,,\n系统管理,上报周期管理,上报周期及频率管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击上报频率配置,上报频率配置,输入上报频率配置信息,E,上报频率信息,上报频率ID、上报频率名称、上报频率类型,1,,,,\n系统管理,上报周期管理,上报周期及频率管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击上报频率配置,上报频率配置,获取上报频率字典,R,上报频率信息,上报频率ID、上报频率名称、上报频率类型,1,,,,\n系统管理,上报周期管理,上报周期及频率管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击上报频率配置,上报频率配置,上报频率配置保存,W,上报频率信息,上报频率ID、上报频率名称、上报频率类型,1,,,,\n系统管理,上报周期管理,上报周期及频率管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击上报频率配置,上报频率配置,上报内容更新,R,上报内容信息,上报内容ID、上报内容名称、上报频率,1,,,,\n系统管理,上报周期管理,上报周期及频率管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击上报频率配置,上报频率配置,上报频率配置更新,W,上报频率信息,上报频率ID、上报内容名称、上报频率,1,,,,\n系统管理,上报周期管理,上报周期及频率管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击上报频率配置,上报频率配置,返回展示上报频率配置结果,X,操作结果信息,操作结果,1,,,,\n系统管理,上报周期管理,上报周期及频率管理,发起者：用户，接收者：密服平台-集省对接模块,用户点击上报频率配置,上报频率配置,上报频率配置归档,W,上报频率信息,上报频率ID、上报频率名称、归档时间,1,,,,\n系统管理,日志管理/统计分析,登录日志管理,发起者：用户，接收者：密服平台-日志管理模块,用户在登录日志管理页面点击查询按钮,查询登录日志,输入登录日志查询条件,E,登录日志查询请求,页码、单页数量、登录日志ID、登录时间、登录结果、登录人,1,,,,\n系统管理,日志管理/统计分析,登录日志管理,发起者：用户，接收者：密服平台-日志管理模块,用户在登录日志管理页面点击查询按钮,查询登录日志,获取登录日志,R,登录日志信息,登录日志ID、登录时间、登录结果、登录人,1,,,,\n系统管理,日志管理/统计分析,登录日志管理,发起者：用户，接收者：密服平台-日志管理模块,用户在登录日志管理页面点击查询按钮,查询登录日志,返回登录日志查询结果展示,X,登录日志查询结果,登录日志ID、登录时间、登录结果、登录人、分页数,1,,,,\n系统管理,日志管理/统计分析,登录日志管理,发起者：用户，接收者：密服平台-日志管理模块,用户在登录日志管理页面点击批量审计按钮,批量审计,发起登录日志批量审计请求,E,登录日志批量审计请求,登录日志批量审计参数,1,,,,\n系统管理,日志管理/统计分析,登录日志管理,发起者：用户，接收者：密服平台-日志管理模块,用户在登录日志管理页面点击批量审计按钮,批量审计,获取需要批量审计的登录日志,R,登录日志信息,登录日志ID、登录时间、登录结果、登录人,1,,,,\n系统管理,日志管理/统计分析,登录日志管理,发起者：用户，接收者：密服平台-日志管理模块,用户在登录日志管理页面点击批量审计按钮,批量审计,登录日志批量审计保存,W,登录日志批量审计结果,登录日志ID、登录时间、登录结果、登录人、审计结果,1,,,,\n系统管理,日志管理/统计分析,登录日志管理,发起者：用户，接收者：密服平台-日志管理模块,用户在登录日志管理页面点击批量审计按钮,批量审计,返回展示登录日志批量审计结果,X,登录日志批量审计内容,登录日志ID、登录时间、登录结果、登录人、审计结果,1,,,,\n系统管理,日志管理/统计分析,登录日志管理,发起者：用户，接收者：密服平台-日志管理模块,用户在登录日志管理页面点击导出按钮,日志导出,发起登录日志导出请求,E,登录日志导出请求,登录日志导出参数,1,,,,\n系统管理,日志管理/统计分析,登录日志管理,发起者：用户，接收者：密服平台-日志管理模块,用户在登录日志管理页面点击导出按钮,日志导出,获取登录日志,R,登录日志信息,登录日志ID、登录时间、登录结果、登录人,1,,,,\n系统管理,日志管理/统计分析,登录日志管理,发起者：用户，接收者：密服平台-日志管理模块,用户在登录日志管理页面点击导出按钮,日志导出,登录日志导出,X,登录日志导出内容,登录日志ID、登录时间、登录结果、登录人、文件格式,1,,,,\n系统管理,日志管理/统计分析,操作日志管理,发起者：用户，接收者：密服平台-操作日志管理模块,用户在操作日志管理页面点击操作日志查询按钮,操作日志查询,输入操作日志查询条件,E,操作日志查询请求,操作日志查询条件、操作日志查询项,1,,,,\n系统管理,日志管理/统计分析,操作日志管理,发起者：用户，接收者：密服平台-操作日志管理模块,用户在操作日志管理页面点击操作日志查询按钮,操作日志查询,读取操作日志,R,操作日志,操作日志名称、操作日志类型、操作日志ID、操作人、操作时间,1,,,,\n系统管理,日志管理/统计分析,操作日志管理,发起者：用户，接收者：密服平台-操作日志管理模块,用户在操作日志管理页面点击操作日志查询按钮,操作日志查询,返回操作日志查询结果展示,X,操作日志查询结果,操作日志名称、操作日志类型、操作日志ID、分页数、操作人、操作时间,1,,,,\n系统管理,日志管理/统计分析,操作日志管理,发起者：用户，接收者：密服平台-操作日志管理模块,用户在操作日志管理页面点击操作日志查询按钮,操作日志查询,保存操作日志查询记录,W,操作日志查询记录,操作日志名称、操作日志类型、操作日志ID、操作人、系统时间,1,,,,\n系统管理,日志管理/统计分析,操作日志管理,发起者：用户，接收者：密服平台-操作日志管理模块,用户在操作日志管理页面点击批量审批按钮,批量审批,发起批量审批请求,E,批量审批请求,批量审批参数,1,,,,\n系统管理,日志管理/统计分析,操作日志管理,发起者：用户，接收者：密服平台-操作日志管理模块,用户在操作日志管理页面点击批量审批按钮,批量审批,获取批量审批信息,R,批量审批信息,批量审批数量、批量审批ID,1,,,,\n系统管理,日志管理/统计分析,操作日志管理,发起者：用户，接收者：密服平台-操作日志管理模块,用户在操作日志管理页面点击批量审批按钮,批量审批,批量审批信息保存,W,批量审批结果,批量审批结果、批量审批时间、批量审批操作员,1,,,,\n系统管理,日志管理/统计分析,操作日志管理,发起者：用户，接收者：密服平台-操作日志管理模块,用户在操作日志管理页面点击批量审批按钮,批量审批,返回展示批量审批结果,X,批量审批结果展示信息,批量审批结果、批量审批内容,1,,,,\n系统管理,日志管理/统计分析,操作日志管理,发起者：用户，接收者：密服平台-操作日志管理模块,用户在操作日志管理页面点击日志导出按钮,日志导出,输入日志导出参数,E,日志导出请求,日志导出参数,1,,,,\n系统管理,日志管理/统计分析,操作日志管理,发起者：用户，接收者：密服平台-操作日志管理模块,用户在操作日志管理页面点击日志导出按钮,日志导出,读取日志,R,日志,日志名称、日志类型、日志ID,1,,,,\n系统管理,日志管理/统计分析,操作日志管理,发起者：用户，接收者：密服平台-操作日志管理模块,用户在操作日志管理页面点击日志导出按钮,日志导出,日志导出,X,日志导出内容,日志名称、日志类型、日志ID、导出时间,1,,,,\n系统管理,日志管理/统计分析,操作日志管理,发起者：用户，接收者：密服平台-操作日志管理模块,用户在操作日志管理页面点击日志导出按钮,日志导出,日志导出数据归档,W,日志导出记录,日志名称、日志类型、日志ID、归档时间,1,,,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击分页查询按钮,密码应用类型分页列表查询,输入分页查询条件,E,分页查询条件,分页数、分页大小,1,,3.0,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击分页查询按钮,密码应用类型分页列表查询,读取密码应用类型分页列表,R,密码应用类型信息,类型编码、类型名称、备注、创建时间,1,,3.0,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击分页查询按钮,密码应用类型分页列表查询,返回密码应用类型分页列表查询结果展示,X,密码应用类型分页列表,类型编码、类型名称、备注、创建时间、分页数、分页大小,1,,3.0,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击分页查询按钮,密码应用类型分页列表查询,保存密码应用类型分页列表查询记录,W,密码应用类型分页列表,类型编码、类型名称、备注、创建时间、分页数、分页大小,1,,3.0,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击过滤查询按钮,密码应用类型过滤查询,输入过滤条件,E,过滤条件,类型名称、类型编码,1,,3.0,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击过滤查询按钮,密码应用类型过滤查询,读取密码应用类型,R,密码应用类型信息,类型编码、类型名称、备注,1,,3.0,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击过滤查询按钮,密码应用类型过滤查询,返回密码应用类型过滤查询结果,X,密码应用类型过滤查询结果,类型编码、类型名称、备注、过滤维度,1,,3.0,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击新增按钮,新增密码应用类型,输入新增密码应用类型信息,E,密码应用类型信息,类型编码、类型名称、备注,1,,4.0,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击新增按钮,新增密码应用类型,密码应用类型重复性校验,R,密码应用类型约束条件,类型编码、类型名称、非空校验、判重、类型名称长度,1,,4.0,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击新增按钮,新增密码应用类型,新增密码应用类型入库,W,密码应用类型信息,类型编码、类型名称、备注,1,,4.0,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击新增按钮,新增密码应用类型,返回展示新增密码应用类型,X,密码应用类型新增内容,类型编码、类型名称、备注、新增结果,1,,4.0,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击编辑按钮,编辑密码应用类型,输入编辑密码应用类型信息,E,密码应用类型编辑信息,类型编码、类型名称、备注,1,,3.0,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击编辑按钮,编辑密码应用类型,获取密码应用类型编辑项,R,密码应用类型编辑项,类型编码、类型名称、备注,1,,3.0,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击编辑按钮,编辑密码应用类型,密码应用类型编辑保存,W,密码应用类型编辑结果,类型编码、类型名称、备注、编辑人,1,,3.0,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击编辑按钮,编辑密码应用类型,返回展示密码应用类型编辑结果,X,密码应用类型编辑结果,类型编码、类型名称、备注、编辑结果,1,,3.0,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击删除按钮,删除密码应用类型,发起密码应用类型删除请求,E,密码应用类型删除请求,密码应用类型ID、删除参数,1,,4.0,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击删除按钮,删除密码应用类型,获取操作员权限并判断密码应用类型是否可删除,R,密码应用类型删除权限,操作员权限、密码应用类型名称、密码应用类型ID、操作员ID,1,,4.0,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击删除按钮,删除密码应用类型,密码应用类型删除保存,W,密码应用类型删除结果,密码应用类型名称、密码应用类型ID、删除结果,1,,4.0,,\n密码应用数据管理,密码应用类型管理,密码应用类型管理,发起者：用户，接收者：密码服务管理平台,用户点击删除按钮,删除密码应用类型,返回展示密码应用类型删除内容,X,密码应用类型删除内容,密码应用类型名称、密码应用类型ID、删除内容,1,,4.0,,\n密码应用数据管理,密码应用类型管理,应用关联应用类型,发起者：用户，接收者：密码服务管理平台-密码应用类型管理模块,用户点击密码应用类型下拉框,密码应用类型下拉选择,获取密码应用类型名称和ID,R,密码应用类型,密码应用类型名称、密码应用类型ID,1,,,,\n密码应用数据管理,密码应用类型管理,应用关联应用类型,发起者：用户，接收者：密码服务管理平台-密码应用类型管理模块,用户点击密码应用类型下拉框,密码应用类型下拉选择,返回密码应用类型名称和ID,X,密码应用类型,密码应用类型名称、密码应用类型ID,1,,,,\n密码应用数据管理,密码应用类型管理,应用关联应用类型,发起者：用户，接收者：密码服务管理平台-密码应用类型管理模块,用户点击密码应用类型下拉框,密码应用类型下拉选择,保存密码应用类型名称和ID,W,密码应用类型,密码应用类型名称、密码应用类型ID,1,,,,\n密码应用数据管理,密码应用类型管理,应用关联应用类型,发起者：用户，接收者：密码服务管理平台-密码应用类型管理模块,用户点击密码应用类型应用数量分布,密码应用类型应用数量分布,获取密码应用类型名称,R,密码应用类型,密码应用类型名称、密码应用类型ID,1,,,,\n密码应用数据管理,密码应用类型管理,应用关联应用类型,发起者：用户，接收者：密码服务管理平台-密码应用类型管理模块,用户点击密码应用类型应用数量分布,密码应用类型应用数量分布,统计密码应用类型应用数量,R,应用数量,密码应用类型名称、密码应用类型ID、应用数量,1,,,,\n密码应用数据管理,密码应用类型管理,应用关联应用类型,发起者：用户，接收者：密码服务管理平台-密码应用类型管理模块,用户点击密码应用类型应用数量分布,密码应用类型应用数量分布,返回密码应用类型应用数量分布,X,密码应用类型应用数量分布,密码应用类型名称、密码应用类型ID、应用数量,1,,,,\n密码应用数据管理,密码应用类型管理,应用关联应用类型,发起者：用户，接收者：密码服务管理平台-密码应用类型管理模块,用户点击密码应用类型应用数量分布,密码应用类型应用数量分布,保存密码应用类型应用数量分布,W,密码应用类型应用数量分布,密码应用类型名称、密码应用类型ID、应用数量,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击密码应用分页列表,密码应用分页列表查询,输入密码应用分页列表查询条件,E,密码应用分页列表查询请求,密码应用分页列表查询条件、密码应用分页列表查询项,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击密码应用分页列表,密码应用分页列表查询,读取密码应用分页列表,R,密码应用分页列表,密码应用分页列表名称、密码应用分页列表类型、密码应用分页列表ID,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击密码应用分页列表,密码应用分页列表查询,返回密码应用分页列表查询结果展示,X,密码应用分页列表查询结果,密码应用分页列表名称、密码应用分页列表类型、密码应用分页列表ID、查询时间,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击密码应用分页列表,密码应用分页列表查询,保存密码应用分页列表查询记录,W,密码应用分页列表查询记录,密码应用分页列表名称、密码应用分页列表类型、密码应用分页列表ID、操作人、系统时间,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击密码应用过滤查询,密码应用过滤查询,输入密码应用过滤查询条件,E,密码应用过滤查询请求,密码应用过滤查询条件、密码应用过滤查询项,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击密码应用过滤查询,密码应用过滤查询,读取密码应用过滤查询结果,R,密码应用过滤查询结果,密码应用过滤查询维度、密码应用过滤查询时间、密码应用过滤查询数量,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击密码应用过滤查询,密码应用过滤查询,输出密码应用过滤查询结果,X,密码应用过滤查询结果展示,密码应用过滤查询维度、密码应用过滤查询时间、密码应用过滤查询数量、分页数、系统时间,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击新增密码应用,新增密码应用,输入新增密码应用信息,E,新增密码应用信息,密码应用ID、密码应用名称、密码应用类型,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击新增密码应用,新增密码应用,密码应用重复性校验,R,密码应用校验信息,密码应用约束条件、密码应用名称、主键、非空校验、判重、密码应用类型,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击新增密码应用,新增密码应用,新增密码应用入库,W,新增密码应用入库信息,密码应用ID、密码应用名称、密码应用类型、新增时间,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击新增密码应用,新增密码应用,返回展示新增密码应用,X,新增密码应用展示信息,密码应用ID、密码应用名称、密码应用类型、新增结果,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击新增密码应用,新增密码应用,记录新增密码应用日志,W,新增密码应用日志,密码应用ID、密码应用名称、密码应用类型、操作人、系统时间,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击新增密码应用,新增密码应用,新增密码应用数据统计,W,新增密码应用统计数据,密码应用ID、密码应用名称、密码应用类型、统计维度、统计时间,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击编辑密码应用,编辑密码应用,输入编辑密码应用信息,E,编辑密码应用信息,密码应用ID、密码应用名称、密码应用类型,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击编辑密码应用,编辑密码应用,获取密码应用编辑项,R,密码应用编辑项,密码应用约束条件、密码应用名称、主键、非空校验、判重、密码应用类型,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击编辑密码应用,编辑密码应用,密码应用编辑保存,W,密码应用编辑结果,密码应用ID、密码应用名称、密码应用类型、编辑内容、编辑结果,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击编辑密码应用,编辑密码应用,返回展示编辑密码应用,X,密码应用编辑结果展示,密码应用ID、密码应用名称、密码应用类型、编辑内容、编辑结果,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击删除密码应用,删除密码应用,发起删除密码应用请求,E,删除密码应用请求,删除密码应用指令,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击删除密码应用,删除密码应用,获取操作员权限并判断密码应用是否可删除,R,删除权限,操作员权限、操作员ID、操作员名称,1,,,,\n密码应用数据管理,密码应用管理,密码应用管理,发起者：用户，接收者：密码服务管理平台,用户点击删除密码应用,删除密码应用,密码应用删除保存,W,密码应用删除结果,密码应用ID、密码应用名称、密码应用类型、删除结果,1,,,,", "validation_focus": {"completeness_check": "每个功能过程是否包含至少1个E和1个X", "data_group_aggregation": "是否将关联数据属性合并为最小单元", "storage_boundary": "R/W是否仅针对边界内持久存储", "no_duplicate_counting": "同一数据组在同一功能过程中是否被重复计数"}}