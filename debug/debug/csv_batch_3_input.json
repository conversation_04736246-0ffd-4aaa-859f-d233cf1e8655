{"data_format": "CSV", "batch_info": {"batch_index": 3, "total_batches": 6, "batch_size": 200, "data_range": "第401行到第600行"}, "summary": {"total_records": 1177, "columns": ["一级功能模块", "二级功能模块", "三级功能模块", "功能用户", "触发事件", "功能过程", "子过程描述", "数据移动类型", "数据组", "数据属性", "CFP", "功能点个数", "预估工作量", "功能过程ID", "功能过程名称"], "data_movement_types": {"E": 307, "R": 299, "X": 296, "W": 275}, "cfp_distribution": {"1": 1177}, "level1_modules": ["系统管理", "密码应用数据管理", "密码资产数据管理", "密码应用测评管理", "密码应用漏洞/安全事件管理", "数据上报接口", "合计"], "level2_modules": ["总部一级平台对接", "用户认证管理", "访问控制管理", "上报周期管理", "日志管理/统计分析", "密码应用类型管理", "密码应用管理", "密码应用场景管理", "密码应用改造厂商管理", "密码资产名称管理", "密码资产数据管理", "密码产品证书及编号管理", "密钥信息管理", "密码文档信息管理", "改造阶段管理", "应用测评报告、测评分数管理", "密码应用方案管理", "密评机构管理", "密码漏洞/安全事件类型管理", "漏洞/安全事件级别管理", "漏洞/安全事件详情管理", "密码产品监控范围管理", "密码应用数据上报类接口", "密码资产数据上报类接口", "密码应用测评数据上报类接口", "密码应用漏洞/安全事件上报类接口"], "level3_modules": ["总部平台HTTPS对接", "总部平台AKSK认证对接", "用户注册审核", "用户信息管理", "用户口令管理", "用户ukey策略管理", "用户口令策略管理", "上报周期及频率管理", "登录日志管理", "操作日志管理", "密码应用类型管理", "应用关联应用类型", "密码应用管理", "密码应用认证凭证管理", "密码应用业务管理", "密码应用场景管理", "密码应用改造厂商管理", "密码服务管理", "密码服务组管理", "密码服务镜像管理", "密码服务数据库管理", "密码服务数据库模式管理", "API网关管理", "网关路由管理", "设备类型管理", "密码设备集群管理", "云密码机管理", "云密码机虚机网络管理", "虚拟密码机管理", "物理密码机管理", "保护主密钥管理", "用户证书管理", "应用证书管理", "密钥及生命周期管理", "密码文档信息管理", "密码应用测评管理", "应用测评管理", "应用测评方案管理", "应用测评模板管理", "密评进度推进管理", "密评进度跟踪报告管理", "密码应用测评差距管理", "密评机构管理", "密码漏洞/安全事件类型管理", "漏洞/安全事件管理", "漏洞/安全事件通知人管理", "告警邮箱配置管理", "漏洞/安全事件详情管理", "密码产品监控范围管理", "密码应用数据上报管理", "密码产品信息上报", "密钥信息上报", "证书信息上报", "密码文档信息上报", "应用测评信息上报", "密码应用漏洞/安全事件上报"]}, "csv_content": "一级功能模块,二级功能模块,三级功能模块,功能用户,触发事件,功能过程,子过程描述,数据移动类型,数据组,数据属性,CFP,功能点个数,预估工作量,功能过程ID,功能过程名称\n密码资产数据管理,密码资产数据管理,API网关管理,发起者：用户，接收者：密码服务平台,用户点击网关列表菜单,API网关列表,读取网关列表,R,API网关信息,网关名称、所属区域、网关类型、网关ID、分页数、分页大小,1,,,,\n密码资产数据管理,密码资产数据管理,API网关管理,发起者：用户，接收者：密码服务平台,用户点击网关列表菜单,API网关列表,返回网关列表,X,API网关信息,网关名称、所属区域、网关类型、网关ID、分页数、分页大小,1,,,,\n密码资产数据管理,密码资产数据管理,API网关管理,发起者：用户，接收者：密码服务平台,用户点击网关列表菜单,API网关列表,保存网关列表,W,API网关信息,网关名称、所属区域、网关类型、网关ID、分页数、分页大小,1,,,,\n密码资产数据管理,密码资产数据管理,API网关管理,发起者：平台，接收者：密码服务平台,平台部署成功,API网关初始化,获取平台部署信息,E,API网关信息,网关名称、所属区域、网关类型、网关ID,1,,,,\n密码资产数据管理,密码资产数据管理,API网关管理,发起者：平台，接收者：密码服务平台,平台部署成功,API网关初始化,保存网关信息,W,API网关信息,网关名称、所属区域、网关类型、网关ID,1,,,,\n密码资产数据管理,密码资产数据管理,API网关管理,发起者：平台，接收者：密码服务平台,平台部署成功,API网关初始化,返回网关初始化结果,X,API网关信息,网关名称、所属区域、网关类型、网关ID,1,,,,\n密码资产数据管理,密码资产数据管理,API网关管理,发起者：用户，接收者：密码服务平台,用户点击新增按钮,API网关新增,输入新增网关信息,E,API网关信息,网关名称、所属需求、网关标识、网关类型、管理端口,1,,,,\n密码资产数据管理,密码资产数据管理,API网关管理,发起者：用户，接收者：密码服务平台,用户点击新增按钮,API网关新增,保存新增网关信息,W,API网关信息,网关名称、所属需求、网关标识、网关类型、管理端口,1,,,,\n密码资产数据管理,密码资产数据管理,API网关管理,发起者：用户，接收者：密码服务平台,用户点击新增按钮,API网关新增,返回新增网关信息,X,API网关信息,网关名称、所属需求、网关标识、网关类型、管理端口,1,,,,\n密码资产数据管理,密码资产数据管理,API网关管理,发起者：用户，接收者：密码服务平台,用户点击编辑按钮,API网关编辑,输入编辑网关信息,E,API网关信息,网关名称、管理端口,1,,,,\n密码资产数据管理,密码资产数据管理,API网关管理,发起者：用户，接收者：密码服务平台,用户点击编辑按钮,API网关编辑,更新网关信息,W,API网关信息,网关名称、管理端口、修改时间,1,,,,\n密码资产数据管理,密码资产数据管理,API网关管理,发起者：用户，接收者：密码服务平台,用户点击编辑按钮,API网关编辑,返回编辑网关信息,X,API网关信息,网关名称、管理端口,1,,,,\n密码资产数据管理,密码资产数据管理,API网关管理,发起者：用户，接收者：密码服务平台,用户点击删除按钮,API网关删除,输入删除网关信息,E,API网关信息,网关名称、网关ID,1,,,,\n密码资产数据管理,密码资产数据管理,API网关管理,发起者：用户，接收者：密码服务平台,用户点击删除按钮,API网关删除,删除网关信息,W,API网关信息,网关名称、网关ID,1,,,,\n密码资产数据管理,密码资产数据管理,API网关管理,发起者：用户，接收者：密码服务平台,用户点击删除按钮,API网关删除,返回删除网关信息,X,API网关信息,网关名称、网关ID,1,,,,\n密码资产数据管理,密码资产数据管理,网关路由管理,发起者：用户，接收者：网关路由管理模块,用户点击路由管理菜单,路由管理列表,输入查询条件,E,查询条件信息,页码、单页数量、路由名称,1,,,,\n密码资产数据管理,密码资产数据管理,网关路由管理,发起者：用户，接收者：网关路由管理模块,用户点击路由管理菜单,路由管理列表,读取路由列表,R,路由列表信息,路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间,1,,,,\n密码资产数据管理,密码资产数据管理,网关路由管理,发起者：用户，接收者：网关路由管理模块,用户点击路由管理菜单,路由管理列表,返回路由列表,X,路由列表信息,路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间,1,,,,\n密码资产数据管理,密码资产数据管理,网关路由管理,发起者：用户，接收者：网关路由管理模块,用户点击路由管理菜单,路由管理列表,保存路由列表,W,路由列表信息,路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间,1,,,,\n密码资产数据管理,密码资产数据管理,网关路由管理,发起者：用户，接收者：网关路由管理模块,用户点击路由详情,路由管理详情,输入路由ID,E,路由ID,路由ID,1,,,,\n密码资产数据管理,密码资产数据管理,网关路由管理,发起者：用户，接收者：网关路由管理模块,用户点击路由详情,路由管理详情,读取路由详情,R,路由详情信息,路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间,1,,,,\n密码资产数据管理,密码资产数据管理,网关路由管理,发起者：用户，接收者：网关路由管理模块,用户点击路由详情,路由管理详情,读取服务列表,R,服务列表信息,服务名称、服务类型、服务ID,1,,,,\n密码资产数据管理,密码资产数据管理,网关路由管理,发起者：用户，接收者：网关路由管理模块,用户点击路由详情,路由管理详情,返回路由详情,X,路由详情信息,路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型列表,设备类型展示,输入设备类型查询条件,E,设备类型查询请求,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口、页码、单页数量,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型列表,设备类型展示,读取设备类型信息,R,设备类型信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型列表,设备类型展示,读取设备类型监控信息,R,设备类型监控信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口、监控方式,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型列表,设备类型展示,返回设备类型列表展示,X,设备类型列表,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口、查询时间,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,平台部署时,设备类型初始化,输入设备类型初始化指令,E,设备类型初始化指令,设备类型初始化参数,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,平台部署时,设备类型初始化,读取设备类型初始化参数,R,设备类型初始化参数,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,平台部署时,设备类型初始化,初始化设备类型信息,W,设备类型信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,平台部署时,设备类型初始化,返回设备类型初始化结果,X,设备类型初始化结果,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口、初始化结果,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型新增按钮,设备类型新增,输入设备类型新增信息,E,设备类型新增信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型新增按钮,设备类型新增,校验设备类型是否重复,R,设备类型校验信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型新增按钮,设备类型新增,保存设备类型新增信息,W,设备类型新增信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型新增按钮,设备类型新增,返回设备类型新增内容,X,设备类型新增内容,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口、新增结果,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型编辑按钮,设备类型编辑,输入设备类型编辑信息,E,设备类型编辑信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型编辑按钮,设备类型编辑,读取设备类型信息,R,设备类型信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型编辑按钮,设备类型编辑,更新设备类型信息,W,设备类型信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型编辑按钮,设备类型编辑,返回设备类型编辑结果,X,设备类型编辑结果,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口、编辑结果,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型停用按钮,设备类型停用,输入设备类型停用指令,E,设备类型停用指令,设备类型停用参数,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型停用按钮,设备类型停用,停用设备类型,W,设备类型停用信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口、停用时间,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型启用按钮,设备类型启用,输入设备类型启用指令,E,设备类型启用指令,设备类型启用参数,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型启用按钮,设备类型启用,读取设备类型信息,R,设备类型信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型启用按钮,设备类型启用,启用设备类型,W,设备类型启用信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口、启用结果,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型删除按钮,设备类型删除,输入设备类型删除指令,E,设备类型删除指令,设备类型删除参数,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型删除按钮,设备类型删除,读取设备类型信息,R,设备类型信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击设备类型删除按钮,设备类型删除,删除设备类型,W,设备类型删除信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口、删除结果,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击监控信息配置查询按钮,监控信息配置查看,输入监控信息配置查询条件,E,监控信息配置查询请求,监控信息配置查询条件,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击监控信息配置查询按钮,监控信息配置查看,读取监控信息配置,R,监控信息配置,监控方式、监控信息,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击监控信息配置查询按钮,监控信息配置查看,返回监控信息配置查询结果,X,监控信息配置查询结果,监控方式、监控信息、查询时间,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击监控信息配置保存按钮,监控信息配置,输入监控信息配置,E,监控信息配置,监控方式、监控信息,1,,,,\n密码资产数据管理,密码资产数据管理,设备类型管理,发起者：用户，接收者：密码资产数据管理-设备类型管理模块,用户点击监控信息配置保存按钮,监控信息配置,保存监控信息配置,W,监控信息配置,监控方式、监控信息,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击密码设备集群列表,密码设备集群列表,输入密码设备集群列表查询条件,E,密码设备集群列表查询请求,密码设备集群列表查询条件、密码设备集群列表查询项,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击密码设备集群列表,密码设备集群列表,读取密码设备集群列表,R,密码设备集群列表,密码设备集群列表名称、密码设备集群列表类型、密码设备集群列表ID、密码设备集群列表数量,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击密码设备集群列表,密码设备集群列表,返回密码设备集群列表查询结果展示,X,密码设备集群列表查询结果,密码设备集群列表名称、密码设备集群列表类型、密码设备集群列表ID、密码设备集群列表数量、分页数,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击密码设备集群列表,密码设备集群列表,保存密码设备集群列表查询记录,W,密码设备集群列表查询记录,密码设备集群列表名称、密码设备集群列表类型、密码设备集群列表ID、操作人、系统时间,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击密码设备集群新增,密码设备集群新增,输入密码设备集群新增信息,E,密码设备集群新增信息,密码设备集群新增ID、密码设备集群新增名称、密码设备集群新增类型,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击密码设备集群新增,密码设备集群新增,密码设备集群重复性校验,R,密码设备集群校验信息,密码设备集群名称、主键、非空校验、判重、密码设备集群类型,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击密码设备集群新增,密码设备集群新增,密码设备集群新增入库,W,密码设备集群新增入库信息,密码设备集群名称、密码设备集群类型、密码设备集群ID、新增时间,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击密码设备集群新增,密码设备集群新增,返回展示密码设备集群新增内容,X,密码设备集群新增内容,密码设备集群名称、密码设备集群类型、密码设备集群ID、新增结果,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击密码设备集群编辑,密码设备集群编辑,输入密码设备集群编辑信息,E,密码设备集群编辑条件,密码设备集群ID、密码设备集群编辑项,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击密码设备集群编辑,密码设备集群编辑,获取密码设备集群编辑项,R,密码设备集群编辑项,密码设备集群约束条件、密码设备集群名称、密码设备集群数量、密码设备集群ID,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击密码设备集群编辑,密码设备集群编辑,密码设备集群编辑保存,W,密码设备集群编辑结果,编辑时间、密码设备集群名称、密码设备集群类型、密码设备集群ID,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击密码设备集群编辑,密码设备集群编辑,输出密码设备集群编辑结果,X,密码设备集群编辑结果展示信息,密码设备集群名称、密码设备集群类型、密码设备集群ID、编辑内容、编辑结果,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击密码设备集群删除,密码设备集群删除,发起密码设备集群删除请求,E,密码设备集群删除请求,密码设备集群删除参数,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击密码设备集群删除,密码设备集群删除,获取操作员权限并判断密码设备集群是否可删除,R,密码设备集群删除权限,密码设备集群名称、操作员权限、操作员ID,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击密码设备集群删除,密码设备集群删除,密码设备集群删除保存,W,密码设备集群删除结果,密码设备集群名称、密码设备集群类型、密码设备集群ID、删除结果,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击密码设备集群删除,密码设备集群删除,返回展示密码设备集群删除内容,X,密码设备集群删除内容,密码设备集群名称、密码设备集群类型、密码设备集群ID、删除内容,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击绑定密码设备,绑定密码设备,输入绑定密码设备指令,E,绑定密码设备指令,绑定密码设备请求,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击绑定密码设备,绑定密码设备,获取需要绑定的密码设备,R,密码设备,密码设备名称、密码设备类型、密码设备ID,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击绑定密码设备,绑定密码设备,获取密码设备集群,R,密码设备集群,密码设备集群名称、密码设备集群类型、密码设备集群ID,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击绑定密码设备,绑定密码设备,判断是否需要进行保护密钥的创建和同步,R,密码设备集群配置,是否需要进行保护密钥的创建和同步,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击绑定密码设备,绑定密码设备,绑定密码设备,W,绑定密码设备结果,绑定密码设备名称、绑定密码设备类型、绑定密码设备ID、操作人,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击绑定密码设备,绑定密码设备,返回展示绑定密码设备结果,X,绑定密码设备结果展示信息,绑定密码设备名称、绑定密码设备类型、绑定密码设备ID、绑定时间,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击释放密码设备,释放密码设备,发起释放密码设备请求,E,释放密码设备请求,释放密码设备参数,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击释放密码设备,释放密码设备,获取操作员权限并判断密码设备集群是否可释放,R,释放密码设备权限,密码设备集群名称、操作员权限、操作员ID,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击释放密码设备,释放密码设备,释放密码设备,W,释放密码设备结果,密码设备集群名称、密码设备集群类型、密码设备集群ID、释放结果,1,,,,\n密码资产数据管理,密码资产数据管理,密码设备集群管理,发起者：用户，接收者：密码资产数据管理-密码设备集群管理模块,用户点击释放密码设备,释放密码设备,返回展示释放密码设备内容,X,释放密码设备内容,密码设备集群名称、密码设备集群类型、密码设备集群ID、释放内容,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机列表菜单,云密码机列表,输入云密码机列表查询条件,E,云密码机列表查询请求,云密码机列表查询条件、云密码机列表查询项,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机列表菜单,云密码机列表,读取云密码机列表,R,云密码机列表,云密码机名称、云密码机ID、管理IP、管理端口、操作员、操作时间,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机列表菜单,云密码机列表,返回云密码机列表查询结果展示,X,云密码机列表查询结果,云密码机名称、云密码机ID、管理IP、管理端口、分页数、分页大小、查询时间,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机列表菜单,云密码机列表,保存云密码机列表查询记录,W,云密码机列表查询记录,云密码机列表查询条件、云密码机列表查询结果、系统时间,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机新建按钮,云密码机新建,输入云密码机信息,E,云密码机新建信息,云密码机名称、云密码机类型、云密码机ID、管理IP、管理端口,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机新建按钮,云密码机新建,云密码机重复性校验,R,云密码机校验信息,云密码机名称、云密码机类型、云密码机ID、管理IP、管理端口、校验规则,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机新建按钮,云密码机新建,云密码机新建保存,W,云密码机新建结果,云密码机名称、云密码机类型、云密码机ID、管理IP、管理端口、新建结果,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机新建按钮,云密码机新建,返回展示云密码机新建内容,X,云密码机新建内容,云密码机名称、云密码机类型、云密码机ID、管理IP、管理端口、新建内容,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机编辑按钮,云密码机编辑,输入云密码机编辑信息,E,云密码机编辑条件,云密码机名称、云密码机类型、云密码机ID、编辑项,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机编辑按钮,云密码机编辑,获取云密码机编辑项,R,云密码机编辑项,云密码机名称、云密码机类型、云密码机ID、编辑项、编辑项类型,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机编辑按钮,云密码机编辑,云密码机编辑保存,W,云密码机编辑结果,云密码机名称、云密码机类型、云密码机ID、编辑结果,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机编辑按钮,云密码机编辑,返回展示云密码机编辑内容,X,云密码机编辑内容,云密码机名称、云密码机类型、云密码机ID、编辑内容,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机删除按钮,云密码机删除,发起云密码机删除请求,E,云密码机删除请求,云密码机删除参数,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机删除按钮,云密码机删除,获取云密码机是否可删除,R,云密码机删除约束,云密码机名称、云密码机类型、云密码机ID、是否可删除,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机删除按钮,云密码机删除,云密码机删除保存,W,云密码机删除结果,云密码机名称、云密码机类型、云密码机ID、删除结果,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机删除按钮,云密码机删除,返回展示云密码机删除内容,X,云密码机删除内容,云密码机名称、云密码机类型、云密码机ID、删除内容,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机详情按钮,云密码机详情,发起云密码机详情请求,E,云密码机详情请求,云密码机详情查询条件,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机详情按钮,云密码机详情,获取云密码机详情,R,云密码机详情,云密码机名称、云密码机类型、云密码机ID、详情,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机管理,发起者：用户，接收者：云密码机管理模块,用户点击云密码机详情按钮,云密码机详情,返回展示云密码机详情,X,云密码机详情查询结果,云密码机名称、云密码机类型、云密码机ID、分页数、分页大小、查询时间,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,发起者：用户，接收者：云密码机虚机网络管理模块,用户点击网络配置列表,网络配置列表,读取网络配置信息,R,网络配置信息,网络配置ID、网络名称、网卡名称、网关、子网掩码、IP范围、创建时间,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,发起者：用户，接收者：云密码机虚机网络管理模块,用户点击网络配置列表,网络配置列表,展示网络配置列表,X,网络配置信息,网络配置ID、网络名称、网卡名称、网关、子网掩码、IP范围、创建时间,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,发起者：用户，接收者：云密码机虚机网络管理模块,用户点击新增网络配置,新增虚拟机网络配置,输入新增网络配置信息,E,新增网络配置信息,网络配置ID、网络名称、网卡名称、网关、子网掩码、IP范围,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,发起者：用户，接收者：云密码机虚机网络管理模块,用户点击新增网络配置,新增虚拟机网络配置,读取网络配置信息,R,网络配置信息,网络配置ID、网络名称、网卡名称、网关、子网掩码、IP范围,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,发起者：用户，接收者：云密码机虚机网络管理模块,用户点击新增网络配置,新增虚拟机网络配置,新增网络配置信息,W,新增网络配置信息,网络配置ID、网络名称、网卡名称、网关、子网掩码、IP范围,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,发起者：用户，接收者：云密码机虚机网络管理模块,用户点击新增网络配置,新增虚拟机网络配置,返回新增网络配置结果,X,新增网络配置结果,新增结果,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,发起者：用户，接收者：云密码机虚机网络管理模块,用户点击新增网络配置,新增虚拟机网络配置,记录新增网络配置日志,W,新增网络配置日志,新增结果、新增时间,1,,,,\n密码资产数据管理,密码资产数据管理,云密码机虚机网络管理,发起者：用户，接收者：云密码机虚机网络管理模块,用户点击新增网络配置,新增虚拟机网络配置,新增网络配置数据归档,W,新增网络配置归档信息,网络配置ID、网络名称、网卡名称、网关、子网掩码、IP范围、归档时间,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击批量创建虚拟机,批量创建虚拟机,输入批量创建虚拟机请求,E,虚拟密码机创建请求,虚拟密码机创建数量、虚拟密码机名称、虚拟密码机类型、虚拟密码机ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击批量创建虚拟机,批量创建虚拟机,调用云密码机0088标准创建虚拟机,R,虚拟密码机创建请求,虚拟密码机创建数量、虚拟密码机名称、虚拟密码机类型、虚拟密码机ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击批量创建虚拟机,批量创建虚拟机,调用云密码机0088标准创建虚拟机,W,虚拟密码机创建结果,虚拟密码机创建数量、虚拟密码机名称、虚拟密码机类型、虚拟密码机ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击批量创建虚拟机,批量创建虚拟机,返回批量创建虚拟机结果,X,虚拟密码机创建结果,虚拟密码机创建数量、虚拟密码机名称、虚拟密码机类型、虚拟密码机ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击批量创建虚拟机,批量创建虚拟机,获取虚拟机网络配置,R,虚拟密码机网络配置,虚拟密码机网络名称、虚拟密码机网络类型、虚拟密码机网络ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击批量创建虚拟机,批量创建虚拟机,保存虚拟机网络配置,W,虚拟密码机网络配置,虚拟密码机网络名称、虚拟密码机网络类型、虚拟密码机网络ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击批量创建虚拟机,批量创建虚拟机,获取虚拟机资源,R,虚拟密码机资源,虚拟密码机资源名称、虚拟密码机资源类型、虚拟密码机资源ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击批量创建虚拟机,批量创建虚拟机,保存虚拟机资源,W,虚拟密码机资源,虚拟密码机资源名称、虚拟密码机资源类型、虚拟密码机资源ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击批量创建虚拟机,批量创建虚拟机,获取虚拟机网络,R,虚拟密码机网络,虚拟密码机网络名称、虚拟密码机网络类型、虚拟密码机网络ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击批量创建虚拟机,批量创建虚拟机,保存虚拟机网络,W,虚拟密码机网络,虚拟密码机网络名称、虚拟密码机网络类型、虚拟密码机网络ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击虚拟密码机列表,虚拟密码机列表,输入虚拟密码机列表查询条件,E,虚拟密码机列表查询请求,虚拟密码机列表查询条件、虚拟密码机列表查询项,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击虚拟密码机列表,虚拟密码机列表,读取虚拟密码机列表,R,虚拟密码机列表,虚拟密码机列表名称、虚拟密码机列表类型、虚拟密码机列表ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击虚拟密码机列表,虚拟密码机列表,返回虚拟密码机列表查询结果展示,X,虚拟密码机列表查询结果,虚拟密码机列表名称、虚拟密码机列表类型、虚拟密码机列表ID、查询时间,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击虚拟密码机列表,虚拟密码机列表,保存虚拟密码机列表查询记录,W,虚拟密码机列表查询记录,虚拟密码机列表名称、虚拟密码机列表类型、虚拟密码机列表ID、操作人、系统时间,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击虚拟密码机列表查询,虚拟密码机列表查询,输入虚拟密码机列表查询条件,E,虚拟密码机列表查询条件,虚拟密码机列表名称、虚拟密码机列表类型、虚拟密码机列表ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击虚拟密码机列表查询,虚拟密码机列表查询,获取虚拟密码机列表,R,虚拟密码机列表,虚拟密码机列表名称、虚拟密码机列表类型、虚拟密码机列表ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击虚拟密码机列表查询,虚拟密码机列表查询,返回虚拟密码机列表查询结果,X,虚拟密码机列表查询结果,虚拟密码机列表名称、虚拟密码机列表类型、虚拟密码机列表ID、分页数、查询时间,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击创建虚拟密码机,创建虚拟密码机,输入创建虚拟密码机信息,E,虚拟密码机创建信息,虚拟密码机名称、虚拟密码机类型、虚拟密码机ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击创建虚拟密码机,创建虚拟密码机,调用云密码机0088标准创建虚拟密码机,R,虚拟密码机创建信息,虚拟密码机名称、虚拟密码机类型、虚拟密码机ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击创建虚拟密码机,创建虚拟密码机,返回创建虚拟密码机结果,X,虚拟密码机创建结果,虚拟密码机名称、虚拟密码机类型、虚拟密码机ID、创建结果,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击虚拟密码机详情,虚拟密码机详情,发起虚拟密码机详情请求,E,虚拟密码机详情请求,虚拟密码机详情查询条件,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击虚拟密码机详情,虚拟密码机详情,获取虚拟密码机详情,R,虚拟密码机详情,虚拟密码机详情名称、虚拟密码机详情类型、虚拟密码机详情ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击虚拟密码机详情,虚拟密码机详情,返回虚拟密码机详情,X,虚拟密码机详情查询结果,虚拟密码机详情名称、虚拟密码机详情类型、虚拟密码机详情ID、分页数、查询时间,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击虚拟密码机详情,虚拟密码机详情,保存虚拟密码机详情,W,虚拟密码机详情,虚拟密码机详情名称、虚拟密码机详情类型、虚拟密码机详情ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击虚拟密码机详情,虚拟密码机详情,更新虚拟密码机详情,W,虚拟密码机详情,虚拟密码机详情名称、虚拟密码机详情类型、虚拟密码机详情ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击编辑虚拟密码机,编辑虚拟密码机,发起编辑虚拟密码机请求,E,虚拟密码机编辑请求,虚拟密码机编辑参数,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击编辑虚拟密码机,编辑虚拟密码机,获取虚拟密码机编辑项,R,虚拟密码机编辑项,虚拟密码机名称、虚拟密码机类型、虚拟密码机ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击编辑虚拟密码机,编辑虚拟密码机,编辑虚拟密码机名称、连接密码,E,虚拟密码机编辑内容,虚拟密码机名称、虚拟密码机类型、虚拟密码机ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击编辑虚拟密码机,编辑虚拟密码机,动态下发虚拟密码机名称、连接密码,X,虚拟密码机编辑内容,虚拟密码机名称、虚拟密码机类型、虚拟密码机ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击删除虚拟密码机,删除虚拟密码机,发起删除虚拟密码机请求,E,虚拟密码机删除请求,虚拟密码机删除参数,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击删除虚拟密码机,删除虚拟密码机,获取虚拟密码机,R,虚拟密码机,虚拟密码机名称、虚拟密码机类型、虚拟密码机ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击删除虚拟密码机,删除虚拟密码机,删除虚拟密码机,W,虚拟密码机删除结果,虚拟密码机名称、虚拟密码机类型、虚拟密码机ID、删除结果,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击启动虚拟密码机,启动虚拟密码机,发起启动虚拟密码机请求,E,虚拟密码机启动请求,虚拟密码机启动参数,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击启动虚拟密码机,启动虚拟密码机,获取虚拟密码机,R,虚拟密码机,虚拟密码机名称、虚拟密码机类型、虚拟密码机ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击启动虚拟密码机,启动虚拟密码机,启动虚拟密码机,W,虚拟密码机启动结果,虚拟密码机名称、虚拟密码机类型、虚拟密码机ID、启动结果,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击停止虚拟密码机,停止虚拟密码机,发起停止虚拟密码机请求,E,虚拟密码机停止请求,虚拟密码机停止参数,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击停止虚拟密码机,停止虚拟密码机,获取虚拟密码机,R,虚拟密码机,虚拟密码机名称、虚拟密码机类型、虚拟密码机ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击停止虚拟密码机,停止虚拟密码机,停止虚拟密码机,W,虚拟密码机停止结果,虚拟密码机名称、虚拟密码机类型、虚拟密码机ID、停止结果,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击重启虚拟密码机,重启虚拟密码机,发起重启虚拟密码机请求,E,虚拟密码机重启请求,虚拟密码机重启参数,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击重启虚拟密码机,重启虚拟密码机,获取虚拟密码机,R,虚拟密码机,虚拟密码机名称、虚拟密码机类型、虚拟密码机ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击重启虚拟密码机,重启虚拟密码机,重启虚拟密码机,W,虚拟密码机重启结果,虚拟密码机名称、虚拟密码机类型、虚拟密码机ID、重启结果,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击强制删除虚拟密码机,强制删除虚拟密码机,发起强制删除虚拟密码机请求,E,虚拟密码机强制删除请求,虚拟密码机强制删除参数,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击强制删除虚拟密码机,强制删除虚拟密码机,获取虚拟密码机,R,虚拟密码机,虚拟密码机名称、虚拟密码机类型、虚拟密码机ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击强制删除虚拟密码机,强制删除虚拟密码机,强制删除虚拟密码机,W,虚拟密码机强制删除结果,虚拟密码机名称、虚拟密码机类型、虚拟密码机ID、强制删除结果,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击生成虚机影像,生成虚机影像,发起生成虚机影像请求,E,虚机影像生成请求,虚机影像生成参数,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击生成虚机影像,生成虚机影像,获取虚机影像生成信息,R,虚机影像生成信息,虚机影像名称、虚机影像类型、虚机影像ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击生成虚机影像,生成虚机影像,生成虚机影像,W,虚机影像生成结果,虚机影像名称、虚机影像类型、虚机影像ID、生成结果,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击下载虚机影像,下载虚机影像,发起下载虚机影像请求,E,虚机影像下载请求,虚机影像下载参数,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击下载虚机影像,下载虚机影像,获取虚机影像,R,虚机影像,虚机影像名称、虚机影像类型、虚机影像ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击下载虚机影像,下载虚机影像,下载虚机影像,X,虚机影像下载结果,虚机影像名称、虚机影像类型、虚机影像ID、下载结果,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击导入虚机影像,导入虚机影像,发起导入虚机影像请求,E,虚机影像导入请求,虚机影像导入参数,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击导入虚机影像,导入虚机影像,获取虚机影像,R,虚机影像,虚机影像名称、虚机影像类型、虚机影像ID,1,,,,\n密码资产数据管理,密码资产数据管理,虚拟密码机管理,发起者：用户，接收者：虚拟密码机,用户点击导入虚机影像,导入虚机影像,导入虚机影像,W,虚机影像导入结果,虚机影像名称、虚机影像类型、虚机影像ID、导入结果,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机列表页,物理密码机列表,输入物理密码机列表查询条件,E,物理密码机列表查询请求,物理密码机列表查询条件、物理密码机列表查询项,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机列表页,物理密码机列表,读取物理密码机列表,R,物理密码机列表,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机列表页,物理密码机列表,返回物理密码机列表查询结果展示,X,物理密码机列表查询结果,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注、分页数,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机列表页,物理密码机列表,保存物理密码机列表查询记录,W,物理密码机列表查询记录,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注、操作人、系统时间,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机新建按钮,物理密码机新建,输入物理密码机新建信息,E,物理密码机新建信息,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机新建按钮,物理密码机新建,物理密码机重复性校验,R,物理密码机校验信息,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机新建按钮,物理密码机新建,物理密码机新建保存,W,物理密码机新建结果,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机新建按钮,物理密码机新建,返回展示物理密码机新建内容,X,物理密码机新建内容,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机新建按钮,物理密码机新建,记录物理密码机新建日志,W,物理密码机新建日志,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注、操作人、系统时间,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机编辑按钮,物理密码机编辑,输入物理密码机编辑信息,E,物理密码机编辑条件,物理密码机ID、物理密码机编辑项,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机编辑按钮,物理密码机编辑,获取物理密码机编辑项,R,物理密码机编辑项,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机编辑按钮,物理密码机编辑,物理密码机编辑保存,W,物理密码机编辑结果,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机编辑按钮,物理密码机编辑,返回展示物理密码机编辑内容,X,物理密码机编辑内容,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机删除按钮,物理密码机删除,发起物理密码机删除请求,E,物理密码机删除请求,物理密码机删除参数,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机删除按钮,物理密码机删除,获取物理密码机,R,物理密码机,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机删除按钮,物理密码机删除,物理密码机删除保存,W,物理密码机删除结果,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机删除按钮,物理密码机删除,返回展示物理密码机删除内容,X,物理密码机删除内容,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机详情按钮,物理密码机详情,发起物理密码机详情请求,E,物理密码机详情请求,物理密码机详情参数,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机详情按钮,物理密码机详情,获取物理密码机详情,R,物理密码机详情,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机详情按钮,物理密码机详情,返回展示物理密码机详情,X,物理密码机详情展示信息,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机详情按钮,物理密码机详情,保存物理密码机详情,W,物理密码机详情,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机强制删除按钮,强制删除,发起物理密码机强制删除请求,E,物理密码机强制删除请求,物理密码机强制删除参数,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机强制删除按钮,强制删除,获取物理密码机,R,物理密码机,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机强制删除按钮,强制删除,物理密码机强制删除保存,W,物理密码机强制删除结果,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机强制删除按钮,强制删除,返回展示物理密码机强制删除内容,X,物理密码机强制删除内容,物理密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机管理页面跳转按钮,管理页面跳转,发起物理密码机管理页面跳转请求,E,物理密码机管理页面跳转请求,物理密码机管理页面跳转参数,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机管理页面跳转按钮,管理页面跳转,获取物理密码机管理页面跳转地址,R,物理密码机管理页面跳转地址,物理密码机管理页面跳转地址,1,,,,\n密码资产数据管理,密码资产数据管理,物理密码机管理,发起者：用户，接收者：密码资产数据管理-物理密码机管理模块,用户点击物理密码机管理页面跳转按钮,管理页面跳转,物理密码机管理页面跳转,X,物理密码机管理页面跳转结果,物理密码机管理页面跳转结果,1,,,,\n密码资产数据管理,密码资产数据管理,保护主密钥管理,发起者：用户，接收者：密服平台-保护主密钥模块,用户在保护主密钥管理页面点击保护主密钥创建按钮,保护主密钥创建,输入保护主密钥创建信息,E,保护主密钥创建信息,保护主密钥名称、保护主密钥类型、保护主密钥ID,1,,,,\n密码资产数据管理,密码资产数据管理,保护主密钥管理,发起者：用户，接收者：密服平台-保护主密钥模块,用户在保护主密钥管理页面点击保护主密钥创建按钮,保护主密钥创建,获取设备信息,R,设备信息,设备名称、设备类型、设备ID,1,,,,\n密码资产数据管理,密码资产数据管理,保护主密钥管理,发起者：用户，接收者：密服平台-保护主密钥模块,用户在保护主密钥管理页面点击保护主密钥创建按钮,保护主密钥创建,生成保护主密钥,X,保护主密钥生成信息,保护主密钥名称、保护主密钥类型、保护主密钥ID、生成时间,1,,,,\n密码资产数据管理,密码资产数据管理,保护主密钥管理,发起者：用户，接收者：密服平台-保护主密钥模块,用户在保护主密钥管理页面点击保护主密钥创建按钮,保护主密钥创建,保存保护主密钥,W,保护主密钥信息,保护主密钥名称、保护主密钥类型、保护主密钥ID、设备ID、生成结果,1,,,,\n密码资产数据管理,密码资产数据管理,保护主密钥管理,发起者：用户，接收者：密服平台-保护主密钥模块,用户在保护主密钥管理页面点击保护主密钥创建按钮,保护主密钥创建,返回展示保护主密钥创建结果,X,保护主密钥创建结果,保护主密钥名称、保护主密钥类型、保护主密钥ID、创建结果,1,,,,\n密码资产数据管理,密码资产数据管理,保护主密钥管理,发起者：用户，接收者：密服平台-保护主密钥模块,用户在保护主密钥管理页面点击保护主密钥同步按钮,保护主密钥同步,输入保护主密钥同步信息,E,保护主密钥同步信息,保护主密钥名称、保护主密钥类型、保护主密钥ID,1,,,,\n密码资产数据管理,密码资产数据管理,保护主密钥管理,发起者：用户，接收者：密服平台-保护主密钥模块,用户在保护主密钥管理页面点击保护主密钥同步按钮,保护主密钥同步,获取设备信息,R,设备信息,设备名称、设备类型、设备ID,1,,,,\n密码资产数据管理,密码资产数据管理,保护主密钥管理,发起者：用户，接收者：密服平台-保护主密钥模块,用户在保护主密钥管理页面点击保护主密钥同步按钮,保护主密钥同步,获取保护主密钥,R,保护主密钥信息,保护主密钥名称、保护主密钥类型、保护主密钥ID,1,,,,\n密码资产数据管理,密码资产数据管理,保护主密钥管理,发起者：用户，接收者：密服平台-保护主密钥模块,用户在保护主密钥管理页面点击保护主密钥同步按钮,保护主密钥同步,保护主密钥同步,X,保护主密钥同步信息,保护主密钥名称、保护主密钥类型、保护主密钥ID、同步时间,1,,,,\n密码资产数据管理,密码资产数据管理,保护主密钥管理,发起者：用户，接收者：密服平台-保护主密钥模块,用户在保护主密钥管理页面点击保护主密钥同步按钮,保护主密钥同步,记录保护主密钥同步信息,W,保护主密钥同步记录,保护主密钥名称、保护主密钥类型、保护主密钥ID、操作人、系统时间,1,,,,\n密码资产数据管理,密码资产数据管理,保护主密钥管理,发起者：用户，接收者：密服平台-保护主密钥模块,用户在保护主密钥管理页面点击保护主密钥备份按钮,保护主密钥备份,输入保护主密钥备份信息,E,保护主密钥备份信息,保护主密钥名称、保护主密钥类型、保护主密钥ID、备份类型,1,,,,\n密码资产数据管理,密码资产数据管理,保护主密钥管理,发起者：用户，接收者：密服平台-保护主密钥模块,用户在保护主密钥管理页面点击保护主密钥备份按钮,保护主密钥备份,获取保护主密钥,R,保护主密钥信息,保护主密钥名称、保护主密钥类型、保护主密钥ID,1,,,,\n密码资产数据管理,密码资产数据管理,保护主密钥管理,发起者：用户，接收者：密服平台-保护主密钥模块,用户在保护主密钥管理页面点击保护主密钥备份按钮,保护主密钥备份,生成保护主密钥备份文件,X,保护主密钥备份文件,保护主密钥名称、保护主密钥类型、保护主密钥ID、文件格式、文件大小,1,,,,\n密码资产数据管理,密码资产数据管理,保护主密钥管理,发起者：用户，接收者：密服平台-保护主密钥模块,用户在保护主密钥管理页面点击保护主密钥备份按钮,保护主密钥备份,保存保护主密钥备份记录,W,保护主密钥备份记录,保护主密钥名称、保护主密钥类型、保护主密钥ID、备份结果,1,,,,", "validation_focus": {"completeness_check": "每个功能过程是否包含至少1个E和1个X", "data_group_aggregation": "是否将关联数据属性合并为最小单元", "storage_boundary": "R/W是否仅针对边界内持久存储", "no_duplicate_counting": "同一数据组在同一功能过程中是否被重复计数"}}