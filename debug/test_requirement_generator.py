#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
功能需求文档生成器测试脚本

用于测试功能需求文档生成功能
"""

import sys
import os
import pandas as pd

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from requirement_generator import RequirementGenerator


def create_test_data():
    """创建测试数据"""
    test_data = [
        {
            "一级功能模块": "用户管理",
            "二级功能模块": "用户账户管理", 
            "三级功能模块": "用户注册",
            "功能过程": "用户注册",
            "功能用户": "发起者：新用户，接收者：系统",
            "触发事件": "用户注册请求",
            "子过程描述": "输入用户注册信息",
            "数据移动类型": "E",
            "数据组": "用户注册信息",
            "数据属性": "用户名、密码、邮箱、手机号"
        },
        {
            "一级功能模块": "用户管理",
            "二级功能模块": "用户账户管理",
            "三级功能模块": "用户注册", 
            "功能过程": "用户注册",
            "功能用户": "发起者：新用户，接收者：系统",
            "触发事件": "用户注册请求",
            "子过程描述": "验证用户信息",
            "数据移动类型": "R",
            "数据组": "用户验证信息",
            "数据属性": "用户名唯一性、邮箱格式、密码强度"
        },
        {
            "一级功能模块": "用户管理",
            "二级功能模块": "用户账户管理",
            "三级功能模块": "用户注册",
            "功能过程": "用户注册", 
            "功能用户": "发起者：新用户，接收者：系统",
            "触发事件": "用户注册请求",
            "子过程描述": "保存用户信息",
            "数据移动类型": "W",
            "数据组": "用户信息",
            "数据属性": "用户ID、用户名、密码哈希、邮箱、手机号、创建时间"
        },
        {
            "一级功能模块": "用户管理",
            "二级功能模块": "用户账户管理",
            "三级功能模块": "用户注册",
            "功能过程": "用户注册",
            "功能用户": "发起者：新用户，接收者：系统", 
            "触发事件": "用户注册请求",
            "子过程描述": "返回注册结果",
            "数据移动类型": "X",
            "数据组": "注册结果信息",
            "数据属性": "注册状态、用户ID、成功消息"
        },
        {
            "一级功能模块": "用户管理",
            "二级功能模块": "用户账户管理",
            "三级功能模块": "用户登录",
            "功能过程": "用户登录",
            "功能用户": "发起者：用户，接收者：系统",
            "触发事件": "用户登录请求", 
            "子过程描述": "输入登录凭证",
            "数据移动类型": "E",
            "数据组": "登录凭证信息",
            "数据属性": "用户名、密码"
        },
        {
            "一级功能模块": "用户管理",
            "二级功能模块": "用户账户管理",
            "三级功能模块": "用户登录",
            "功能过程": "用户登录",
            "功能用户": "发起者：用户，接收者：系统",
            "触发事件": "用户登录请求",
            "子过程描述": "验证登录凭证",
            "数据移动类型": "R", 
            "数据组": "用户认证信息",
            "数据属性": "用户名、密码哈希、账户状态"
        },
        {
            "一级功能模块": "用户管理",
            "二级功能模块": "用户账户管理",
            "三级功能模块": "用户登录",
            "功能过程": "用户登录",
            "功能用户": "发起者：用户，接收者：系统",
            "触发事件": "用户登录请求",
            "子过程描述": "返回登录结果",
            "数据移动类型": "X",
            "数据组": "登录结果信息", 
            "数据属性": "登录状态、用户信息、访问令牌"
        }
    ]
    
    return pd.DataFrame(test_data)


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试功能需求文档生成器基本功能 ===")
    
    # 创建测试数据
    test_df = create_test_data()
    test_csv_file = "debug/test_cosmic_data.csv"
    test_df.to_csv(test_csv_file, index=False, encoding='utf-8-sig')
    print(f"创建测试数据文件: {test_csv_file}")
    
    # 创建生成器
    generator = RequirementGenerator(
        start_number="2",
        max_subprocess_per_batch=10
    )
    
    # 测试数据加载
    print("\n1. 测试数据加载...")
    df = generator.load_cosmic_data(test_csv_file)
    print(f"   加载数据行数: {len(df)}")
    
    # 测试数据组织
    print("\n2. 测试数据组织...")
    hierarchy = generator.organize_data_by_hierarchy(df)
    print(f"   组织后的层级数: {len(hierarchy)}")
    for level1, level2_data in hierarchy.items():
        print(f"   - {level1}: {len(level2_data)} 个二级模块")
        for level2, level3_data in level2_data.items():
            print(f"     - {level2}: {len(level3_data)} 个三级模块")
    
    # 测试批次创建
    print("\n3. 测试批次创建...")
    batches = generator.create_batches(hierarchy)
    print(f"   创建批次数: {len(batches)}")
    
    # 测试文档生成
    print("\n4. 测试文档生成...")
    output_file = generator.generate_requirements_document(test_csv_file)
    
    if output_file and os.path.exists(output_file):
        print(f"   ✅ 文档生成成功: {output_file}")
        print(f"   📊 文件大小: {os.path.getsize(output_file)} 字节")
        
        # 显示部分内容
        with open(output_file, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            print(f"   📄 文档行数: {len(lines)}")
            print("   📝 前10行内容:")
            for i, line in enumerate(lines[:10], 1):
                print(f"      {i:2d}: {line}")
    else:
        print("   ❌ 文档生成失败")
    
    # 清理测试文件
    try:
        os.remove(test_csv_file)
        print(f"\n清理测试文件: {test_csv_file}")
    except:
        pass


def test_different_start_numbers():
    """测试不同的起始序号"""
    print("\n=== 测试不同起始序号 ===")
    
    test_cases = ["2", "3", "3.1", "4.2"]
    
    for start_num in test_cases:
        print(f"\n测试起始序号: {start_num}")
        generator = RequirementGenerator(start_number=start_num)
        print(f"  解析结果 - 基础序号: {generator.base_number}, 一级计数: {generator.level1_counter}")


def main():
    """主测试函数"""
    print("功能需求文档生成器测试")
    print("=" * 50)
    
    # 测试不同起始序号
    test_different_start_numbers()
    
    # 测试基本功能
    test_basic_functionality()
    
    print("\n" + "=" * 50)
    print("测试完成")


if __name__ == "__main__":
    main()
