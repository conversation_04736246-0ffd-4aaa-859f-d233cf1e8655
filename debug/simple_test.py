#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

print("开始测试...")

try:
    from config import EMBEDDING_MODEL, EMBEDDING_API_BASE, EMBEDDING_API_KEY
    print(f"配置加载成功:")
    print(f"  模型: {EMBEDDING_MODEL}")
    print(f"  API: {EMBEDDING_API_BASE}")
    print(f"  密钥: {EMBEDDING_API_KEY[:10]}...")
    
    from knowledge_base import OpenAIEmbedding
    print("OpenAIEmbedding类导入成功")
    
    embedding = OpenAIEmbedding(
        model_name=EMBEDDING_MODEL,
        api_base=EMBEDDING_API_BASE,
        api_key=EMBEDDING_API_KEY
    )
    print(f"嵌入模型初始化成功，API端点: {embedding.api_base}")
    
    # 测试简单编码
    result = embedding.encode(["测试文本"])
    print(f"编码测试成功，向量形状: {result.shape}")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()

print("测试完成")
