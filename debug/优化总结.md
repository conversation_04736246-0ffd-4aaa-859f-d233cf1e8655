# prompt.md 优化总结

## 优化目标
基于《功能点拆分表（示例）.csv》中的各数据列定义、填写说明及案例，对prompt.md中的系统提示词进行优化，使其更符合COSMIC标准和功能点拆分表规范。

## 主要优化内容

### 1. 角色定义优化
**原内容**：基础的COSMIC专家描述
**优化后**：
- 增加了COSMIC标准核心概念的详细定义
- 明确了功能用户、触发事件、功能过程、子过程、数据组、数据属性的标准定义
- 强调了对COSMIC方法论的深度理解

### 2. 技能描述优化
**原内容**：通用的技能描述
**优化后**：
- 基于CSV文件的填写要求，细化了各项技能
- 明确了功能用户识别的1对1原则
- 规范了触发事件、功能过程、子过程的描述格式
- 强调了CFP计数规则的严格执行

### 3. 任务和目标重构
**原内容**：简单的任务描述
**优化后**：
- 明确了三个层级的拆解要求（功能过程级别、子过程级别、数据移动级别）
- 细化了10个具体的任务目标
- 每个目标都对应CSV文件中的具体字段要求

### 4. 约束条件全面重构
**原内容**：14个分散的注意事项
**优化后**：
- 重新组织为10个结构化的约束类别
- 每个约束都明确标注了是否必填
- 基于CSV文件的定义和填写说明进行规范

#### 具体约束优化：
1. **功能用户约束**：明确只包含数据发起者和接受者，强调1对1原则
2. **触发事件约束**：规范格式为"操作+对象"或"对象+被操作"
3. **功能过程约束**：规范格式为"主+谓+宾"
4. **子过程约束**：规范格式为"主+谓+宾"，强调原子性
5. **数据移动类型约束**：明确四种类型的定义和选择要求
6. **数据组约束**：强化统一原则，明确合并要求和禁止拆分规则
7. **数据属性约束**：明确最小信息单元概念，强调一致性
8. **CFP约束**：明确固定值和计数规则
9. **知识库上下文约束**：规范知识库信息的使用方式
10. **输出格式约束**：明确JSON格式和完整性要求

### 5. 新增填写规范参考
**新增内容**：
- 基于CSV文件的各字段填写要求
- 提供了具体的案例参考
- 明确了每个字段的必填性和格式要求

### 6. 知识库上下文使用指南优化
**原内容**：基础的使用说明
**优化后**：
- 详细说明了用户手册和数据库上下文的使用方法
- 提供了数据组合并的正确和错误示例
- 强化了强制要求和禁止事项

### 7. 示例保持一致
**保持原有示例**：确保与现有使用习惯的兼容性

## 优化效果

### 1. 规范性提升
- 所有描述格式都基于CSV文件的标准要求
- 明确了每个字段的填写规范和必填性
- 统一了术语定义和使用标准

### 2. 操作性增强
- 提供了具体的格式模板
- 明确了每个步骤的执行要求
- 增加了实际案例参考

### 3. 一致性保证
- 基于COSMIC标准的统一定义
- 确保与功能点拆分表的完全对应
- 消除了歧义和不确定性

### 4. 完整性提升
- 覆盖了CSV文件中的所有字段要求
- 包含了所有必要的约束条件
- 提供了完整的操作指南

## 关键改进点

1. **格式规范化**：所有描述都采用标准格式（主+谓+宾）
2. **约束结构化**：将分散的注意事项整理为结构化的约束体系
3. **定义标准化**：基于COSMIC标准和CSV文件统一所有概念定义
4. **要求明确化**：每个字段都明确了必填性和具体要求
5. **示例实用化**：提供了基于实际案例的参考模板

## 使用建议

1. **严格遵循格式要求**：按照优化后的格式规范进行功能拆解
2. **充分利用约束指导**：参考10个约束类别确保拆解质量
3. **参考填写规范**：使用新增的填写规范参考提高准确性
4. **合理使用知识库**：按照指南充分利用知识库上下文信息
5. **保持一致性**：确保所有拆解结果符合统一标准

通过这次优化，prompt.md现在完全符合《功能点拆分表（示例）.csv》的要求，能够指导生成高质量、标准化的功能点拆分结果。
