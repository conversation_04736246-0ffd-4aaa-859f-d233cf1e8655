DeepWiki | AI documentation you can talk to, for every repo[Get free private DeepWikis in Devin](/private-repo)[DeepWiki](https://deepwiki.com)[Get free private DeepWikis withDevin](/private-repo)Share

# Which repo would you like to understand?

Add repo[microsoft/vscode

Visual Studio Code

170.1k](/microsoft/vscode)[mark3labs/mcp-go

A Go implementation of the Model Context Protocol (MCP), enabling seamless integration between LLM applications and external data sources and tools.

3.4k](/mark3labs/mcp-go)[antiwork/gumroad5.2k](/antiwork/gumroad)[langchain-ai/local-deep-researcher

Fully local web research and report writing assistant

7.0k](/langchain-ai/local-deep-researcher)[meta-llama/llama-models

Utilities intended for use with Llama models.

6.8k](/meta-llama/llama-models)[huggingface/transformers

🤗 Transformers: State-of-the-art Machine Learning for Pytorch, TensorFlow, and JAX.

143.1k](/huggingface/transformers)[langchain-ai/langchain

🦜🔗 Build context-aware reasoning applications

105.8k](/langchain-ai/langchain)[expressjs/express](/expressjs/express)[lodash/lodash

A modern JavaScript utility library delivering modularity, performance, & extras.

60.3k](/lodash/lodash)[sqlite/sqlite

Official Git mirror of the SQLite source tree

7.7k](/sqlite/sqlite)[microsoft/monaco-editor

A browser based code editor

42.1k](/microsoft/monaco-editor)[openai/openai-agents-python

A lightweight, powerful framework for multi-agent workflows

8.8k](/openai/openai-agents-python)[openai/openai-python

The official Python library for the OpenAI API

26.3k](/openai/openai-python)[anthropics/anthropic-sdk-python1.9k](/anthropics/anthropic-sdk-python)[microsoft/markitdown

Python tool for converting files and office documents to Markdown.

49.2k](/microsoft/markitdown)[hydralauncher/hydra

Hydra is a game launcher with its own embedded bittorrent client

12.3k](/hydralauncher/hydra)[redis/redis](/redis/redis)[microsoft/BitNet

Official inference framework for 1-bit LLMs

13.2k](/microsoft/BitNet)[infiniflow/ragflow

RAGFlow is an open-source RAG (Retrieval-Augmented Generation) engine based on deep document understanding.

49.3k](/infiniflow/ragflow)[localsend/localsend

An open-source cross-platform alternative to AirDrop

60.1k](/localsend/localsend)[colinhacks/zod

TypeScript-first schema validation with static type inference

37.1k](/colinhacks/zod)[mermaid-js/mermaid

Generation of diagrams like flowcharts or sequence diagrams from text in a similar manner as markdown

78.2k](/mermaid-js/mermaid)[microsoft/playwright

Playwright is a framework for Web Testing and Automation. It allows testing Chromium, Firefox and WebKit with a single API.

71.7k](/microsoft/playwright)[remy/nodemon

Monitor for any changes in your node.js application and automatically restart the server - perfect for development

26.5k](/remy/nodemon)[mlflow/mlflow

Open source platform for the machine learning lifecycle

20.2k](/mlflow/mlflow)[freeCodeCamp/freeCodeCamp

freeCodeCamp.org's open-source codebase and curriculum. Learn to code for free.

416.6k](/freeCodeCamp/freeCodeCamp)[codecrafters-io/build-your-own-x

Master programming by recreating your favorite technologies from scratch.

372.1k](/codecrafters-io/build-your-own-x)[EbookFoundation/free-programming-books

:books: Freely available programming books

355.0k](/EbookFoundation/free-programming-books)[public-apis/public-apis

A collective list of free APIs

335.7k](/public-apis/public-apis)[jwasham/coding-interview-university

A complete computer science study plan to become a software engineer.

314.4k](/jwasham/coding-interview-university)[kamranahmedse/developer-roadmap

Interactive roadmaps, guides and other educational content to help developers grow in their careers.

313.8k](/kamranahmedse/developer-roadmap)[donnemartin/system-design-primer

Learn how to design large-scale systems. Prep for the system design interview. Includes Anki flashcards.

296.5k](/donnemartin/system-design-primer)[996icu/996.ICU

Repo for counting stars and contributing. Press F to pay respect to glorious developers.

270.5k](/996icu/996.ICU)[facebook/react

The library for web and native user interfaces.

234.7k](/facebook/react)[practical-tutorials/project-based-learning

Curated list of project-based tutorials

224.5k](/practical-tutorials/project-based-learning)[vuejs/vue

This is the repo for Vue 2. For Vue 3, go to https://github.com/vuejs/core

208.7k](/vuejs/vue)[TheAlgorithms/Python

All Algorithms implemented in Python

199.5k](/TheAlgorithms/Python)[torvalds/linux

Linux kernel source tree

191.8k](/torvalds/linux)[trekhleb/javascript-algorithms

📝 Algorithms and data structures implemented in JavaScript with explanations and links to further readings

191.0k](/trekhleb/javascript-algorithms)[tensorflow/tensorflow

An Open Source Machine Learning Framework for Everyone

189.4k](/tensorflow/tensorflow)[getify/You-Dont-Know-JS

A book series (2 published editions) on the JS language.

181.8k](/getify/You-Dont-Know-JS)[CyC2018/CS-Notes

:books: 技术面试必备基础知识、Leetcode、计算机操作系统、计算机网络、系统设计

179.8k](/CyC2018/CS-Notes)[ossu/computer-science

🎓 Path to a free self-taught education in Computer Science!

178.2k](/ossu/computer-science)[Significant-Gravitas/AutoGPT

AutoGPT is the vision of accessible AI for everyone, to use and to build on. Our mission is to provide the tools, so that you can focus on what matters.

174.5k](/Significant-Gravitas/AutoGPT)[twbs/bootstrap

The most popular HTML, CSS, and JavaScript framework for developing responsive, mobile first projects on the web.

172.2k](/twbs/bootstrap)[flutter/flutter

Flutter makes it easy and fast to build beautiful apps for mobile and beyond

169.8k](/flutter/flutter)[github/gitignore

A collection of useful .gitignore templates

165.8k](/github/gitignore)[jackfrued/Python-100-Days

Python - 100天从新手到大师

164.1k](/jackfrued/Python-100-Days)[trimstray/the-book-of-secret-knowledge

A collection of inspiring lists, manuals, cheatsheets, blogs, hacks, one-liners, cli/web tools and more.

163.3k](/trimstray/the-book-of-secret-knowledge)[jlevy/the-art-of-command-line

Master the command line, in one page

155.6k](/jlevy/the-art-of-command-line)[AUTOMATIC1111/stable-diffusion-webui

Stable Diffusion web UI

151.3k](/AUTOMATIC1111/stable-diffusion-webui)[Snailclimb/JavaGuide

「Java学习+面试指南」一份涵盖大部分 Java 程序员所需要掌握的核心知识。准备 Java 面试，首选 JavaGuide！

149.2k](/Snailclimb/JavaGuide)[airbnb/javascript

JavaScript Style Guide

146.6k](/airbnb/javascript)[ollama/ollama

Get up and running with Llama 3.3, DeepSeek-R1, Phi-4, Gemma 3, Mistral Small 3.1 and other large language models.

137.6k](/ollama/ollama)[ytdl-org/youtube-dl

Command-line program to download videos from YouTube.com and other video sites

135.2k](/ytdl-org/youtube-dl)[massgravel/Microsoft-Activation-Scripts

Open-source Windows and Office activator featuring HWID, Ohook, TSforge, KMS38, and Online KMS activation methods, along with advanced troubleshooting.

131.3k](/massgravel/Microsoft-Activation-Scripts)[vercel/next.js

The React Framework

131.1k](/vercel/next.js)[labuladong/fucking-algorithm

刷算法全靠套路，认准 labuladong 就够了！English version supported! Crack LeetCode, not only how, but also why.

127.6k](/labuladong/fucking-algorithm)[golang/go

The Go programming language

127.2k](/golang/go)[yangshun/tech-interview-handbook

💯 Curated coding interview preparation materials for busy software engineers

125.3k](/yangshun/tech-interview-handbook)[Chalarangelo/30-seconds-of-code

Coding articles to level up your development skills

123.4k](/Chalarangelo/30-seconds-of-code)[facebook/react-native

A framework for building native applications using React

121.6k](/facebook/react-native)[Genymobile/scrcpy

Display and control your Android device

120.7k](/Genymobile/scrcpy)[microsoft/PowerToys

Windows system utilities to maximize productivity

117.4k](/microsoft/PowerToys)[electron/electron

:electron: Build cross-platform desktop apps with JavaScript, HTML, and CSS

116.4k](/electron/electron)[kubernetes/kubernetes

Production-Grade Container Scheduling and Management

114.5k](/kubernetes/kubernetes)[justjavac/free-programming-books-zh\_CN

:books: 免费的计算机编程类中文书籍，欢迎投稿

113.4k](/justjavac/free-programming-books-zh_CN)[krahets/hello-algo

《Hello 算法》：动画图解、一键运行的数据结构与算法教程。支持 Python, Java, C++, C, C#, JS, Go, Swift, Rust, Ruby, Kotlin, TS, Dart 代码。简体版和繁体版同步更新，English version ongoing

111.7k](/krahets/hello-algo)[nodejs/node

Node.js JavaScript runtime ✨🐢🚀✨

110.6k](/nodejs/node)[d3/d3

Bring data to life with SVG, Canvas and HTML. :bar\_chart::chart\_with\_upwards\_trend::tada:

110.4k](/d3/d3)[yt-dlp/yt-dlp

A feature-rich command-line audio/video downloader

107.9k](/yt-dlp/yt-dlp)[axios/axios

Promise based HTTP client for the browser and node.js

106.7k](/axios/axios)[microsoft/TypeScript

TypeScript is a superset of JavaScript that compiles to clean JavaScript output.

104.0k](/microsoft/TypeScript)[facebook/create-react-app

Set up a modern web app by running one command.

103.2k](/facebook/create-react-app)[rust-lang/rust

Empowering everyone to build reliable and efficient software.

102.8k](/rust-lang/rust)[denoland/deno

A modern runtime for JavaScript and TypeScript.

102.6k](/denoland/deno)[goldbergyoni/nodebestpractices

:white\_check\_mark: The Node.js best practices list (July 2024)

102.4k](/goldbergyoni/nodebestpractices)[521xueweihan/HelloGitHub

:octocat: 分享 GitHub 上有趣、入门级的开源项目。Share interesting, entry-level open source projects on GitHub.

101.5k](/521xueweihan/HelloGitHub)[microsoft/terminal

The new Windows Terminal and the original Windows console host, all in the same place!

97.7k](/microsoft/terminal)[excalidraw/excalidraw

Virtual whiteboard for sketching hand-drawn like diagrams

97.7k](/excalidraw/excalidraw)[angular/angular

Deliver web apps with confidence 🚀

97.5k](/angular/angular)[godotengine/godot

Godot Engine – Multi-platform 2D and 3D game engine

96.3k](/godotengine/godot)[deepseek-ai/DeepSeek-V395.7k](/deepseek-ai/DeepSeek-V3)[mui/material-ui

Material UI: Comprehensive React component library that implements Google's Material Design. Free forever.

95.4k](/mui/material-ui)[ripienaar/free-for-dev

A list of SaaS, PaaS and IaaS offerings that have free tiers of interest to devops and infradev

94.7k](/ripienaar/free-for-dev)[ant-design/ant-design

An enterprise-class UI design language and React UI library

94.3k](/ant-design/ant-design)[ryanmcdermott/clean-code-javascript

Clean Code concepts adapted for JavaScript

92.8k](/ryanmcdermott/clean-code-javascript)[fatedier/frp

A fast reverse proxy to help you expose a local server behind a NAT or firewall to the internet.

92.7k](/fatedier/frp)[iptv-org/iptv

Collection of publicly available IPTV channels from all over the world

92.5k](/iptv-org/iptv)[langgenius/dify

Dify is an open-source LLM app development platform. Dify's intuitive interface combines AI workflow, RAG pipeline, agent capabilities, model management, observability features and more, letting you q...

92.4k](/langgenius/dify)[papers-we-love/papers-we-love

Papers from the computer science community to read and discuss.

92.4k](/papers-we-love/papers-we-love)[tauri-apps/tauri

Build smaller, faster, and more secure desktop and mobile applications with a web frontend.

91.6k](/tauri-apps/tauri)[nvbn/thefuck

Magnificent app which corrects your previous console command.

91.5k](/nvbn/thefuck)[iluwatar/java-design-patterns

Design patterns implemented in Java

91.3k](/iluwatar/java-design-patterns)[puppeteer/puppeteer

JavaScript API for Chrome and Firefox

90.4k](/puppeteer/puppeteer)[open-webui/open-webui

User-friendly AI Interface (Supports Ollama, OpenAI API, ...)

89.9k](/open-webui/open-webui)[PanJiaChen/vue-element-admin

:tada: A magical vue admin https://panjiachen.github.io/vue-element-admin

89.1k](/PanJiaChen/vue-element-admin)[pytorch/pytorch

Tensors and Dynamic neural networks in Python with strong GPU acceleration

89.1k](/pytorch/pytorch)[neovim/neovim

Vim-fork focused on extensibility and usability

88.7k](/neovim/neovim)[deepseek-ai/DeepSeek-R188.5k](/deepseek-ai/DeepSeek-R1)[microsoft/Web-Dev-For-Beginners

24 Lessons, 12 Weeks, Get Started as a Web Developer

87.3k](/microsoft/Web-Dev-For-Beginners)[tailwindlabs/tailwindcss

A utility-first CSS framework for rapid UI development.

87.2k](/tailwindlabs/tailwindcss)[rustdesk/rustdesk

An open-source remote desktop application designed for self-hosting, as an alternative to TeamViewer.

86.9k](/rustdesk/rustdesk)[mtdvio/every-programmer-should-know

A collection of (mostly) technical things every software developer should know about

86.9k](/mtdvio/every-programmer-should-know)[storybookjs/storybook

Storybook is the industry standard workshop for building, documenting, and testing UI components in isolation

86.4k](/storybookjs/storybook)[shadcn-ui/ui

A set of beautifully-designed, accessible components and a code distribution platform. Works with your favorite frameworks. Open Source. Open Code.

85.5k](/shadcn-ui/ui)[nvm-sh/nvm

Node Version Manager - POSIX-compliant bash script to manage multiple active node.js versions

84.0k](/nvm-sh/nvm)[fastapi/fastapi

FastAPI framework, high performance, easy to learn, fast to code, ready for production

83.4k](/fastapi/fastapi)[django/django

The Web framework for perfectionists with deadlines.

83.2k](/django/django)[florinpop17/app-ideas

A Collection of application ideas which can be used to improve your coding skills.

82.9k](/florinpop17/app-ideas)[bitcoin/bitcoin

Bitcoin Core integration/staging tree

82.9k](/bitcoin/bitcoin)[ChatGPTNextWeb/NextChat

✨ Light and Fast AI Assistant. Support: Web | iOS | MacOS | Android | Linux | Windows

82.8k](/ChatGPTNextWeb/NextChat)[sveltejs/svelte

web development for the rest of us

82.3k](/sveltejs/svelte)[n8n-io/n8n

Fair-code workflow automation platform with native AI capabilities. Combine visual building with custom code, self-host or cloud, 400+ integrations.

82.1k](/n8n-io/n8n)[gin-gonic/gin

Gin is a HTTP web framework written in Go (Golang). It features a Martini-like API with much better performance -- up to 40 times faster. If you need smashing performance, get yourself some Gin.

81.8k](/gin-gonic/gin)[opencv/opencv

Open Source Computer Vision Library

81.7k](/opencv/opencv)[animate-css/animate.css

🍿 A cross-browser library of CSS animations. As easy to use as an easy thing.

81.6k](/animate-css/animate.css)[gothinkster/realworld

"The mother of all demo apps" — Exemplary fullstack Medium.com clone powered by React, Angular, Node, Django, and many more

81.4k](/gothinkster/realworld)[supabase/supabase

The open source Firebase alternative. Supabase gives you a dedicated Postgres database to build your web, mobile, and AI applications.

81.0k](/supabase/supabase)[laravel/laravel

Laravel is a web application framework with expressive, elegant syntax. We’ve already laid the foundation for your next big idea — freeing you to create without sweating the small things.

80.6k](/laravel/laravel)[openai/whisper

Robust Speech Recognition via Large-Scale Weak Supervision

80.1k](/openai/whisper)[macrozheng/mall

mall项目是一套电商系统，包括前台商城系统及后台管理系统，基于Spring Boot+MyBatis实现，采用Docker容器化部署。 前台商城系统包含首页门户、商品推荐、商品搜索、商品展示、购物车、订单流程、会员中心、客户服务、帮助中心等模块。 后台管理系统包含商品管理、订单管理、会员管理、促销管理、运营管理、内容管理、统计报表、财务管理、权限管理、设置等模块。

79.9k](/macrozheng/mall)[gohugoio/hugo

The world’s fastest framework for building websites.

79.6k](/gohugoio/hugo)[tonsky/FiraCode

Free monospaced font with programming ligatures

78.7k](/tonsky/FiraCode)[microsoft/generative-ai-for-beginners

21 Lessons, Get Started Building with Generative AI 🔗 https://microsoft.github.io/generative-ai-for-beginners/

78.5k](/microsoft/generative-ai-for-beginners)[ggml-org/llama.cpp

LLM inference in C/C++

78.3k](/ggml-org/llama.cpp)[2dust/v2rayN

A GUI client for Windows, Linux and macOS, support Xray and sing-box and others

78.2k](/2dust/v2rayN)[home-assistant/core

:house\_with\_garden: Open source home automation that puts local control and privacy first.

78.1k](/home-assistant/core)[oven-sh/bun

Incredibly fast JavaScript runtime, bundler, test runner, and package manager – all in one

77.5k](/oven-sh/bun)[tensorflow/models

Models and examples built with TensorFlow

77.5k](/tensorflow/models)[doocs/advanced-java

😮 Core Interview Questions & Answers For Experienced Java(Backend) Developers | 互联网 Java 工程师进阶知识完全扫盲：涵盖高并发、分布式、高可用、微服务、海量数据处理等领域知识

77.5k](/doocs/advanced-java)[spring-projects/spring-boot

Spring Boot helps you to create Spring-powered, production-grade applications and services with absolute minimum fuss.

76.8k](/spring-projects/spring-boot)[3b1b/manim

Animation engine for explanatory math videos

76.8k](/3b1b/manim)[MisterBooo/LeetCodeAnimation

Demonstrate all the questions on LeetCode in the form of animation.（用动画的形式呈现解LeetCode题目的思路）

75.8k](/MisterBooo/LeetCodeAnimation)[FortAwesome/Font-Awesome

The iconic SVG, font, and CSS toolkit

74.9k](/FortAwesome/Font-Awesome)[comfyanonymous/ComfyUI

The most powerful and modular diffusion model GUI, api and backend with a graph/nodes interface.

74.5k](/comfyanonymous/ComfyUI)[netdata/netdata

X-Ray Vision for your infrastructure!

74.3k](/netdata/netdata)[typicode/json-server

Get a full fake REST API with zero coding in less than 30 seconds (seriously)

74.0k](/typicode/json-server)[bregman-arie/devops-exercises

Linux, Jenkins, AWS, SRE, Prometheus, Docker, Python, Ansible, Git, Kubernetes, Terraform, OpenStack, SQL, NoSQL, Azure, GCP, DNS, Elastic, Network, Virtualization. DevOps Interview Questions

74.0k](/bregman-arie/devops-exercises)[Anduin2017/HowToCook

程序员在家做饭方法指南。Programmer's guide about how to cook at home (Simplified Chinese only).

73.8k](/Anduin2017/HowToCook)[nomic-ai/gpt4all

GPT4All: Run Local LLMs on Any Device. Open-source and available for commercial use.

73.1k](/nomic-ai/gpt4all)[anuraghazra/github-readme-stats

:zap: Dynamically generated stats for your github readmes

72.6k](/anuraghazra/github-readme-stats)[fighting41love/funNLP

中英文敏感词、语言检测、中外手机/电话归属地/运营商查询、名字推断性别、手机号抽取、身份证抽取、邮箱抽取、中日文人名库、中文缩写库、拆字词典、词汇情感值、停用词、反动词表、暴恐词表、繁简体转换、英文模拟中文发音、汪峰歌词生成器、职业名称词库、同义词库、反义词库、否定词库、汽车品牌词库、汽车零件词库、连续英文切割、各种中文词向量、公司名字大全、古诗词库、IT词库、财经词库、成语词库、地名词库、历史名...

72.5k](/fighting41love/funNLP)[vitejs/vite

Next generation frontend tooling. It's fast!

72.1k](/vitejs/vite)[microsoft/ML-For-Beginners

12 weeks, 26 lessons, 52 quizzes, classic Machine Learning for all

71.9k](/microsoft/ML-For-Beginners)[ByteByteGoHq/system-design-101

Explain complex systems using visuals and simple terms. Help you prepare for system design interviews.

71.7k](/ByteByteGoHq/system-design-101)[hoppscotch/hoppscotch

Open source API development ecosystem - https://hoppscotch.io (open-source alternative to Postman, Insomnia)

71.2k](/hoppscotch/hoppscotch)[coder/code-server

VS Code in the browser

71.1k](/coder/code-server)[nestjs/nest

A progressive Node.js framework for building efficient, scalable, and enterprise-grade server-side applications with TypeScript/JavaScript 🚀

70.5k](/nestjs/nest)[CompVis/stable-diffusion

A latent text-to-image diffusion model

70.4k](/CompVis/stable-diffusion)[thedaviddias/Front-End-Checklist

🗂 The perfect Front-End Checklist for modern websites and meticulous developers

69.9k](/thedaviddias/Front-End-Checklist)[abi/screenshot-to-code

Drop in a screenshot and convert it to clean code (HTML/Tailwind/React/Vue)

69.6k](/abi/screenshot-to-code)[moby/moby

The Moby Project - a collaborative project for the container ecosystem to assemble container-based systems

69.5k](/moby/moby)[syncthing/syncthing

Open Source Continuous File Synchronization

69.5k](/syncthing/syncthing)[junegunn/fzf

:cherry\_blossom: A command-line fuzzy finder

69.5k](/junegunn/fzf)[pallets/flask

The Python micro framework for building web applications.

69.3k](/pallets/flask)[hakimel/reveal.js

The HTML Presentation Framework

68.7k](/hakimel/reveal.js)[base/node

Everything required to run your own Base node

68.5k](/base/node)[Developer-Y/cs-video-courses

List of Computer Science courses with video lectures.

68.5k](/Developer-Y/cs-video-courses)[d2l-ai/d2l-zh

《动手学深度学习》：面向中文读者、能运行、可讨论。中英文版被70多个国家的500多所大学用于教学。

68.5k](/d2l-ai/d2l-zh)[swiftlang/swift

The Swift Programming Language

68.4k](/swiftlang/swift)[binary-husky/gpt\_academic

为GPT/GLM等LLM大语言模型提供实用化交互接口，特别优化论文阅读/润色/写作体验，模块化设计，支持自定义快捷按钮&函数插件，支持Python和C++等项目剖析&自译解功能，PDF/LaTex论文翻译&总结功能，支持并行问询多种LLM模型，支持chatglm3等本地模型。接入通义千问, deepseekcoder, 讯飞星火, 文心一言, llama2, rwkv, claude2, moss...

68.2k](/binary-husky/gpt_academic)[grafana/grafana

The open and composable observability and data visualization platform. Visualize metrics, logs, and traces from multiple sources like Prometheus, Loki, Elasticsearch, InfluxDB, Postgres and many more....

67.5k](/grafana/grafana)[protocolbuffers/protobuf

Protocol Buffers - Google's data interchange format

67.3k](/protocolbuffers/protobuf)[louislam/uptime-kuma

A fancy self-hosted monitoring tool

67.3k](/louislam/uptime-kuma)[sdmg15/Best-websites-a-programmer-should-visit

:link: Some useful websites for programmers.

66.6k](/sdmg15/Best-websites-a-programmer-should-visit)[python/cpython

The Python programming language

66.3k](/python/cpython)[tesseract-ocr/tesseract

Tesseract Open Source OCR Engine (main repository)

66.2k](/tesseract-ocr/tesseract)[strapi/strapi

🚀 Strapi is the leading open-source headless CMS. It’s 100% JavaScript/TypeScript, fully customizable, and developer-first.

66.1k](/strapi/strapi)[ventoy/Ventoy

A new bootable USB solution.

66.1k](/ventoy/Ventoy)[apache/superset

Apache Superset is a Data Visualization and Data Exploration Platform

65.8k](/apache/superset)[chartjs/Chart.js

Simple HTML5 Charts using the <canvas> tag

65.7k](/chartjs/Chart.js)[webpack/webpack

A bundler for javascript and friends. Packs many modules into a few bundled assets. Code Splitting allows for loading parts of the application on demand. Through "loaders", modules can be CommonJs, AM...

65.2k](/webpack/webpack)[leonardomso/33-js-concepts

📜 33 JavaScript concepts every developer should know.

65.0k](/leonardomso/33-js-concepts)[ocornut/imgui

Dear ImGui: Bloat-free Graphical User interface for C++ with minimal dependencies

64.8k](/ocornut/imgui)[ansible/ansible

Ansible is a radically simple IT automation platform that makes your applications and systems easier to deploy and maintain. Automate everything from code deployment to network configuration to cloud ...

64.8k](/ansible/ansible)[swisskyrepo/PayloadsAllTheThings

A list of useful payloads and bypass for Web Application Security and Pentest/CTF

64.7k](/swisskyrepo/PayloadsAllTheThings)[kdn251/interviews

Everything you need to know to get the job.

64.1k](/kdn251/interviews)[xtekky/gpt4free

The official gpt4free repository | various collection of powerful language models | o3 and deepseek r1, gpt-4.5

64.1k](/xtekky/gpt4free)[immich-app/immich

High performance self-hosted photo and video management solution.

63.8k](/immich-app/immich)[lydiahallie/javascript-questions

A long list of (advanced) JavaScript questions, and their explanations :sparkles:

63.8k](/lydiahallie/javascript-questions)[sherlock-project/sherlock

Hunt down social media accounts by username across social networks

63.7k](/sherlock-project/sherlock)[obsproject/obs-studio

OBS Studio - Free and open source software for live streaming and screen recording

63.6k](/obsproject/obs-studio)[caddyserver/caddy

Fast and extensible multi-platform HTTP/1-2-3 web server with automatic HTTPS

63.6k](/caddyserver/caddy)[apache/echarts

Apache ECharts is a powerful, interactive charting and data visualization library for browser

63.3k](/apache/echarts)[openai/openai-cookbook

Examples and guides for using the OpenAI API

63.2k](/openai/openai-cookbook)[twitter/the-algorithm

Source code for Twitter's Recommendation Algorithm

63.1k](/twitter/the-algorithm)[Eugeny/tabby

A terminal for a more modern age

63.1k](/Eugeny/tabby)[keras-team/keras

Deep Learning for humans

62.9k](/keras-team/keras)[kelseyhightower/nocode

The best way to write secure and reliable applications. Write nothing; deploy nowhere.

62.6k](/kelseyhightower/nocode)[resume/resume.github.com

Resumes generated using the GitHub informations

62.4k](/resume/resume.github.com)[FuelLabs/sway

🌴 Empowering everyone to build reliable and efficient smart contracts.

62.4k](/FuelLabs/sway)[danielmiessler/SecLists

SecLists is the security tester's companion. It's a collection of multiple types of lists used during security assessments, collected in one place. List types include usernames, passwords, URLs, sensi...

62.2k](/danielmiessler/SecLists)[AppFlowy-IO/AppFlowy

Bring projects, wikis, and teams together with AI. AppFlowy is the AI collaborative workspace where you achieve more without losing control of your data. The leading open source Notion alternative.

62.1k](/AppFlowy-IO/AppFlowy)[PKUFlyingPig/cs-self-learning

计算机自学指南

62.1k](/PKUFlyingPig/cs-self-learning)[scikit-learn/scikit-learn

scikit-learn: machine learning in Python

61.8k](/scikit-learn/scikit-learn)[chrislgarry/Apollo-11

Original Apollo 11 Guidance Computer (AGC) source code for the command and lunar modules.

61.6k](/chrislgarry/Apollo-11)[TheAlgorithms/Java

All Algorithms implemented in Java

61.3k](/TheAlgorithms/Java)[reduxjs/redux

A JS library for predictable global state management

61.1k](/reduxjs/redux)[Alvin9999/new-pac

翻墙-科学上网、自由上网、免费科学上网、免费翻墙、fanqiang、油管youtube/视频下载、软件、VPN、一键翻墙浏览器，vps一键搭建翻墙服务器脚本/教程，免费shadowsocks/ss/ssr/v2ray/goflyway账号/节点，翻墙梯子，电脑、手机、iOS、安卓、windows、Mac、Linux、路由器翻墙、科学上网、youtube视频下载、youtube油管镜像/免翻墙网站、...

60.5k](/Alvin9999/new-pac)[bradtraversy/design-resources-for-developers

Curated list of design and UI resources from stock photos, web templates, CSS frameworks, UI libraries, tools and much more

60.4k](/bradtraversy/design-resources-for-developers)[atom/atom

:atom: The hackable text editor

60.4k](/atom/atom)[h5bp/Front-end-Developer-Interview-Questions

A list of helpful front-end related questions you can use to interview potential candidates, test yourself or completely ignore.

60.2k](/h5bp/Front-end-Developer-Interview-Questions)[xingshaocheng/architect-awesome

后端架构师技术图谱

60.1k](/xingshaocheng/architect-awesome)[labmlai/annotated\_deep\_learning\_paper\_implementations

🧑‍🏫 60+ Implementations/tutorials of deep learning papers with side-by-side notes 📝; including transformers (original, xl, switch, feedback, vit, ...), optimizers (adam, adabelief, sophia, ...), ga...

60.0k](/labmlai/annotated_deep_learning_paper_implementations)[adam-p/markdown-here

Google Chrome, Firefox, and Thunderbird extension that lets you write email in Markdown and render it before sending.

59.9k](/adam-p/markdown-here)[nektos/act

Run your GitHub Actions locally 🚀

59.8k](/nektos/act)[facebook/docusaurus

Easy to maintain open source documentation websites.

59.4k](/facebook/docusaurus)[OpenInterpreter/open-interpreter

A natural language interface for computers

59.1k](/OpenInterpreter/open-interpreter)[lobehub/lobe-chat

🤯 Lobe Chat - an open-source, modern-design AI chat framework. Supports Multi AI Providers( OpenAI / Claude 3 / Gemini / Ollama / DeepSeek / Qwen), Knowledge Base (file upload / knowledge management ...

58.9k](/lobehub/lobe-chat)[jesseduffield/lazygit

simple terminal UI for git commands

58.9k](/jesseduffield/lazygit)[shadowsocks/shadowsocks-windows

A C# port of shadowsocks

58.8k](/shadowsocks/shadowsocks-windows)[localstack/localstack

💻 A fully functional local AWS cloud stack. Develop and test your cloud & Serverless apps offline

58.6k](/localstack/localstack)[alacritty/alacritty

A cross-platform, OpenGL terminal emulator.

58.4k](/alacritty/alacritty)[prometheus/prometheus

The Prometheus monitoring system and time series database.

58.2k](/prometheus/prometheus)[meta-llama/llama

Inference code for Llama models

58.1k](/meta-llama/llama)[spring-projects/spring-framework

Spring Framework

57.9k](/spring-projects/spring-framework)[juliangarnier/anime

JavaScript animation engine

57.8k](/juliangarnier/anime)[FuelLabs/fuel-core

Rust full node implementation of the Fuel v2 protocol.

57.7k](/FuelLabs/fuel-core)[rust-lang/rustlings

:crab: Small exercises to get you used to reading and writing Rust code!

57.7k](/rust-lang/rustlings)[ryanoasis/nerd-fonts

Iconic font aggregator, collection, & patcher. 3,600+ icons, 50+ patched fonts: Hack, Source Code Pro, more. Glyph collections: Font Awesome, Material Design Icons, Octicons, & more

57.3k](/ryanoasis/nerd-fonts)[zed-industries/zed

Code at the speed of thought – Zed is a high-performance, multiplayer code editor from the creators of Atom and Tree-sitter.

57.1k](/zed-industries/zed)[nuxt/nuxt

The Intuitive Vue Framework.

56.8k](/nuxt/nuxt)[browser-use/browser-use

Make websites accessible for AI agents

56.4k](/browser-use/browser-use)[NationalSecurityAgency/ghidra

Ghidra is a software reverse engineering (SRE) framework

56.3k](/NationalSecurityAgency/ghidra)[Stirling-Tools/Stirling-PDF

#1 Locally hosted web application that allows you to perform various operations on PDF files

56.0k](/Stirling-Tools/Stirling-PDF)[scutan90/DeepLearning-500-questions

深度学习500问，以问答形式对常用的概率知识、线性代数、机器学习、深度学习、计算机视觉等热点问题进行阐述，以帮助自己及有需要的读者。 全书分为18个章节，50余万字。由于水平有限，书中不妥之处恳请广大读者批评指正。 未完待续............ 如有意合作，联系****************** 版权所有，违权必究 Tan 2018.0...

55.8k](/scutan90/DeepLearning-500-questions)[gatsbyjs/gatsby

The best React-based framework with performance, scalability and security built in.

55.8k](/gatsbyjs/gatsby)[zylon-ai/private-gpt

Interact with your documents using the power of GPT, 100% privately, no data leaks

55.6k](/zylon-ai/private-gpt)[youngyangyang04/leetcode-master

《代码随想录》LeetCode 刷题攻略：200道经典题目刷题顺序，共60w字的详细图解，视频难点剖析，50余张思维导图，支持C++，Java，Python，Go，JavaScript等多语言版本，从此算法学习不再迷茫！🔥🔥 来看看，你会发现相见恨晚！🚀

55.5k](/youngyangyang04/leetcode-master)[soimort/you-get

:arrow\_double\_down: Dumb downloader that scrapes the web

55.5k](/soimort/you-get)[azl397985856/leetcode

LeetCode Solutions: A Record of My Problem Solving Journey.( leetcode题解，记录自己的leetcode解题之路。)

55.2k](/azl397985856/leetcode)[langflow-ai/langflow

Langflow is a powerful tool for building and deploying AI-powered agents and workflows.

55.2k](/langflow-ai/langflow)[dair-ai/Prompt-Engineering-Guide

🐙 Guides, papers, lecture, notebooks and resources for prompt engineering

55.1k](/dair-ai/Prompt-Engineering-Guide)[clash-verge-rev/clash-verge-rev

A modern GUI client based on Tauri, designed to run in Windows, macOS and Linux for tailored proxy experience

55.1k](/clash-verge-rev/clash-verge-rev)[tldr-pages/tldr

📚 Collaborative cheatsheets for console commands

54.8k](/tldr-pages/tldr)[geekan/MetaGPT

🌟 The Multi-Agent Framework: First AI Software Company, Towards Natural Language Programming

54.7k](/geekan/MetaGPT)[ageitgey/face\_recognition

The world's simplest facial recognition api for Python and the command line

54.6k](/ageitgey/face_recognition)[remix-run/react-router

Declarative routing for React

54.6k](/remix-run/react-router)[unionlabs/union

The trust-minimized, zero-knowledge bridging protocol, designed for censorship resistance, extremely high security, and usage in decentralized finance.

54.4k](/unionlabs/union)[ElemeFE/element

A Vue.js 2.0 UI Toolkit for Web

54.2k](/ElemeFE/element)[traefik/traefik

The Cloud Native Application Proxy

54.2k](/traefik/traefik)[ruanyf/weekly

科技爱好者周刊，每周五发布

54.2k](/ruanyf/weekly)[CorentinJ/Real-Time-Voice-Cloning

Clone a voice in 5 seconds to generate arbitrary speech in real-time

54.0k](/CorentinJ/Real-Time-Voice-Cloning)[jgraph/drawio-desktop

Official electron build of draw.io

53.9k](/jgraph/drawio-desktop)[AntonOsika/gpt-engineer

CLI platform to experiment with codegen. Precursor to: https://lovable.dev

53.9k](/AntonOsika/gpt-engineer)[nocodb/nocodb

🔥 🔥 🔥 Open Source Airtable Alternative

53.8k](/nocodb/nocodb)[lencx/ChatGPT

🔮 ChatGPT Desktop Application (Mac, Windows and Linux)

53.7k](/lencx/ChatGPT)[deepfakes/faceswap

Deepfakes Software For All

53.7k](/deepfakes/faceswap)[ultralytics/yolov5

YOLOv5 🚀 in PyTorch > ONNX > CoreML > TFLite

53.4k](/ultralytics/yolov5)[FiloSottile/mkcert

A simple zero-config tool to make locally trusted development certificates with any names you'd like.

53.1k](/FiloSottile/mkcert)[commaai/openpilot

openpilot is an operating system for robotics. Currently, it upgrades the driver assistance system on 300+ supported cars.

53.1k](/commaai/openpilot)[necolas/normalize.css

A modern alternative to CSS resets

53.0k](/necolas/normalize.css)[All-Hands-AI/OpenHands

🙌 OpenHands: Code Less, Make More

52.9k](/All-Hands-AI/OpenHands)[gorhill/uBlock

uBlock Origin - An efficient blocker for Chromium and Firefox. Fast and lean.

52.8k](/gorhill/uBlock)[psf/requests

A simple, yet elegant, HTTP library.

52.8k](/psf/requests)[topjohnwu/Magisk

The Magic Mask for Android

52.2k](/topjohnwu/Magisk)[sharkdp/bat

A cat(1) clone with wings.

52.2k](/sharkdp/bat)[Z4nzu/hackingtool

ALL IN ONE Hacking Tool For Hackers

52.2k](/Z4nzu/hackingtool)[golang-standards/project-layout

Standard Go Project Layout

51.9k](/golang-standards/project-layout)[minio/minio

MinIO is a high-performance, S3 compatible object store, open sourced under GNU AGPLv3 license.

51.8k](/minio/minio)[BurntSushi/ripgrep

ripgrep recursively searches directories for a regex pattern while respecting your gitignore

51.7k](/BurntSushi/ripgrep)[Textualize/rich

Rich is a Python library for rich text and beautiful formatting in the terminal.

51.7k](/Textualize/rich)[pmndrs/zustand

🐻 Bear necessities for state management in React

51.7k](/pmndrs/zustand)[ionic-team/ionic-framework

A powerful cross-platform UI toolkit for building native-quality iOS, Android, and Progressive Web Apps with HTML, CSS, and JavaScript.

51.6k](/ionic-team/ionic-framework)[pi-hole/pi-hole

A black hole for Internet advertisements

51.5k](/pi-hole/pi-hole)[google/material-design-icons

Material Design icons by Google (Material Symbols)

51.4k](/google/material-design-icons)[Semantic-Org/Semantic-UI

Semantic is a UI component framework based around useful principles from natural language.

51.2k](/Semantic-Org/Semantic-UI)[google/guava

Google core libraries for Java

50.7k](/google/guava)[withastro/astro

The web framework for content-driven websites. ⭐️ Star to support our work!

50.5k](/withastro/astro)[meilisearch/meilisearch

A lightning-fast search engine API bringing AI-powered hybrid search to your sites and applications.

50.5k](/meilisearch/meilisearch)[mozilla/pdf.js

PDF Reader in JavaScript

50.5k](/mozilla/pdf.js)[tiimgreen/github-cheat-sheet

A list of cool features of Git and GitHub.

50.4k](/tiimgreen/github-cheat-sheet)[JetBrains/kotlin

The Kotlin Programming Language.

50.4k](/JetBrains/kotlin)[wagoodman/dive

A tool for exploring each layer in a docker image

50.3k](/wagoodman/dive)[hacksider/Deep-Live-Cam

real time face swap and one-click video deepfake with only a single image

50.3k](/hacksider/Deep-Live-Cam)[prettier/prettier

Prettier is an opinionated code formatter.

50.3k](/prettier/prettier)[xai-org/grok-1

Grok open release

50.2k](/xai-org/grok-1)[sickcodes/Docker-OSX

Run macOS VM in a Docker! Run near native OSX-KVM in Docker! X11 Forwarding! CI/CD for OS X Security Research! Docker mac Containers.

50.2k](/sickcodes/Docker-OSX)[astral-sh/uv

An extremely fast Python package and project manager, written in Rust.

50.0k](/astral-sh/uv)[rclone/rclone

"rsync for cloud storage" - Google Drive, S3, Dropbox, Backblaze B2, One Drive, Swift, Hubic, Wasabi, Google Cloud Storage, Azure Blob, Azure Files, Yandex Files

49.9k](/rclone/rclone)[toeverything/AFFiNE

There can be more than Notion and Miro. AFFiNE(pronounced [ə‘fain]) is a next-gen knowledge base that brings planning, sorting and creating all together. Privacy first, open-source, customizable and r...

49.8k](/toeverything/AFFiNE)[facebookresearch/segment-anything

The repository provides code for running inference with the SegmentAnything Model (SAM), links for downloading the trained model checkpoints, and example notebooks that show how to use the model.

49.8k](/facebookresearch/segment-anything)[jgthms/bulma

Modern CSS framework based on Flexbox

49.7k](/jgthms/bulma)[vuejs/core

🖖 Vue.js is a progressive, incrementally-adoptable JavaScript framework for building UI on the web.

49.7k](/vuejs/core)[DefinitelyTyped/DefinitelyTyped

The repository for high quality TypeScript type definitions.

49.6k](/DefinitelyTyped/DefinitelyTyped)[ngosang/trackerslist

Updated list of public BitTorrent trackers

49.5k](/ngosang/trackerslist)[marktext/marktext

📝A simple and elegant markdown editor, available for Linux, macOS and Windows.

49.3k](/marktext/marktext)[mlabonne/llm-course

Course to get into Large Language Models (LLMs) with roadmaps and Colab notebooks.

49.3k](/mlabonne/llm-course)[FFmpeg/FFmpeg

Mirror of https://git.ffmpeg.org/ffmpeg.git

49.2k](/FFmpeg/FFmpeg)[etcd-io/etcd

Distributed reliable key-value store for the most critical data of a distributed system

49.1k](/etcd-io/etcd)[TryGhost/Ghost

Independent technology for modern publishing, memberships, subscriptions and newsletters.

49.0k](/TryGhost/Ghost)[chinese-poetry/chinese-poetry

The most comprehensive database of Chinese poetry 🧶最全中华古诗词数据库, 唐宋两朝近一万四千古诗人, 接近5.5万首唐诗加26万宋诗. 两宋时期1564位词人，21050首词。

49.0k](/chinese-poetry/chinese-poetry)[romkatv/powerlevel10k

A Zsh theme

48.8k](/romkatv/powerlevel10k)[ethereum/go-ethereum

Go implementation of the Ethereum protocol

48.8k](/ethereum/go-ethereum)[laurent22/joplin

Joplin - the privacy-focused note taking app with sync capabilities for Windows, macOS, Linux, Android and iOS.

48.8k](/laurent22/joplin)[cypress-io/cypress

Fast, easy and reliable testing for anything that runs in a browser.

48.5k](/cypress-io/cypress)[AlistGo/alist

🗂️A file list/WebDAV program that supports multiple storages, powered by Gin and Solidjs. / 一个支持多存储的文件列表/WebDAV程序，使用 Gin 和 Solidjs。

48.4k](/AlistGo/alist)[PaddlePaddle/PaddleOCR

Awesome multilingual OCR toolkits based on PaddlePaddle (practical ultra lightweight OCR system, support 80+ languages recognition, provide data annotation and synthesis tools, support training and de...

48.4k](/PaddlePaddle/PaddleOCR)[NARKOZ/hacker-scripts

Based on a true story

48.4k](/NARKOZ/hacker-scripts)

## What is DeepWiki?

DeepWiki provides up-to-date documentation you can talk to, for every repo in the world. Think Deep Research for GitHub.