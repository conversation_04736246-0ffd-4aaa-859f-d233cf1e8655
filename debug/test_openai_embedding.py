#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试OpenAI嵌入模型功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_openai_embedding():
    """测试OpenAI嵌入模型功能"""
    print("=== OpenAI嵌入模型功能测试 ===\n")
    
    try:
        from knowledge_base import OpenAIEmbedding
        from config import EMBEDDING_MODEL, EMBEDDING_API_BASE, EMBEDDING_API_KEY
        
        # 1. 初始化嵌入模型
        print("1. 初始化OpenAI嵌入模型")
        print(f"   - 模型名称: {EMBEDDING_MODEL}")
        print(f"   - API端点: {EMBEDDING_API_BASE}")
        print(f"   - API密钥: {EMBEDDING_API_KEY[:10]}...")
        
        embedding_model = OpenAIEmbedding(
            model_name=EMBEDDING_MODEL,
            api_base=EMBEDDING_API_BASE,
            api_key=EMBEDDING_API_KEY
        )
        print("   ✓ 嵌入模型初始化成功")
        print()
        
        # 2. 测试单个文本编码
        print("2. 测试单个文本编码")
        test_text = "用户管理功能包括用户注册、登录、权限分配等操作"
        print(f"   测试文本: {test_text}")
        
        embeddings = embedding_model.encode([test_text])
        print(f"   ✓ 编码成功，向量维度: {embeddings.shape}")
        print(f"   向量前5个值: {embeddings[0][:5]}")
        print()
        
        # 3. 测试批量文本编码
        print("3. 测试批量文本编码")
        test_texts = [
            "用户管理功能",
            "密码重置操作", 
            "权限分配管理",
            "系统配置设置",
            "数据库表结构"
        ]
        print(f"   测试文本数量: {len(test_texts)}")
        
        batch_embeddings = embedding_model.encode(test_texts, show_progress_bar=True)
        print(f"   ✓ 批量编码成功，形状: {batch_embeddings.shape}")
        print()
        
        # 4. 测试相似度计算
        print("4. 测试相似度计算")
        from sklearn.metrics.pairwise import cosine_similarity
        
        # 计算第一个文本与其他文本的相似度
        similarities = cosine_similarity([batch_embeddings[0]], batch_embeddings[1:]).flatten()
        
        print("   相似度结果:")
        for i, sim in enumerate(similarities):
            print(f"   - '{test_texts[0]}' vs '{test_texts[i+1]}': {sim:.4f}")
        print()
        
        # 5. 测试知识库集成
        print("5. 测试知识库集成")
        from knowledge_base import knowledge_base
        
        if knowledge_base.enabled and knowledge_base.use_chromadb:
            print("   ✓ 知识库已启用ChromaDB模式")
            print(f"   - 嵌入模型类型: {type(knowledge_base.embedding_model).__name__}")
            
            # 测试搜索功能
            print("   测试搜索功能:")
            results = knowledge_base.search_knowledge("用户管理")
            manual_results = results['manual_results']
            sql_results = results['sql_results']
            
            print(f"   - 用户手册结果: {len(manual_results)} 条")
            print(f"   - 数据库实体结果: {len(sql_results)} 条")
            
            if manual_results:
                print(f"   - 最相关结果: {manual_results[0]['title'][:50]}... (相似度: {manual_results[0]['similarity']:.4f})")
        else:
            print("   知识库未启用ChromaDB模式或未启用")
        
        print("\n=== 测试完成 ===")
        
    except ImportError as e:
        print(f"导入错误: {e}")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_openai_embedding()
