import threading
from collections import OrderedDict
from typing import List, Dict, Any

class ThreadSafeResultCollector:
    """线程安全的结果收集器"""
    
    def __init__(self):
        self._results = []
        self._lock = threading.Lock()
        self._module_order = {}  # 用于记录二级模块的原始顺序
        self._order_counter = 0
    
    def register_module_order(self, level_2_module: str):
        """注册二级模块的处理顺序"""
        with self._lock:
            if level_2_module not in self._module_order:
                self._module_order[level_2_module] = self._order_counter
                self._order_counter += 1
    
    def add_results(self, results: List[Dict[str, Any]], level_2_module: str):
        """添加处理结果"""
        with self._lock:
            # 为每个结果添加模块顺序信息
            for result in results:
                result['_module_order'] = self._module_order.get(level_2_module, 999999)
            self._results.extend(results)
    
    def get_sorted_results(self) -> List[Dict[str, Any]]:
        """获取按原始顺序排序的结果"""
        with self._lock:
            # 按模块顺序排序
            sorted_results = sorted(self._results, key=lambda x: x.get('_module_order', 999999))
            # 移除临时的排序字段
            for result in sorted_results:
                result.pop('_module_order', None)
            return sorted_results
    
    def get_results_count(self) -> int:
        """获取当前结果数量"""
        with self._lock:
            return len(self._results)
