#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的文件格式处理逻辑
"""

import pandas as pd
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_file_reading():
    """测试文件读取功能"""
    print("=== 测试文件读取功能 ===")
    
    # 测试读取调整后的文件
    excel_file = "附件4：中国移动河南公司2025年移动爱家终端管理平台研发项目-软件功能清单-调整.xlsx"
    level_1_name, level_2_name, level_3_name, func_process_name = "一级功能模块", "二级功能模块","三级功能模块","功能过程"
    function_description_name = "功能描述"
    estimated_workload_name = "预估工作量\n（人天）"
    
    try:
        df = pd.read_excel(excel_file, sheet_name=1, header=0)
        print(f"文件读取成功，共 {len(df)} 行")
        print(f"列名: {df.columns.tolist()}")
        
        # 检查列名是否存在
        required_cols = [level_1_name, level_2_name, level_3_name, func_process_name, function_description_name, estimated_workload_name]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"缺少列: {missing_cols}")
            return False
        else:
            print("所有必需的列都存在")
        
        # 向后填充一级/二级/三级模块列      
        df[[level_1_name, level_2_name, level_3_name]] = df[[level_1_name, level_2_name, level_3_name]].ffill()
        
        # 显示前几行数据
        print("\n前5行数据:")
        print(df[required_cols].head())
        
        # 统计三级模块数量
        unique_level3 = df[level_3_name].dropna().unique()
        print(f"\n唯一三级模块数量: {len(unique_level3)}")
        print(f"前10个三级模块: {unique_level3[:10].tolist()}")
        
        return True
        
    except Exception as e:
        print(f"文件读取失败: {e}")
        return False

def test_data_processing():
    """测试数据处理逻辑"""
    print("\n=== 测试数据处理逻辑 ===")
    
    excel_file = "附件4：中国移动河南公司2025年移动爱家终端管理平台研发项目-软件功能清单-调整.xlsx"
    level_1_name, level_2_name, level_3_name, func_process_name = "一级功能模块", "二级功能模块","三级功能模块","功能过程"
    function_description_name = "功能描述"
    estimated_workload_name = "预估工作量\n（人天）"
    
    try:
        df = pd.read_excel(excel_file, sheet_name=1, header=0)
        df[[level_1_name, level_2_name, level_3_name]] = df[[level_1_name, level_2_name, level_3_name]].ffill()
        
        # 模拟按三级模块分组处理
        current_level_3 = None
        current_level_2 = None
        current_level_1 = None
        user_inputs = []
        current_function_process_list = []
        
        group_count = 0
        
        for idx, row in df.iterrows():
            level_1_module = str(row[level_1_name])
            level_2_module = str(row[level_2_name])
            level_3_module = str(row[level_3_name])
            func_process = str(row[func_process_name])
            function_description = str(row[function_description_name])
            estimated_workload = str(row[estimated_workload_name])
            
            # 跳过无效三级模块
            if pd.isna(level_3_module) or str(level_3_module).strip().lower() in ['nan', 'none', '']:
                continue

            # 如果遇到新的三级功能模块，先处理上一个
            if (current_level_3 is not None) and (level_3_module != current_level_3 or level_2_module != current_level_2 or level_1_module != current_level_1):
                if user_inputs:
                    group_count += 1
                    print(f"\n第{group_count}组: 【{current_level_1}-{current_level_2}-{current_level_3}】")
                    print(f"包含 {len(user_inputs)} 个功能过程:")
                    for i, input_str in enumerate(user_inputs):
                        print(f"  {i+1}. {input_str}")
                    
                    # 清空累计
                    user_inputs = []
                    current_function_process_list = []
            
            # 累计当前三级功能模块下的功能过程
            user_input = f"{level_1_name}：{level_1_module}，{level_2_name}：{level_2_module}，{level_3_name}：{level_3_module}, {func_process_name}: {func_process}, {function_description_name}: {function_description}, {estimated_workload_name}: {estimated_workload}"
            user_inputs.append(user_input)
            current_function_process_list.append(func_process)

            current_level_3 = level_3_module
            current_level_2 = level_2_module
            current_level_1 = level_1_module
            
            # 只处理前3组作为测试
            if group_count >= 3:
                break
        
        # 处理最后一组
        if user_inputs:
            group_count += 1
            print(f"\n第{group_count}组: 【{current_level_1}-{current_level_2}-{current_level_3}】")
            print(f"包含 {len(user_inputs)} 个功能过程:")
            for i, input_str in enumerate(user_inputs):
                print(f"  {i+1}. {input_str}")
        
        print(f"\n总共处理了 {group_count} 个三级模块组")
        return True
        
    except Exception as e:
        print(f"数据处理测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试新的文件格式处理...")
    
    # 切换到项目根目录
    os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    success1 = test_file_reading()
    success2 = test_data_processing()
    
    if success1 and success2:
        print("\n✅ 所有测试通过！")
    else:
        print("\n❌ 测试失败！")
