[{"table_name": "config", "table_comment": "配置表", "field_count": 12, "content_length": 365, "content": "表名: config(配置表)\n字段: config_id(主键)[int8], config_code(配置编码)[varchar(100)], config_name(配置名)[varchar(100)], config_value(配置值)[varchar(1000)], config_type(配置类型;0明文1密文)[int4], maintain_flag(可维护标志;0：不可维护1：可维护)[int4], sord_num(排序)[int4], remark(备注)[varchar(1000)], create_by(创建人)[int8], create_time(创建时间)[varchar(30)], update_by(更新人)[int8], update_time(更新时间)[varchar(30)]", "type": "entity", "source": "sql_file"}, {"table_name": "dic_frequency", "table_comment": "上报频率字典", "field_count": 3, "content_length": 130, "content": "表名: dic_frequency(上报频率字典)\n字段: frequency_id(频率ID)[int8], frequency_name(频率名称)[varchar(500)], cron_expression(cron表达式)[varchar(500)]", "type": "entity", "source": "sql_file"}, {"table_name": "dict_item", "table_comment": "枚举项表", "field_count": 15, "content_length": 479, "content": "表名: dict_item(枚举项表)\n字段: id(ID)[int8], group_code(枚举分组标识)[varchar(255)], dict_code(枚举项标识，可以为空，主要用于多层枚举使用)[varchar(1000)], dict_value(枚举值)[varchar(255)], default_flag(是否默认(1默认 0非默认))[int4], remark(备注)[varchar(1000)], dict_sort(排序)[int4], parent_code(上级枚举标识，可以为空，为空表示为最上级枚举)[varchar(1000)], parent_id(上级枚举ID)[int8], css_class(前端样式)[varchar(255)], in_use(是否启用(0启用，1禁用))[int4], create_by(创建人)[int8], create_time(创建时间)[varchar(30)], update_by(更新人)[int8], update_time(更新时间)[varchar(30)]", "type": "entity", "source": "sql_file"}, {"table_name": "file_info", "table_comment": "文件信息", "field_count": 14, "content_length": 421, "content": "表名: file_info(文件信息)\n字段: id(主键)[int8], file_name(文件名称)[varchar(255)], file_remote_path(文件路径)[varchar(255)], file_digest(文件摘要)[varchar(255)], file_business(文件业务类型)[varchar(255)], file_size(文件大小/字节)[int8], status(文件状态)[int4], storage_type(存储方式-1本地 2hdfs)[int4], hmac(完整性校验字段)[varchar(255)], remark(备注)[varchar(1000)], create_by(创建人)[int8], create_time(创建时间)[varchar(30)], update_by(更新人)[int8], update_time(更新时间)[varchar(30)]", "type": "entity", "source": "sql_file"}, {"table_name": "province_platform", "table_comment": "集省平台信息表", "field_count": 9, "content_length": 287, "content": "表名: province_platform(集省平台信息表)\n字段: id(ID)[int8], platform_name(平台名称)[varchar(500)], platform_code(平台编码)[varchar(500)], province_code(省份编码)[varchar(500)], remark(备注)[varchar(1000)], create_by(创建人)[int8], create_time(创建时间)[varchar(30)], update_by(更新人)[int8], update_time(更新时间)[varchar(30)]", "type": "entity", "source": "sql_file"}, {"table_name": "province_platform_aksk", "table_comment": "集省平台AKSK", "field_count": 11, "content_length": 327, "content": "表名: province_platform_aksk(集省平台AKSK)\n字段: id(ID)[int8], platform_id(平台ID)[int8], accesskey_id(AK)[varchar(500)], secret_key(SK)[varchar(500)], status(状态;1启用 0停用)[int4], remark(备注)[varchar(1000)], hmac(HMAC)[varchar(500)], create_by(创建人)[int8], create_time(创建时间)[varchar(30)], update_by(更新人)[int8], update_time(更新时间)[varchar(30)]", "type": "entity", "source": "sql_file"}, {"table_name": "report_config", "table_comment": "上报信息配置表", "field_count": 11, "content_length": 365, "content": "表名: report_config(上报信息配置表)\n字段: id(上报配置ID)[int8], report_code(上报内容标识)[varchar(500)], report_name(上报内容名称)[varchar(500)], frequency_id(上报频率ID)[int8], enable(是否开启; 1开启, 0关闭)[int4], last_report_time(最后上报时间)[varchar(30)], remark(备注)[varchar(1000)], create_by(创建人)[varchar(255)], create_time(创建时间)[varchar(30)], update_by(更新人)[varchar(255)], update_time(更新时间)[varchar(30)]", "type": "entity", "source": "sql_file"}, {"table_name": "sys_job", "table_comment": "定时任务表", "field_count": 15, "content_length": 533, "content": "表名: sys_job(定时任务表)\n字段: job_id(任务号)[int8], job_name(任务名称)[varchar(255)], job_group(任务组名)[varchar(255)], server_id(服务模块)[varchar(100)], method_url(调用接口)[varchar(1500)], json_param(json格式参数)[text], cron_expression(CRON执行表达式)[varchar(255)], misfire_policy(计划执行错误策略;计划执行错误策略（1立即执行 2执行一次 3放弃执行）)[varchar(20)], concurrent(是否并发执行（0允许 1禁止）;是否并发执行（0允许 1禁止）)[varchar(1)], job_status(状态（0正常 1暂停）)[varchar(1)], created_by(创建人)[int8], create_time(创建时间)[varchar(30)], updated_by(更新人)[int8], update_time(更新时间)[varchar(30)], remark(备注)[varchar(1500)]", "type": "entity", "source": "sql_file"}, {"table_name": "sys_job_log", "table_comment": "定时任务执行日志表", "field_count": 7, "content_length": 239, "content": "表名: sys_job_log(定时任务执行日志表)\n字段: job_log_id(任务日志ID)[int8], job_id(任务ID)[int8], job_message(日志信息)[varchar(1500)], status(执行状态（0失败 1正常）)[varchar(1)], exception_info(异常信息)[varchar(6000)], create_time(创建时间)[varchar(30)], trigger_time(触发时间)[int8]", "type": "entity", "source": "sql_file"}, {"table_name": "sys_task", "table_comment": "异步任务表", "field_count": 14, "content_length": 503, "content": "表名: sys_task(异步任务表)\n字段: task_id(任务号)[int8], task_name(任务名称)[varchar(255)], task_group(任务组名;执行任务串行)[varchar(255)], server_id(服务模块)[varchar(255)], method_url(调用接口)[varchar(1000)], json_param(json格式参数)[text], task_status(状态(0-未执行 1-执行中 2-成功 3-异常 4-超时);状态(0-未执行 1-执行中 2-成功 3-异常 4-超时))[int4], create_time(创建时间)[varchar(30)], update_time(更新时间)[varchar(30)], remark(备注)[varchar(300)], timeout(超时时间;单位秒)[int4], start_time(开始时间)[varchar(255)], end_time(结束时间)[varchar(255)], policy(是否允许重复执行;0-不允许，1允许)[varchar(1)]", "type": "entity", "source": "sql_file"}, {"table_name": "sys_task_log", "table_comment": "异步任务执行日志表", "field_count": 7, "content_length": 255, "content": "表名: sys_task_log(异步任务执行日志表)\n字段: task_log_id(任务日志ID)[int8], task_id(任务ID)[int8], task_message(日志信息)[varchar(3000)], status(执行状态（0失败 1正常）)[varchar(1)], exception_info(异常信息)[varchar(6000)], create_time(创建时间;单位毫秒)[varchar(30)], trigger_time(触发时间;任务服务上送)[int8]", "type": "entity", "source": "sql_file"}, {"table_name": "up_app", "table_comment": "密码应用", "field_count": 11, "content_length": 409, "content": "表名: up_app(密码应用)\n字段: id(ID)[int8], app_id(业务系统ID，内部ID即可，主要用于和密评信息和资料关联使用)[varchar(1000)], app_name(业务系统名称)[varchar(1000)], app_type(业务系统类型,可选择类型如下，1：重要业务系统；2：关基业务系统；3：其他)[int4], province_code(参照附录省份编码，添加对应省份)[varchar(100)], remark(业务系统的描述)[varchar(3000)], app_scenarios(描述当前业务系统应用的主要场景)[varchar(3000)], create_by(创建人)[int8], create_time(创建时间)[varchar(30)], update_by(更新人)[int8], update_time(更新时间)[varchar(30)]", "type": "entity", "source": "sql_file"}, {"table_name": "up_app_cypher_type", "table_comment": "密码应用业务类型", "field_count": 12, "content_length": 468, "content": "表名: up_app_cypher_type(密码应用业务类型)\n字段: id(ID)[int8], up_app_id(密码应用ID)[int8], cypher_type_code(业务系统进行密码改造采用的密码产品。具体密码业务类型参照附录：密码业务类型)[varchar(100)], cypher_type_name(密码业务名称)[varchar(1000)], product_type(密码业务使用的密码产品形态，具体形态参考服务：密码产品形态)[varchar(100)], product_id(密码产品ID)[varchar(1000)], product_name(密码产品名称)[varchar(1000)], product_firm(提供该密码产品的厂商名称)[varchar(1000)], create_by(创建人)[int8], create_time(创建时间)[varchar(30)], update_by(更新人)[int8], update_time(更新时间)[varchar(30)]", "type": "entity", "source": "sql_file"}, {"table_name": "up_assess", "table_comment": "密评信息", "field_count": 14, "content_length": 562, "content": "表名: up_assess(密评信息)\n字段: id(ID)[int8], assess_id(密评ID)[varchar(1000)], province_code(参照附录省份编码，添加对应省份)[varchar(100)], app_id(业务系统ID，内部ID即可)[varchar(1000)], assess_reform_code(根据密码阶段，上报当前进度。具体密改阶段参照附录密码改造阶段编码)[varchar(100)], assess_reform_rate(根据密评进度不同类型内部的进度100为完成。根据进度填写具体进度0~100)[int4], product_firm(提供密码改造支持的厂商名称，可多个逗号隔开)[varchar(1000)], assess_agency(密评机构名称)[varchar(1000)], assess_level(应用系统完成的安全等级:1:一级,2：二级,3：三级,4:四级)[int4], assess_score(密评得分)[int4], create_by(创建人)[int8], create_time(创建时间)[varchar(30)], update_by(更新人)[int8], update_time(更新时间)[varchar(30)]", "type": "entity", "source": "sql_file"}, {"table_name": "up_certificate", "table_comment": "证书", "field_count": 12, "content_length": 468, "content": "表名: up_certificate(证书)\n字段: id(ID)[int8], certificate_id(证书ID，内部ID即可)[varchar(1000)], certificate_code(证书的标识、名称或编号)[varchar(1000)], province_code(参照附录省份编码，添加对应省份)[varchar(100)], key_certificate_type(证书类型。参考附录密钥证书类型中证书类型)[varchar(100)], app_id(所属应用系统ID)[varchar(1000)], certificate_algorithm(证书采用的具体算法名称)[varchar(1000)], certificate_state(证书状态：0：未使用；1：使用中)[int4], create_by(创建人)[int8], create_time(创建时间)[varchar(30)], update_by(更新人)[int8], update_time(更新时间)[varchar(30)]", "type": "entity", "source": "sql_file"}, {"table_name": "up_document", "table_comment": "密码文档", "field_count": 13, "content_length": 541, "content": "表名: up_document(密码文档)\n字段: id(ID)[int8], document_id(文档ID，内部ID即可)[varchar(1000)], document_name(问档的名称)[varchar(1000)], province_code(参照附录省份编码，添加对应省份)[varchar(100)], object_type(所属对象类型，分为1：应用系统、2：密码产品、3：安全漏洞事件)[int4], object_id(根据所属对象类型，录入应用系统、密码产品或安全漏洞事件的ID，用于数据和文件关联)[varchar(1000)], document_type(文档类型参照附录资料类型)[varchar(1000)], file_extension(文件扩展名，如pdf、doc、excel等，根据不同文件类型，采用不同的展示形态)[varchar(1000)], file_id(文件存储的FILE_INFO表ID)[int8], create_by(创建人)[int8], create_time(创建时间)[varchar(30)], update_by(更新人)[int8], update_time(更新时间)[varchar(30)]", "type": "entity", "source": "sql_file"}, {"table_name": "up_key", "table_comment": "密钥", "field_count": 13, "content_length": 449, "content": "表名: up_key(密钥)\n字段: id(ID)[int8], key_id(密钥ID，内部ID即可)[varchar(1000)], key_code(密钥的标识、名称或编号)[varchar(2000)], province_code(参照附录省份编码，添加对应省份)[varchar(100)], app_id(所属应用系统ID)[varchar(1000)], key_certificate_type(密钥类型。参考附录密钥证书类型中密钥类型)[varchar(100)], key_algorithm(密钥算法)[varchar(1000)], key_length(密钥长度)[int4], key_state(密钥状态;0:未激活；1：激活；2：归档)[int4], create_by(创建人)[int8], create_time(创建时间)[varchar(30)], update_by(更新人)[int8], update_time(更新时间)[varchar(30)]", "type": "entity", "source": "sql_file"}, {"table_name": "up_product", "table_comment": "无注释", "field_count": 14, "content_length": 556, "content": "表名: up_product(无注释)\n字段: id(ID)[int8], product_id(密码产品ID)[varchar(1000)], product_name(密码产品名称)[varchar(1000)], product_type(密码产品的不同形态，具体形态参考服务：密码产品形态)[varchar(100)], province_code(参照附录省份编码，添加对应省份)[varchar(100)], product_firm(提供该密码产品的厂商名称)[varchar(1000)], product_ref(商用密码产品的认证证书编号)[varchar(100)], product_address(密码产品的IP、端口或域名)[varchar(1000)], product_state(当前密码产品的使用状态； 1：使用中、2:空闲中、3：异常、4：停用)[int4], product_remark(密码产品的功能描述和应用场景等信息)[varchar(5000)], create_by(创建人)[int8], create_time(创建时间)[varchar(30)], update_by(更新人)[int8], update_time(更新时间)[varchar(30)]", "type": "entity", "source": "sql_file"}, {"table_name": "up_product_cypher_type", "table_comment": "密码资产业务类型", "field_count": 8, "content_length": 302, "content": "表名: up_product_cypher_type(密码资产业务类型)\n字段: id(ID)[int8], up_product_id(密码资产ID)[int8], cypher_type_code(业务系统进行密码改造采用的密码产品。具体密码业务类型参照附录：密码业务类型)[varchar(100)], cypher_type_name(密码业务名称)[varchar(1000)], create_by(创建人)[int8], create_time(创建时间)[varchar(30)], update_by(更新人)[int8], update_time(更新时间)[varchar(30)]", "type": "entity", "source": "sql_file"}, {"table_name": "up_security_flaw_event", "table_comment": "安全漏洞事件", "field_count": 13, "content_length": 501, "content": "表名: up_security_flaw_event(安全漏洞事件)\n字段: id(ID)[int8], flaw_event_id(安全漏洞事件ID，内部ID即可)[varchar(1000)], flaw_event_name(安全漏洞事件名称)[varchar(1000)], flaw_event_type(安全漏洞事件类型,可选择类型参考附录安全漏洞事件类型)[varchar(100)], province_code(参照附录省份编码，添加对应省份)[varchar(100)], app_id(所属应用系统)[varchar(1000)], remark(安全事件的描述信息)[varchar(3000)], influence(本次安全事件的影响范围)[varchar(3000)], state(当前安全事件的处理状态。1：未处理、2：处理中、3：已处理)[int4], create_by(创建人)[int8], create_time(创建时间)[varchar(30)], update_by(更新人)[int8], update_time(更新时间)[varchar(30)]", "type": "entity", "source": "sql_file"}]