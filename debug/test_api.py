#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import EMBEDDING_API_BASE, EMBEDDING_API_KEY, EMBEDDING_MODEL

def test_api():
    print("=== API测试 ===")
    print(f"API端点: {EMBEDDING_API_BASE}")
    print(f"模型: {EMBEDDING_MODEL}")
    
    # 构建正确的嵌入API端点
    if 'chat/completions' in EMBEDDING_API_BASE:
        api_url = EMBEDDING_API_BASE.replace('chat/completions', 'embeddings')
    else:
        api_url = EMBEDDING_API_BASE
    
    print(f"实际请求URL: {api_url}")
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {EMBEDDING_API_KEY}"
    }
    
    data = {
        "model": EMBEDDING_MODEL,
        "input": ["测试文本"]
    }
    
    try:
        print("发送请求...")
        response = requests.post(api_url, headers=headers, json=data, timeout=10)
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("请求成功!")
            print(f"返回数据键: {list(result.keys())}")
            if 'data' in result:
                print(f"嵌入向量数量: {len(result['data'])}")
                if result['data']:
                    print(f"向量维度: {len(result['data'][0].get('embedding', []))}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    test_api()
