#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试知识库集成功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from knowledge_base import knowledge_base

def test_integration():
    """测试知识库集成功能"""
    print("=== 知识库集成功能测试 ===")
    
    # 模拟主程序中的模块信息
    test_modules = [
        {
            "level_1": "密码资产数据管理",
            "level_2": "密码资产数据管理",
            "level_3": "密码设备管理",
            "function_desc": "对各种密码设备进行统一管理，包括设备注册、状态监控、配置管理等功能"
        },
        {
            "level_1": "用户管理",
            "level_2": "用户权限管理", 
            "level_3": "角色权限分配",
            "function_desc": "管理员可以为不同角色分配相应的权限"
        },
        {
            "level_1": "系统管理",
            "level_2": "系统配置",
            "level_3": "基础配置管理",
            "function_desc": "管理员可以配置系统的基本参数"
        }
    ]
    
    print(f"知识库状态: {'启用' if knowledge_base.enabled else '禁用'}")
    print(f"功能文档数量: {len(knowledge_base.function_docs)}")
    print(f"实体文档数量: {len(knowledge_base.entity_docs)}")
    print()
    
    # 为每个模块获取知识库上下文
    for i, module in enumerate(test_modules, 1):
        print(f"=== 测试模块 {i}: {module['level_3']} ===")
        
        context = knowledge_base.get_context_for_module(
            module['level_1'],
            module['level_2'], 
            module['level_3'],
            module['function_desc']
        )
        
        if context:
            print("获取到的知识库上下文:")
            print(context)
        else:
            print("未找到相关的知识库上下文")
        
        print("-" * 50)
        print()

def test_prompt_enhancement():
    """测试提示词增强功能"""
    print("=== 提示词增强功能测试 ===")
    
    # 读取原始提示词
    with open('prompt.md', 'r', encoding='utf-8') as f:
        original_prompt = f.read()
    
    # 获取知识库上下文
    context = knowledge_base.get_context_for_module(
        "用户管理",
        "用户权限管理",
        "角色权限分配", 
        "管理员可以为不同角色分配相应的权限"
    )
    
    # 模拟增强后的提示词
    if context:
        enhanced_prompt = f"{original_prompt}\n\n{context}"
        print("原始提示词长度:", len(original_prompt))
        print("知识库上下文长度:", len(context))
        print("增强后提示词长度:", len(enhanced_prompt))
        print()
        print("知识库上下文内容:")
        print(context)
    else:
        print("未获取到知识库上下文")

if __name__ == "__main__":
    test_integration()
    print("\n" + "="*60 + "\n")
    test_prompt_enhancement()
