{"input_summary": {"total_records": 600, "columns": ["一级功能模块", "二级功能模块", "三级功能模块", "功能用户", "触发事件", "功能过程", "子过程描述", "数据移动类型", "数据组", "CFP"], "data_movement_types": {"E": 150, "R": 150, "W": 150, "X": 150}, "cfp_distribution": {"1.0": 600}, "level1_modules": ["模块1", "模块2", "模块3", "模块4", "模块5", "模块6"], "level2_modules": ["子模块1", "子模块2", "子模块3", "子模块4", "子模块5", "子模块6", "子模块7", "子模块8", "子模块9", "子模块10", "子模块11", "子模块12"], "level3_modules": ["功能1", "功能2", "功能3", "功能4", "功能5", "功能6", "功能7", "功能8", "功能9", "功能10", "功能11", "功能12", "功能13", "功能14", "功能15", "功能16", "功能17", "功能18", "功能19", "功能20", "功能21", "功能22", "功能23", "功能24", "功能25", "功能26", "功能27", "功能28", "功能29", "功能30", "功能31", "功能32", "功能33", "功能34", "功能35", "功能36", "功能37", "功能38", "功能39", "功能40", "功能41", "功能42", "功能43", "功能44", "功能45", "功能46", "功能47", "功能48", "功能49", "功能50", "功能51", "功能52", "功能53", "功能54", "功能55", "功能56", "功能57", "功能58", "功能59", "功能60", "功能61", "功能62", "功能63", "功能64", "功能65", "功能66", "功能67", "功能68", "功能69", "功能70", "功能71", "功能72", "功能73", "功能74", "功能75", "功能76", "功能77", "功能78", "功能79", "功能80", "功能81", "功能82", "功能83", "功能84", "功能85", "功能86", "功能87", "功能88", "功能89", "功能90", "功能91", "功能92", "功能93", "功能94", "功能95", "功能96", "功能97", "功能98", "功能99", "功能100", "功能101", "功能102", "功能103", "功能104", "功能105", "功能106", "功能107", "功能108", "功能109", "功能110", "功能111", "功能112", "功能113", "功能114", "功能115", "功能116", "功能117", "功能118", "功能119", "功能120", "功能121", "功能122", "功能123", "功能124", "功能125", "功能126", "功能127", "功能128", "功能129", "功能130", "功能131", "功能132", "功能133", "功能134", "功能135", "功能136", "功能137", "功能138", "功能139", "功能140", "功能141", "功能142", "功能143", "功能144", "功能145", "功能146", "功能147", "功能148", "功能149", "功能150", "功能151", "功能152", "功能153", "功能154", "功能155", "功能156", "功能157", "功能158", "功能159", "功能160", "功能161", "功能162", "功能163", "功能164", "功能165", "功能166", "功能167", "功能168", "功能169", "功能170", "功能171", "功能172", "功能173", "功能174", "功能175", "功能176", "功能177", "功能178", "功能179", "功能180", "功能181", "功能182", "功能183", "功能184", "功能185", "功能186", "功能187", "功能188", "功能189", "功能190", "功能191", "功能192", "功能193", "功能194", "功能195", "功能196", "功能197", "功能198", "功能199", "功能200", "功能201", "功能202", "功能203", "功能204", "功能205", "功能206", "功能207", "功能208", "功能209", "功能210", "功能211", "功能212", "功能213", "功能214", "功能215", "功能216", "功能217", "功能218", "功能219", "功能220", "功能221", "功能222", "功能223", "功能224", "功能225", "功能226", "功能227", "功能228", "功能229", "功能230", "功能231", "功能232", "功能233", "功能234", "功能235", "功能236", "功能237", "功能238", "功能239", "功能240", "功能241", "功能242", "功能243", "功能244", "功能245", "功能246", "功能247", "功能248", "功能249", "功能250", "功能251", "功能252", "功能253", "功能254", "功能255", "功能256", "功能257", "功能258", "功能259", "功能260", "功能261", "功能262", "功能263", "功能264", "功能265", "功能266", "功能267", "功能268", "功能269", "功能270", "功能271", "功能272", "功能273", "功能274", "功能275", "功能276", "功能277", "功能278", "功能279", "功能280", "功能281", "功能282", "功能283", "功能284", "功能285", "功能286", "功能287", "功能288", "功能289", "功能290", "功能291", "功能292", "功能293", "功能294", "功能295", "功能296", "功能297", "功能298", "功能299", "功能300", "功能301", "功能302", "功能303", "功能304", "功能305", "功能306", "功能307", "功能308", "功能309", "功能310", "功能311", "功能312", "功能313", "功能314", "功能315", "功能316", "功能317", "功能318", "功能319", "功能320", "功能321", "功能322", "功能323", "功能324", "功能325", "功能326", "功能327", "功能328", "功能329", "功能330", "功能331", "功能332", "功能333", "功能334", "功能335", "功能336", "功能337", "功能338", "功能339", "功能340", "功能341", "功能342", "功能343", "功能344", "功能345", "功能346", "功能347", "功能348", "功能349", "功能350", "功能351", "功能352", "功能353", "功能354", "功能355", "功能356", "功能357", "功能358", "功能359", "功能360", "功能361", "功能362", "功能363", "功能364", "功能365", "功能366", "功能367", "功能368", "功能369", "功能370", "功能371", "功能372", "功能373", "功能374", "功能375", "功能376", "功能377", "功能378", "功能379", "功能380", "功能381", "功能382", "功能383", "功能384", "功能385", "功能386", "功能387", "功能388", "功能389", "功能390", "功能391", "功能392", "功能393", "功能394", "功能395", "功能396", "功能397", "功能398", "功能399", "功能400", "功能401", "功能402", "功能403", "功能404", "功能405", "功能406", "功能407", "功能408", "功能409", "功能410", "功能411", "功能412", "功能413", "功能414", "功能415", "功能416", "功能417", "功能418", "功能419", "功能420", "功能421", "功能422", "功能423", "功能424", "功能425", "功能426", "功能427", "功能428", "功能429", "功能430", "功能431", "功能432", "功能433", "功能434", "功能435", "功能436", "功能437", "功能438", "功能439", "功能440", "功能441", "功能442", "功能443", "功能444", "功能445", "功能446", "功能447", "功能448", "功能449", "功能450", "功能451", "功能452", "功能453", "功能454", "功能455", "功能456", "功能457", "功能458", "功能459", "功能460", "功能461", "功能462", "功能463", "功能464", "功能465", "功能466", "功能467", "功能468", "功能469", "功能470", "功能471", "功能472", "功能473", "功能474", "功能475", "功能476", "功能477", "功能478", "功能479", "功能480", "功能481", "功能482", "功能483", "功能484", "功能485", "功能486", "功能487", "功能488", "功能489", "功能490", "功能491", "功能492", "功能493", "功能494", "功能495", "功能496", "功能497", "功能498", "功能499", "功能500", "功能501", "功能502", "功能503", "功能504", "功能505", "功能506", "功能507", "功能508", "功能509", "功能510", "功能511", "功能512", "功能513", "功能514", "功能515", "功能516", "功能517", "功能518", "功能519", "功能520", "功能521", "功能522", "功能523", "功能524", "功能525", "功能526", "功能527", "功能528", "功能529", "功能530", "功能531", "功能532", "功能533", "功能534", "功能535", "功能536", "功能537", "功能538", "功能539", "功能540", "功能541", "功能542", "功能543", "功能544", "功能545", "功能546", "功能547", "功能548", "功能549", "功能550", "功能551", "功能552", "功能553", "功能554", "功能555", "功能556", "功能557", "功能558", "功能559", "功能560", "功能561", "功能562", "功能563", "功能564", "功能565", "功能566", "功能567", "功能568", "功能569", "功能570", "功能571", "功能572", "功能573", "功能574", "功能575", "功能576", "功能577", "功能578", "功能579", "功能580", "功能581", "功能582", "功能583", "功能584", "功能585", "功能586", "功能587", "功能588", "功能589", "功能590", "功能591", "功能592", "功能593", "功能594", "功能595", "功能596", "功能597", "功能598", "功能599", "功能600"]}, "batch_processing_info": {"total_batches": 2, "successful_batches": 2, "failed_batches": 0, "processing_method": "CSV分批次处理，每批次包含header"}, "batch_results": [{"batch_index": 1, "data_range": "第1行到第500行", "validation_result": {"overall_assessment": {"total_records": 600, "compliance_rate": "82.5%", "major_issues_count": 8, "minor_issues_count": 15}, "detailed_findings": [{"module_path": "模块1/子模块1/功能1", "function_process": "过程1", "subprocess_description": "子过程描述1", "issue_type": "数据组聚合", "severity": "高", "current_value": "数据组1", "issue_description": "数据组命名不规范", "suggested_fix": "使用标准命名规范", "example": "标准数据组名称"}, {"module_path": "模块1/子模块2/功能2", "function_process": "过程2", "subprocess_description": "子过程描述2", "issue_type": "重复计数", "severity": "中", "current_value": "数据组2", "issue_description": "存在重复计数", "suggested_fix": "合并重复操作", "example": "合并后的操作"}], "summary_recommendations": ["批次1: 优先解决数据组命名问题", "批次1: 检查重复计数情况"]}}, {"batch_index": 2, "data_range": "第501行到第600行", "validation_result": {"overall_assessment": {"total_records": 600, "compliance_rate": "82.5%", "major_issues_count": 8, "minor_issues_count": 15}, "detailed_findings": [{"module_path": "模块2/子模块2/功能2", "function_process": "过程2", "subprocess_description": "子过程描述2", "issue_type": "数据组聚合", "severity": "高", "current_value": "数据组2", "issue_description": "数据组命名不规范", "suggested_fix": "使用标准命名规范", "example": "标准数据组名称"}, {"module_path": "模块2/子模块3/功能3", "function_process": "过程3", "subprocess_description": "子过程描述3", "issue_type": "重复计数", "severity": "中", "current_value": "数据组3", "issue_description": "存在重复计数", "suggested_fix": "合并重复操作", "example": "合并后的操作"}], "summary_recommendations": ["批次2: 优先解决数据组命名问题", "批次2: 检查重复计数情况"]}}], "timestamp": "2025-07-28 08:48:41", "merged_validation_result": {"batch_count": 2, "individual_results": [{"overall_assessment": {"total_records": 600, "compliance_rate": "82.5%", "major_issues_count": 8, "minor_issues_count": 15}, "detailed_findings": [{"module_path": "模块1/子模块1/功能1", "function_process": "过程1", "subprocess_description": "子过程描述1", "issue_type": "数据组聚合", "severity": "高", "current_value": "数据组1", "issue_description": "数据组命名不规范", "suggested_fix": "使用标准命名规范", "example": "标准数据组名称"}, {"module_path": "模块1/子模块2/功能2", "function_process": "过程2", "subprocess_description": "子过程描述2", "issue_type": "重复计数", "severity": "中", "current_value": "数据组2", "issue_description": "存在重复计数", "suggested_fix": "合并重复操作", "example": "合并后的操作"}], "summary_recommendations": ["批次1: 优先解决数据组命名问题", "批次1: 检查重复计数情况"]}, {"overall_assessment": {"total_records": 600, "compliance_rate": "82.5%", "major_issues_count": 8, "minor_issues_count": 15}, "detailed_findings": [{"module_path": "模块2/子模块2/功能2", "function_process": "过程2", "subprocess_description": "子过程描述2", "issue_type": "数据组聚合", "severity": "高", "current_value": "数据组2", "issue_description": "数据组命名不规范", "suggested_fix": "使用标准命名规范", "example": "标准数据组名称"}, {"module_path": "模块2/子模块3/功能3", "function_process": "过程3", "subprocess_description": "子过程描述3", "issue_type": "重复计数", "severity": "中", "current_value": "数据组3", "issue_description": "存在重复计数", "suggested_fix": "合并重复操作", "example": "合并后的操作"}], "summary_recommendations": ["批次2: 优先解决数据组命名问题", "批次2: 检查重复计数情况"]}], "summary": {"total_issues_found": 4, "total_records": 600, "severity_statistics": {"counts": {"高": 2, "中": 2, "低": 0}, "percentages": {"高": 50.0, "中": 50.0, "低": 0.0}}, "all_findings": [{"module_path": "模块1/子模块1/功能1", "function_process": "过程1", "subprocess_description": "子过程描述1", "issue_type": "数据组聚合", "severity": "高", "current_value": "数据组1", "issue_description": "数据组命名不规范", "suggested_fix": "使用标准命名规范", "example": "标准数据组名称"}, {"module_path": "模块1/子模块2/功能2", "function_process": "过程2", "subprocess_description": "子过程描述2", "issue_type": "重复计数", "severity": "中", "current_value": "数据组2", "issue_description": "存在重复计数", "suggested_fix": "合并重复操作", "example": "合并后的操作"}, {"module_path": "模块2/子模块2/功能2", "function_process": "过程2", "subprocess_description": "子过程描述2", "issue_type": "数据组聚合", "severity": "高", "current_value": "数据组2", "issue_description": "数据组命名不规范", "suggested_fix": "使用标准命名规范", "example": "标准数据组名称"}, {"module_path": "模块2/子模块3/功能3", "function_process": "过程3", "subprocess_description": "子过程描述3", "issue_type": "重复计数", "severity": "中", "current_value": "数据组3", "issue_description": "存在重复计数", "suggested_fix": "合并重复操作", "example": "合并后的操作"}], "all_recommendations": ["批次2: 检查重复计数情况", "批次1: 优先解决数据组命名问题", "批次1: 检查重复计数情况", "批次2: 优先解决数据组命名问题"], "overall_assessment": "基于分批次处理的综合评估"}}, "ai_generated_summary": {"priority_recommendations": ["优先解决高严重程度的数据组聚合问题，这类问题影响CFP计算准确性", "其次处理中等严重程度的重复计数问题，避免功能点重复统计", "建立统一的数据组命名规范，确保跨模块一致性"], "common_patterns": ["数据组命名不规范是最常见的问题", "重复计数问题主要出现在批量操作中", "缺少统一的业务实体定义标准"], "improvement_measures": ["制定并执行数据组命名规范文档", "建立代码审查机制，重点检查数据组定义", "开发自动化检测工具，识别重复计数问题"], "best_practices": ["使用'业务实体+操作类型'的数据组命名模式", "建立数据组字典，统一管理所有数据组定义"], "severity_analysis": {"高": "主要涉及数据组聚合问题，直接影响CFP计算的准确性", "中": "重复计数问题会导致功能点估算偏高", "低": "格式和命名问题，不影响功能但需要规范化"}}}