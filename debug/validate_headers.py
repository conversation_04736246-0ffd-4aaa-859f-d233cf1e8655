#!/usr/bin/env python3
"""
验证MD文件中标题层级是否与编号对应
"""

import re
import sys

def validate_headers(file_path):
    """验证标题层级"""
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    errors = []
    
    for line_num, line in enumerate(lines, 1):
        # 匹配标题行
        match = re.match(r'^(#{1,6})\s+(\d+(?:\.\d+)*)\.\s+', line)
        if match:
            hash_count = len(match.group(1))
            number_parts = match.group(2).split('.')
            expected_hash_count = len(number_parts)
            
            if hash_count != expected_hash_count:
                errors.append(f"行 {line_num}: 标题层级不匹配")
                errors.append(f"  内容: {line.strip()}")
                errors.append(f"  当前: {hash_count}个#, 期望: {expected_hash_count}个#")
                errors.append("")
    
    return errors

if __name__ == "__main__":
    file_path = "三未信安密码服务平台管理端用户手册v3.4.0(无区域多租户模式).md"
    errors = validate_headers(file_path)
    
    if errors:
        print("发现标题层级问题:")
        for error in errors:
            print(error)
        sys.exit(1)
    else:
        print("所有标题层级都正确!")
        sys.exit(0)
