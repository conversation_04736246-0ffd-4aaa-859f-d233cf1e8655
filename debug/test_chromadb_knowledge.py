#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试ChromaDB知识库功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_chromadb_knowledge():
    """测试ChromaDB知识库功能"""
    print("=== ChromaDB知识库功能测试 ===\n")
    
    try:
        from knowledge_base import knowledge_base
        
        # 1. 检查知识库状态
        print("1. 知识库状态检查")
        print(f"   - 知识库启用: {'是' if knowledge_base.enabled else '否'}")
        print(f"   - 功能文档数量: {len(knowledge_base.function_docs)}")
        print(f"   - 数据实体数量: {len(knowledge_base.entity_docs)}")
        
        if not knowledge_base.enabled:
            print("   知识库未启用，可能是依赖未安装")
            return
        
        print(f"   - ChromaDB客户端: {'已连接' if knowledge_base.chroma_client else '未连接'}")
        print(f"   - 嵌入模型: {'已加载' if knowledge_base.embedding_model else '未加载'}")
        print()
        
        # 2. 测试搜索功能
        print("2. 搜索功能测试")
        test_queries = ["用户管理", "密码", "证书", "配置"]
        
        for query in test_queries:
            print(f"\n   查询: '{query}'")
            results = knowledge_base.search_knowledge(query)
            
            manual_results = results['manual_results']
            sql_results = results['sql_results']
            
            print(f"   - 用户手册结果: {len(manual_results)} 条")
            for i, result in enumerate(manual_results[:2], 1):
                print(f"     {i}. {result['title'][:40]}... (相似度: {result['similarity']:.3f})")
            
            print(f"   - 数据库实体结果: {len(sql_results)} 条")
            for i, result in enumerate(sql_results[:2], 1):
                print(f"     {i}. {result['table_name']} (相似度: {result['similarity']:.3f})")
        
        print()
        
        # 3. 测试上下文生成
        print("3. 上下文生成测试")
        context = knowledge_base.get_context_for_module(
            "用户管理",
            "用户权限管理",
            "用户信息管理", 
            "管理用户的基本信息，包括用户ID、用户名、密码等"
        )
        
        if context:
            print("   生成的上下文:")
            lines = context.split('\n')
            for line in lines[:20]:  # 只显示前20行
                print(f"   {line}")
            if len(lines) > 20:
                print(f"   ... (还有 {len(lines) - 20} 行)")
        else:
            print("   未生成上下文")
        
        print()
        
        # 4. 测试ChromaDB集合信息
        print("4. ChromaDB集合信息")
        if knowledge_base.function_collection:
            func_count = knowledge_base.function_collection.count()
            print(f"   - 功能文档集合: {func_count} 条记录")
        
        if knowledge_base.entity_collection:
            entity_count = knowledge_base.entity_collection.count()
            print(f"   - 实体文档集合: {entity_count} 条记录")
        
        print("\n=== 测试完成 ===")
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装 chromadb 和 sentence-transformers")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_chromadb_knowledge()
