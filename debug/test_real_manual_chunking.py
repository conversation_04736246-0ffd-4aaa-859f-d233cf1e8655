#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试真实用户手册的切块逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from knowledge_base import KnowledgeBase

def test_real_manual_chunking():
    """测试真实用户手册的切块功能"""

    # 使用实际的用户手册文件
    manual_file = "三未信安密码服务平台管理端用户手册v3.4.0(无区域多租户模式).md"

    if not os.path.exists(manual_file):
        print(f"用户手册文件不存在: {manual_file}")
        return

    print("测试真实用户手册的层次结构切块逻辑:")
    print("-" * 50)

    # 直接读取文件内容并测试切块
    with open(manual_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 创建一个简化的知识库实例来测试切块
    from config import MARKDOWN_CHILD_PATTERN

    # 模拟切块逻辑
    import re

    # 解析所有标题和内容
    lines = content.split('\n')
    sections = []
    current_section = None

    for line in lines:
        # 检测标题行
        heading_match = re.match(r'^(#{1,6})\s+(.+)$', line.strip())
        if heading_match:
            # 保存前一个section
            if current_section:
                sections.append(current_section)

            # 创建新section
            level = len(heading_match.group(1))
            title = heading_match.group(2).strip()
            current_section = {
                'level': level,
                'title': title,
                'content': '',
                'line': line
            }
        else:
            # 内容行
            if current_section:
                current_section['content'] += line + '\n'
            else:
                # 文档开头的内容（没有标题）
                if line.strip():
                    if not sections or sections[-1]['level'] != 0:
                        sections.append({
                            'level': 0,
                            'title': '文档前言',
                            'content': line + '\n',
                            'line': ''
                        })
                    else:
                        sections[-1]['content'] += line + '\n'

    # 保存最后一个section
    if current_section:
        sections.append(current_section)

    # 根据配置确定子块层级
    child_level = MARKDOWN_CHILD_PATTERN.count('#')

    print(f"检测到 {len(sections)} 个section")
    print(f"目标子块层级: {child_level} (####)")
    print()

    # 统计各层级的数量
    level_counts = {}
    for section in sections:
        level = section['level']
        level_counts[level] = level_counts.get(level, 0) + 1

    print("各层级标题统计:")
    for level in sorted(level_counts.keys()):
        level_name = "文档前言" if level == 0 else f"{'#' * level} 标题"
        print(f"  {level_name}: {level_counts[level]} 个")

    print()

    # 显示前20个四级标题
    fourth_level_sections = [s for s in sections if s['level'] == 4]
    print(f"四级标题 (####) 总数: {len(fourth_level_sections)}")
    print("前20个四级标题:")
    for i, section in enumerate(fourth_level_sections[:20], 1):
        print(f"  {i}. {section['title']} (内容: {len(section['content'].strip())} 字符)")

    if len(fourth_level_sections) > 20:
        print(f"  ... 还有 {len(fourth_level_sections) - 20} 个四级标题")

if __name__ == "__main__":
    test_real_manual_chunking()
