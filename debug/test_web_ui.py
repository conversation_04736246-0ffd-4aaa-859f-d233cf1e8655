#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web UI 测试脚本

测试Web UI的基本功能：
1. 文件列表获取
2. 文档生成
3. 文档渲染
"""

import os
import sys
import requests
import time
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_web_ui():
    """测试Web UI功能"""
    base_url = "http://localhost:5000"
    
    print("=== Web UI 功能测试 ===")
    print(f"测试服务器: {base_url}")
    print()
    
    # 测试1: 获取文件列表
    print("1. 测试文件列表API...")
    try:
        response = requests.get(f"{base_url}/api/files", timeout=10)
        if response.status_code == 200:
            files = response.json()
            print(f"✅ 文件列表获取成功，共找到 {len(files)} 个文件")
            for file in files[:3]:  # 只显示前3个文件
                print(f"   - {file['name']} ({file['extension']}, {file['size']} bytes)")
                print(f"     Markdown状态: {'已生成' if file['has_markdown'] else '未生成'}")
        else:
            print(f"❌ 文件列表获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 文件列表API测试失败: {e}")
        return False
    
    print()
    
    # 测试2: 选择一个测试文件进行文档生成
    if files:
        test_file = None
        # 优先选择output.csv或类似的测试文件
        for file in files:
            if 'output' in file['name'].lower() and file['extension'] == '.csv':
                test_file = file
                break
        
        if not test_file:
            test_file = files[0]  # 选择第一个文件
        
        print(f"2. 测试文档生成API (使用文件: {test_file['name']})...")
        try:
            generate_data = {
                'file_path': test_file['path'],
                'force_regenerate': False
            }
            
            response = requests.post(
                f"{base_url}/api/generate",
                json=generate_data,
                timeout=120  # 文档生成可能需要较长时间
            )
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    print(f"✅ 文档生成成功: {result['message']}")
                    print(f"   输出文件: {result['markdown_path']}")
                    
                    # 测试3: 测试文档渲染
                    print()
                    print("3. 测试文档渲染...")
                    render_url = f"{base_url}/api/render/{result['markdown_path']}"
                    render_response = requests.get(render_url, timeout=30)
                    
                    if render_response.status_code == 200:
                        html_content = render_response.text
                        if 'mermaid' in html_content.lower() and 'sequenceDiagram' in html_content:
                            print("✅ 文档渲染成功，包含Mermaid时序图支持")
                        else:
                            print("✅ 文档渲染成功，但可能缺少Mermaid内容")
                    else:
                        print(f"❌ 文档渲染失败: {render_response.status_code}")
                        return False
                else:
                    print(f"❌ 文档生成失败: {result.get('error', '未知错误')}")
                    return False
            else:
                print(f"❌ 文档生成API调用失败: {response.status_code}")
                try:
                    error_info = response.json()
                    print(f"   错误信息: {error_info.get('error', '未知错误')}")
                except:
                    print(f"   响应内容: {response.text[:200]}...")
                return False
                
        except Exception as e:
            print(f"❌ 文档生成API测试失败: {e}")
            return False
    
    print()
    print("✅ 所有测试通过！")
    return True

def check_dependencies():
    """检查依赖是否安装"""
    print("检查依赖...")
    
    required_packages = ['flask', 'flask_cors', 'markdown', 'pygments', 'pandas', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} (未安装)")
    
    if missing_packages:
        print(f"\n请安装缺失的依赖: pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖已安装")
    return True

def main():
    """主函数"""
    print("=== COSMIC Web UI 测试工具 ===")
    print()
    
    # 检查依赖
    if not check_dependencies():
        return
    
    print()
    
    # 检查Web服务是否运行
    print("检查Web服务状态...")
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        if response.status_code == 200:
            print("✅ Web服务正在运行")
        else:
            print(f"❌ Web服务响应异常: {response.status_code}")
            print("请确保运行了: python web_ui.py")
            return
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Web服务")
        print("请先启动Web服务: python web_ui.py")
        return
    except Exception as e:
        print(f"❌ Web服务检查失败: {e}")
        return
    
    print()
    
    # 运行测试
    if test_web_ui():
        print("\n🎉 Web UI 测试完成，所有功能正常！")
        print("\n使用说明:")
        print("1. 在浏览器中访问: http://localhost:5000")
        print("2. 选择xlsx或csv文件")
        print("3. 点击'生成文档'按钮")
        print("4. 点击'查看文档'查看渲染结果")
        print("5. 支持Mermaid时序图在线渲染")
    else:
        print("\n❌ 测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
