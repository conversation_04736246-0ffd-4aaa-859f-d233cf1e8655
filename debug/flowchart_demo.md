# 流程图备选渲染功能演示

本文档演示了当Mermaid时序图无法正常渲染时，系统会自动提供可视化流程图作为备选方案。

## 功能特点

1. **自动检测**：系统会自动检测Mermaid时序图是否能正常渲染
2. **智能切换**：当时序图渲染失败时，自动显示流程图备选方案
3. **手动切换**：用户可以通过按钮手动在时序图和流程图之间切换
4. **可视化效果**：流程图采用类似您提供的图2的可视化风格

## 示例1：用户登录验证流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant System as 系统
    participant DB as 数据库

    User->>System: 提交登录请求
    System->>System: 输入参数验证(E)
    System->>DB: 验证登录信息(R)
    DB-->>System: 返回验证结果
    System->>DB: 保存登录信息(W)
    System->>User: 返回登录结果(X)
```

## 示例2：数据查询与缓存流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant System as 系统
    participant Cache as 缓存
    participant DB as 数据库

    User->>System: 发起查询请求
    System->>Cache: 检查缓存数据(R)
    Cache-->>System: 返回缓存结果
    System->>DB: 查询数据库(R)
    DB-->>System: 返回查询结果
    System->>Cache: 更新缓存(W)
    System->>User: 返回查询结果(X)
```

## 示例3：文件上传处理流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant System as 系统
    participant Storage as 存储系统
    participant Validator as 验证器

    User->>System: 上传文件请求
    System->>Validator: 文件格式验证
    Validator-->>System: 验证结果
    System->>Storage: 保存文件数据(W)
    Storage-->>System: 返回保存结果
    System->>User: 返回上传结果
```

## 使用说明

1. **正常情况**：时序图会正常显示，您可以看到标准的Mermaid时序图
2. **备选情况**：如果时序图无法渲染，系统会自动显示流程图
3. **手动切换**：点击图表下方的"切换到流程图"或"切换到时序图"按钮进行切换

## 技术实现

- 使用JavaScript检测Mermaid渲染错误
- 自动解析时序图内容生成流程图HTML
- CSS样式实现类似传统流程图的可视化效果
- 支持参与者框、箭头、消息标签等元素的渲染

这个功能确保了即使在Mermaid库出现问题的情况下，用户仍然能够看到清晰的流程可视化图表。
