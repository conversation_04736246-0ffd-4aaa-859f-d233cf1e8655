#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新数据格式的解析逻辑
"""

import json
from collections import OrderedDict

def test_new_format_parsing():
    """测试新数据格式的解析逻辑"""
    
    # 模拟新格式的数据
    new_format_data = [
        {
            "集团平台配置": [
                {
                    "功能用户": "发起者：用户，接收者：密服平台-集省对接模块",
                    "触发事件": "用户点击上级平台配置菜单",
                    "功能过程": "查看上级平台配置信息",
                    "子过程": [
                        {"子过程描述": "查询总部平台信息", "数据移动类型": "E", "数据组": "分页信息", "数据属性": "页码、单页数量", "CFP": 1},
                        {"子过程描述": "读取上级平台信息", "数据移动类型": "R", "数据组": "总部平台信息", "数据属性": "上级平台名称、上级平台IP、上级平台端口", "CFP": 1}
                    ]
                },
                {
                    "功能用户": "发起者：用户，接收者：密服平台-集省对接模块",
                    "触发事件": "用户在上级平台管理页面点击上级平台地址配置按钮，配置上级平台地址配置",
                    "功能过程": "配置上级平台信息",
                    "子过程": [
                        {"子过程描述": "输入上级平台信息", "数据移动类型": "E", "数据组": "上级平台信息", "数据属性": "上级平台名称、上级平台IP、上级平台端口", "CFP": 1}
                    ]
                }
            ]
        },
        {
            "角色权限分配": [
                {
                    "功能用户": "发起者：管理员，接收者：用户管理系统",
                    "触发事件": "管理员点击角色权限分配菜单",
                    "功能过程": "查看角色权限分配信息",
                    "子过程": [
                        {"子过程描述": "查询角色权限分配信息", "数据移动类型": "E", "数据组": "分页信息", "数据属性": "页码、单页数量", "CFP": 1},
                        {"子过程描述": "读取角色权限分配信息", "数据移动类型": "R", "数据组": "角色权限分配信息", "数据属性": "角色名称、权限列表", "CFP": 1}
                    ]
                },
                {
                    "功能用户": "发起者：管理员，接收者：用户管理系统",
                    "触发事件": "管理员在角色权限分配页面点击分配权限按钮，为角色分配权限",
                    "功能过程": "分配角色权限",
                    "子过程": [
                        {"子过程描述": "选择角色", "数据移动类型": "E", "数据组": "角色信息", "数据属性": "角色名称", "CFP": 1},
                        {"子过程描述": "选择权限", "数据移动类型": "E", "数据组": "权限信息", "数据属性": "权限名称", "CFP": 1},
                        {"子过程描述": "保存角色权限分配", "数据移动类型": "W", "数据组": "角色权限分配信息", "数据属性": "角色名称、权限列表", "CFP": 1}
                    ]
                }
            ]
        }
    ]
    
    # 模拟当前的模块信息
    current_level_1 = "集省对接"
    current_level_2 = "集省对接配置"
    current_level_3_list = ["集团平台配置", "角色权限分配"]
    current_function_description_list = ["集团平台配置功能", "角色权限分配功能"]
    current_estimated_workload_list = ["3", "5"]
    
    # 列名定义
    level_1_name = "一级功能模块"
    level_2_name = "二级功能模块"
    level_3_name = "三级功能模块"
    function_description_name = "功能描述"
    estimated_workload_name = "预估工作量（人天）"
    
    # 模拟批次处理
    start_row = 0
    end_row = 2
    all_results = []
    
    # 新的解析逻辑
    result = new_format_data
    if result and isinstance(result, list):
        # 新格式：每个元素是 {"三级功能模块名称": [功能过程列表]}
        for module_dict in result:
            if isinstance(module_dict, dict):
                # 遍历每个三级模块
                for module_name, function_processes in module_dict.items():
                    print(f"处理模块: {module_name}")
                    # 找到对应的三级模块信息
                    try:
                        module_index = current_level_3_list[start_row:end_row].index(module_name)
                        level_3 = current_level_3_list[start_row + module_index]
                        func_desc = current_function_description_list[start_row + module_index]
                        est_work = current_estimated_workload_list[start_row + module_index]
                        print(f"找到对应信息: {level_3}, {func_desc}, {est_work}")
                    except (ValueError, IndexError):
                        # 如果找不到对应的模块，使用第一个作为默认值
                        if current_level_3_list[start_row:end_row]:
                            level_3 = current_level_3_list[start_row]
                            func_desc = current_function_description_list[start_row]
                            est_work = current_estimated_workload_list[start_row]
                            print(f"使用默认信息: {level_3}, {func_desc}, {est_work}")
                        else:
                            continue
                    
                    # 处理该模块下的每个功能过程
                    if isinstance(function_processes, list):
                        for process in function_processes:
                            ordered_item = OrderedDict()
                            ordered_item[level_1_name] = current_level_1
                            ordered_item[level_2_name] = current_level_2
                            ordered_item[level_3_name] = level_3
                            ordered_item[function_description_name] = func_desc
                            ordered_item[estimated_workload_name] = est_work
                            # 添加功能过程的所有字段
                            for k, v in process.items():
                                ordered_item[k] = v 
                            all_results.append(ordered_item)
                            print(f"添加结果项: {ordered_item['功能过程']}")
    
    print(f"\n总共处理了 {len(all_results)} 个结果项")
    
    # 输出前几个结果查看
    for i, item in enumerate(all_results[:3]):
        print(f"\n结果项 {i+1}:")
        for k, v in item.items():
            print(f"  {k}: {v}")
    
    return all_results

def flatten_results_with_subprocess(all_items):
    """
    输入：all_items为包含模块信息和大模型返回的所有item（每个item有'子过程'字段，值为列表）
    输出：每个子过程一行的扁平化结果，所有父级信息每行都保留
    """
    flat_results = []
    for item in all_items:
        subprocess_list = item.get('子过程', [])
        # 拷贝除'子过程'外的所有字段
        base_info = {k: v for k, v in item.items() if k != '子过程'}
        for subprocess in subprocess_list:
            row = base_info.copy()
            row.update(subprocess)  # 子过程的key直接变成列
            flat_results.append(row)
    return flat_results

if __name__ == "__main__":
    print("测试新数据格式解析逻辑...")
    results = test_new_format_parsing()
    
    print("\n\n测试扁平化处理...")
    flat_results = flatten_results_with_subprocess(results)
    print(f"扁平化后共 {len(flat_results)} 行")
    
    # 输出前几行查看
    for i, item in enumerate(flat_results[:5]):
        print(f"\n扁平化结果 {i+1}:")
        for k, v in item.items():
            print(f"  {k}: {v}")
    
    # 保存测试结果到JSON文件
    with open('debug/test_results.json', 'w', encoding='utf-8') as f:
        json.dump(flat_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n测试结果已保存到 debug/test_results.json")
