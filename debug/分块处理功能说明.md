# 知识库分块处理功能说明

## 概述

为了解决向量化内容超过模型限制的问题，我们实现了智能分块处理功能。该功能可以将大型用户手册文档和SQL数据库文件分解为适当大小的块，以便进行向量化处理。

## 功能特性

### 1. 可配置的分块策略

在 `config.py` 中新增了以下配置选项：

```python
# 分块处理配置
CHUNK_PROCESSING_ENABLED = True  # 是否启用分块处理
MARKDOWN_CHUNK_BY_H1 = True  # Markdown文档是否按一级标题（#）分块
MARKDOWN_CHUNK_PATTERN = r'^#\s+'  # Markdown分块的正则表达式模式，可配置为其他级别标题
SQL_CHUNK_BY_TABLE = True  # SQL文件是否按数据表分块
MAX_CHUNK_SIZE = 8000  # 单个分块的最大字符数，超过此大小会进一步分割
MIN_CHUNK_SIZE = 100  # 单个分块的最小字符数，小于此大小会合并到相邻分块
EMBEDDING_BATCH_SIZE = 5  # 向量化处理的批次大小，避免超过模型限制
```

### 2. Markdown文档分块处理

#### 按一级标题分块
- 使用正则表达式 `^#\s+` 识别一级标题（#）
- 每个一级标题及其内容作为一个独立分块
- 支持文档前言部分的单独处理

#### 智能内容分割
当单个章节内容超过 `MAX_CHUNK_SIZE` 时，系统会：
1. 首先按段落（双换行符）分割
2. 如果段落仍然过大，则按句子分割
3. 确保每个分块都在合理的大小范围内

#### 分块效果示例
```
原始文档: 三未信安密码服务平台管理端用户手册v3.4.0(无区域多租户模式).md
总分块数: 41个
分块大小统计:
  最小: 27 字符
  最大: 7995 字符
  平均: 4891 字符

大章节自动分割示例:
- "3. 功能模块介绍" (20450 字符) → 分割为 3 个子块
- "3.5.设备管理" (30192 字符) → 分割为 4 个子块
- "3.14. 密评管理" (25437 字符) → 分割为 4 个子块
```

### 3. SQL文件分块处理

#### 按数据表分块
- 每个 `CREATE TABLE` 语句作为一个独立分块
- 自动提取表名、字段信息和注释
- 支持PostgreSQL语法的表和字段注释

#### 分块效果示例
```
SQL文件: ccsp_data.sql (72551 字符)
总表数: 20个
每个表作为独立分块处理

示例分块:
- config表: 12个字段, 365字符
- dict_item表: 15个字段, 479字符
- user_info表: 多个字段, 包含完整的字段定义和注释
```

### 4. JSON文件分块处理

#### 支持PDMA格式
- 解析JSON格式的数据库设计文件
- 每个实体（entity）作为一个独立分块
- 提取完整的字段信息、类型、约束和注释

#### 分块效果示例
```
JSON文件: V3.4.pdma.json (1747915 字符)
总实体数: 231个
每个实体作为独立分块处理

包含详细的字段信息:
- 字段名称和注释
- 数据类型和长度
- 主键、非空、自增等约束
- 默认值信息
```

### 5. 优化的向量化处理

#### 分批处理
- 默认批次大小调整为5个文档
- 避免单次请求payload过大
- 支持进度显示和错误恢复

#### API兼容性
- 自动调整批次大小以适应不同的API限制
- 支持失败重试和降级处理
- 兼容OpenAI格式的嵌入API

## 实现细节

### 核心方法

1. **`_chunk_markdown_by_h1()`**: 按一级标题分块处理Markdown文档
2. **`_split_large_content()`**: 智能分割过大的内容块
3. **`_split_by_sentences()`**: 按句子分割段落
4. **`_chunk_sql_by_table()`**: 按数据表分块处理SQL文件
5. **`_parse_single_table()`**: 解析单个数据表的详细信息

### 向量化优化

- OpenAI嵌入模型的批次大小限制为10个文档
- 支持自定义批次大小参数
- 增强的错误处理和进度显示

## 测试结果

### 分块处理测试
运行 `debug/test_chunk_processing.py` 的测试结果：

```
=== Markdown分块处理测试 ===
总分块数: 41
分块大小统计:
  最小: 27 字符
  最大: 7995 字符
  平均: 4891 字符
✓ 所有分块大小都在限制范围内

=== SQL分块处理测试 ===
总表数: 20
分块大小统计:
  最小: 130 字符
  最大: 479 字符
  平均: 324 字符

=== JSON分块处理测试 ===
总实体数: 231
分块大小统计:
  最小: 89 字符
  最大: 2156 字符
  平均: 445 字符
```

### 知识库集成测试
```
✓ 知识库初始化成功
功能文档数量: 42
实体文档数量: 251

测试搜索: '用户管理'
用户手册结果: 0
SQL结果: 5 (包含USER_REGISTER, DIC_USER_TYPE, USER_INFO等相关表)
```

## 配置建议

### 针对不同文档类型的建议配置

1. **大型技术文档**:
   ```python
   MAX_CHUNK_SIZE = 8000
   MIN_CHUNK_SIZE = 100
   MARKDOWN_CHUNK_BY_H1 = True
   ```

2. **API文档或规范**:
   ```python
   MAX_CHUNK_SIZE = 5000
   MIN_CHUNK_SIZE = 200
   MARKDOWN_CHUNK_PATTERN = r'^##\s+'  # 按二级标题分块
   ```

3. **数据库设计文档**:
   ```python
   SQL_CHUNK_BY_TABLE = True
   MAX_CHUNK_SIZE = 10000  # 表结构通常较大
   ```

### 向量化性能优化

1. **小批次处理**:
   ```python
   EMBEDDING_BATCH_SIZE = 5  # 适合大多数API限制
   ```

2. **大批次处理**（如果API支持）:
   ```python
   EMBEDDING_BATCH_SIZE = 20  # 提高处理速度
   ```

## 总结

分块处理功能成功解决了以下问题：

1. ✅ **向量化内容超过模型限制**: 通过智能分块将大文档分解为适当大小的块
2. ✅ **用户手册按一级标题分块**: 实现了可配置的Markdown分块策略
3. ✅ **SQL表按数据表分块**: 每个表作为独立的知识单元
4. ✅ **批量向量化优化**: 避免API请求过大导致的错误
5. ✅ **保持语义完整性**: 分块策略保持了内容的逻辑结构

该功能现已集成到知识库系统中，可以处理各种大小的文档，并提供高质量的向量化搜索服务。
