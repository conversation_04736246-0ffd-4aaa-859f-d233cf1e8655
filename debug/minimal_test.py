#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

print("开始最小测试...")

try:
    # 只测试OpenAI嵌入类，不初始化知识库
    from knowledge_base import OpenAIEmbedding
    from config import EMBEDDING_MODEL, EMBEDDING_API_BASE, EMBEDDING_API_KEY
    
    print("创建嵌入模型实例...")
    embedding = OpenAIEmbedding(
        model_name=EMBEDDING_MODEL,
        api_base=EMBEDDING_API_BASE,
        api_key=EMBEDDING_API_KEY
    )
    
    print(f"API端点: {embedding.api_base}")
    
    print("测试编码...")
    result = embedding.encode(["测试"])
    
    if result is None:
        print("编码失败，API不可用")
    else:
        print(f"编码成功，形状: {result.shape}")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()

print("测试完成")
