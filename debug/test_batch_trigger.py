#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分批次处理触发机制
通过临时修改token阈值来触发分批次处理
"""

import sys
import os
import json
sys.path.append('..')

from cosmic_validator import CosmicValidator
import config

def test_csv_batch_trigger():
    """测试CSV分批次处理触发"""
    print("=" * 60)
    print("测试CSV分批次处理触发")
    print("=" * 60)
    
    validator = CosmicValidator(config)
    
    # 临时修改_perform_validation方法中的token阈值
    original_method = validator._perform_validation
    
    def modified_perform_validation(validation_data_str, prompt, summary_info):
        """修改后的校验方法，强制触发分批次处理"""
        # 检查数据大小
        data_size = len(validation_data_str)
        estimated_tokens = data_size * 1.5  # 使用原始计算方法
        print(f"数据大小: {data_size} 字符, 预估token: {estimated_tokens}")

        # 强制触发分批次处理（设置很低的阈值）
        validation_data_dict = json.loads(validation_data_str)
        if estimated_tokens > 1000:  # 很低的阈值，强制触发分批处理
            print("数据量过大，进行分批次处理...")
            return validator._process_large_data_in_batches(validation_data_dict, prompt, summary_info)
        
        # 如果没有触发，继续原有逻辑
        return original_method(validation_data_str, prompt, summary_info)
    
    # 临时替换方法
    validator._perform_validation = modified_perform_validation
    
    try:
        # 使用原始CSV文件测试
        csv_file = "../output-sft.csv"
        if not os.path.exists(csv_file):
            print(f"测试文件 {csv_file} 不存在")
            return False
        
        print(f"开始处理CSV文件: {csv_file}")
        result = validator.validate_cosmic_data(csv_file, "../check_prompt.md")
        
        if "error" in result:
            print(f"CSV分批次处理失败: {result['error']}")
            return False
        
        print("CSV分批次处理成功！")
        
        # 检查是否确实进行了分批次处理
        if "batch_processing_info" in result:
            batch_info = result["batch_processing_info"]
            print(f"✅ 确认进行了分批次处理:")
            print(f"  总批次数: {batch_info.get('total_batches', 0)}")
            print(f"  成功批次: {batch_info.get('successful_batches', 0)}")
            print(f"  失败批次: {batch_info.get('failed_batches', 0)}")
            print(f"  处理方法: {batch_info.get('processing_method', '未知')}")
            
            # 保存结果
            with open("csv_batch_test_result.json", 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print("结果已保存到: csv_batch_test_result.json")
            
            return True
        else:
            print("❌ 没有触发分批次处理")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 恢复原始方法
        validator._perform_validation = original_method

def test_json_batch_trigger():
    """测试JSON分批次处理触发"""
    print("=" * 60)
    print("测试JSON分批次处理触发")
    print("=" * 60)
    
    validator = CosmicValidator(config)
    
    # 临时修改_perform_validation方法中的token阈值
    original_method = validator._perform_validation
    
    def modified_perform_validation(validation_data_str, prompt, summary_info):
        """修改后的校验方法，强制触发分批次处理"""
        # 检查数据大小
        data_size = len(validation_data_str)
        estimated_tokens = data_size * 1.5
        print(f"数据大小: {data_size} 字符, 预估token: {estimated_tokens}")

        # 强制触发分批次处理（设置很低的阈值）
        validation_data_dict = json.loads(validation_data_str)
        if estimated_tokens > 1000:  # 很低的阈值，强制触发分批处理
            print("数据量过大，进行分批次处理...")
            return validator._process_large_data_in_batches(validation_data_dict, prompt, summary_info)
        
        # 如果没有触发，继续原有逻辑
        return original_method(validation_data_str, prompt, summary_info)
    
    # 临时替换方法
    validator._perform_validation = modified_perform_validation
    
    try:
        # 使用原始JSON文件测试
        json_file = "../output-sft.json"
        if not os.path.exists(json_file):
            print(f"测试文件 {json_file} 不存在")
            return False
        
        print(f"开始处理JSON文件: {json_file}")
        result = validator.validate_cosmic_data(json_file, "../check_prompt.md")
        
        if "error" in result:
            print(f"JSON分批次处理失败: {result['error']}")
            return False
        
        print("JSON分批次处理成功！")
        
        # 检查是否确实进行了分批次处理
        if "batch_processing_info" in result:
            batch_info = result["batch_processing_info"]
            print(f"✅ 确认进行了分批次处理:")
            print(f"  总批次数: {batch_info.get('total_batches', 0)}")
            print(f"  成功批次: {batch_info.get('successful_batches', 0)}")
            print(f"  失败批次: {batch_info.get('failed_batches', 0)}")
            print(f"  处理方法: {batch_info.get('processing_method', '未知')}")
            
            # 显示功能过程摘要
            fp_summary = result.get('function_process_summary', {})
            if fp_summary:
                print(f"  功能过程: 总数={fp_summary.get('total_processes', 0)}, "
                      f"完整={fp_summary.get('complete_processes', 0)}, "
                      f"不完整={fp_summary.get('incomplete_processes', 0)}")
            
            # 保存结果
            with open("json_batch_test_result.json", 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print("结果已保存到: json_batch_test_result.json")
            
            return True
        else:
            print("❌ 没有触发分批次处理")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 恢复原始方法
        validator._perform_validation = original_method

def check_batch_debug_files():
    """检查生成的批次调试文件"""
    print("=" * 60)
    print("检查生成的批次调试文件")
    print("=" * 60)
    
    batch_files = []
    for file in os.listdir('.'):
        if file.startswith('csv_batch_') and file.endswith('_input.json'):
            batch_files.append(('CSV', file))
        elif file.startswith('json_batch_') and file.endswith('_input.json'):
            batch_files.append(('JSON', file))
    
    if batch_files:
        print(f"找到 {len(batch_files)} 个批次调试文件:")
        for file_type, file in sorted(batch_files):
            file_size = os.path.getsize(file)
            print(f"  [{file_type}] {file} ({file_size} 字节)")
            
            # 检查文件内容结构
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                batch_info = data.get('batch_info', {})
                print(f"    批次: {batch_info.get('batch_index', '?')}/{batch_info.get('total_batches', '?')}")
                
                if file_type == 'CSV':
                    csv_content = data.get('csv_content', '')
                    lines = csv_content.count('\n') + 1 if csv_content else 0
                    print(f"    CSV行数: {lines}")
                elif file_type == 'JSON':
                    processes = data.get('function_processes', {})
                    print(f"    功能过程数: {len(processes)}")
                    
            except Exception as e:
                print(f"    ❌ 读取文件失败: {e}")
    else:
        print("没有找到批次调试文件")

def main():
    """主函数"""
    print("开始测试分批次处理触发机制")
    print("通过临时降低token阈值来强制触发分批次处理")
    print()
    
    # 测试CSV分批次处理
    csv_success = test_csv_batch_trigger()
    print()
    
    # 测试JSON分批次处理  
    json_success = test_json_batch_trigger()
    print()
    
    # 检查生成的批次文件
    check_batch_debug_files()
    
    print("=" * 60)
    print("分批次处理触发测试完成！")
    if csv_success and json_success:
        print("✅ 所有测试通过")
        print("分批次处理功能正常工作")
    else:
        print("❌ 部分测试失败")
    print("=" * 60)

if __name__ == "__main__":
    main()
