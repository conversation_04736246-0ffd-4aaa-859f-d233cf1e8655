#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分块处理功能
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from knowledge_base import KnowledgeBase
from config import (
    CHUNK_PROCESSING_ENABLED,
    MARKDOWN_CHUNK_BY_H1,
    MARKDOWN_CHUNK_PATTERN,
    SQL_CHUNK_BY_TABLE,
    MAX_CHUNK_SIZE,
    MIN_CHUNK_SIZE,
    EMBEDDING_BATCH_SIZE
)

def test_chunk_configuration():
    """测试分块配置"""
    print("=== 分块处理配置测试 ===")
    print(f"分块处理启用: {CHUNK_PROCESSING_ENABLED}")
    print(f"Markdown按H1分块: {MARKDOWN_CHUNK_BY_H1}")
    print(f"Markdown分块模式: {MARKDOWN_CHUNK_PATTERN}")
    print(f"SQL按表分块: {SQL_CHUNK_BY_TABLE}")
    print(f"最大分块大小: {MAX_CHUNK_SIZE}")
    print(f"最小分块大小: {MIN_CHUNK_SIZE}")
    print(f"嵌入批次大小: {EMBEDDING_BATCH_SIZE}")
    print()

def test_markdown_chunking():
    """测试Markdown分块处理"""
    print("=== Markdown分块处理测试 ===")
    
    # 创建知识库实例
    kb = KnowledgeBase()
    
    # 测试用户手册文档
    manual_path = "三未信安密码服务平台管理端用户手册v3.4.0(无区域多租户模式).md"
    if os.path.exists(manual_path):
        print(f"测试文档: {manual_path}")
        
        # 解析文档
        function_docs = kb._parse_user_manual(manual_path)
        
        print(f"总分块数: {len(function_docs)}")
        
        # 统计分块大小
        chunk_sizes = [len(doc['content']) for doc in function_docs]
        print(f"分块大小统计:")
        print(f"  最小: {min(chunk_sizes)} 字符")
        print(f"  最大: {max(chunk_sizes)} 字符")
        print(f"  平均: {sum(chunk_sizes) // len(chunk_sizes)} 字符")
        
        # 显示前几个分块的信息
        print(f"\n前5个分块信息:")
        for i, doc in enumerate(function_docs[:5]):
            print(f"  {i+1}. {doc['title']} ({len(doc['content'])} 字符)")
            print(f"     内容预览: {doc['content'][:100]}...")
            print()
        
        # 检查是否有超大分块
        large_chunks = [doc for doc in function_docs if len(doc['content']) > MAX_CHUNK_SIZE]
        if large_chunks:
            print(f"警告: 发现 {len(large_chunks)} 个超大分块:")
            for doc in large_chunks:
                print(f"  - {doc['title']}: {len(doc['content'])} 字符")
        else:
            print("✓ 所有分块大小都在限制范围内")
            
        # 保存分块结果到调试文件
        debug_file = "debug/markdown_chunks.json"
        with open(debug_file, 'w', encoding='utf-8') as f:
            json.dump([{
                'title': doc['title'],
                'content_length': len(doc['content']),
                'content_preview': doc['content'][:200] + "..." if len(doc['content']) > 200 else doc['content'],
                'type': doc['type'],
                'source': doc['source']
            } for doc in function_docs], f, ensure_ascii=False, indent=2)
        print(f"分块结果已保存到: {debug_file}")
        
    else:
        print(f"测试文档不存在: {manual_path}")
    
    print()

def test_sql_chunking():
    """测试SQL分块处理"""
    print("=== SQL分块处理测试 ===")
    
    # 创建知识库实例
    kb = KnowledgeBase()
    
    # 测试SQL文件
    sql_path = "ccsp_data.sql"
    if os.path.exists(sql_path):
        print(f"测试SQL文件: {sql_path}")
        
        # 解析SQL文件
        entity_docs = kb._parse_sql_file(sql_path)
        
        print(f"总表数: {len(entity_docs)}")
        
        # 统计分块大小
        chunk_sizes = [len(doc['content']) for doc in entity_docs]
        print(f"分块大小统计:")
        print(f"  最小: {min(chunk_sizes)} 字符")
        print(f"  最大: {max(chunk_sizes)} 字符")
        print(f"  平均: {sum(chunk_sizes) // len(chunk_sizes)} 字符")
        
        # 显示前几个表的信息
        print(f"\n前5个表信息:")
        for i, doc in enumerate(entity_docs[:5]):
            print(f"  {i+1}. {doc['table_name']} ({len(doc['content'])} 字符)")
            print(f"     注释: {doc.get('table_comment', '无')}")
            print(f"     字段数: {len(doc.get('fields', []))}")
            print()
        
        # 保存分块结果到调试文件
        debug_file = "debug/sql_chunks.json"
        with open(debug_file, 'w', encoding='utf-8') as f:
            json.dump([{
                'table_name': doc['table_name'],
                'table_comment': doc.get('table_comment', ''),
                'field_count': len(doc.get('fields', [])),
                'content_length': len(doc['content']),
                'content': doc['content'],
                'type': doc['type'],
                'source': doc['source']
            } for doc in entity_docs], f, ensure_ascii=False, indent=2)
        print(f"分块结果已保存到: {debug_file}")
        
    else:
        print(f"测试SQL文件不存在: {sql_path}")
    
    print()

def test_json_chunking():
    """测试JSON分块处理"""
    print("=== JSON分块处理测试 ===")
    
    # 创建知识库实例
    kb = KnowledgeBase()
    
    # 测试JSON文件
    json_path = "V3.4.pdma.json"
    if os.path.exists(json_path):
        print(f"测试JSON文件: {json_path}")
        
        # 解析JSON文件
        entity_docs = kb._parse_json_file(json_path)
        
        print(f"总实体数: {len(entity_docs)}")
        
        # 统计分块大小
        chunk_sizes = [len(doc['content']) for doc in entity_docs]
        print(f"分块大小统计:")
        print(f"  最小: {min(chunk_sizes)} 字符")
        print(f"  最大: {max(chunk_sizes)} 字符")
        print(f"  平均: {sum(chunk_sizes) // len(chunk_sizes)} 字符")
        
        # 显示前几个实体的信息
        print(f"\n前5个实体信息:")
        for i, doc in enumerate(entity_docs[:5]):
            print(f"  {i+1}. {doc['table_name']} ({len(doc['content'])} 字符)")
            print(f"     注释: {doc.get('table_comment', '无')}")
            print(f"     字段数: {len(doc.get('fields', []))}")
            print()
        
        # 保存分块结果到调试文件
        debug_file = "debug/json_chunks.json"
        with open(debug_file, 'w', encoding='utf-8') as f:
            json.dump([{
                'table_name': doc['table_name'],
                'table_comment': doc.get('table_comment', ''),
                'field_count': len(doc.get('fields', [])),
                'content_length': len(doc['content']),
                'content': doc['content'],
                'type': doc['type'],
                'source': doc['source']
            } for doc in entity_docs], f, ensure_ascii=False, indent=2)
        print(f"分块结果已保存到: {debug_file}")
        
    else:
        print(f"测试JSON文件不存在: {json_path}")
    
    print()

def test_knowledge_base_integration():
    """测试知识库集成"""
    print("=== 知识库集成测试 ===")
    
    try:
        # 创建知识库实例（这会触发完整的构建过程）
        kb = KnowledgeBase()
        
        if kb.enabled:
            print("✓ 知识库初始化成功")
            print(f"功能文档数量: {len(kb.function_docs)}")
            print(f"实体文档数量: {len(kb.entity_docs)}")
            
            # 测试搜索功能
            test_query = "用户管理"
            print(f"\n测试搜索: '{test_query}'")
            results = kb.search_knowledge(test_query)
            
            print(f"用户手册结果: {len(results['manual_results'])}")
            print(f"SQL结果: {len(results['sql_results'])}")
            
            # 显示搜索结果
            if results['manual_results']:
                print("\n用户手册搜索结果:")
                for i, result in enumerate(results['manual_results'][:3]):
                    print(f"  {i+1}. {result['title']} (相似度: {result['similarity']:.3f})")
                    print(f"     {result['content'][:100]}...")
                    print()
            
            if results['sql_results']:
                print("SQL搜索结果:")
                for i, result in enumerate(results['sql_results'][:3]):
                    print(f"  {i+1}. {result['table_name']} (相似度: {result['similarity']:.3f})")
                    print(f"     {result['content'][:100]}...")
                    print()
        else:
            print("✗ 知识库初始化失败")
            
    except Exception as e:
        print(f"✗ 知识库集成测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("开始分块处理功能测试...\n")
    
    # 确保调试目录存在
    os.makedirs("debug", exist_ok=True)
    
    # 运行各项测试
    test_chunk_configuration()
    test_markdown_chunking()
    test_sql_chunking()
    test_json_chunking()
    test_knowledge_base_integration()
    
    print("分块处理功能测试完成!")

if __name__ == "__main__":
    main()
