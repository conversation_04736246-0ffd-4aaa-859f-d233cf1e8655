[{"table_name": "USER_INFO", "table_comment": "用户信息表（330）", "field_count": 19, "content_length": 676, "content": "表名: USER_INFO(用户信息表（330）)\n字段: USER_ID(账户ID)[BIGINT]<PK,NOT NULL>, USER_CODE(登录账号)[VARCHAR]<NOT NULL>, USER_NAME(账号名称)[VARCHAR]<NOT NULL>, USER_STATUS(用户状态;1启用 2禁用 3锁定)[INT]<NOT NULL>, DISABLE_REASON_ID(禁用原因;0非禁用状态 1手动禁用 2长期未登录禁用)[INT]<NOT NULL,DEFAULT:0>, PHONE_NUM(手机号码;加密存储)[VARCHAR], EMAIL_ADDRESS(邮箱;加密存储)[VARCHAR], TENANT_ID(租户ID)[BIGINT]<NOT NULL>, TENANT_CODE(租户标识)[VARCHAR], APP_ID(应用ID;330), ORGANIZATION_ID(组织机构ID)[BIGINT], ROLE_ID(角色ID)[BIGINT]<NOT NULL>, SALT(用户盐值;加密存储)[VARCHAR]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "USER_REGISTER", "table_comment": "用户注册信息表 （330）", "field_count": 27, "content_length": 882, "content": "表名: USER_REGISTER(用户注册信息表 （330）)\n字段: USER_REGISTER_ID(账户注册ID)[BIGINT]<PK,NOT NULL>, USER_CODE(登录账号)[VARCHAR]<NOT NULL>, USER_NAME(账号名称)[VARCHAR]<NOT NULL>, PHONE_NUMBER(手机号码)[INT(8)]<NOT NULL>, EMAIL_ADDRESS(邮箱)[VARCHAR(100)], TENANT_ID(租户ID)[BIGINT]<NOT NULL>, TENANT_MARK(租户标识), ORGANIZATION_ID(部门ID)[BIGINT], ROLE_ID(角色ID)[BIGINT]<NOT NULL>, UKEY_CERT(UKEY证书)[VARCHAR(100)], UKEY_SERIAL(UKEY序列号)[VARCHAR(100)], SALT(用户盐值)<NOT NULL>, USER_TYPE(1平台管理员 2租户用户)[INT], AUDIT_STATUS(0 待审核 1 审核通过 2 拒绝)[INT]<NOT NULL>, AUDIT_TIME(审核时间)[VARCHAR], AUDIT_BY(审核人)[BIGINT], AUDIT_SUGGESTION(审核意见)[VARCHAR], INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR], PHONE_NUM(手机号码)[VARCHAR]<NOT NULL>, TENANT_CODE(租户标识)[VARCHAR], APP_ID(应用ID;330), AUTH_CODE(用户口令)[VARCHAR(64)]", "type": "entity", "source": "json_file"}, {"table_name": "USER_ROLE", "table_comment": "用户角色表", "field_count": 11, "content_length": 364, "content": "表名: USER_ROLE(用户角色表)\n字段: ROLE_ID(角色ID)[BIGINT]<PK,NOT NULL>, ROLE_NAME(角色名称)[VARCHAR(40)]<NOT NULL>, SORD_NUM(排序序号)[INT]<NOT NULL>, TENANT_ID(租户ID)[BIGINT], ROLE_TYPE(角色类别;1平台角色 2租户角色)[INT], INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "SYS_MENU", "table_comment": "菜单表", "field_count": 21, "content_length": 651, "content": "表名: SYS_MENU(菜单表)\n字段: MENU_ID(菜单ID)[BIGINT]<PK,NOT NULL>, MENU_NAME(菜单名称)[VARCHAR(100)]<NOT NULL>, PARENT_ID(父菜单ID)[BIGINT]<NOT NULL>, PATH(路由地址)[VARCHAR(200)], COMPONENT(组件路径)[VARCHAR], IS_CACHE(是否缓存)[INT], MENU_TYPE(菜单类型（M目录 C菜单 F按钮）)[VARCHAR(100)], VISIBLE(菜单状态（0显示 1隐藏）)[INT], STATUS(菜单状态（0正常 1停用）)[INT], PERMS(权限标识)[VARCHAR(100)], ICON(菜单图标)[VARCHAR(100)], ACTIVE_MENU(菜单分组)[VARCHAR(100)], ORDER_NUM(排序序号)[INT], INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR], IS_IFRAME(是否iframe)[VARCHAR], IFRAME_URL(iframe路径)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "USER_AUTH_CODE", "table_comment": "用户历史口令", "field_count": 10, "content_length": 355, "content": "表名: USER_AUTH_CODE(用户历史口令)\n字段: ID[BIGINT]<PK,NOT NULL>, USER_ID(账号ID)[BIGINT]<NOT NULL>, AUTH_CODE(口令;加密存储)[VARCHAR(100)]<NOT NULL>, CREATE_TYPE(来源 1初始化 2修改口令 3重置口令)[INT]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "SYS_ROLE_MENU", "table_comment": "角色菜单对应关系表(330)", "field_count": 9, "content_length": 249, "content": "表名: SYS_ROLE_MENU(角色菜单对应关系表(330))\n字段: ID[BIGINT]<PK,NOT NULL>, TENANT_ID(租户ID), MENU_ID(菜单ID)[BIGINT], ROLE_ID(角色ID)[BIGINT], REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "AUTH_CODE_BLACKLIST", "table_comment": "口令黑名单", "field_count": 9, "content_length": 318, "content": "表名: AUTH_CODE_BLACKLIST(口令黑名单)\n字段: ID[BIGINT]<PK,NOT NULL>, AUTH_CODE(明文)[VARCHAR(100)]<NOT NULL>, CRYPTO_AUTH_CODE(密文)[VARCHAR(100)]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "ORGANIZATION_INFO", "table_comment": "组织字典表（330）", "field_count": 11, "content_length": 385, "content": "表名: ORGANIZATION_INFO(组织字典表（330）)\n字段: ORGANIZATION_ID(组织机构ID)[BIGINT]<PK,NOT NULL>, ORGANIZATION_CODE(组织机构代码)[VARCHAR], ORGANIZATION_NAME(组织机构名称)[VARCHAR]<NOT NULL>, PARENT_ID(父ID)[VARCHAR], SORD_NUM(排序序号)[INT], INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "USER_SECURITY_EXTEND", "table_comment": "用户安全信息扩展表", "field_count": 16, "content_length": 573, "content": "表名: USER_SECURITY_EXTEND(用户安全信息扩展表)\n字段: ID[BIGINT]<PK,NOT NULL>, USER_ID(账户ID)[BIGINT]<NOT NULL>, HMAC(完整性校验值)[VARCHAR], LAST_ACTIVE_TIME(最后一次活跃时间)[VARCHAR], LAST_UPDATE_PWD_TIME(最后一次修改密码时间)[VARCHAR], ACCOUNT_START_TIME(账户有效期开始时间)[VARCHAR], ACCOUNT_END_TIME(账户有效期结束时间)[VARCHAR], LOGIN_ERROR_TIMES(登录失败次数)[INT], UNLOCK_TIME(解锁时间（时间戳）)[BIGINT], PASSWORD_CHANGE_FLAG(口令是否需要修改)[INT]<NOT NULL,DEFAULT:1>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "USER_CERT", "table_comment": "用户认证证书表", "field_count": 11, "content_length": 357, "content": "表名: USER_CERT(用户认证证书表)\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, USER_ID[BIGINT]<NOT NULL>, CERT(证书;加密存储)[VARCHAR(3000)]<NOT NULL>, SERIAL(证书;加密存储)[VARCHAR(100)], CERT_TYPE(证书类型;1UKEY证书)[INT], INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "CONFIG", "table_comment": "配置表(330)", "field_count": 16, "content_length": 612, "content": "表名: CONFIG(配置表(330))\n字段: CONFIG_ID(主键)[BIGINT]<PK,NOT NULL>, BUSI_SERVICE_TYPE_ID(业务服务类型id)[INT]<NOT NULL>, CONFIG_CODE(配置编码)[VARCHAR(100)]<NOT NULL>, CONFIG_NAME(配置名)[VARCHAR(100)]<NOT NULL>, CONFIG_VALUE(配置值)[VARCHAR]<NOT NULL>, CONFIG_TYPE(配置类型;0明文1密文)[INT]<NOT NULL,DEFAULT:1>, MAINTAIN_FLAG(可维护标志;0：不可维护1：可维护)[INT]<NOT NULL,DEFAULT:1>, SORD_NUM(排序)[INT]<NOT NULL>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR], TENANT_ID(租户id;空或者-1代表全局配置，有值是租户独有配置), APP_ID(应用id;有值是应用独有配置), VISIBLE_FLAG(可见标志；0：不可见；1：可见)[INT]<NOT NULL,DEFAULT:1>", "type": "entity", "source": "json_file"}, {"table_name": "DIC_BUSI_SERVICE_TYPE", "table_comment": "业务服务类型字典表", "field_count": 10, "content_length": 375, "content": "表名: DIC_BUSI_SERVICE_TYPE(业务服务类型字典表)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, PARENT_ID(父级ID)[INT]<NOT NULL>, BUSI_SERVICE_TYPE_CODE(业务服务类型编码)[VARCHAR(100)]<NOT NULL>, BUSI_SERVICE_TYPE_NAME(业务服务类型名称)[VARCHAR(100)]<NOT NULL>, SORD_NUM(排序)[INT]<NOT NULL>, REMARK(备注)[VARCHAR(300)], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DIC_SYS_DATA", "table_comment": "系统数据表", "field_count": 11, "content_length": 387, "content": "表名: DIC_SYS_DATA(系统数据表)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, DICT_TYPE(字典类型)[VARCHAR(100)]<NOT NULL>, DICT_LABEL(字典标签)[VARCHAR(100)]<NOT NULL>, DICT_VALUE(字典值)[VARCHAR(100)]<NOT NULL>, DEFAULT_FLAG(是否默认 1默认 0不默认)[INT]<NOT NULL,DEFAULT:0>, SORD_NUM(排序)[INT]<NOT NULL>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DIC_DEVICE_TYPE", "table_comment": "设备类型字典表", "field_count": 28, "content_length": 1103, "content": "表名: DIC_DEVICE_TYPE(设备类型字典表)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, DEVICE_TYPE_NAME(名称)[VARCHAR(50)]<NOT NULL>, REMARK(备注)[VARCHAR], DEFAULT_FLAG(是否默认 1默认 0不默认)[INT]<NOT NULL,DEFAULT:0>, DEVICE_TYPE_VALUE[INT], SUPPORT_BUSI_TYPES(支持的业务类型id，多个类型以逗号分隔)[VARCHAR], CODE(设备类型编码，与厂商编码共同使用，获取设备信息，不可修改)[VARCHAR(40)], IS_PHYSICAL(业务端口是否需要密码1需要 0不需要)[DECIMAL(8)], VENDOR_ID(所属厂商)[BIGINT]<NOT NULL>, READ_INFO(读取设备信息 1需要 0不需要)[INT], NEED_PASSWORD[INT], KEY_TEMPLET_IDS(该类型设备支持的密钥模板id，多个以逗号分隔)[TEXT], SUPPORT_SNMP(是否支持SNMP监控0不支持 1支持)[INT], HCCS_IMAGE(超融合虚拟机镜像类型)[VARCHAR(40)], SUPPORT_MAIN_KEY(是否支持生成主密钥0不支持 1支持)[INT], SUPPORT_SEC_MANAGE(是否支持安全管理，0不支持 1支持)[INT], DEFAULT_KEY_TEMPLET_IDS(默认自动生成密钥时支持的密钥模板id，多个以逗号分隔)[VARCHAR(500)], SUPPORT_GEN_KEY(是否支持生成密钥)[INT], PARENT_ID(上级ID顶级默认0)[INT], INVALID_FLAG(无效标识 0可用 1无效)[INT]<DEFAULT:0>, FAMILY_TYPE(1云密码机、2物理机、3虚拟机、4门禁卡 99其他)[INT], OWN_MANAGE_FLAG((是否监管 1监管 0只登记不监管,用于资产登记) 备用)[INT], MACHINE_TYPE(密码机服务类型(提供密码服务使用) 6服务器密码机 12签名验签服务器 15时间戳服务器)[INT], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR], EXTEND1(备用)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DIC_GENTYPE", "table_comment": "密钥生成类型字典表", "field_count": 9, "content_length": 305, "content": "表名: DIC_GENTYPE(密钥生成类型字典表)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, D_NAME(名称)[VARCHAR(40)]<NOT NULL>, D_VALUE(1 随机数 0 分量)[INT]<NOT NULL>, ISSYM(1 对称)[INT], DEFAULT_FLAG(是否默认 1默认 0不默认)[INT]<NOT NULL,DEFAULT:0>, CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DIC_KEYALGO", "table_comment": "密钥算法字典表", "field_count": 12, "content_length": 465, "content": "表名: DIC_KEYALGO(密钥算法字典表)\n字段: ID[BIGINT]<PK,NOT NULL>, D_NAME(名称)[VARCHAR(40)]<NOT NULL>, D_VALUE(1 对称 0 非对称)[INT]<NOT NULL>, D_LENGTH_ID(长度id 多个，分割)[VARCHAR(50)]<NOT NULL>, D_GENTYPE_ID(生成方式 0 分量 1 随机数)[VARCHAR(20)]<NOT NULL>, D_KEYUSE_ID(密钥用途 多个,分割)[VARCHAR(40)]<NOT NULL>, KMS_VALUE(KMS字典值)[INT]<NOT NULL>, DEFAULT_FLAG(是否默认 1默认 0不默认)[INT]<NOT NULL,DEFAULT:0>, CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DIC_KEYLEN", "table_comment": "密钥长度字典表", "field_count": 8, "content_length": 276, "content": "表名: DIC_KEYLEN(密钥长度字典表)\n字段: ID[BIGINT]<PK,NOT NULL>, D_NAME(算法名称)[VARCHAR(40)]<NOT NULL>, D_VALUE(算法长度)[INT]<NOT NULL>, DEFAULT_FLAG(是否默认 1默认 0不默认)[INT]<NOT NULL,DEFAULT:0>, CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DIC_KEYLIFEATTRIBUTE", "table_comment": "密钥生命周期属性字典表", "field_count": 8, "content_length": 294, "content": "表名: DIC_KEYLIFEATTRIBUTE(密钥生命周期属性字典表)\n字段: ID[BIGINT]<PK,NOT NULL>, D_NAME(名称)[VARCHAR(40)]<NOT NULL>, D_VALUE(1 对称 0 非对称)[INT]<NOT NULL>, DEFAULT_FLAG(是否默认 1默认 0不默认)[INT]<NOT NULL,DEFAULT:0>, CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DIC_KEYTYPE", "table_comment": "密钥类型字典表", "field_count": 8, "content_length": 281, "content": "表名: DIC_KEYTYPE(密钥类型字典表)\n字段: ID[BIGINT]<PK,NOT NULL>, D_NAME(名称)[VARCHAR(40)]<NOT NULL>, D_VALUE(1 对称 0 非对称)[INT]<NOT NULL>, DEFAULT_FLAG(是否默认 1默认 0不默认)[INT]<NOT NULL,DEFAULT:0>, CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DIC_KEYUSE", "table_comment": "密钥用途字典表", "field_count": 10, "content_length": 347, "content": "表名: DIC_KEYUSE(密钥用途字典表)\n字段: ID[BIGINT]<PK,NOT NULL>, D_NAME(名称)[VARCHAR(40)]<NOT NULL>, D_VALUE(1 对称 0 非对称)[INT]<NOT NULL>, ISSYM(1对称 0 非对称)[INT]<NOT NULL>, KMS_VALUE(kms字典值)[INT]<NOT NULL>, DEFAULT_FLAG(是否默认 1默认 0不默认)[INT]<NOT NULL,DEFAULT:0>, CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DIC_TENANT_STATUS", "table_comment": "租户状态字典表", "field_count": 8, "content_length": 298, "content": "表名: DIC_TENANT_STATUS(租户状态字典表)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, S_NAME(状态名1初始化、2启动中、3启动失败、4运行、5扩展中、6恢复中、7停服中8、停服、9销毁中、10销毁失败)[VARCHAR(40)]<NOT NULL>, S_VALUE(状态值)[INT], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR], EXTEND1(备用1)[VARCHAR(60)]", "type": "entity", "source": "json_file"}, {"table_name": "TENANT", "table_comment": "租户表（1030   330 520 332）", "field_count": 21, "content_length": 745, "content": "表名: TENANT(租户表（1030   330 520 332）)\n字段: TENANT_ID(租户ID)[BIGINT]<PK,NOT NULL>, TENANT_CODE(租户标识)[VARCHAR(64)]<NOT NULL>, TENANT_NAME(租户名称)[VARCHAR]<NOT NULL>, TENANT_LEVEL(租户级别)[INT]<NOT NULL>, ORGANIZATION_ID(组织机构ID), SENIOR_TENANT_ID(上级租户ID)[BIGINT], TENANT_STATUS(租户状态;1：初始化（未添加设备或服务）；2：运行中；3：停用中；4：停用)[INT], REGION_ID(区域ID), DEVICE_AREA_ID(租户设备资源组)<NOT NULL>, SERVICE_AREA_ID(租户服务资源组)<NOT NULL>, EXCLUSIVE(是否支持专享;0：不支持；1：支持)<NOT NULL>, ORGAN(机构名称)[VARCHAR], HMAC(完整性校验)[VARCHAR], INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT]<NOT NULL>, CREATE_TIME(创建时间)[VARCHAR]<NOT NULL>, UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR], DEVICE_GROUP_ID(租户设备资源组)[BIGINT], SERVICE_GROUP_ID(租户服务资源组)[BIGINT]", "type": "entity", "source": "json_file"}, {"table_name": "TENANT_TO_BUSI_TYPE", "table_comment": "租户和业务类型关联表", "field_count": 8, "content_length": 287, "content": "表名: TENANT_TO_BUSI_TYPE(租户和业务类型关联表)\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, TENANT_ID(租户ID)[BIGINT]<NOT NULL>, BUSI_TYPE_ID(业务类型ID)[BIGINT]<NOT NULL>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT]<NOT NULL>, CREATE_TIME(创建时间)[VARCHAR]<NOT NULL>, UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "APP_TO_TENANT", "table_comment": "应用和租户关联表", "field_count": 8, "content_length": 251, "content": "表名: APP_TO_TENANT(应用和租户关联表)\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, APP_ID(应用ID)[BIGINT]<NOT NULL>, TENANT_ID(租户ID)[BIGINT]<NOT NULL>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "APP_BUSI_TO_GROUP", "table_comment": "应用业务和设备组/服务组关联表", "field_count": 12, "content_length": 436, "content": "表名: APP_BUSI_TO_GROUP(应用业务和设备组/服务组关联表)\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, APP_ID(应用ID)[BIGINT]<NOT NULL>, BUSI_TYPE_ID(业务类型ID)[BIGINT]<NOT NULL>, GROUP_TYPE(组类型；1:设备组；2：服务组)[BIGINT]<NOT NULL>, GROUP_ID(组ID)[BIGINT]<NOT NULL>, TENANT_ID(应用所属租户ID)[BIGINT]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "APP_REGISTER_TO_TENANT", "table_comment": "应用申请和租户关联表", "field_count": 10, "content_length": 357, "content": "表名: APP_REGISTER_TO_TENANT(应用申请和租户关联表)\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, APP_REGISTER_ID(应用申请信息ID)[BIGINT]<NOT NULL>, APP_ID(应用ID)[BIGINT]<NOT NULL>, TENANT_ID(租户ID)[BIGINT]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_TO_DEVICE_GROUP", "table_comment": "服务和设备组关联表", "field_count": 9, "content_length": 323, "content": "表名: SERVICE_TO_DEVICE_GROUP(服务和设备组关联表)\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, SERVICE_ID(服务ID)[BIGINT]<NOT NULL>, DEVICE_GROUP_ID(设备组ID)[BIGINT]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "TENANT_TO_KMS_RELATION", "table_comment": "租户与kms租户关联关系表", "field_count": 10, "content_length": 366, "content": "表名: TENANT_TO_KMS_RELATION(租户与kms租户关联关系表)\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, TENANT_ID(租户id)[BIGINT]<NOT NULL>, TENANT_CODE(租户标识)[VARCHAR]<NOT NULL>, KMS_TENANT_NAME(KMS租户名称)[VARCHAR]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "APP_TO_KMS_RELATION", "table_comment": "应用与KMS用户关联关系表", "field_count": 13, "content_length": 460, "content": "表名: APP_TO_KMS_RELATION(应用与KMS用户关联关系表)\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, TENANT_ID(租户ID)[BIGINT]<NOT NULL>, TENANT_CODE(租户标识)[VARCHAR]<NOT NULL>, SERVICE_TYPE_ID(服务类型ID)[BIGINT], APP_ID(应用ID)[BIGINT]<NOT NULL>, APP_CODE(应用标识)[VARCHAR]<NOT NULL>, KMS_APP_CODE(KMS用户名称)[VARCHAR]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "APP_INFO", "table_comment": "应用表（1030）", "field_count": 15, "content_length": 454, "content": "表名: APP_INFO(应用表（1030）)\n字段: APP_ID(应用ID)[BIGINT]<PK,NOT NULL>, APP_CODE(应用标识)[VARCHAR]<NOT NULL>, APP_NAME(应用名称)[VARCHAR]<NOT NULL>, APP_SHORT(应用简称)[VARCHAR], REGION_ID(区域ID), AUTH_TYPE(授权类型)[INT]<NOT NULL>, TENANT_ID(所属租户ID)[BIGINT]<NOT NULL>, APPLY_BY(申请人)[BIGINT], INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, HMAC(完整性字段), REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "APP_AUTH_CODE", "table_comment": "应用登录口令", "field_count": 9, "content_length": 298, "content": "表名: APP_AUTH_CODE(应用登录口令)\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, APP_ID(应用ID)[BIGINT]<NOT NULL>, AUTH_CODE(口令)[VARCHAR]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "APP_AUTH_CERT", "table_comment": "应用登录证书", "field_count": 10, "content_length": 328, "content": "表名: APP_AUTH_CERT(应用登录证书)\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, APP_ID(应用ID)[BIGINT]<NOT NULL>, CERT_NAME(证书名称)[VARCHAR]<NOT NULL>, CERT(证书内容)[TEXT]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "APP_REGISTER", "table_comment": "应用申请表", "field_count": 18, "content_length": 546, "content": "表名: APP_REGISTER(应用申请表)\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, APP_ID(应用ID)[BIGINT], APP_REGISTER_STATUS(应用申请状态)[INT]<NOT NULL>, APP_CODE(应用标识)[VARCHAR]<NOT NULL>, APP_NAME(应用名称)[VARCHAR]<NOT NULL>, APP_SHORT(应用简称)[VARCHAR], AUTH_TYPE(授权类型)[INT]<NOT NULL>, IS_PASS(是否通过)[INT], AUDIT_BY(审批人)[BIGINT], AUDIT_REMARK(审批意见)[VARCHAR], TENANT_ID(所属租户ID)[BIGINT]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, HMAC(完整性字段), REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DIC_BUSI_TYPE", "table_comment": "业务类型字典表", "field_count": 8, "content_length": 258, "content": "表名: DIC_BUSI_TYPE(业务类型字典表)\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, BUSI_TYPE_ID(业务类型ID)<NOT NULL>, BUSI_TYPE_NAME(业务类型名称)[VARCHAR]<NOT NULL>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "TENANT_REGISTER", "table_comment": "租户注册表", "field_count": 17, "content_length": 490, "content": "表名: TENANT_REGISTER(租户注册表)\n字段: TENANT_ID(租户ID)<PK,NOT NULL>, TENANT_CODE(租户标识)[VARCHAR(64)]<NOT NULL>, TENANT_NAME(租户名称)<NOT NULL>, TENANT_LEVEL(租户级别)<NOT NULL>, SENIOR_TENANT_ID(上级租户ID), TENANT_STATUS(租户状态;1：初始化；2：运行中；3：停用;4:销毁), PARTITION_ID(租户资源分区ID)<NOT NULL>, ORGAN(机构名称)<NOT NULL>, STATUS(状态), AUDIT_BY(审批人)<NOT NULL>, AUDIT_REMARK(审批意见), INVALID_FLAG(是否作废;默认为0)<NOT NULL,DEFAULT:0>, REMARK(备注), CREATE_BY(创建人)<NOT NULL>, CREATE_TIME(创建时间)<NOT NULL>, UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "TENANT_REGISTER_USER", "table_comment": "租户注册用户信息", "field_count": 16, "content_length": 456, "content": "表名: TENANT_REGISTER_USER(租户注册用户信息)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, TENANT_ID(租户ID)[BIGINT], USER_TYPE(用户类型)[INT], USER_NAME(用户名称)[VARCHAR(50)], USER_CODE(账号)[VARCHAR], AUTH_CODE(密码;加密存储)[VARCHAR(100)], CERT(证书内容)[VARCHAR(2000)], SERIAL(Ukey序列号)[VARCHAR(100)], RANDOM(Ukey验证随机数)[VARCHAR], SIGNATURE(Ukey验证签名值)[VARCHAR], HMAC(完整性字段), REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DEVICE_VENDOR", "table_comment": "厂商信息表", "field_count": 12, "content_length": 474, "content": "表名: DEVICE_VENDOR(厂商信息表)\n字段: VENDOR_ID(厂商ID)[BIGINT]<PK,NOT NULL>, VENDOR_NAME(厂商名称)[VARCHAR(80)]<NOT NULL>, VENDOR_SHORT_NAME(厂商简称)[VARCHAR(50)]<NOT NULL>, LINK_MAN(联系人;加密存储)[VARCHAR(40)], LINK_MAN_PHONE(联系方式;加密存储)[VARCHAR(40)], DEFAULT_FLAG(是否默认（1默认 0自定义）;1默认 0自定义 。默认的不可删除，可修改基础信息)[INT]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DEVICE_NET_DETAIL", "table_comment": "IP资源详情（1030）", "field_count": 14, "content_length": 501, "content": "表名: DEVICE_NET_DETAIL(IP资源详情（1030）)\n字段: IP_ID(IP地址ID)[BIGINT]<PK,NOT NULL>, MGT_IP(管理IP)[VARCHAR(60)]<NOT NULL>, BUSI_IP(业务IP)[VARCHAR(60)], MGT_IP_NUM(管理IP数字)[BIGINT]<NOT NULL>, BUSI_IP_NUM(业务IP数字)[BIGINT], GATEWAY(管理IP网关)[VARCHAR(60)]<NOT NULL>, STATUS(状态（1未使用 2锁定 3被使用）)[INT]<NOT NULL,DEFAULT:1>, DEVICE_ID(绑定云机的ID)[BIGINT], INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DIC_DEVICE_API_TEMPLATE", "table_comment": "设备接口模板字典表", "field_count": 13, "content_length": 518, "content": "表名: DIC_DEVICE_API_TEMPLATE(设备接口模板字典表)\n字段: ID(设备接口模板ID)[BIGINT]<PK,NOT NULL>, INTERACTION_API_ID(所属设备交互类型ID)[BIGINT]<NOT NULL>, SORD_NUM(接口序号（展示顺序）)[INT]<NOT NULL>, API_SERIAL_NUM(接口编号(同交互类型下唯一))[VARCHAR(100)]<NOT NULL>, API_NAME(接口名称)[VARCHAR(100)]<NOT NULL>, CONTEXT_PATH(环境路径)[VARCHAR(100)]<NOT NULL>, API_PATH(接口路径)[VARCHAR(200)]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DIC_DEVICE_INTERACTION_TYPE", "table_comment": "设备交互类型字典表", "field_count": 10, "content_length": 429, "content": "表名: DIC_DEVICE_INTERACTION_TYPE(设备交互类型字典表)\n字段: ID(设备交互类型ID)[BIGINT]<PK,NOT NULL>, INTERACTION_NAME(接口交互类型名称)[VARCHAR(100)]<NOT NULL>, INTERACTION_SERIAL_NUMBER(接口交互类型序编号)[VARCHAR(100)]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR], FAMILY_TYPE(所属设备类型（设备类型(1：云密码机:0：物理机和虚拟机)）)[INT]<NOT NULL>", "type": "entity", "source": "json_file"}, {"table_name": "DEVICE_API", "table_comment": "设备接口信息", "field_count": 16, "content_length": 643, "content": "表名: DEVICE_API(设备接口信息)\n字段: API_ID(设备接口ID)[BIGINT]<PK,NOT NULL>, API_NAME(接口名称)[VARCHAR(100)]<NOT NULL>, DEVIICE_TYPE_ID(所属设备类型ID)<NOT NULL>, INTERACTION_SERIAL_NUMBER(设备交互类型编号)[VARCHAR(100)]<NOT NULL>, API_TEMPLATE_ID(所属接口模板ID)[BIGINT]<NOT NULL>, API_SERIAL_NUMBER(所属接口编号)[VARCHAR(100)]<NOT NULL>, CONTEXT_PATH(环境路径)[VARCHAR(100)]<NOT NULL>, API_PATH(接口路径)[VARCHAR(200)]<NOT NULL>, DEFAULT_FLAG(是否默认（0：否；1：是）)[INT]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR], DEVICE_TYPE_ID(所属设备类型ID)[BIGINT]<NOT NULL>", "type": "entity", "source": "json_file"}, {"table_name": "DIC_DEVICE_MACHINE_TYPE", "table_comment": "密码机服务类型字典表", "field_count": 10, "content_length": 390, "content": "表名: DIC_DEVICE_MACHINE_TYPE(密码机服务类型字典表)\n字段: ID(设备类型ID)[BIGINT]<PK,NOT NULL>, SERVER_NAME(设备类型名称)[VARCHAR(100)]<NOT NULL>, SERVER_CODE(设备类型值)[INT]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR], FAMILY_TYPE(所属设备类型（设备类型(1：云密码机:0：物理机和虚拟机)）)[INT]<NOT NULL>", "type": "entity", "source": "json_file"}, {"table_name": "DIC_DEVICE_MANAGEMENT_STATUS", "table_comment": "密码机使用状态字典表", "field_count": 9, "content_length": 334, "content": "表名: DIC_DEVICE_MANAGEMENT_STATUS(密码机使用状态字典表)\n字段: ID(操作状态ID)[BIGINT]<PK,NOT NULL>, STATUS_NAME(状态名)[VARCHAR(100)]<NOT NULL>, STATUS_CODE(状态值)[VARCHAR]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DIC_DEVICE_OPERATION_STATUS", "table_comment": "密码机操作状态字典表", "field_count": 9, "content_length": 326, "content": "表名: DIC_DEVICE_OPERATION_STATUS(密码机操作状态字典表)\n字段: ID(操作状态ID)[BIGINT]<PK,NOT NULL>, OPER_NAME(状态名称)[VARCHAR(100)]<NOT NULL>, OPER_CODE(状态值)[INT]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DEVICE_BUSITYPE", "table_comment": "设备类型与业务类型关系表", "field_count": 11, "content_length": 382, "content": "表名: DEVICE_BUSITYPE(设备类型与业务类型关系表)\n字段: ID(主键标识)[BIGINT]<PK,NOT NULL>, DEVICE_ID(设备ID)<NOT NULL>, BUSITYPE_ID(业务类型ID)<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR], DEVICE_TYPE_ID(设备类型ID)[BIGINT]<NOT NULL>, BUSI_TYPE_ID(业务类型ID)[BIGINT]<NOT NULL>", "type": "entity", "source": "json_file"}, {"table_name": "DEVICE_GROUP", "table_comment": "设备组（1030）", "field_count": 16, "content_length": 517, "content": "表名: DEVICE_GROUP(设备组（1030）)\n字段: DEVICE_GROUP_ID(设备组ID)[BIGINT]<PK,NOT NULL>, DEVICE_GROUP_CODE(设备组标识), DEVICE_GROUP_NAME(设备组名称)[VARCHAR]<NOT NULL>, DEVICE_GROUP_TYPE(设备组类型)[INT]<NOT NULL>, REGION_ID(区域ID), TENANT_ID(所属租户ID)[BIGINT], IS_REST(是否支持直接调用;0：不支持；1：支持), NET_PRO(网络协议;1：TCP；2：HTTPS), DEVICE_TYPE_ID(设备类型ID), IS_SHARE(是否支持共享;0：独享；1：共享)[INT], INVALID_FLAG(是否作废)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_GROUP", "table_comment": "服务组（1030）", "field_count": 14, "content_length": 503, "content": "表名: SERVICE_GROUP(服务组（1030）)\n字段: SERVICE_GROUP_ID(服务组ID)[BIGINT]<PK,NOT NULL>, SERVICE_GROUP_CODE(服务组CODE)<NOT NULL>, SERVICE_GROUP_NAME(服务组名称)[VARCHAR]<NOT NULL>, SERVICE_GROUP_TYPE(服务组类型)[INT]<NOT NULL>, SERVICE_GROUP_STATUS(服务组状态)<NOT NULL>, REGION_ID(区域ID), TENANT_ID(租户ID)[BIGINT], IS_SHARE(是否共享；0：否；1：是)[INT]<NOT NULL,DEFAULT:0>, INVALID_FLAG(是否作废；0：否；1：是)[INT]<DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_TYPE_TO_BUSI_TYPE", "table_comment": "服务类型和业务类型关联表", "field_count": 9, "content_length": 315, "content": "表名: SERVICE_TYPE_TO_BUSI_TYPE(服务类型和业务类型关联表)\n字段: ID(id)[BIGINT]<NOT NULL>, SERVICE_TYPE_ID(服务类型ID)[BIGINT]<NOT NULL>, BUSI_TYPE_ID(业务类型ID)[BIGINT]<NOT NULL>, BUSI_TYPE_NAME(业务名称)[VARCHAR(50)], REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_GATEWAY", "table_comment": "API网关表（1030 520）", "field_count": 15, "content_length": 568, "content": "表名: SERVICE_GATEWAY(API网关表（1030 520）)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, GATEWAY_CODE(网关组件标识(apisix中的id))[VARCHAR(100)], GATEWAY_NAME(网关名称)[VARCHAR(50)]<NOT NULL>, GATEWAY_TYPE(网关类型，1：管理，2：业务)[INT], IP(网关IP)[VARCHAR(20)]<NOT NULL>, PORT(网关端口)[INT]<NOT NULL>, REAL_IP(业务网关真实IP地址), REAL_PORT(业务网关真实端口), PROXY_MGT_NODES(反向代理管理节点信息;json样式存储，[{\"IP\":\"************\",\"PORT\":\"88656\"},{\"IP\":\"************\",\"PORT\":\"88656\"}])[VARCHAR(1000)], REGION_ID(区域ID), REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_GATEWAY_ROUTE", "table_comment": "API网关路由表（1030）", "field_count": 28, "content_length": 986, "content": "表名: SERVICE_GATEWAY_ROUTE(API网关路由表（1030）)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, ROUTE_CODE(路由组件标识(apisix中的id))[VARCHAR(100)], ROUTE_NAME(路由名称)[VARCHAR(50)]<NOT NULL>, REGION_ID(区域ID), GATEWAY_ID(网关表主键)[BIGINT], URIS(一组URL路径)[VARCHAR(500)], METHODS(一组请求限制)[VARCHAR(100)], HOSTS(一组host域名)[VARCHAR(500)], VARS(一组元素列表)[VARCHAR], UPSTREAM_CODE(upstream组件标识(apisix中的id))[VARCHAR(100)], UPSTREAM(upstream)[VARCHAR], REMOTE_ADDRS(一组客户端请求 IP 地址)[VARCHAR(500)], TIMEOUT(为route 设置 upstream 的连接、发送消息、接收消息的超时时间)[VARCHAR(500)], PLUGINS(route 绑定插件)[VARCHAR(500)], FILTER_FUNC(用户自定义的过滤函数)[VARCHAR], SERVICE_CODE(网关中服务组件标识(apisix中的id))[VARCHAR(100)], REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR], ROUTE_TYPE(路由类型 1管理 2业务 3业务二 4TCP 5UDP)[INT], TENANT_ID(租户id)[BIGINT], BUSI_TYPE_ID(业务/服务类型)[BIGINT], GROUP_ID(服务/设备组)[BIGINT], GROUP_TYPE(组类型 1服务组 2设备组)[INT], SERVER_PORT(NGINX TCP服务端口)[INT], APISIX_SERVER_PORT(APISIX TCP服务端口)[INT]", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_GROUP_TO_BUSI_TYPE", "table_comment": "服务组和业务类型关联表", "field_count": 8, "content_length": 283, "content": "表名: SERVICE_GROUP_TO_BUSI_TYPE(服务组和业务类型关联表)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, SERVICE_GROUP_ID(服务组ID)[BIGINT]<NOT NULL>, BUSI_TYPE_ID(业务类型ID)[BIGINT]<NOT NULL>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DEVICE_GROUP_TO_BUSI_TYPE", "table_comment": "设备组和业务类型关联表", "field_count": 8, "content_length": 281, "content": "表名: DEVICE_GROUP_TO_BUSI_TYPE(设备组和业务类型关联表)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, DEVICE_GROUP_ID(设备组ID)[BIGINT]<NOT NULL>, BUSI_TYPE_ID(业务类型ID)[BIGINT]<NOT NULL>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "APP_TO_BUSI_TYPE", "table_comment": "应用和业务关联表", "field_count": 8, "content_length": 279, "content": "表名: APP_TO_BUSI_TYPE(应用和业务关联表)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, APP_ID(应用ID)[BIGINT]<NOT NULL>, BUSI_TYPE_ID(业务类型ID)[BIGINT]<NOT NULL>, CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR], INVALID_FLAG(是否作废;默认为0)[INT]<DEFAULT:0>", "type": "entity", "source": "json_file"}, {"table_name": "TENANT_SECRET_KEY_RESOURCE", "table_comment": "租户密钥资源管理", "field_count": 11, "content_length": 346, "content": "表名: TENANT_SECRET_KEY_RESOURCE(租户密钥资源管理)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, TENANT_ID(租户ID)[BIGINT]<NOT NULL>, SECRET_NUM(密钥数量;-1表示无限)[INT], CERT_NUM(证书数量;-1表示无限)[INT], USE_SECRET_NUM(使用密钥数量)[INT], USE_CERT_NUM(使用证书数量)[INT], REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DIC_BUSI_URL_TYPE", "table_comment": "业务地址类型", "field_count": 8, "content_length": 262, "content": "表名: DIC_BUSI_URL_TYPE(业务地址类型)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, NAME(类型名称)[VARCHAR]<NOT NULL>, PROTOCOL(1 http 2https 3tcp)[INT]<NOT NULL>, REMARK(描述)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "BUSI_URL_INFO", "table_comment": "业务地址对象（1030）", "field_count": 13, "content_length": 398, "content": "表名: BUSI_URL_INFO(业务地址对象（1030）)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, REGION_ID(区域ID), TENANT_ID(租户ID)[BIGINT]<NOT NULL>, BUSI_URL_TYPE_ID(业务地址类型)[BIGINT]<NOT NULL>, NAME(名称)[VARCHAR]<NOT NULL>, PROTOCOL(请求协议)[INT]<NOT NULL>, IP(IP地址)[VARCHAR]<NOT NULL>, PORT(端口)[INT]<NOT NULL>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "BUSI_URL_TYPE_TO_BUSI", "table_comment": "业务地址类型和业务类型关联表", "field_count": 8, "content_length": 279, "content": "表名: BUSI_URL_TYPE_TO_BUSI(业务地址类型和业务类型关联表)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, URL_TYPE_ID(业务地址类型ID)[BIGINT]<NOT NULL>, BUSI_TYPE_ID(业务类型ID)[BIGINT]<NOT NULL>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DIC_SERVICE_QUOTA", "table_comment": "业务服务配额信息", "field_count": 17, "content_length": 650, "content": "表名: DIC_SERVICE_QUOTA(业务服务配额信息)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, QUOTA_NAME(配额名称;key值)[VARCHAR]<NOT NULL>, SHOW_NAME(配额显示名称;展示名称)[VARCHAR]<NOT NULL>, VALUE_UNIT(配额值单位)[VARCHAR]<NOT NULL>, SERVICE_TYPE_ID(服务类型ID)[BIGINT]<NOT NULL>, SERVICE_CODE(服务标识)[VARCHAR]<NOT NULL>, TIME_VALID(时间是否生效;1：有效，0：不生效)[INT]<NOT NULL,DEFAULT:0>, DEFAULT_VALUE(默认配额值)[INT]<NOT NULL,DEFAULT:-1>, MIN_VALUE(允许设置的最小值)[INT], MAX_VALUE(允许设置的最大值)[INT], MANAGE_TYPE(管理侧，1：服务侧管理，2：平台侧管理)[INT]<NOT NULL>, IS_ENABLE(是否有效;1：有效；0：无效)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "TENANT_QUOTA_INFO", "table_comment": "租户业务服务配额限制信息", "field_count": 13, "content_length": 425, "content": "表名: TENANT_QUOTA_INFO(租户业务服务配额限制信息)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, TENANT_ID(租户ID)[BIGINT]<NOT NULL>, SERVICE_CODE(服务标识;服务标识+配额信息key对应唯一配额信息)[VARCHAR]<NOT NULL>, QUOTA_NAME(配额信息key)[VARCHAR]<NOT NULL>, QUOTA_VALUE(配额值)[INT]<NOT NULL>, START_TIME(开始时间)[VARCHAR], END_TIME(结束数据)[VARCHAR], HMAC(完整性字段), REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "TENANT_QUOTA_USE_INFO", "table_comment": "业务服务配额使用信息", "field_count": 12, "content_length": 413, "content": "表名: TENANT_QUOTA_USE_INFO(业务服务配额使用信息)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, TENANT_ID(租户ID)[BIGINT]<NOT NULL>, SERVICE_CODE(服务标识;服务标识+配额信息key对应唯一配额信息)[VARCHAR]<NOT NULL>, QUOTA_NAME(配额信息key)[VARCHAR]<NOT NULL>, TOTAL_QUOTA(配额总量)[VARCHAR], USED_QUOTA(已使用量)[VARCHAR], RESIDUE_QUOTA(剩余量)[VARCHAR], REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_TYPE_TO_DEVICE_GROUP", "table_comment": "服务类型绑定设备组关联表", "field_count": 9, "content_length": 313, "content": "表名: SERVICE_TYPE_TO_DEVICE_GROUP(服务类型绑定设备组关联表)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, TENANT_ID(租户ID)[BIGINT]<NOT NULL>, SERVICE_TYPE_ID(服务类型ID)[BIGINT]<NOT NULL>, DEVICE_GROUP_ID(设备组ID)[BIGINT], REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "BUSI_URL_TO_GATEWAY", "table_comment": "业务地址和网关关联表", "field_count": 8, "content_length": 275, "content": "表名: BUSI_URL_TO_GATEWAY(业务地址和网关关联表)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, BUSI_URL_ID(业务地址ID)[BIGINT]<NOT NULL>, SERVICE_GATEWAY_ID(网关ID)[BIGINT]<NOT NULL>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "CA_CERT", "table_comment": "信任域证书信息", "field_count": 31, "content_length": 1233, "content": "表名: CA_CERT(信任域证书信息)\n字段: CA_ID(证书ID)[BIGINT]<PK,NOT NULL>, ALIAS(证书标签)[VARCHAR(128)]<NOT NULL>, SIGN_ALGORITHM(签名算法)[VARCHAR(32)]<NOT NULL>, SERIALNUMBER(序列号(16进制数))[VARCHAR(64)]<NOT NULL>, VALID_TIME(证书起始时间)[VARCHAR]<NOT NULL>, EXPIRE_TIME(证书过期时间)[VARCHAR]<NOT NULL>, SUBJECT_DN(证书主题)[VARCHAR(512)]<NOT NULL>, ISSUER_DN(颁发者DN)[VARCHAR], CERTIFICATE(证书内容（base64编码))[LONGTEXT]<NOT NULL>, VERIFY_TYPE(校验类型（0.不校验1.CA证书校验2.CA证书+crl校验,3.ocsp校验）)[INT]<NOT NULL>, ALLOW_EXPIRED_FLAG(是否与允许过期（0允许，1不允许）;默认0)[INT]<NOT NULL,DEFAULT:0>, CRL_TYPE(CRL认证时，CRL来源)[VARCHAR], CRL_LENGTH(CRL的长度)[INT], CRL_INFO(CRL)[LONGTEXT], CRL_UPDATE_DAY(从CRL发布站点更新CRL时间0：每天，1.周一...7.周日)[INT], CRL_UPDATE_HOUR(从CRL发布站点更新CRL的时间0:0点...23:23点)[INT], CRL_UPDATE_TIME(从CRL发布站点更新CRL的时间)[INT], STATUS(证书状态（1.有效，2.无效）)[INT], DOMAIN_CERT_TYPE(信任域作用类型：1应用证书信任域，2平台证书信任域)[INT], OCSP_CLIENT_CERT(OCSP客户端证书)[LONGTEXT], OCSP_AUTH_CODE(OCSP口令)[VARCHAR], OCSP_URL(OCSP地址)[VARCHAR], TENANT_ID(租户ID，平台添加的信任域租户id为空)[BIGINT]<PK,NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR], OCSP_CA_CERT(ocspCA证书)[LONGTEXT], OCSP_NODE_CA_CERT(ocsp节点证书)[LONGTEXT]", "type": "entity", "source": "json_file"}, {"table_name": "DOC_DOWNLOAD_PATH", "table_comment": "服务sdk api文档路径表", "field_count": 10, "content_length": 351, "content": "表名: DOC_DOWNLOAD_PATH(服务sdk api文档路径表)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, SERVICE_TYPE_ID(服务类型id)[BIGINT]<NOT NULL>, CONTEXT_PATH(服务类型路径(sdk和api路径中公共的地方))[VARCHAR(500)], SDK_PATH(sdk下载路径)[VARCHAR], API_PATH(api下载路径)[VARCHAR], REMARK(备注)[VARCHAR(100)], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "LICENSE_USE_TIME", "table_comment": "服务license时间", "field_count": 5, "content_length": 217, "content": "表名: LICENSE_USE_TIME(服务license时间)\n字段: ID(主键服务实例ID)[BIGINT]<PK,NOT NULL>, START_TIME(开始时间;开始时间)[VARCHAR]<NOT NULL>, END_TIME(结束时间;结束时间)[VARCHAR], LICENSE_NUM(使用license个数)[INT]<NOT NULL>, HMAC(完整性校验值)[VARCHAR]<NOT NULL>", "type": "entity", "source": "json_file"}, {"table_name": "ROUTE_SERVER_PORT", "table_comment": "路由TCP服务端口资源表（1030）", "field_count": 9, "content_length": 309, "content": "表名: ROUTE_SERVER_PORT(路由TCP服务端口资源表（1030）)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, SERVER_PORT(NGINX TCP服务端口)[INT]<NOT NULL>, APISIX_SERVER_PORT(APISIX TCP服务端口)[INT]<NOT NULL>, REGION_ID(区域ID), REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "ROUTE_TO_APP", "table_comment": "路由与应用关联表", "field_count": 9, "content_length": 265, "content": "表名: ROUTE_TO_APP(路由与应用关联表)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, ROUTE_ID(路由表主键)[BIGINT]<NOT NULL>, APP_ID(应用主键)[BIGINT], APP_CODE(应用标识)[VARCHAR], REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "ROUTE_TO_SERVICE", "table_comment": "路由与服务或设备关联表", "field_count": 9, "content_length": 284, "content": "表名: ROUTE_TO_SERVICE(路由与服务或设备关联表)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, ROUTE_ID(路由表主键)[BIGINT]<NOT NULL>, SERVICE_ID(服务主键/设备主键)[BIGINT], SERVICE_TYPE(1服务 2设备)[INT], REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_INTERFACE_API_RECORD", "table_comment": "服务调用错误日志", "field_count": 8, "content_length": 247, "content": "表名: SERVICE_INTERFACE_API_RECORD(服务调用错误日志)\n字段: ID(主键 uuid)[BIGINT]<PK,NOT NULL>, API_NAME(接口名称)[VARCHAR(20)], IP(请求ip)[VARCHAR(20)], PORT(请求端口)[INT], HEADERS(请求头)[TEXT], REQUEST_INFO(请求体)[TEXT], RESPONSE_INFO(响应体)[TEXT], CREATE_TIME(创建时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DEVICE_API_RECORD", "table_comment": "设备接口信息", "field_count": 20, "content_length": 686, "content": "表名: DEVICE_API_RECORD(设备接口信息)\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, DEVIICE_ID(所属设备ID)[BIGINT]<NOT NULL>, API_NAME(接口名称)[VARCHAR(100)]<NOT NULL>, REQUESET_ID(接口请求流水号)[VARCHAR(100)]<NOT NULL>, API_ID(设备接口ID)[VARCHAR], METHOD_NAME(方法名)[VARCHAR(100)]<NOT NULL>, INTERFACE_URL(接口地址)[VARCHAR(200)]<NOT NULL>, STATUS(状态)[VARCHAR(100)]<NOT NULL>, NOTIFY_STATUS(回调状态)[VARCHAR(50)], NOTIFY_INFO(回调结果)[VARCHAR(100)], OPER_STATUS(操作状态)[VARCHAR(50)], OPER_INFO(操作结果)[VARCHAR(100)], REQINFO(请求信息)[VARCHAR], RESINF<PERSON>(响应信息)[VARCHAR], INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DIC_STATISTIC", "table_comment": "统计指标字典表", "field_count": 9, "content_length": 429, "content": "表名: DIC_STATISTIC(统计指标字典表)\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, SERVICE_CODE(服务简称)[VARCHAR(50)]<NOT NULL>, STATISTIC_TYPE(统计类型 1增量 2非增量数量计算类 3非增量非计算类)[INT]<NOT NULL>, STATISTIC_SHOW_NAME(统计指标展示名称)[VARCHAR(100)]<NOT NULL>, STATISTIC_NAME(统计指标名称)[VARCHAR(100)]<NOT NULL>, IS_AVAILABLE(是否启用 1启用 0停用)[INT]<NOT NULL>, IS_INVOKE_MULTI_SERVICE(是否同服务类型全服务调用 1是 2否)[INT]<NOT NULL>, OID(OID)[VARCHAR]<NOT NULL>, UNIT(单位)[VARCHAR(50)]<NOT NULL>", "type": "entity", "source": "json_file"}, {"table_name": "DIC_KEY_STATUS", "table_comment": "密钥状态字典表", "field_count": 4, "content_length": 168, "content": "表名: DIC_KEY_STATUS(密钥状态字典表)\n字段: ID[BIGINT]<PK,NOT NULL>, D_NAME(名称)[VARCHAR(40)]<NOT NULL>, D_VALUE(值)[VARCHAR(50)]<NOT NULL>, IS_AVAILABLE(是否启用 1启用 0停用)[INT]<NOT NULL>", "type": "entity", "source": "json_file"}, {"table_name": "DIC_SEAL_STATUS", "table_comment": "签章状态字典表", "field_count": 4, "content_length": 169, "content": "表名: DIC_SEAL_STATUS(签章状态字典表)\n字段: ID[BIGINT]<PK,NOT NULL>, D_NAME(名称)[VARCHAR(40)]<NOT NULL>, D_VALUE(值)[VARCHAR(50)]<NOT NULL>, IS_AVAILABLE(是否启用 1启用 0停用)[INT]<NOT NULL>", "type": "entity", "source": "json_file"}, {"table_name": "LOG_OPERATE", "table_comment": "操作日志表（330）", "field_count": 16, "content_length": 535, "content": "表名: LOG_OPERATE(操作日志表（330）)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, TENANT_ID(租户ID)[BIGINT]<NOT NULL>, APP_ID(应用ID), RESULT(操作结果，0-成功，1-失败，2-获取操作结果失败)[VARCHAR(10)]<NOT NULL>, BUSI_TYPE_ID(业务类型ID)[BIGINT], CREATE_TIME(保存时间)[VARCHAR]<NOT NULL>, OPER_IP(请求IP)[VARCHAR(60)], OPER_NAME(操作人)[VARCHAR(60)], MODULE_NAME(模块名)[VARCHAR(60)], OPER_CONTENT(操作内容)[VARCHAR(200)], HMAC(完整性保护字段)[VARCHAR], AUDIT_STATUS(审计状态;0-审计，1-未审计)[INT], ERROR_MSG(错误信息)[VARCHAR], LOG_TYPE(日志类型，1-平台侧日志，2-服务侧日志)[INT], EXTEND1(备用1)[VARCHAR(100)], EXTEND2(备用2)[VARCHAR(100)]", "type": "entity", "source": "json_file"}, {"table_name": "MONITOR_CONNECTION", "table_comment": "监控连接信息表", "field_count": 13, "content_length": 453, "content": "表名: MONITOR_CONNECTION(监控连接信息表)\n字段: ID(id)[BIGINT]<PK,NOT NULL>, SOURCE_TYPE(来源 1 服务 2 设备)[INT]<NOT NULL>, DEVICE_TYPE(设备类型)[INT]<NOT NULL>, CONNECTION_NAME(连接名称)[VARCHAR(100)]<NOT NULL>, CONNECTION_STATUS(连接状态 1已连接 2未连接)[INT]<NOT NULL>, TENANT_CODE(租户标识)[VARCHAR(64)], IP(设备IP)[VARCHAR(20)]<NOT NULL>, PORT(设备端口)[INT]<NOT NULL>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "STATISTIC_INCRE_CAL", "table_comment": "增量类统计采集计算表（1030）", "field_count": 19, "content_length": 664, "content": "表名: STATISTIC_INCRE_CAL(增量类统计采集计算表（1030）)\n字段: ID(id)[BIGINT UNSIGNED]<PK,NOT NULL>, REGION_ID(区域ID), TENANT_CODE(租户标识)[VARCHAR(64)]<NOT NULL>, APP_CODE(应用标识)[VARCHAR], SERVICE_CODE(服务简称)[VARCHAR(50)]<NOT NULL>, SERVICE_GROUP_ID(服务组ID)<NOT NULL>, SERVICE_ID(服务id)[BIGINT], STATISTIC_NAME(统计指标名称)[VARCHAR(100)]<NOT NULL>, TOTAL_STATISTIC(总量统计)[BIGINT]<NOT NULL>, SUCCESS_STATISTIC(成功量统计)[BIGINT], ERROR_STATISTIC(失败量统计)[BIGINT], STATISTIC_YEAR(年)[INT]<NOT NULL>, STATISTIC_MONTH(月)[INT]<NOT NULL>, STATISTIC_DAY(日)[INT]<NOT NULL>, STATISTIC_HOUR(时)[INT]<NOT NULL>, STATISTIC_MINUTE(分)[INT], STATISTIC_SECOND(秒)[INT], HMAC(完整性校验值)[VARCHAR], CREATE_TIME(创建时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "STATISTIC_INCRE_RECORD", "table_comment": "增量类统计采集记录表（1030）", "field_count": 20, "content_length": 677, "content": "表名: STATISTIC_INCRE_RECORD(增量类统计采集记录表（1030）)\n字段: ID(id)[BIGINT]<PK,NOT NULL>, REGION_ID(区域ID), TENANT_CODE(租户标识)[VARCHAR(64)]<NOT NULL>, APP_CODE(应用标识)[VARCHAR], SERVICE_CODE(服务简称)[VARCHAR(50)]<NOT NULL>, SERVICE_GROUP_ID(服务组ID)<NOT NULL>, SERVICE_ID(服务id)[BIGINT], STATISTIC_NAME(统计指标名称)[VARCHAR(100)]<NOT NULL>, TOTAL_STATISTIC(总量统计)[BIGINT]<NOT NULL>, SUCCESS_STATISTIC(成功量统计)[BIGINT], ERROR_STATISTIC(失败量统计)[BIGINT], STATISTIC_YEAR(年)[INT]<NOT NULL>, STATISTIC_MONTH(月)[INT]<NOT NULL>, STATISTIC_DAY(日)[INT]<NOT NULL>, STATISTIC_HOUR(时)[INT]<NOT NULL>, STATISTIC_MINUTE(分)[INT], STATISTIC_SECOND(秒)[INT], TIME_STAMP(插入时间戳), HMAC(完整性校验值)[VARCHAR], CREATE_TIME(创建时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "STATISTIC_INCRE_TOTAL_CAL", "table_comment": "增量类统计采集总量计算表（1030）", "field_count": 18, "content_length": 637, "content": "表名: STATISTIC_INCRE_TOTAL_CAL(增量类统计采集总量计算表（1030）)\n字段: ID(id)[BIGINT]<PK,NOT NULL>, REGION_ID(区域ID), TENANT_CODE(租户标识)[VARCHAR(64)]<NOT NULL>, APP_CODE(应用标识)[VARCHAR], SERVICE_GROUP_ID(服务组ID)<NOT NULL>, SERVICE_CODE(服务简称)[VARCHAR(50)]<NOT NULL>, STATISTIC_NAME(统计指标名称)[VARCHAR(100)]<NOT NULL>, TOTAL_STATISTIC(总量统计)[BIGINT]<NOT NULL>, SUCCESS_STATISTIC(成功量统计)[BIGINT], ERROR_STATISTIC(失败量统计)[BIGINT], STATISTIC_YEAR(年)[INT]<NOT NULL>, STATISTIC_MONTH(月)[INT]<NOT NULL>, STATISTIC_DAY(日)[INT]<NOT NULL>, STATISTIC_HOUR(时)[INT]<NOT NULL>, STATISTIC_MINUTE(分)[INT], STATISTIC_SECOND(秒)[INT], HMAC(完整性校验值)[VARCHAR], CREATE_TIME(创建时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "STATISTIC_UNINCRE_CAL", "table_comment": "非增量类统计采集计算表（1030）", "field_count": 18, "content_length": 614, "content": "表名: STATISTIC_UNINCRE_CAL(非增量类统计采集计算表（1030）)\n字段: ID(id)[BIGINT]<PK,NOT NULL>, REGION_ID(区域ID), TENANT_CODE(租户标识)[VARCHAR(64)]<NOT NULL>, APP_CODE(应用标识)[VARCHAR], SERVICE_TYPE_ID(服务类型)[BIGINT]<NOT NULL>, SERVICE_GROUP_ID(服务组ID)<NOT NULL>, SERVICE_ID(服务id)[BIGINT], STATISTIC_NAME(统计指标名称)[VARCHAR(100)]<NOT NULL>, SUB_STATISTIC_NAME(子统计指标名称)[VARCHAR(100)]<NOT NULL>, SUB_STATISTIC_VALUE(子统计指标值)[BIGINT]<NOT NULL>, STATISTIC_YEAR(年)[INT], STATISTIC_MONTH(月)[INT], STATISTIC_DAY(日)[INT], STATISTIC_HOUR(时)[INT], STATISTIC_MINUTE(分)[INT], STATISTIC_SECOND(秒)[INT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "LICENSE_APPLY", "table_comment": "许可证申请表", "field_count": 13, "content_length": 478, "content": "表名: LICENSE_APPLY(许可证申请表)\n字段: ID(主键;许可证标识)[BIGINT]<PK,NOT NULL>, KEY_ID(密钥ID)[VARCHAR(100)]<NOT NULL>, PRI_KEY(私钥;平台临时私钥)[TEXT]<NOT NULL>, PERIOD_TYPE(周期类型;1-永久授权, 2-按年授权)[INT]<NOT NULL>, PERIOD_NUM(服务周期)[INT]<NOT NULL>, SERVER_TYPE(许可证类型;1-业务服务，2-平台服务)[INT]<NOT NULL>, SERVER_NUM(服务数量)[INT]<NOT NULL>, STATUS(状态;1-申请中 2-已使用 3-作废)[INT]<NOT NULL>, LICENSE(许可证内容;加密数据)[TEXT]<NOT NULL>, HMAC(完整性校验值)[VARCHAR], REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "LICENSE_INFO", "table_comment": "许可证表", "field_count": 11, "content_length": 385, "content": "表名: LICENSE_INFO(许可证表)\n字段: ID(主键;许可证标识)[BIGINT]<PK,NOT NULL>, SERVER_TYPE(服务类型;1-业务服务，2-平台服务)[INT]<NOT NULL>, SERVER_NUM(服务数量)[INT]<NOT NULL>, PERIOD_TYPE(周期类型;1-永久授权, 2-按年授权)[INT]<NOT NULL>, PERIOD_NUM(服务周期)[INT]<NOT NULL>, LICENSE(许可证内容)[TEXT]<NOT NULL>, CONTENT(内容;解析的LICENSE原始数据)[TEXT], HMAC(完整性校验值)[VARCHAR], REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "LICENSE_USE", "table_comment": "许可证使用表", "field_count": 15, "content_length": 554, "content": "表名: LICENSE_USE(许可证使用表)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, LICENSE_ID(许可证ID)[BIGINT]<NOT NULL>, NAME(许可证名称;L+yyyyMMddHHmmss+num)[VARCHAR(100)]<NOT NULL>, SERVER_TYPE(服务类型;1-业务服务，2-平台服务)[INT]<NOT NULL>, PERIOD_TYPE(周期类型;1-永久授权, 2-按年授权)[INT]<NOT NULL>, PERIOD_NUM(服务周期)[INT]<NOT NULL>, START_TIME(开始时间;开始时间)[VARCHAR], END_TIME(结束时间;结束时间)[VARCHAR], STATUS(状态;1-未使用，2-已使用，3-续约)[INT]<NOT NULL>, HMAC(完整性校验值)[VARCHAR]<NOT NULL>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR]<NOT NULL>, UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "LICENSE_USE_REL", "table_comment": "许可证服务使用关联表", "field_count": 5, "content_length": 227, "content": "表名: LICENSE_USE_REL(许可证服务使用关联表)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, LICENSE_ID(许可证使用ID;已使用状态的LICENSE的ID)[BIGINT]<NOT NULL>, SERVER_ID(服务实例ID)[BIGINT]<NOT NULL>, HMAC(完整性校验值)[VARCHAR]<NOT NULL>, CREATE_TIME(创建时间)[VARCHAR]<NOT NULL>", "type": "entity", "source": "json_file"}, {"table_name": "TENANT_KEY", "table_comment": "租户密钥关系表", "field_count": 6, "content_length": 238, "content": "表名: TENANT_KEY(租户密钥关系表)\n字段: ID(主键)[BIGINT]<PK,NOT NULL>, TENANT_ID(租户ID)[BIGINT]<NOT NULL>, KEY_TYPE(密钥类型：1-2号非对称公钥，2-2号非对称私钥，3-2号对称密钥分量)[INT]<NOT NULL>, CONTENT(密钥内容)[VARCHAR]<NOT NULL>, CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "SYS_JOB", "table_comment": "定时任务表", "field_count": 15, "content_length": 621, "content": "表名: SYS_JOB(定时任务表)\n字段: JOB_ID(任务号)[BIGINT]<PK,NOT NULL>, JOB_NAME(任务名称)[VARCHAR(100)]<NOT NULL>, JOB_GROUP(任务组名)[VARCHAR(100)]<NOT NULL>, SERVER_ID(服务模块)[VARCHAR(100)]<NOT NULL>, METHOD_URL(调用接口)[VARCHAR(500)], JSON_PARAM(json格式参数)[TEXT]<NOT NULL>, CRON_EXPRESSION(CRON执行表达式)[VARCHAR(100)], MISFIRE_POLICY(计划执行错误策略;计划执行错误策略（1立即执行 2执行一次 3放弃执行）)[VARCHAR(20)]<DEFAULT:'3'>, CONCURRENT(是否并发执行（0允许 1禁止）;是否并发执行（0允许 1禁止）)[VARCHAR(1)]<DEFAULT:'1'>, JOB_STATUS(状态（0正常 1暂停）)[VARCHAR(1)]<DEFAULT:'0'>, CREATED_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATED_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR], REMARK(备注)[VARCHAR(500)]", "type": "entity", "source": "json_file"}, {"table_name": "SYS_JOB_LOG", "table_comment": "定时任务执行日志表", "field_count": 7, "content_length": 278, "content": "表名: SYS_JOB_LOG(定时任务执行日志表)\n字段: JOB_LOG_ID(任务日志ID)[BIGINT]<PK,NOT NULL>, JOB_ID(任务ID)[BIGINT]<NOT NULL>, JOB_MESSAGE(日志信息)[VARCHAR], STATUS(执行状态（0正常 1失败）)[VARCHAR(1)]<NOT NULL>, EXCEPTION_INFO(异常信息)[VARCHAR(2000)], CREATE_TIME(创建时间)[VARCHAR]<NOT NULL>, TRIGGER_TIME(触发时间)[BIGINT]", "type": "entity", "source": "json_file"}, {"table_name": "SYS_TASK", "table_comment": "异步任务表", "field_count": 15, "content_length": 596, "content": "表名: SYS_TASK(异步任务表)\n字段: TASK_ID(任务号)[BIGINT]<PK,NOT NULL>, TASK_NAME(任务名称)[VARCHAR(64)]<NOT NULL>, TASK_GROUP(任务组名;执行任务串行)[VARCHAR(64)]<NOT NULL>, SERVER_ID(服务模块)[VARCHAR]<NOT NULL>, METHOD_URL(调用接口)[VARCHAR(500)], JSON_PARAM(json格式参数)[TEXT]<NOT NULL>, TASK_STATUS(状态(0-未执行 1-执行中 2-成功 3-异常 4-超时))[INT]<NOT NULL,DEFAULT:0>, CREATE_TIME(创建时间)[VARCHAR], UPDATE_TIME(更新时间)[VARCHAR], REMARK(备注)[VARCHAR], TIMEOUT(超时时间;单位秒)[INT], START_TIME(开始时间)[VARCHAR], END_TIME(结束时间)[VARCHAR], REPEAT(是否允许重复执行;0-不允许，1允许)[VARCHAR(1)]<NOT NULL,DEFAULT:0>, POLICY(是否允许重复执行;0-不允许，1允许)[VARCHAR(1)]<NOT NULL,DEFAULT:'0'>", "type": "entity", "source": "json_file"}, {"table_name": "SYS_TASK_LOG", "table_comment": "异步任务执行日志表", "field_count": 7, "content_length": 284, "content": "表名: SYS_TASK_LOG(异步任务执行日志表)\n字段: TASK_LOG_ID(任务日志ID)[BIGINT]<PK,NOT NULL>, TASK_ID(任务ID)[BIGINT]<NOT NULL>, TASK_MESSAGE(日志信息)[VARCHAR], STATUS(执行状态（0正常 1失败）)[VARCHAR(1)]<NOT NULL>, EXCEPTION_INFO(异常信息)[VARCHAR(2000)], CREATE_TIME(创建时间;单位毫秒)[VARCHAR], TRIGGER_TIME(触发时间;任务服务上送)[BIGINT]", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_INFO", "table_comment": "服务信息表（V3.3.3、V3.3.4）", "field_count": 38, "content_length": 1319, "content": "表名: SERVICE_INFO(服务信息表（V3.3.3、V3.3.4）)\n字段: ID(id)[BIGINT]<PK,NOT NULL>, SERVICE_TYPE_ID(服务类型)[BIGINT]<NOT NULL>, SERVICE_NAME(服务名称)[VARCHAR(50)]<NOT NULL>, DEVICE_GROUP_ID(设备组id)[BIGINT], SERVICE_GROUP_ID(服务组id)[BIGINT], REGION_ID(区域ID), TENANT_ID(租户id)[BIGINT]<NOT NULL>, OPER_STATUS(1运行中（操作完成）、 2添加中、3添加失败、4启动中、5停止中、6删除中)[INT], RUN_STATUS(1功能运行正常 2.功能运行异常 3启动中 4停止中)[INT UNSIGNED]<NOT NULL,DEFAULT:2>, MGT_IP(管理IP)[VARCHAR(20)], MGT_PORT(管理端口)[INT], MGT_GATEWAY_IP(管理网关IP)[VARCHAR(20)], MGT_GATEWAY_PORT(管理网关端口)[INT], BUSI_IP(业务IP)[VARCHAR(20)], BUSI_PORT(业务端口)[INT], BUSI_GATEWAY_IP(业务网关IP)[VARCHAR(20)], BUSI_GATEWAY_PORT(业务网关端口)[INT], REMOTE_IP(管控IP)[VARCHAR(20)], REMOTE_PORT(管控端口), GATEWAY_ID(API网关表主键)[BIGINT], TCP_PORT(TCP/UDP端口), DEPLOY_MOD(部署模式 1宿主进程 2Docker容器 默认1)<NOT NULL,DEFAULT:1>, IMAGE_ID(Docker镜像ID), CONTAINER_NAME(Docker容器名称 ccsp-服务类型标识-服务ID), ROUTE_ID(路由表主键)[BIGINT], IS_ACTIVE_STANDBY(是否为主机或者备机  1主机 2备机)[INT]<NOT NULL,DEFAULT:1>, DB_CREATED(数据库是否已创建 1是 0否)[INT], INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, HMAC(完整性校验;*******新增), SERVICE_SPECIFICATION_ID(服务规格ID), IMAGE_OLD_ID(原镜像ID（更新镜像后保留）), IMAGE_UPDATE_COUNT(镜像更新次数（更新、回退都累加）)[INT(255)], REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR], EXPAND_PORT(扩展端口)[INT]", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_TYPE", "table_comment": "服务类型字典表", "field_count": 37, "content_length": 1390, "content": "表名: SERVICE_TYPE(服务类型字典表)\n字段: ID(服务类型主键)[BIGINT]<PK,NOT NULL,DEFAULT:2>, SERVICE_TYPE_NAME(服务类型名称)[VARCHAR(50)]<NOT NULL>, SERVICE_CODE(服务简称)[VARCHAR(50)]<NOT NULL>, PARENT_ID(服务信息父类型)[BIGINT]<NOT NULL>, SERVICE_CREATE_TYPE(服务创建类型 1手动创建 2自动创建)[INT], SERVICE_USE_TYPE(1独享 2共享)[INT], MGT_PORT(内网管理端口)[INT], MGT_GATEWAY_PORT(管理网关端口)[INT], BUSI_PORT(业务端口)[INT], BUSI_GATEWAY_PORT(业务网关端口)[INT], REMOTE_PORT(管控端口), IMAGE_ID(镜像ID)[BIGINT], SERVICE_PATH(服务前缀)[VARCHAR(100)], DB_UPDATE_CONFIG_FLAG(平台是否需要更新服务数据库配置 1是 0否)[INT], DB_COMMON(服务是否共用数据库 1是 0否)[INT], DB_TENANT_COMMON(服务是否同租户下共用数据库 1是 0否)[INT], DB_NAME(数据库实例名称)[VARCHAR(50)], SERVICE_CLASS(服务分类 1基础服务 2增值服务 3.组件服务)[INT], REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR], EXPAND_PORT(扩展端口)[INT], TCP_PORT(TCP/UDP端口)[INT], MONITOR_PORT(监控组件端口)[INT], IS_RESTART(是否允许启动停止 1允许 2不允许)[INT]<DEFAULT:1>, IS_CREATE_MGT_ROUTE(是否创建管理路由 1创建 2不创建)[INT]<DEFAULT:1>, IS_CREATE_BUSI_ROUTE(是否创建业务路由 1创建 2不创建)[INT]<DEFAULT:1>, IS_CREATE_LOG_PLUGIN(是否创建日志插件 1创建 2不创建)[INT]<DEFAULT:1>, LOG_PLUGIN_FORMAT(日志插件日志格式)[VARCHAR(500)], IS_MONITOR_DEVICE(是否监控服务设备 1是 2否)[INT]<DEFAULT:2>, CONFIG_DEVICE_TYPE(分配设备类型 1不分配 2自动分配 3按设备类型下拉选择)[INT]<DEFAULT:2>, AVAILABLE_DEVICE_TYPE(服务类型可用设备类型 空代表全部可用 不全部可用逗号分隔)[VARCHAR(200)], CONNECT_TIME(连接超时时间)[INT], SEND_TIME(发送超时时间)[INT], READ_TIME(接收超时时间)[INT]", "type": "entity", "source": "json_file"}, {"table_name": "VERSION", "table_comment": "版本表", "field_count": 10, "content_length": 280, "content": "表名: VERSION(版本表)\n字段: ID(主键)<PK,NOT NULL>, PRODUCT_CODE(产品简称)[VARCHAR(50)]<PK,NOT NULL>, PRODUCT_NAME(产品名称)[VARCHAR(50)]<PK,NOT NULL>, VERSION(版本)[VARCHAR(50)]<PK,NOT NULL>, SORD_NUM(排序)<PK,NOT NULL>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DIC_DATABASE_TYPE", "table_comment": "数据库类型字典表", "field_count": 8, "content_length": 213, "content": "表名: DIC_DATABASE_TYPE(数据库类型字典表)\n字段: ID(数据库类型主键)<PK,NOT NULL>, DATABASE_TYPE_CODE(数据库类型名称)[VARCHAR(100)], VERISON(版本号)[VARCHAR(255)], REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DATABASE_INFO", "table_comment": "数据库信息表（1030）", "field_count": 17, "content_length": 512, "content": "表名: DATABASE_INFO(数据库信息表（1030）)\n字段: ID(数据库id)<PK,NOT NULL>, DATABASE_TYPE_ID(数据库类型id)<NOT NULL>, DATABASE_NAME(数据库名称)[VARCHAR(100)]<NOT NULL>, REGION_ID(区域ID), DATABASE_IP(数据库IP)[VARCHAR(60)], DATABASE_PORT(数据库端口), DATABASE_MAP_IP(数据库映射IP)[VARCHAR(60)], DATABASE_MAP_PORT(数据库映射端口), CASE_NAME(数据库实例名称)[VARCHAR(100)], ADMIN_USER(数据库管理员用户)[VARCHAR(100)], ADMIN_AUTH_CODE(数据库密码)[VARCHAR(200)], AUTO_CREATED(数据库是否自动创建 1是 0否)<DEFAULT:0>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DATABASE_MINIMUM_UNIT", "table_comment": "数据库最小单元", "field_count": 11, "content_length": 311, "content": "表名: DATABASE_MINIMUM_UNIT(数据库最小单元)\n字段: ID(id)<PK,NOT NULL>, DATABASE_UNIT_NAME(单元名称)[VARCHAR(100)]<NOT NULL>, SERVICE_TYPE_ID(服务类型id)<NOT NULL>, DATABASE_ID(数据库id)<NOT NULL>, USER(用户名)[VARCHAR(100)], AUTH_CODE(用户密码)[VARCHAR(100)], REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DATABASE_UNIT_TO_SERVICE_GROUP", "table_comment": "数据库单元和服务组的关联表", "field_count": 9, "content_length": 247, "content": "表名: DATABASE_UNIT_TO_SERVICE_GROUP(数据库单元和服务组的关联表)\n字段: ID(ID)<PK,NOT NULL>, DATABASE_UNIT_ID(数据库单元ID)<NOT NULL>, DATABASE_ID(数据库ID), SERVICE_GROUP_ID(服务组ID)<NOT NULL>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DIC_BUSI_TYPE_TO_DATABASE", "table_comment": "业务服务对应数据库名称", "field_count": 3, "content_length": 132, "content": "表名: DIC_BUSI_TYPE_TO_DATABASE(业务服务对应数据库名称)\n字段: ID(主键)<PK,NOT NULL>, BUSI_TYPE_ID(业务类型ID)<NOT NULL>, DATABASE_NAME(数据库默认名称)<NOT NULL>", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_VERSION", "table_comment": "业务服务版本信息", "field_count": 11, "content_length": 289, "content": "表名: SERVICE_VERSION(业务服务版本信息)\n字段: ID(主键)<PK,NOT NULL>, SERVICE_TYPE_ID(服务类型ID)<NOT NULL>, SERVICE_GROUP_ID(服务组ID), SERVICE_NAME(产品名称)<NOT NULL>, SERVICE_MODEL(产品型号)<NOT NULL>, SERVICE_VERSION(产品版本)<NOT NULL>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "CONFIG_REGULAR", "table_comment": "配置表正则校验", "field_count": 9, "content_length": 209, "content": "表名: CONFIG_REGULAR(配置表正则校验)\n字段: ID(ID)<PK,NOT NULL>, CONFIG_CODE(配置编码), REGULAR(正则表达式), INVALID_FLAG(是否作废;默认为0)[INT]<DEFAULT:0>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "TENANT_TO_DATABASE", "table_comment": "租户和数据库服务关联表", "field_count": 7, "content_length": 178, "content": "表名: TENANT_TO_DATABASE(租户和数据库服务关联表)\n字段: ID(主键)<PK,NOT NULL>, TENANT_ID(租户ID)<NOT NULL>, DATABASE_ID(数据库服务ID), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "REGION", "table_comment": "区域表(1030 nmg)", "field_count": 10, "content_length": 264, "content": "表名: REGION(区域表(1030 nmg))\n字段: REGION_ID(区域ID)<PK,NOT NULL>, REGION_CODE(区域标识)<NOT NULL>, REGION_NAME(区域名称)<NOT NULL>, REGION_TYPE(区域类型;1：普通区域；2：平台区域), SUB_PlATFORM_ID(所属平台ID;NMG add), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "SIM_KEY_USER", "table_comment": "SIMKEY用户信息表", "field_count": 14, "content_length": 402, "content": "表名: SIM_KEY_USER(SIMKEY用户信息表)\n字段: ID(id)<PK,NOT NULL>, PHONE(手机号)[VARCHAR(40)], CERT_ID(证书序列号)[VARCHAR(40)], SIM_FLAG(是否开通;0 未申请 1已申请), USER_STATUS(用户状态;1启用 2禁用), COMM_NAME(预留字段)[VARCHAR(100)], PHONE_UUID(手机序列号)[VARCHAR(100)], EMAIL(邮件)[VARCHAR(100)], USER_ID(账户ID)[BIGINT], TENANT_ID(租户ID)[BIGINT], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "SIM_KEY_CERT", "table_comment": "SIMKEY证书信息表", "field_count": 20, "content_length": 500, "content": "表名: SIM_KEY_CERT(SIMKEY证书信息表)\n字段: ID(id)<PK,NOT NULL>, PHONE(手机号)[VARCHAR(40)], ALIAS(证书别名)[VARCHAR(128)], CERT_TYPE(证书类型;备用  RSA2048/SM2等), SIGN_ALGORITHM(签名算法), START_TIME(证书开始时间), EXPIRE_TIME(过期时间), STATUS(用户状态;1启用 2禁用), SIMKEY_USER_ID(SIMKEY用户ID)[BIGINT], PUBLIC_KEY(签名证书公钥), SIGN_CERT(签名证书), TRAN_CERT(加密证书), ENC_ENVELOP(加密私钥数字信封), SING_SN(签名证书序列号), TRAN_SN(加密证书序列号), TENANT_ID(租户ID)[BIGINT], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "SIM_FLOW_DATA", "table_comment": "SIM操作日志", "field_count": 16, "content_length": 406, "content": "表名: SIM_FLOW_DATA(SIM操作日志)\n字段: ID(id)<PK,NOT NULL>, FLOW_TYPE(日志类型;1sim盾  2simkey), PHONE(手机号)[VARCHAR(50)], OPER_TYPE(操作类型(验签、证书等))[VARCHAR(100)], FUNC_NAME(接口名称)[VARCHAR(200)], FUNC_TYPE(1密码平台接口  2SIM盾接口 3业务系统接口)[VARCHAR(10)], REQUEST_DATA(请求参数), RESPONSE_DATA(返回结果), STATUS(操作结果 0成功 其他失败), REMARK(备注), TENANT_ID(租户id), LOG_HMAC(hmac), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "SIM_SHIELD_CERT", "table_comment": "SIM盾证书信息表", "field_count": 16, "content_length": 408, "content": "表名: SIM_SHIELD_CERT(SIM盾证书信息表)\n字段: ID(id)<PK,NOT NULL>, PHONE(手机号)[VARCHAR(40)], ALIAS(证书别名)[VARCHAR(128)], CERT_TYPE(证书类型;备用  RSA2048/SM2等), SIGN_ALGORITHM(签名算法), START_TIME(证书开始时间), EXPIRE_TIME(过期时间), STATUS(用户状态;1启用 2禁用), USER_ID(账户ID)[BIGINT], CERTIFICATE(证书内容), SERIAL_NUM(证书序列号), PUBLIC_KEY(签名证书公钥), CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "SIM_WHITE_LIST", "table_comment": "SIM白名单", "field_count": 13, "content_length": 311, "content": "表名: SIM_WHITE_LIST(SIM白名单)\n字段: ID(id)<PK,NOT NULL>, GENDER(性别 1：男 2：女), USER_NAME(姓名)[VARCHAR(60)], POSITION(职务)[VARCHAR(20)], ORGANIZATION(组织)[VARCHAR(100)], DEPARTMENT(部门)[VARCHAR(100)], PHONE(手机号)[VARCHAR(20)], REMARK(备注), TENANT_ID(租户id), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DEVICE_TYPE", "table_comment": "设备类型（V3.3.3）", "field_count": 33, "content_length": 1593, "content": "表名: DEVICE_TYPE(设备类型（V3.3.3）)\n字段: DEVICE_TYPE_ID(设备类型ID)[BIGINT]<PK,NOT NULL>, DEVICE_TYPE_NAME(设备类型名称)[VARCHAR(50)]<NOT NULL>, DEFAULT_FLAG(是否默认 1默认 0不默认;默认0)[INT]<NOT NULL,DEFAULT:0>, VENDOR_ID(所属厂商ID)[BIGINT]<NOT NULL>, FAMILY_TYPE(设备类型(1：云密码机:2：物理机:3：虚拟机))[INT]<NOT NULL>, MACHINE_TYPE_ID(密码机服务类型ID)[BIGINT], PARENT_ID(上级设备类型ID（虚拟机独有）;默认0)[BIGINT]<DEFAULT:0>, HCCS_IMAGE(超融合虚拟机镜像类型（虚拟机独有）)[VARCHAR(40)], INTERACTION_SERIAL_NUMBER(管理规范（交互类型编号）)[VARCHAR(100)], MGT_METHOD(管理管理接口协议（1：HTTPS；2：HTTP）)[INT], MGT_PORT(管理端口)[INT]<NOT NULL>, BUSI_PORT(服务端口)[INT], SUPPORT_MAIN_KEY_FLAG(是否支持生成主密钥0不支持 1支持)[INT]<NOT NULL,DEFAULT:0>, SUPPORT_SEC_MANAGE_FLAG(是否支持安全管理，0不支持 1支持)[INT]<NOT NULL,DEFAULT:0>, SUPPORT_GEN_KEY_FLAG(是否支持生成密钥)[INT]<NOT NULL,DEFAULT:0>, SUPPORT_SNMP_FLAG(是否支持SNMP监控0不支持 1支持)[INT]<NOT NULL,DEFAULT:0>, TOKEN_CALL_BACK_FLAG(是否支持token回调)[INT]<DEFAULT:0>, NEED_PASSWORD_FLAG(是否支持连接密码或token)[INT]<NOT NULL,DEFAULT:0>, READ_INFO_FLAG(读取设备信息 1需要 0不需要)[INT]<NOT NULL,DEFAULT:0>, MGT_PUBLICKEY_FLAG(是否支持管理接口公钥验签（0：否；1：是）)[INT]<NOT NULL,DEFAULT:0>, KEY_TEMPLET_IDS(该类型设备支持的密钥模板id，多个以逗号分隔)[TEXT], EXTERNAL_DATABASE(外置数据库 0-不需要 1-需要), DEFAULT_KEY_TEMPLET_IDS(默认自动生成密钥时支持的密钥模板id，多个以逗号分隔)[VARCHAR(500)], INVALID_FLAG(无效标识 0可用 1无效)[INT]<NOT NULL,DEFAULT:0>, CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR], REMARK(备注)[VARCHAR], HCCS_IMAGE_ID(超融合虚拟机镜像类型（虚拟机独有）)[BIGINT], SUPPORT_EXTERNAL_SERVICE(是否支持对外服务配置，0不支持 1支持)<NOT NULL,DEFAULT:0>, SUPPORT_MANAGE_PT(是否支持跳转设备管理界面，0不支持 1支持)<NOT NULL,DEFAULT:0>, CONNECT_AUTH_CODE(默认连接密码)[VARCHAR(200)]", "type": "entity", "source": "json_file"}, {"table_name": "DEVICE_VSM_NET_CONFIG", "table_comment": "虚拟机网络配置（1030）", "field_count": 15, "content_length": 504, "content": "表名: DEVICE_VSM_NET_CONFIG(虚拟机网络配置（1030）)\n字段: ID(网络配置ID)[BIGINT]<PK,NOT NULL>, NET_NAME(网络名称)[VARCHAR(60)], NET_CARD_NAME(网卡名称)[VARCHAR(60)], HOST_SERIALNUM(关联宿主机序列号)[VARCHAR(80)], HOST_ID(关联宿主机ID)[BIGINT], SUBDOMAIN(网域)[VARCHAR(80)], GATEWAY(网关)[VARCHAR(80)]<NOT NULL>, HCCS_ID(超融合网络ID)[BIGINT], MASK(子网掩码)[VARCHAR(80)]<NOT NULL>, INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DIC_DEVICE_IMAGE_TYPE", "table_comment": "虚拟机镜像类型字典表（1030）", "field_count": 11, "content_length": 391, "content": "表名: DIC_DEVICE_IMAGE_TYPE(虚拟机镜像类型字典表（1030）)\n字段: ID(镜像类型ID)[BIGINT]<PK,NOT NULL>, IMAGE_NAME(镜像名称)[VARCHAR(100)]<NOT NULL>, IMAGE_VALUE(镜像值)[VARCHAR(100)]<NOT NULL>, IMAGE_VERSION(镜像版本号)[VARCHAR(100)], INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, VENDOR_ID(厂商ID), REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "DEVICE_MASTER_COVER_RECORD1", "table_comment": "密钥恢复记录表（1030）", "field_count": 11, "content_length": 306, "content": "表名: DEVICE_MASTER_COVER_RECORD1(密钥恢复记录表（1030）)\n字段: ID(主键)<PK,NOT NULL>, DEVICE_ID(设备ID)<NOT NULL>, TENANT_ID(租户ID)<NOT NULL>, COVER_STATUS(恢复状态0-恢复成功，1-恢复中，2-恢复失败), COVER_TYPE(恢复记录类型 1-主密钥，2-内部密钥)<NOT NULL>, COVER_TIME(恢复时间), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DEVICE_TYPE_RELA_VALUE", "table_comment": "设备类型关联值表（1030）", "field_count": 10, "content_length": 335, "content": "表名: DEVICE_TYPE_RELA_VALUE(设备类型关联值表（1030）)\n字段: ID(主键)<PK,NOT NULL>, DEVICE_TYPE_ID(设备类型id)<NOT NULL>, VALUE(关联值)[VARCHAR(100)]<NOT NULL>, VALUE_TYPE(关联值类型;1：云宿主机关联的虚机镜像id，2：虚拟机关联的资源分配类型code)<NOT NULL>, DEFAULT_FLAG(是否默认 1默认 0不默认；默认0)<NOT NULL,DEFAULT:0>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DIC_DEVICE_VSM_RESOURCE", "table_comment": "虚拟机资源配置字典表（1030）", "field_count": 9, "content_length": 291, "content": "表名: DIC_DEVICE_VSM_RESOURCE(虚拟机资源配置字典表（1030）)\n字段: ID(主键)<PK,NOT NULL>, RESOURCE_VALUE(虚拟机资源配置值)[VARCHAR(50)], RESOURCE_NAME(虚拟机资源配置名称)[VARCHAR(90)]<NOT NULL>, INVALID_FLAG(是否有效，0有效，1无效，默认0)<NOT NULL,DEFAULT:0>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DEVICE_TYPE_MANAGE_PATH", "table_comment": "设备类型与设备管理平台路径参数关联表（1030）", "field_count": 10, "content_length": 293, "content": "表名: DEVICE_TYPE_MANAGE_PATH(设备类型与设备管理平台路径参数关联表（1030）)\n字段: ID(主键)<PK,NOT NULL>, DEVICE_TYPE_ID(设备类型id)<NOT NULL>, DEVICE_PT_METHOD(管理管理接口协议（1：HTTPS；2：HTTP）)<NOT NULL>, DEVICE_PT_PORT(管理端口), DEVICE_PT_PATH(管理平台路径), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "disaster_recover_db", "table_comment": "数据灾备任务主表（1030）", "field_count": 23, "content_length": 840, "content": "表名: disaster_recover_db(数据灾备任务主表（1030）)\n字段: ID(主键)<PK,NOT NULL>, DISASTER_NAME(灾备任务名称), TENANT_ID(租户id)<NOT NULL>, SERVICE_TYPE_ID(服务类型id)<NOT NULL>, SERVICE_GROUP_ID(服务组id), SOURCE_DB_TYPE(源数据库类型)[VARCHAR(50)]<NOT NULL>, SOURCE_DB_UNIT_ID(源数据库实例/模式id)<NOT NULL>, TARGET_DB_TYPE(目标数据库类型)[VARCHAR(50)]<NOT NULL>, TARGET_DB_IP(目标数据库IP)[VARCHAR(50)]<NOT NULL>, TARGET_DB_PORT(目标数据库端口)<NOT NULL>, TARGET_DB_USER(目标数据库用户)[VARCHAR(200)]<NOT NULL>, TARGET_DB_AUTH_CODE(目标数据库密码)[VARCHAR(200)]<NOT NULL>, TARGET_DB_CASE_NAME(目标数据库实例名称)[VARCHAR(200)], TARGET_DB_UNIT_NAME(目标数据库实例/模式名称)[VARCHAR(200)]<NOT NULL>, TARGET_DB_MAP_IP(目标数据库映射IP)[VARCHAR(50)], TARGET_DB_MAP_PORT(目标数据库映射端口), STATUS(任务状态;默认开启，0-未运行；1-运行中)<NOT NULL>, INVALID_FLAG(是否作废;默认为0)<NOT NULL,DEFAULT:0>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "disaster_recover_db_sub", "table_comment": "数据灾备子任务表（1030）", "field_count": 19, "content_length": 711, "content": "表名: disaster_recover_db_sub(数据灾备子任务表（1030）)\n字段: ID(主键)<PK,NOT NULL>, DISASTER_RECOVER_DB_ID(数据灾备任务主表id)<NOT NULL>, STATUS(任务状态;默认开启，0-未运行；1-运行中), DATA_SYNC_TYPE(全量1、增量2)<NOT NULL>, CRON(任务周期), FAIL_RETRY_COUNT(失败次数), SOURCE_DB_TABLE_NAME(源数据库表名称)[VARCHAR(100)]<NOT NULL>, TARGET_DB_TABLE_NAME(目标数据库表名称)[VARCHAR(100)]<NOT NULL>, WHERE_SQL(源数据查询条件)[VARCHAR(1000)], PRE_SQL(目标数据前置执行sql)[VARCHAR(1000)], SOURCE_DB_COLUMN(源数据表字段 默认全部字段)[VARCHAR(1000)], TARGET_DB_COLUMN(目标数据表字段 默认全部字段)[VARCHAR(1000)], JVM_PARAM(jvm参数  -Xms256m -Xmx256m)[VARCHAR(200)]<NOT NULL,DEFAULT:‘-Xms256m -Xmx256m’>, INVALID_FLAG(是否作废;默认为0)<NOT NULL,DEFAULT:0>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "disaster_recover_db_sub_tem", "table_comment": "数据灾备子任务模板（1030）", "field_count": 19, "content_length": 734, "content": "表名: disaster_recover_db_sub_tem(数据灾备子任务模板（1030）)\n字段: ID(主键)<PK,NOT NULL>, DATA_SYNC_TYPE(全量1、增量2)<NOT NULL>, CRON(任务周期), FAIL_RETRY_COUNT(失败次数), SOURCE_DB_TYPE(源数据库类型)[VARCHAR(50)]<NOT NULL>, TARGET_DB_TYPE(目标数据库类型)[VARCHAR(50)]<NOT NULL>, SOURCE_DB_TABLE_NAME(源数据库表名称)[VARCHAR(100)]<NOT NULL>, TARGET_DB_TABLE_NAME(目标数据库表名称)[VARCHAR(100)]<NOT NULL>, WHERE_SQL(源数据查询条件)[VARCHAR(1000)], PRE_SQL(目标数据前置执行sql)[VARCHAR(1000)], SOURCE_DB_COLUMN(源数据表字段 默认全部字段)[VARCHAR(1000)], TARGET_DB_COLUMN(目标数据表字段 默认全部字段)[VARCHAR(1000)], JVM_PARAM(jvm参数  -Xms256m -Xmx256m)[VARCHAR(200)]<NOT NULL,DEFAULT:'-Xms256m -Xmx256m'>, INVALID_FLAG(是否作废;默认为0)<NOT NULL,DEFAULT:0>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DEVICE_MONITOR_CONFIG", "table_comment": "设备类型监控配置(1030)", "field_count": 18, "content_length": 536, "content": "表名: DEVICE_MONITOR_CONFIG(设备类型监控配置(1030))\n字段: ID(id)<PK,NOT NULL>, DEVICE_TYPE_ID(设备类型id), MONITOR_TYPE(监控类型;1 监控组件 2snmp 3监控组件http 4 自定义http), URL(url地址;http方式预留字段), SNMP_VERSION(SNMP版本;snmp版本备用), SNMP_PROTO(snmp监控协议;snmp监控协议  TCP/UDP,默认UDP，备用), SNMP_PORT(snmp端口;snmp端口), SAFE_LEVEL(安全等级;安全等级 1 不认证 2认证不加密 3认证+加密), SECURITY_NAME(SNMP用户名), AUTHENTICATION_PROTOCOL(认证算法id), AUTHENTICATION_AUTH_CODE(认证密码), PRIVACY_PROTOCOL(加密算法id), PRIVACY_AUTH_CODE(加密密码), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DEVICE_SNMP_OID_CONFIG", "table_comment": "设备snmp配置表(1030)", "field_count": 17, "content_length": 470, "content": "表名: DEVICE_SNMP_OID_CONFIG(设备snmp配置表(1030))\n字段: ID(id)<PK,NOT NULL>, DEVICE_TYPE_ID(设备类型id), OID(oid)[VARCHAR(150)], NAME(名称)[VARCHAR(100)], VALUE_TYPE(oid数据类型)[VARCHAR(50)], REQUEST_TYPE(请求类型)[VARCHAR(50)], COLLECTOR_TYPE(计算方式)[VARCHAR(50)], PARENT_ID(父节点id), SUB_OID_KEY(排序序号), SUB_OID_VALUE(子oid)[VARCHAR(150)], COMPUTING_TYPE(计算方式)[VARCHAR(50)], CALCULATE_TEMPLATE(计算模板)[VARCHAR(100)], REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DIC_SNMP_PRIVACY", "table_comment": "snmp加密方式字典表(1030)", "field_count": 8, "content_length": 202, "content": "表名: DIC_SNMP_PRIVACY(snmp加密方式字典表(1030))\n字段: ID(id), SNMP_PRIVACY_ID(认证方式id;与监控组件保持同步), SNMP_PRIVACY_NAME(认证方式名称;与监控组件同步), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DIC_SNMP_AUTHENTICATION", "table_comment": "snmp认证方式字典表(1030)", "field_count": 8, "content_length": 223, "content": "表名: DIC_SNMP_AUTHENTICATION(snmp认证方式字典表(1030))\n字段: ID(id), SNMP_AUTHENTICATION_ID(认证方式id;与监控组件保持同步), SNMP_AUTHENTICATION_NAME(认证方式名称;与监控组件同步), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DEVICE_GROUP_TO_DEVICE_TYPE", "table_comment": "设备组和设备类型关联表（作废1030）", "field_count": 7, "content_length": 214, "content": "表名: DEVICE_GROUP_TO_DEVICE_TYPE(设备组和设备类型关联表（作废1030）)\n字段: ID(ID)<PK,NOT NULL>, DEVICE_GROUP_ID(设备组ID)<NOT NULL>, DEVICE_TYPE_ID(设备类型ID)<NOT NULL>, CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DEVICE_TYPE_ROUTE_CONFIG", "table_comment": "设备类型路由配置（1030）", "field_count": 9, "content_length": 277, "content": "表名: DEVICE_TYPE_ROUTE_CONFIG(设备类型路由配置（1030）)\n字段: ID(主键)<PK,NOT NULL>, DEVICE_TYPE_ID(设备类型ID)<NOT NULL>, NET_PRO(请求协议)<NOT NULL>, URL_START(URL前缀)[VARCHAR(1000)]<NOT NULL>, HAS_MANAGE_ROUTE(是否有管理路由)<NOT NULL>, CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_AUTO_BIND_DEVICE", "table_comment": "服务自动绑定设备关联关系", "field_count": 5, "content_length": 120, "content": "表名: SERVICE_AUTO_BIND_DEVICE(服务自动绑定设备关联关系)\n字段: ID<PK,NOT NULL>, SERVICE_ID, DEVICE_ID, CREATE_BY(创建人), CREATE_TIME(创建时间)", "type": "entity", "source": "json_file"}, {"table_name": "WORK_ORDER", "table_comment": "工单表", "field_count": 23, "content_length": 524, "content": "表名: WORK_ORDER(工单表)\n字段: ID(工单id)<PK,NOT NULL>, SERIAL(工单编号)[VARCHAR(50)]<NOT NULL>, ORDER_TYPE_ID(工单类型ID)<NOT NULL>, TENANT_ID(租户id)<NOT NULL>, DESCRIPTION(问题描述), PRODUCT_ID(申请密码产品id), QUANTITY(申请数量), ORGANIZATION(申请组织单位), PERSON_NAME(申请联系人), PHONE(联系人手机号), EMAIL(联系人邮箱), SUBMISSION_BY(提交人), SUBMISSION_TIME(提交时间), PROCESSING_BY(处理人), START_PROCESSING_TIME(开始处理时间), END_BY(结束人), END_PROCESSING_TIME(处理完成时间), STATUS(工单状态;1待处理  2处理中  3已处理 4已撤销), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "WORK_ORDER_COMMENT", "table_comment": "工单评论表", "field_count": 9, "content_length": 212, "content": "表名: WORK_ORDER_COMMENT(工单评论表)\n字段: ID(工单评论id)<PK,NOT NULL>, WORK_ORDER_ID(工单id)<NOT NULL>, TENANT_ID(租户id)<NOT NULL>, CONTENT(评论内容), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "WORK_ORDER_ATTACHMENT", "table_comment": "工单附件表", "field_count": 10, "content_length": 237, "content": "表名: WORK_ORDER_ATTACHMENT(工单附件表)\n字段: ID(工单附件id)<PK,NOT NULL>, FATHER_ID(工单id or 工单评论id), TYPE(类型;1工单  2工单评论), STORAGE_PATH(存储路径)<NOT NULL>, FILE_NAME(文件名称), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DIC_WORK_ORDER", "table_comment": "工单相关字典表", "field_count": 10, "content_length": 263, "content": "表名: DIC_WORK_ORDER(工单相关字典表)\n字段: ID(主键)<PK,NOT NULL>, TYPE(字典类型;1：工单类型)<NOT NULL>, DIC_KEY(字典键)<NOT NULL>, DIC_VALUE(字典值)[VARCHAR(90)]<NOT NULL>, IS_AVAILABLE(是否启用 1启用 0停用)<NOT NULL>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_PRODUCT", "table_comment": "密码产品", "field_count": 15, "content_length": 440, "content": "表名: SERVICE_PRODUCT(密码产品)\n字段: ID(主键)<PK,NOT NULL>, PRODUCT_TYPE(服务类型;1：密码服务；2：密码支持服务；3：密码组合服务)<NOT NULL>, DATA_TYPE(数据类型;1：正式数据；2：草稿数据)<NOT NULL>, PRODUCT_NAME(产品名称)[VARCHAR(150)]<NOT NULL>, PRODUCT_ICON_ID(产品图标;图标地址), SALES_VOLUME(销量), PRODUCT_UNIT(单位)[VARCHAR(150)], ANNEX_ID(附件;文件地址), PDF_ANNEX_ID(pdf附件), PRODUCT_STATE(产品状态;0:关闭；1：开启)<NOT NULL,DEFAULT:0>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "PRODUCT_USE", "table_comment": "产品功能", "field_count": 9, "content_length": 214, "content": "表名: PRODUCT_USE(产品功能)\n字段: ID(ID)<PK,NOT NULL>, PRODUCT_ID(产品ID)<NOT NULL>, TITLE(标题)[VARCHAR(150)]<NOT NULL>, REMARK(描述), SORD_NUM(排序)<NOT NULL>, CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "PRODUCT_SPECS", "table_comment": "产品规格", "field_count": 12, "content_length": 290, "content": "表名: PRODUCT_SPECS(产品规格)\n字段: ID(主键)<PK,NOT NULL>, PRODUCT_ID(产品ID)<NOT NULL>, TITLE(标题)[VARCHAR(150)]<NOT NULL>, PRODUCT_UNIT(单位)[VARCHAR(150)]<NOT NULL>, PRODUCT_PRICE(售价), PRODUCT_DISCOUNT(折扣价), SORD_NUM(排序), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "PRODUCT_SUPERIORITY", "table_comment": "产品优势", "field_count": 10, "content_length": 250, "content": "表名: PRODUCT_SUPERIORITY(产品优势)\n字段: ID<PK,NOT NULL>, PRODUCT_ID(产品ID)<NOT NULL>, TITLE(标题)[VARCHAR(150)]<NOT NULL>, SUPERIORITY_ICON_ID(优势图标;图标地址), SORD_NUM(排序)<NOT NULL>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "PRODUCT_SCENE", "table_comment": "产品场景", "field_count": 10, "content_length": 232, "content": "表名: PRODUCT_SCENE(产品场景)\n字段: ID(主键)<PK,NOT NULL>, PRODUCT_ID(产品ID)<NOT NULL>, TITLE(标题)[VARCHAR(150)]<NOT NULL>, SCENE_ICON_ID(场景图片;图片地址), SORD_NUM(排序), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "PRODUCT_ATTACHMENT", "table_comment": "密码产品附件表", "field_count": 9, "content_length": 212, "content": "表名: PRODUCT_ATTACHMENT(密码产品附件表)\n字段: ID(ID)<PK,NOT NULL>, PRODUCT_ID(产品ID), STORAGE_PATH(存储路径)<NOT NULL>, FILE_NAME(文件名称)<NOT NULL>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "TENANT_TO_PRODUCT", "table_comment": "租户的密码产品", "field_count": 11, "content_length": 274, "content": "表名: TENANT_TO_PRODUCT(租户的密码产品)\n字段: ID(主键)<PK,NOT NULL>, TENANT_ID(租户ID)<NOT NULL>, PRODUCT_ID(产品ID)<NOT NULL>, STATE(状态;0:未开通；1：开通)<NOT NULL>, HAS_NUM(持有数量), EXPIRATION_TIME(过期时间)[VARCHAR(30)], REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "TENANT_TO_PRODUCT_RECORD", "table_comment": "租户的密码产品修改记录", "field_count": 11, "content_length": 269, "content": "表名: TENANT_TO_PRODUCT_RECORD(租户的密码产品修改记录)\n字段: ID(主键)<PK,NOT NULL>, TENANT_TO_PRODUCT_ID(租户的密码产品ID)<NOT NULL>, EDIT_TYPE(编辑类型;1：持有数量；2：到期时间), EDIT_BEFORE(编辑前), EDIT_AFTER(编辑后), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间), HMAC(hmac值)", "type": "entity", "source": "json_file"}, {"table_name": "CCSP_MESSAGE", "table_comment": "消息表", "field_count": 18, "content_length": 542, "content": "表名: CCSP_MESSAGE(消息表)\n字段: ID(主键)<PK,NOT NULL>, SOURCE(消息来源;1工单 2告警)<NOT NULL>, TYPE(消息类型)<NOT NULL>, STATUS(消息状态;1未读 2已读)<NOT NULL,DEFAULT:1>, TITLE(消息标题)[VARCHAR(500)], CONTENT(消息内容)[VARCHAR(2000)]<NOT NULL>, SENDER_ID(消息发送者ID;用户ID、租户ID、或者空), SENDER_ROLE_ID(消息发送者角色ID), RECEIVER_TYPE(消息接收者类型;1平台 2租户)<NOT NULL>, RECEIVER_ID(消息接收者ID;0L为通知所有租户、1L为通知平台、租户ID)<NOT NULL>, RECEIVER_ROLE_ID(消息接收者角色ID), JUMP_URL(跳转路径)[VARCHAR(500)], SOURCE_ID(告警源ID;消息源ID，用于判断新增还是更新), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "ALARM_INFO", "table_comment": "告警信息表", "field_count": 11, "content_length": 277, "content": "表名: ALARM_INFO(告警信息表)\n字段: ID(主键)<PK,NOT NULL>, TENANT_ID(租户ID), REGION_ID(区域ID)[BIGINT(20)], ALARM_CODE(告警标识)<NOT NULL>, SOURCE_ID(告警源ID), SOURCE_IP(告警源IP)[VARCHAR(20)], CONTENT(告警内容)<NOT NULL>, TIMES(告警次数), FIRST_TIME(首次告警时间), LAST_TIME(末次告警时间), STATUS(状态;告警状态;1告警中；2已处置；3已恢复)", "type": "entity", "source": "json_file"}, {"table_name": "DIC_ALARM_TYPE", "table_comment": "告警类型字典", "field_count": 5, "content_length": 171, "content": "表名: DIC_ALARM_TYPE(告警类型字典)\n字段: ID(主键)<PK,NOT NULL>, CODE(标识)<NOT NULL>, NAME(名称)<NOT NULL>, LEVEL(等级;1为最高等级，随递增级别减弱)<NOT NULL>, TENANT_VISIBLE(租户可见;0租户不可见，1租户可见)<NOT NULL>", "type": "entity", "source": "json_file"}, {"table_name": "ALARM_HISTORY", "table_comment": "告警历史表", "field_count": 14, "content_length": 332, "content": "表名: ALARM_HISTORY(告警历史表)\n字段: ID(主键)<PK,NOT NULL>, TENANT_ID(租户ID), TENANT_NAME(租户名称), REGION_ID(区域ID)[BIGINT(20)], REGION_NAME(区域名称), ALARM_CODE(告警标识)<NOT NULL>, SOURCE_IP(告警源IP)[VARCHAR(20)], SOURCE_NAME(告警源名称), CONTENT(告警内容), TIMES(告警次数), FIRST_TIME(首次告警时间), LAST_TIME(末次告警时间), STATUS(告警状态;1告警中2已处置3已恢复), REMARK(备注)[VARCHAR(1000)]", "type": "entity", "source": "json_file"}, {"table_name": "ALARM_OID", "table_comment": "告警指标", "field_count": 5, "content_length": 134, "content": "表名: ALARM_OID(告警指标)\n字段: ID(主键)<PK,NOT NULL>, ALARM_CODE(告警标识)<NOT NULL>, OID(指标id)<NOT NULL>, NAME(指标名称)<NOT NULL>, DESC(描述)<NOT NULL>", "type": "entity", "source": "json_file"}, {"table_name": "service_oauth", "table_comment": "认证服务表", "field_count": 11, "content_length": 277, "content": "表名: service_oauth(认证服务表)\n字段: ID(认证服务ID)<PK,NOT NULL>, REGION_ID(区域ID)<NOT NULL>, NAME(服务名称)<NOT NULL>, IP(认证服务IP)[VARCHAR(40)]<NOT NULL>, PORT(认证服务端口)<NOT NULL>, INVALID_FLAG(是否作废;默认为0)<NOT NULL>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "service_swmonitor", "table_comment": "监控服务表", "field_count": 11, "content_length": 281, "content": "表名: service_swmonitor(监控服务表)\n字段: ID(监控服务ID)<PK,NOT NULL>, REGION_ID(区域ID)<NOT NULL>, NAME(服务名称)<NOT NULL>, IP(监控服务IP)[VARCHAR(40)]<NOT NULL>, PORT(监控服务端口)<NOT NULL>, INVALID_FLAG(是否作废;默认为0)<NOT NULL>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "service_remote", "table_comment": "管控服务表", "field_count": 12, "content_length": 317, "content": "表名: service_remote(管控服务表)\n字段: ID(管控服务ID)<PK,NOT NULL>, REGION_ID(区域ID)<NOT NULL>, NAME(服务名称)<NOT NULL>, IP(管控服务IP)[VARCHAR(40)]<NOT NULL>, PORT(管控服务端口)<NOT NULL>, PRIMARY_FLAG(是否为主管控服务;预留字段)<NOT NULL>, INVALID_FLAG(是否作废;默认为0)<NOT NULL>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "STATISTIC_DAY_DATA", "table_comment": "按照天拆分实时数据（330）(3.3.2）", "field_count": 23, "content_length": 805, "content": "表名: STATISTIC_DAY_DATA(按照天拆分实时数据（330）(3.3.2）)\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, COLLECT_DATE_TIME(拆分数据天所在月;拆分数据天所在的月时间格式), COLLECT_TIME(拆分数据天时间)<NOT NULL>, CALL_NUM(调用次数)[BIGINT(10)]<NOT NULL>, REGION_CODE(区域标识)[VARCHAR(200)]<NOT NULL>, TENANT_CODE(租户标识)[VARCHAR(100)]<NOT NULL>, APP_CODE(应用标识)[VARCHAR(100)]<NOT NULL>, SERVICE_IP(服务IP)[VARCHAR(100)]<NOT NULL>, SERVICE_PORT(服务端口)[INT(10)], SERVICE_TYPE_CODE(服务标识)[VARCHAR(100)]<NOT NULL>, SERVICE_URL(服务URL)[VARCHAR(200)], AVG_COST_TIME(平均耗时)[BIGINT], MAX_COST_TIME(最大耗时)[BIGINT], SERVICE_FLOW_NUM(流量)[BIGINT], PEAK_TPS(峰值（TPS）), PEAK_MBPS(峰值（Mbps）), PEAK_TIME(峰值时间)[VARCHAR(100)], INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "STATISTIC_HOUR_DATA", "table_comment": "按照小时拆分实时数据（330）(3.3.2）", "field_count": 23, "content_length": 812, "content": "表名: STATISTIC_HOUR_DATA(按照小时拆分实时数据（330）(3.3.2）)\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, COLLECT_DATE_TIME(拆分数据小时时间所在天;拆分数据小时时间所在的天时间), COLLECT_TIME(拆分数据小时时间)<NOT NULL>, CALL_NUM(调用次数)[BIGINT(10)]<NOT NULL>, REGION_CODE(区域标识)[VARCHAR(200)]<NOT NULL>, TENANT_CODE(租户标识)[VARCHAR(100)]<NOT NULL>, APP_CODE(应用标识)[VARCHAR(100)]<NOT NULL>, SERVICE_IP(服务IP)[VARCHAR(100)]<NOT NULL>, SERVICE_PORT(服务端口)[INT(10)], SERVICE_TYPE_CODE(服务标识)[VARCHAR(100)]<NOT NULL>, SERVICE_URL(服务URL)[VARCHAR(200)], AVG_COST_TIME(平均耗时)[BIGINT], MAX_COST_TIME(最大耗时)[BIGINT], SERVICE_FLOW_NUM(流量)[BIGINT], PEAK_TPS(峰值（TPS）), PEAK_MBPS(峰值（Mbps）), PEAK_TIME(峰值时间)[VARCHAR(100)], INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "STATISTIC_REAL_TIME_DATA", "table_comment": "调用次数实时数据（330）(3.3.2)", "field_count": 21, "content_length": 791, "content": "表名: STATISTIC_REAL_TIME_DATA(调用次数实时数据（330）(3.3.2))\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, COLLECT_DATE_TIME(数据采集时间所在小时;数据采集时间所在的小时时间格式), COLLECT_TIME(数据采集时间(分钟))<NOT NULL>, COLLECT_TIME_SECOND(数据采集时间(秒))[VARCHAR(90)], CALL_NUM(调用次数)[BIGINT(10)]<NOT NULL>, REGION_CODE(区域标识)[VARCHAR(200)]<NOT NULL>, TENANT_CODE(租户标识)[VARCHAR(100)]<NOT NULL>, APP_CODE(应用标识)[VARCHAR(100)]<NOT NULL>, SERVICE_IP(服务IP)[VARCHAR(100)]<NOT NULL>, SERVICE_PORT(服务端口)[INT(10)], SERVICE_TYPE_CODE(服务标识)[VARCHAR(100)]<NOT NULL>, SERVICE_URL(服务URL)[VARCHAR(200)], AVG_COST_TIME(平均耗时)[BIGINT], MAX_COST_TIME(最大耗时)[BIGINT], SERVICE_FLOW_NUM(流量)[BIGINT], INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "STATISTIC_MONTH_DATA", "table_comment": "按照月拆分实时数据（330）(3.3.2）", "field_count": 23, "content_length": 812, "content": "表名: STATISTIC_MONTH_DATA(按照月拆分实时数据（330）(3.3.2）)\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, COLLECT_DATE_TIME(拆分数据月时间所在年;拆分数据月时间所表示的年时间), COLLECT_TIME(拆分数据月时间单位)<NOT NULL>, CALL_NUM(调用次数)[BIGINT(10)]<NOT NULL>, REGION_CODE(区域标识)[VARCHAR(200)]<NOT NULL>, TENANT_CODE(租户标识)[VARCHAR(100)]<NOT NULL>, APP_CODE(应用标识)[VARCHAR(100)]<NOT NULL>, SERVICE_IP(服务IP)[VARCHAR(100)]<NOT NULL>, SERVICE_PORT(服务端口)[INT(10)], SERVICE_TYPE_CODE(服务标识)[VARCHAR(100)]<NOT NULL>, SERVICE_URL(服务URL)[VARCHAR(200)], AVG_COST_TIME(平均耗时)[BIGINT], MAX_COST_TIME(最大耗时)[BIGINT], SERVICE_FLOW_NUM(流量)[BIGINT], PEAK_TPS(峰值（TPS）), PEAK_MBPS(峰值（Mbps）), PEAK_TIME(峰值时间)[VARCHAR(100)], INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "STATISTIC_ALL_COLLECT_DATA", "table_comment": "实时数据汇总拆分（330）", "field_count": 18, "content_length": 658, "content": "表名: STATISTIC_ALL_COLLECT_DATA(实时数据汇总拆分（330）)\n字段: ID(ID)[BIGINT]<PK,NOT NULL>, CALL_NUM(调用次数)[BIGINT(10)]<NOT NULL>, REGION_CODE(区域标识)[VARCHAR(200)]<NOT NULL>, TENANT_CODE(租户标识)[VARCHAR(100)]<NOT NULL>, APP_CODE(应用标识)[VARCHAR(100)]<NOT NULL>, SERVICE_IP(服务IP)[VARCHAR(100)]<NOT NULL>, SERVICE_PORT(服务端口)[INT(10)], SERVICE_TYPE_CODE(服务标识)[VARCHAR(100)]<NOT NULL>, SERVICE_URL(服务URL)[VARCHAR(200)], AVG_COST_TIME(平均耗时)[BIGINT], MAX_COST_TIME(最大耗时)[BIGINT], SERVICE_FLOW_NUM(流量)[BIGINT], INVALID_FLAG(是否作废;默认为0)[INT]<NOT NULL,DEFAULT:0>, REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_OAUTH_MGT", "table_comment": "认证服务管理表（330）", "field_count": 11, "content_length": 288, "content": "表名: SERVICE_OAUTH_MGT(认证服务管理表（330）)\n字段: ID(认证服务ID)<PK,NOT NULL>, REGION_ID(区域ID)<NOT NULL>, NAME(服务名称)<NOT NULL>, IP(认证服务IP)[VARCHAR(40)]<NOT NULL>, PORT(认证服务端口)<NOT NULL>, INVALID_FLAG(是否作废;默认为0)<NOT NULL>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_SWMONITOR_MGT", "table_comment": "监控服务管理表（330）", "field_count": 11, "content_length": 292, "content": "表名: SERVICE_SWMONITOR_MGT(监控服务管理表（330）)\n字段: ID(监控服务ID)<PK,NOT NULL>, REGION_ID(区域ID)<NOT NULL>, NAME(服务名称)<NOT NULL>, IP(监控服务IP)[VARCHAR(40)]<NOT NULL>, PORT(监控服务端口)<NOT NULL>, INVALID_FLAG(是否作废;默认为0)<NOT NULL>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_REMOTE_MGT", "table_comment": "管控服务管理表（330）", "field_count": 12, "content_length": 328, "content": "表名: SERVICE_REMOTE_MGT(管控服务管理表（330）)\n字段: ID(管控服务ID)<PK,NOT NULL>, REGION_ID(区域ID)<NOT NULL>, NAME(服务名称)<NOT NULL>, IP(管控服务IP)[VARCHAR(40)]<NOT NULL>, PORT(管控服务端口)<NOT NULL>, PRIMARY_FLAG(是否为主管控服务;预留字段)<NOT NULL>, INVALID_FLAG(是否作废;默认为0)<NOT NULL>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DIC_USER_TYPE", "table_comment": "用户类型（330）", "field_count": 8, "content_length": 198, "content": "表名: DIC_USER_TYPE(用户类型（330）)\n字段: ID(ID)<PK,NOT NULL>, USER_TYPE_ID(用户类型id;1平台用户 2租户用户 3应用用户), USER_TYPE_NAME(用户类型名称), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_PRODUCT_UNIT_QUOTA", "table_comment": "密码产品单位限额(330)", "field_count": 11, "content_length": 330, "content": "表名: SERVICE_PRODUCT_UNIT_QUOTA(密码产品单位限额(330))\n字段: ID(ID)<PK,NOT NULL>, PRODUCT_ID(密码产品ID)<NOT NULL>, BUSI_TYPE_ID(业务类型ID=服务类型ID), QUOTA_VALUE(配额值)<NOT NULL>, QUOTA_UNIT(配额单位)[VARCHAR(50)]<NOT NULL>, QUOTA_TYPE(配额类型;1：网关控制； 2：密码服务控制；3：不控制)<NOT NULL>, REMARK(描述), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_COUNT_MODULE", "table_comment": "网关统计组件表（330）", "field_count": 10, "content_length": 271, "content": "表名: SERVICE_COUNT_MODULE(网关统计组件表（330）)\n字段: ID(管控服务ID)<PK,NOT NULL>, REGION_ID(区域ID)<NOT NULL>, GATEWAY_ID(网关ID)<NOT NULL>, MODULE_CODE(网关统计组件标识)<NOT NULL>, IP(管控服务IP)[VARCHAR(40)]<NOT NULL>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "TENANT_TO_SERVICE_GROUP", "table_comment": "租户绑定共享服务组（330 520）", "field_count": 9, "content_length": 264, "content": "表名: TENANT_TO_SERVICE_GROUP(租户绑定共享服务组（330 520）)\n字段: ID(ID)<PK,NOT NULL>, TENANT_ID(租户ID)<NOT NULL>, SERVICE_GROUP_ID(共享服务组ID)<NOT NULL>, BUSI_TYPE_ID(业务类型ID)<NOT NULL>, IS_SHARE(是否共享 0：专享  1：共享), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DIC_PROXY_ROUTE", "table_comment": "代理路由字典表(330)", "field_count": 12, "content_length": 574, "content": "表名: DIC_PROXY_ROUTE(代理路由字典表(330))\n字段: ID(主键)<PK,NOT NULL>, ROUTE_USE(路由用途;1页面代理路由代理 2监控服务代理 3认证中心代理 4管控服务代理 5设备管理代理 6服务管理代理 7服务管控代理 8反向统计指标代理 9反向管理平台路由)<NOT NULL>, ROUTE_PROXY_TYPE(路由代理类型https tcp)[VARCHAR(20)]<NOT NULL>, ROUTE_CONDITION(路由匹配条件;1请求头 2请求参数 3cookie)<NOT NULL,DEFAULT:1>, ROUTE_CONDITION_RULE(路由匹配条件比对规则;id、ip、tenantCode等)[VARCHAR(100)]<NOT NULL>, URIS(拦截路径)[VARCHAR(500)]<NOT NULL>, LOAD_BALANCE_TYPE(负载均衡算法;roundrobin:轮询 chash:Hash算法)[VARCHAR(20)]<NOT NULL,DEFAULT:roundrobin>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "PROXY_ROUTE_RECORD", "table_comment": "代理路由记录表(330)", "field_count": 15, "content_length": 432, "content": "表名: PROXY_ROUTE_RECORD(代理路由记录表(330))\n字段: ID(主键)<PK,NOT NULL>, REGION_ID(区域ID)<NOT NULL>, TENANT_ID(租户ID), DIC_PROXY_ROUTE_ID(代理路由字典表ID), ROUTE_CODE(路由组件标识(apisix中的id))[VARCHAR(100)]<NOT NULL>, ROUTE_USE(路由用途)<NOT NULL>, ROUTE_NAME(路由名称), URIS(拦截路径)[VARCHAR(500)]<NOT NULL>, VARS(路由匹配条件)[VARCHAR(1000)], UPSTREAM(下游代理upstream)[VARCHAR(1000)]<NOT NULL>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DIC_SHARE_GROUP_TYPE", "table_comment": "支持创建共享服务类型（330）", "field_count": 4, "content_length": 128, "content": "表名: DIC_SHARE_GROUP_TYPE(支持创建共享服务类型（330）)\n字段: ID<PK,NOT NULL>, BUSI_TYPE_ID<NOT NULL>, IS_NEED_KMS<NOT NULL>, IS_SHARE<NOT NULL>", "type": "entity", "source": "json_file"}, {"table_name": "SERVICE_DOCKER_PORT_MAPPING", "table_comment": "密码服务实例Docker端口映射表（520）", "field_count": 10, "content_length": 333, "content": "表名: SERVICE_DOCKER_PORT_MAPPING(密码服务实例Docker端口映射表（520）)\n字段: ID(主键)[BIGINT]<PK,NOT NULL,DEFAULT:2>, SERVICE_INFO_ID(服务信息ID)[BIGINT]<NOT NULL>, MAPPING_CODE(映射标识)[VARCHAR(50)]<NOT NULL>, OUTSIDE_PORT(外部端口（宿主机端口）)[INT], INSIDE_PORT(内部端口（Docker Expose端口）), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DOCKER_IMAGE", "table_comment": "镜像信息表(520)", "field_count": 16, "content_length": 434, "content": "表名: DOCKER_IMAGE(镜像信息表(520))\n字段: ID(id), IMAGE_NAME(镜像名称)[VARCHAR], IMAGE_VERSION(镜像版本)[VARCHAR(64)], TYPE(类型 1-密码服务 2-组件服务), SERVICE_TYPE(服务类型)[VARCHAR], ENABLE(是否启用 0-未启用 1-启用), FILE_NAME(文件名称)[VARCHAR], FILE_REMOTE_PATH(文件远程路径)[VARCHAR], FILE_SIZE(文件大小/MB)[DECIMAL(24,2)], FILE_DIGEST(文件摘要)[VARCHAR], HMAC(完整性校验值)[VARCHAR], REMARK(备注)[VARCHAR], CREATE_BY(创建人), CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人), UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}, {"table_name": "ENCRYPT_JUDGE_KEY", "table_comment": "加密判定密钥表（520）", "field_count": 18, "content_length": 574, "content": "表名: ENCRYPT_JUDGE_KEY(加密判定密钥表（520）)\n字段: ID(主键)<PK,NOT NULL>, TYPE(类型;1内部密钥 or 2外部密钥)<NOT NULL>, REGION_ID_SENDER(发送方区域ID;发送方区域ID), TENANT_ID_SENDER(发送方租户ID;发送方租户ID), APP_ID_SENDER(发送方业务账号ID;发送方业务账号ID), REGION_ID_RECEIVER(接收方区域ID;接收方区域ID), TENANT_ID_RECEIVER(接收方租户ID;接收方租户ID), APP_ID_RECEIVER(接收方业务账号ID;接收方业务账号ID), KEY_NAME(密钥名称;密钥名称)<NOT NULL>, KEY_MATERIAL(密钥材料;KMS返回，明文密钥材料的Base64编码)[VARCHAR(1000)], KEY_LENGTH(密钥长度;KMS返回，密钥长度), ALG(密钥算法;KMS返回，密钥算法), INVALID_FLAG(是否作废;0未作废  1已作废)<NOT NULL>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "TENANT_TO_DEVICE_GROUP", "table_comment": "租户绑定设备组（520）", "field_count": 8, "content_length": 265, "content": "表名: TENANT_TO_DEVICE_GROUP(租户绑定设备组（520）)\n字段: ID(ID)<PK,NOT NULL>, TENANT_ID(租户ID)<NOT NULL>, DEVICE_GROUP_ID(设备组ID)<NOT NULL>, IS_SHARE(是否支持共享，0：独享，1：共享)<NOT NULL>, IS_REST(是否支持直接调用;0：不支持；1：支持)<NOT NULL>, BUSI_TYPE_ID(业务类型，异构设备使用), CREATE_BY(创建人), CREATE_TIME(创建时间)", "type": "entity", "source": "json_file"}, {"table_name": "LICENSE_RECORD", "table_comment": "许可授权记录表（520）", "field_count": 14, "content_length": 399, "content": "表名: LICENSE_RECORD(许可授权记录表（520）)\n字段: ID(主键)<PK,NOT NULL>, PRODUCT_NAME(产品名称)<NOT NULL>, CUSTOMER_NAME(客户名称)<NOT NULL>, LICENSE_TYPE(许可类型;1永久授权2按年授权3按月授权)<NOT NULL>, APPLY_LICENSE_INFO(申请许可内容)<NOT NULL>, AUTH_LICENSE_INFO(授权许可内容)<NOT NULL>, ISSUE_TIME(许可签发时间)<NOT NULL>, HMAC(完整性校验)<NOT NULL>, SERIAL_ID(序列号)<NOT NULL>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "TENANT_PRODUCT_RECORD", "table_comment": "租户开通密码产品记录表", "field_count": 11, "content_length": 243, "content": "表名: TENANT_PRODUCT_RECORD(租户开通密码产品记录表)\n字段: ID(主键)<PK,NOT NULL>, TENANT_ID(租户标识), PRODUCT_ID(产品ID), PRODUCT_TYPE(产品类型), PRODUCT_NAME(产品名称), STATUS(开通状态;1已开通，2未开通), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "STATISTIC_OVER_LIMIT_DATA", "table_comment": "超限数据表（3.3.2）", "field_count": 18, "content_length": 535, "content": "表名: STATISTIC_OVER_LIMIT_DATA(超限数据表（3.3.2）)\n字段: ID(ID)<PK,NOT NULL>, COLLECT_TIME(数据采集时间(秒);字符串格式)[VARCHAR(30)]<NOT NULL>, COLLECT_TIMESTAMP(数据采集时间(时间戳);时间戳), REGION_CODE(区域标识), TENANT_CODE(租户标识), APP_CODE(应用标识)<NOT NULL>, SERVICE_IP(服务IP)[VARCHAR(100)]<NOT NULL>, SERVICE_PORT(服务端口)<NOT NULL>, SERVICE_TYPE_CODE(服务类型)[VARCHAR(100)], CALL_NUM(调用次数)<NOT NULL>, SERVICE_FLOW_NUM(流量;单位：Byte), QUOTA_CALL_NUM(配额（调用次数）;单位：TPS), QUOTA_FLOW_NUM(配额（流量）;单位：Mbps), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "SUB_TENANT_LOGIN_CONFIG", "table_comment": "下级租户跳转配置表(332)", "field_count": 11, "content_length": 423, "content": "表名: SUB_TENANT_LOGIN_CONFIG(下级租户跳转配置表(332))\n字段: ID(主键ID)<PK,NOT NULL>, SUB_TENANT_ID(租户绑定信息表ID;随机自增主键，不是下级平台的租户ID)<NOT NULL>, TYPE(类型;1CCSP租户用户 2华为租户管理账户 3华为租户账户)<NOT NULL>, TENANT_CODE(租户账号;CPCS：租户名/原华为云账号 CCSP：租户标识)<NOT NULL>, USER_CODE(用户账号;CPCS：IAM用户名/邮件地址 CCSP：租户用户账号)<NOT NULL>, AUTH_CODE(账号口令;存储前端传递的摘要值，加密存储)<NOT NULL>, REMARK(备注)[VARCHAR(1000)], CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "SUB_TENANT", "table_comment": "下级租户绑定信息表(332)", "field_count": 11, "content_length": 340, "content": "表名: SUB_TENANT(下级租户绑定信息表(332))\n字段: ID(ID;主键标识，非下级租户ID)[BIGINT]<PK,NOT NULL>, TENANT_ID(租户ID), SUB_PLATFORM_ID(下级平台ID)<NOT NULL>, SUB_TENANT_TYPE(绑定租户类型;下级平台类型;1：CCSP；2：CPCS)<NOT NULL>, SUB_TENANT_CODE(绑定租户标识), SUB_TENANT_NAME(绑定租户名称)<NOT NULL>, REMARK(备注)[VARCHAR(1000)], CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DIC_REGION_NODE", "table_comment": "区域节点（332）", "field_count": 8, "content_length": 170, "content": "表名: DIC_REGION_NODE(区域节点（332）)\n字段: ID(ID)<PK,NOT NULL>, NODE_CODE(节点标识), NODE_NAME(节点名称), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DIC_REGION_TYPE", "table_comment": "区域类型（332）", "field_count": 8, "content_length": 170, "content": "表名: DIC_REGION_TYPE(区域类型（332）)\n字段: ID(ID)<PK,NOT NULL>, TYPE_CODE(类型标识), TYPE_NAME(类型名称), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DAILY_REPORT", "table_comment": "进度跟踪报告表（332）;保存密评进度日报的风险项、今日进展、明日计划等", "field_count": 13, "content_length": 360, "content": "表名: DAILY_REPORT(进度跟踪报告表（332）;保存密评进度日报的风险项、今日进展、明日计划等)\n字段: ID(主键标识)<PK,NOT NULL>, REL_OBJECT_ID(密评对象ID;关联到密评对象), RISK_ITEMS(风险项), TODAY_PROGRESS(今日进展), TOMORROW_PLAN(明日计划), REMAINING_ISSUES(遗留问题), PROGRESS_CONTENT(流程进度计划;保存历史的流程进度)<NOT NULL>, REPORT_DATE(日报日期;日报对应的日期)<NOT NULL>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "ccase_object", "table_comment": "密评对象332", "field_count": 17, "content_length": 537, "content": "表名: ccase_object(密评对象332)\n字段: ID(id)<PK,NOT NULL>, APP_ID(应用id;应用id)<NOT NULL>, CLASSIFIED_PROTECTION_LEVEL(等保等级;等保等级)<NOT NULL>, CLASSIFIED_PROTECTION_STATUS(等保状态;等保状态 0未定级 1已定级)<NOT NULL>, RECORD_STATUS(备案状态;备案状态 0为备案 1已备案)<NOT NULL>, RECORD_ID(备案证明编号;备案证明编号), RECORD_TIME(备案时间;备案时间), RECODE_LEVEL_AGREE(备案等级是否一致;备案等级是否一致), CHANGE_EXPLANATION(变化情况说明;变化情况说明), STAGE(所处阶段;所处密评阶段), STATUS(是否完成;密评是否完成 0 未完成 1完成), PROGRESS_TEMPLATE(密评进度模版), REQUEST_TEMPLATE(密评要求模版), CREATED_BY(创建人), CREATED_TIME(创建时间), UPDATED_BY(更新人), UPDATED_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "PRODUCT_SALES_VOLUME", "table_comment": "密码产品销量表(332)", "field_count": 10, "content_length": 225, "content": "表名: PRODUCT_SALES_VOLUME(密码产品销量表(332))\n字段: ID(主键)<PK,NOT NULL>, PRODUCT_NAME(产品名称), PRODUCT_TYPE(产品类型), SALES_VOLUME(销量)<DEFAULT:0>, STATUS(状态), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "INDEX_ITEM", "table_comment": "指标条目332", "field_count": 12, "content_length": 311, "content": "表名: INDEX_ITEM(指标条目332)\n字段: ID(主键)<PK,NOT NULL>, INDEX_ITEM_NAME(指标条目名称), INDEX_SYSTEM_ID(指标体系ID;表明当前指标条目属于哪一个指标体系), FIRST_GRADE(第一级;1 可 2宜 3应), SECOND_GRADE(第二级;1 可 2宜 3应), THIRD_GRADE(第三级;1 可 2宜 3应), FOURTH_GRADE(第四级;1 可 2宜 3应), REMARK(描述), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "INDEX_SYSTEM", "table_comment": "指标体系332", "field_count": 8, "content_length": 199, "content": "表名: INDEX_SYSTEM(指标体系332)\n字段: ID(主键)<PK,NOT NULL>, INDEX_SYSTEM_TYPE(指标体系类型;1 技术要求 2 管理要求), INDEX_SYSTEM_NAME(指标体系名称), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "INDEX_ITEM_INVOKE_OPTION", "table_comment": "调研选项332", "field_count": 8, "content_length": 177, "content": "表名: INDEX_ITEM_INVOKE_OPTION(调研选项332)\n字段: ID(主键)<PK,NOT NULL>, INDEX_ITEM_ID(指标条目ID), SCENE(场景), REMARK(描述), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "INDEX_ITEM_SUGGESTION", "table_comment": "指标条目改进建议332", "field_count": 8, "content_length": 218, "content": "表名: INDEX_ITEM_SUGGESTION(指标条目改进建议332)\n字段: ID(主键)<PK,NOT NULL>, INDEX_ITEM_ID(指标条目ID), INDEX_SUGGESTION_CONTENT(指标条目建议内容)[VARCHAR(1000)], REMARK(描述), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DIC_CYPHER_FILE", "table_comment": "密码文件字典表332", "field_count": 8, "content_length": 207, "content": "表名: DIC_CYPHER_FILE(密码文件字典表332)\n字段: ID(主键)<PK,NOT NULL>, CYPHER_FILE_TYPE(密码文件类型;1密评文档 2标准文档 3操作文档), CYPHER_FILE_NAME(密码文件名称), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "CYPHER_PRODUCT", "table_comment": "密码产品信息332", "field_count": 13, "content_length": 304, "content": "表名: CYPHER_PRODUCT(密码产品信息332)\n字段: ID(主键)<PK,NOT NULL>, CYPHER_TYPE(密码产品分类;1 用户侧 2网络侧 3服务端), CYPHER_NAME(密码产品名称), SUB_COMPONENT(子组件), CONFIG_SUGGESTION(配置建议), SOFT_HARDWARE_TYPE(软件硬件类型), CONFIG_RULER(配置规则), FUNCTION(密码产品功能), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "CORPORATION_INFO", "table_comment": "单位信息表（332）", "field_count": 13, "content_length": 296, "content": "表名: CORPORATION_INFO(单位信息表（332）)\n字段: ID(ID)<PK,NOT NULL>, CORPORATION_NAME(单位名称), CORPORATION_ADDRESS(单位地址), DEPARTMENT(单位所属密码管理部门), CORPORATION_TYPE(单位类型), POSTAL_CODE(邮政编码), TENANT_ID(关联租户ID), TENANT_CODE(关联租户标识), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DICT_ITEM", "table_comment": "枚举项表（332）", "field_count": 13, "content_length": 325, "content": "表名: DICT_ITEM(枚举项表（332）)\n字段: ID(ID)<PK,NOT NULL>, DICT_CODE(枚举项标识，可以为空，主要用于多层枚举使用)[VARCHAR(1000)], PARENT_CODE(上级枚举标识，可以为空，为空表示为最上级枚举)[VARCHAR(1000)], PARENT_ID(上级枚举ID), GROUP_COED(枚举分组标识), DICT_VALUE(枚举值), IN_USE(是否启用(0启用，1禁用)), DICT_SORT(排序), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "CRYPT_AGENCY_INFO", "table_comment": "密评机构表（332）", "field_count": 11, "content_length": 247, "content": "表名: CRYPT_AGENCY_INFO(密评机构表（332）)\n字段: ID(ID)<PK,NOT NULL>, AGENCY_NAME(机构名称), AGENCY_ADDRESS(机构地址), CONTACT_NAME(联系人名称), CONTACT_PHONE(联系人电话), CONTACT_EMAIL(联系人电子邮箱), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "VENDOR_INFO", "table_comment": "厂商信息表（332）", "field_count": 11, "content_length": 241, "content": "表名: VENDOR_INFO(厂商信息表（332）)\n字段: ID(ID)<PK,NOT NULL>, VENDOR_NAME(机构名称), VENDOR_ADDRESS(机构地址), CONTACT_NAME(联系人名称), CONTACT_PHONE(联系人电话), CONTACT_EMAIL(联系人电子邮箱), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DIC_REGION_NODE", "table_comment": "区域节点（332）", "field_count": 8, "content_length": 170, "content": "表名: DIC_REGION_NODE(区域节点（332）)\n字段: ID(ID)<PK,NOT NULL>, NODE_CODE(节点标识), NODE_NAME(节点名称), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DIC_REGION_TYPE", "table_comment": "区域类型（332）", "field_count": 8, "content_length": 170, "content": "表名: DIC_REGION_TYPE(区域类型（332）)\n字段: ID(ID)<PK,NOT NULL>, TYPE_CODE(类型标识), TYPE_NAME(类型名称), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "PROGRESS_TEMPLATE", "table_comment": "密评进度模板表（332）", "field_count": 10, "content_length": 235, "content": "表名: PROGRESS_TEMPLATE(密评进度模板表（332）)\n字段: ID(ID)<PK,NOT NULL>, FROM_ID(原始模板ID，所有模板都是copy过来的), REL_OBJECT_ID(关联密评对象), CODE(标识)<NOT NULL>, NAME(名称)<NOT NULL>, REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "PROGRESS_TEMPLATE_STAGE", "table_comment": "密评进度阶段表（332）", "field_count": 10, "content_length": 247, "content": "表名: PROGRESS_TEMPLATE_STAGE(密评进度阶段表（332）)\n字段: ID(ID)<PK,NOT NULL>, REL_TEMPLATE_ID(关联模板ID)<NOT NULL>, CODE(标识)<NOT NULL>, NAME(名称)<NOT NULL>, FINISH(是否完成;1 完成 2 未完成), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "PROGRESS_EXTEND_KEY_DATA", "table_comment": "密评进度拓展-方案编制阶段关键数据表（332）", "field_count": 16, "content_length": 374, "content": "表名: PROGRESS_EXTEND_KEY_DATA(密评进度拓展-方案编制阶段关键数据表（332）)\n字段: ID(ID)<PK,NOT NULL>, REL_TEMPLATE_ID(关联模板ID)<NOT NULL>, REL_STAGE_ID(关联阶段ID)<NOT NULL>, NAME(数据名称)<NOT NULL>, DESP(描述), APP(所属应用模块), POSITION(存储位置), REL_DB_TYPE_ID(关联数据库类型), DATA_SIZE(存储数据大小(MB)), QPS(QPS), SECURE(安全需求;1 机密性；2 不可抵赖性；), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "PROGRESS_STAGE_DATA", "table_comment": "阶段要素信息表（332）", "field_count": 19, "content_length": 470, "content": "表名: PROGRESS_STAGE_DATA(阶段要素信息表（332）)\n字段: ID(ID)<PK,NOT NULL>, REL_TEMPLATE_ID(关联模板ID)<NOT NULL>, REL_STAGE_ID(关联阶段ID)<NOT NULL>, MAIN_CODE(主要标识)<NOT NULL>, MAIN_NAME(主要名称)<NOT NULL>, SECONDARY_CODE(次要标识), SECONDARY_NAME(次要名称), EXTEND_CODE(拓展标识), EXTEND_NAME(拓展名称), TYPE(要素类型)<NOT NULL>, CONTENT(要素内容), SHOWS(是否展示;1 展示 2隐藏)<NOT NULL>, FINISH(是否完成;1 完成 2未完成)<NOT NULL>, FINISH_DATE(预估完成时间), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "PRODUCT_SALES_VOLUME", "table_comment": "密码产品销量表(332)", "field_count": 10, "content_length": 225, "content": "表名: PRODUCT_SALES_VOLUME(密码产品销量表(332))\n字段: ID(主键)<PK,NOT NULL>, PRODUCT_NAME(产品名称), PRODUCT_TYPE(产品类型), SALES_VOLUME(销量)<DEFAULT:0>, STATUS(状态), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "KNOWLEDGE_BASE", "table_comment": "密码知识库(332)", "field_count": 12, "content_length": 380, "content": "表名: KNOWLEDGE_BASE(密码知识库(332))\n字段: ID(主键)<PK,NOT NULL>, KNOWLEDGE_NAME(知识库名称;知识库名称)<NOT NULL>, KNOWLEDGE_CALSSIFICATION(知识库分类;知识库分类1 密评文档 2 标准文档 3 操作文档)<NOT NULL>, FILE_ID(文件ID;文件ID)<NOT NULL>, FILE_TYPE(文件类型;上传的文件类型), FILE_SIZE(文件大小;单位MB)[DECIMAL(24,4)], IS_DISPLAY(是否显示;是否给租户显示，1显示  0不显示), REMARK(备注;备注), CREATE_TIME(上传时间;上传时间), CREATE_BY(创建人), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "CCASE_APP_INFO", "table_comment": "密评应用表3322", "field_count": 11, "content_length": 294, "content": "表名: CCASE_APP_INFO(密评应用表3322)\n字段: APP_ID(应用ID)<PK,NOT NULL>, APP_CODE(应用标识)[VARCHAR]<NOT NULL>, CORPORATION_ID(所属单位ID), TENANT_ID(所属租户ID)[BIGINT], IS_ONLINE(应用是否上线;1：上线  0：未上线), ONLINE_TIME(应用上线时间;IS_ONLINE为1时生效), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "APP_SERVER", "table_comment": "应用服务器表3322", "field_count": 11, "content_length": 327, "content": "表名: APP_SERVER(应用服务器表3322)\n字段: ID(ID)<PK,NOT NULL>, SERVER_NAME(服务器名称)[VARCHAR]<NOT NULL>, OPERATING_SYSTEM(操作系统)[VARCHAR]<NOT NULL>, OPERATING_SYSTEM_VERSION(操作系统版本), IP(IP地址)<NOT NULL>, DATABASE(数据库及版本)[VARCHAR]<NOT NULL>, APP_ID(所属应用ID)[BIGINT]<NOT NULL>, CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "APP_MIDDLEWARE", "table_comment": "应用中间件表3322", "field_count": 8, "content_length": 233, "content": "表名: APP_MIDDLEWARE(应用中间件表3322)\n字段: ID(ID)<PK,NOT NULL>, MIDDLEWARE_NAME(中间件名称)[VARCHAR]<NOT NULL>, VERSION(版本)[VARCHAR]<NOT NULL>, APP_ID(所属应用ID)[BIGINT]<NOT NULL>, CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "APP_CONTACT", "table_comment": "应用联系人表3322", "field_count": 12, "content_length": 294, "content": "表名: APP_CONTACT(应用联系人表3322)\n字段: ID(ID)<PK,NOT NULL>, CONTACT_NAME(联系人名称)[VARCHAR], CONTACT_JOB(联系人职务)[VARCHAR], CONTACT_DEPARTMENT(联系人部门), OFFICE_PHONE(办公电话), MOBILE_PHONE(移动电话), EMAIL(电子邮件), APP_ID(所属应用ID)[BIGINT]<NOT NULL>, CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "APP_BUSINESS_DESCRIPTION", "table_comment": "应用业务描述表3322", "field_count": 13, "content_length": 342, "content": "表名: APP_BUSINESS_DESCRIPTION(应用业务描述表3322)\n字段: ID(ID)<PK,NOT NULL>, APP_UNIT(应用使用单位)[VARCHAR], APP_USER(应用使用人员)[VARCHAR], APP_SCENARIO(应用使用场景), BUSINESS_DESCRIPTION(业务描述), BUSINESS_FUNCTION(业务功能), KINDS_OF_INFORMATION(信息种类), KEY_DATA(关键数据), APP_ID(所属应用ID)[BIGINT]<NOT NULL>, CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DELIVER_FILE", "table_comment": "密评交付模板(332)", "field_count": 11, "content_length": 239, "content": "表名: DELIVER_FILE(密评交付模板(332))\n字段: ID(主键)<PK,NOT NULL>, FILE_NAME(文件名), FILE_TYPE(类型: 0-word 1-excel), FILE_ID(文件id), BUSINESS_TYPE(业务类型-关联字典), BUILD_IN(是否内置), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "CCASE_DELIVER_REL", "table_comment": "密评对象交付模板(332)", "field_count": 9, "content_length": 202, "content": "表名: CCASE_DELIVER_REL(密评对象交付模板(332))\n字段: ID<PK,NOT NULL>, CCASE_ID(密评对象id), DELIVER_FILE_ID(交付模板id), BUSINESS_TYPE(业务类型), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "FILE_INFO", "table_comment": "文件信息(332)", "field_count": 13, "content_length": 274, "content": "表名: FILE_INFO(文件信息(332))\n字段: ID(主键)<PK,NOT NULL>, FILE_NAME(文件名称), FILE_REMOTE_PATH(文件路径), FILE_DIGEST(文件摘要), FILE_BUSINESS(文件业务类型), FILE_SIZE(文件大小/字节)[BIGINT(32)], STATUS(文件状态), HMAC(完整性校验字段), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DIC_SERVICE_SPECIFICATION", "table_comment": "服务规格字典表( V3.4.0)", "field_count": 12, "content_length": 352, "content": "表名: DIC_SERVICE_SPECIFICATION(服务规格字典表( V3.4.0))\n字段: ID(Id)<PK,NOT NULL>, RESOURCE_NAME(资源规格名称)<PK,NOT NULL>, CPU_SIZE(CPU核数)<PK,NOT NULL>, MEMORY_SIZE(内存大小（G）)<PK,NOT NULL>, SERVICE_TYPE_ID(服务类型ID)<DEFAULT:-1>, SERVICE_CONFIG_INFO(服务配置信息), DYNAMIC_FLAG(是否支持动态资源配置：0否，1是), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "SYSLOG_SERVER", "table_comment": "syslog（nmg）", "field_count": 13, "content_length": 335, "content": "表名: SYSLOG_SERVER(syslog（nmg）)\n字段: ID(主键)<PK,NOT NULL>, SYSLOG_SERVER_IP(服务器IP), SYSLOG_SERVER_PORT(服务器Port), PROTOCOL_TYPE(协议类型;1 udp 2tcp), ENABLE_SSL(是否开启ssl;1 开启 2 不开启 默认不开启), PUSH_FILE_TYPE(上报类型;1 操作日志 2 待定), FREQUENCY(上报频率), FREQUENCY_UNIT(上报频率单位), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DEVICE_MASTER_BACKUP_RECORD", "table_comment": "密钥备份记录表 (*******)", "field_count": 10, "content_length": 288, "content": "表名: DEVICE_MASTER_BACKUP_RECORD(密钥备份记录表 (*******))\n字段: ID<PK,NOT NULL>, TENANT_ID<NOT NULL>, BACKUP_TYPE(1-主密钥，2-内部密钥)<NOT NULL>, BACKUP_ENCRYPT_TYPE(1-口令加密，2-ukey加密)<NOT NULL>, BACKUP_STATUS(0-备份成功，1-备份失败), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "SUB_PLATFORM", "table_comment": "下级平台表（332 nmg）", "field_count": 19, "content_length": 711, "content": "表名: SUB_PLATFORM(下级平台表（332 nmg）)\n字段: SUB_PlATFORM_ID(下级平台ID)<PK,NOT NULL>, SUB_PlATFORM_CODE(下级平台标识)[VARCHAR(50)]<NOT NULL>, SUB_PlATFORM_NAME(下级平台名称)[VARCHAR(200)]<NOT NULL>, SUB_PlATFORM_TYPE(下级平台类型;1：CCSP；2：CPCS)<NOT NULL>, SUB_PlATFORM_IP(下级平台IP)[VARCHAR(128)]<NOT NULL>, SUB_PlATFORM_PORT(下级平台端口)<NOT NULL>, SUB_PlATFORM_URL(下级平台管理页面入口)<NOT NULL>, SUB_PlATFORM_TENANT_URL(下级平台租户页面入口)<NOT NULL>, SUB_PlATFORM_PUBLIC_KEY(下级平台交互公钥)<NOT NULL>, SUB_PlATFORM_ACCOUNT(下级平台账号)[VARCHAR(50)], SUB_PlATFORM_AUTH_CODE(下级平台密码)[VARCHAR(32)], SUB_PLATFORM_STATUS(下级平台状态), NET_PARTITION_ID(所属网络分区ID;NMG add), MACHINE_ROOM_ID(所属机房ID;NMG add), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "NET_PARTITION", "table_comment": "网络分区(nmg)", "field_count": 8, "content_length": 229, "content": "表名: NET_PARTITION(网络分区(nmg))\n字段: NET_PARTITION_ID(网络分区ID)<PK,NOT NULL>, NET_PARTITION_NAME(网络分区名称)[VARCHAR(200)]<NOT NULL>, MACHINE_ROOM_ID(所属机房ID), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "MACHINE_ROOM", "table_comment": "机房信息(nmg)", "field_count": 8, "content_length": 235, "content": "表名: MACHINE_ROOM(机房信息(nmg))\n字段: MACHINE_ROOM_ID(机房ID)<PK,NOT NULL>, MACHINE_ROOM_NAME(机房名称)[VARCHAR(200)]<NOT NULL>, MACHINE_ROOM_ADR(机房地址)[VARCHAR(600)], REMARK(描述), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "SYSlOG_RECORD", "table_comment": "syslog上报记录（nmg）", "field_count": 9, "content_length": 214, "content": "表名: SYSlOG_RECORD(syslog上报记录（nmg）)\n字段: ID(主键)<PK,NOT NULL>, SYSLOG_SERVER_ID(syslog服务器ID), DATA_SIZE(日志上报数量), CREATE_TIME(上报时间), STATUS(上报状态;1 成功 2 失败), REMARK(备注), CREATE_BY(创建人), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "STATISTIC_KMS_KEY", "table_comment": "密钥统计（nmg）", "field_count": 15, "content_length": 327, "content": "表名: STATISTIC_KMS_KEY(密钥统计（nmg）)\n字段: ID(ID)<PK,NOT NULL>, KEY_STATE(密钥状态), KEY_ALG(密钥算法), TENANT_CODE(租户标识), SERVICE_ID(服务ID), SERVICE_GROUP_ID(服务组id), REGION_ID(区域ID), KEY_NUM(密钥数量), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间), NET_PARTITION_ID(所属网络分区ID;上级平台), MACHINE_ROOM_ID(所属机房ID;上级平台)", "type": "entity", "source": "json_file"}, {"table_name": "BACKUP_STRATEGY", "table_comment": "备份策略（nmg）", "field_count": 18, "content_length": 512, "content": "表名: BACKUP_STRATEGY(备份策略（nmg）)\n字段: ID(主键)<PK,NOT NULL>, DATABASE_ID(数据库ID;对应DATABASE表ID)<NOT NULL>, DATABASE_TYPE_ID(数据库类型ID)<NOT NULL>, BACKUP_METHOD(备份方式;1 物理备份 2 逻辑备份), BACKUP_SCOPE(备份范围;1全部数据 2指定服务组), BACKUP_TYPE(备份类型;1 全量备份 2增量备份), BACKUP_FREQUENCY(备份频率;备份频率), BACKUP_FREQUENCY_UNIT(备份频率单位;备份频率单位), ENCRYPT_FLAG(是否加密;1加密  0不加密), ENCRYPT_PWD(加密口令;明文展示，密文存储), STORAGE_TYPE(存储位置;1本地 2远程), STATUS(状态;1启用 2 异常), SYS_JOB_ID(定时任务Id), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "BACKUP_RECORDS", "table_comment": "备份记录（nmg）", "field_count": 18, "content_length": 571, "content": "表名: BACKUP_RECORDS(备份记录（nmg）)\n字段: ID(主键)<PK,NOT NULL>, DATABASE_ID(数据库ID;对应DATABASE表ID)<NOT NULL>, DATABASE_TYPE_ID(数据库类型ID)<NOT NULL>, BACKUP_FILE_NAME(备份文件名称;备份文件名称), BACKUP_SCOPE(备份范围;1全部数据 2指定服务组), BACKUP_TYPE(备份类型;1 全量备份 2增量备份), BACKUP_METHOD(备份方式;1物理备份 2 逻辑备份), ENCRYPT_FLAG(是否加密;1 加密  0不加密), ENCRYPT_PWD(加密口令;明文展示，密文存储), STORAGE_LOCATION(存储地址;具体的数据库节点ip), BACKUP_MODE(备份文件产生方式;1 自动备份 2 手动备份), SERVICE_GROUP_IDS(服务组id列表;服务组id列表，字符串，逗号分隔)[VARCHAR(1000)], STATUS(状态;1 成功 2失败，3备份中 4已删除), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "RESTORE_RECORDS", "table_comment": "还原记录（nmg）", "field_count": 13, "content_length": 444, "content": "表名: RESTORE_RECORDS(还原记录（nmg）)\n字段: ID(主键)<PK,NOT NULL>, DATABASE_ID(数据库ID;对应DATABASE表ID)<NOT NULL>, DATABASE_TYPE_ID(数据库类型ID)<NOT NULL>, BACKUP_RECORDS_ID(备份记录ID), RESTORE_FILE_NAME(还原文件名称;对应备份文件名称), SERVICE_GROUP_IDS(服务组id列表;服务组id列表，字符串，逗号分隔)[VARCHAR(1000)], DATABASE_MINIMUM_UNIT_IDS(还原的模式/实例ID列表;还原的模式/实例的id列表，字符串，逗号分隔)[VARCHAR(1000)], STATUS(状态;1成功 2失败 3还原中), CREATE_TIME(还原时间), CREATE_BY(执行人), REMARK(备注), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "CLEAN_BACKUP_STRATEGY", "table_comment": "备份记录清理策略（nmg）", "field_count": 11, "content_length": 290, "content": "表名: CLEAN_BACKUP_STRATEGY(备份记录清理策略（nmg）)\n字段: ID(主键)<PK,NOT NULL>, DATABASE_ID(数据库ID;对应DATABASE表ID), DATABASE_TYPE_ID(数据库类型ID)[BIGINT], RETENTION_PERIOD(保留周期), RETENTION_PERIOD_UNIT(保留周期单位), STATUS(状态;1成功 0失败), CREATE_BY(创建人), CREATE_TIME(创建时间), REMARK(备注), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "BACKUP_STRATRGY_TO_SERVICE_GROUP", "table_comment": "备份策略和服务组关系（nmg）", "field_count": 8, "content_length": 222, "content": "表名: BACKUP_STRATRGY_TO_SERVICE_GROUP(备份策略和服务组关系（nmg）)\n字段: ID(主键)<PK,NOT NULL>, BACKUP_STRATEGY_ID(备份策略ID)<NOT NULL>, SERVICE_GROUP_ID(服务组ID), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "INSP_QUOTA", "table_comment": "巡检指标（nmg）", "field_count": 11, "content_length": 239, "content": "表名: INSP_QUOTA(巡检指标（nmg）)\n字段: ID(主键), NAME(名称), CODE(编码), DATATYPE(数据类型：数值/字符/百分比), COLLECT_TYPE(采集类型: 1-自定义 2-监控  3-接口), ERROR_EXPRESSION(异常表达式：${value}=异常), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "INSP_ITEM", "table_comment": "巡检项（nmg）", "field_count": 10, "content_length": 205, "content": "表名: INSP_ITEM(巡检项（nmg）)\n字段: ID(主键), INSP_TYPE(类型：数据库/平台/服务/设备), INSP_SUB_TYPE(子类型), INSP_SUB_TYPE_NAME(子类型名称), ENABLE(是否启用), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "INSP_TYPE_QUOTA_REL", "table_comment": "巡检类型指标关联（nmg）", "field_count": 9, "content_length": 201, "content": "表名: INSP_TYPE_QUOTA_REL(巡检类型指标关联（nmg）)\n字段: ID(主键), QUOTA_ID(巡检指标id)[BIGINT(255)], INSP_TYPE(巡检类型), INSP_SUB_TYPE(巡检子类型), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "INSP_REPORT", "table_comment": "巡检报告（nmg）", "field_count": 12, "content_length": 272, "content": "表名: INSP_REPORT(巡检报告（nmg）)\n字段: ID(主键), EXECUTE_TYPE(巡检类型：手动、自动), EXECUTE_STATUS(状态：巡检中、巡检完成), INSP_OVERALL_RESULT(巡检总体结果：正常、异常), ERROR_INFO(异常说明)[TEXT(255)], START_TIME(开始时间), END_TIME(结束时间), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "INSP_QUOTA_RECORD", "table_comment": "巡检指标记录（nmg）", "field_count": 26, "content_length": 530, "content": "表名: INSP_QUOTA_RECORD(巡检指标记录（nmg）)\n字段: ID(主键), REPORT_ID(所属报告)[BIGINT], IP(ip), PORT(端口), INSP_TYPE(类型), INSP_SUB_TYPE_NAME(子类型名称), INSP_SUB_TYPE(子类型), UNIQUE_NAME(标识), START_TIME(开始时间), END_TIME(结束时间), TENANT_ID(租户id)[BIGINT], TENANT_NAME(租户名称), REGION_ID(区域id)[BIGINT], REGION_NAME(区域名称), USE(使用方：管理、业务、管理/业务), QUOTA_CODE(指标项), QUOTA_NAME(指标项名称), QUOTA_VALUE(指标值), INSP_ITEM_RESULT(检查结果：正常/异常), NET_PARTITION_ID(所属网络分区ID), MACHINE_ROOM_ID(所属机房ID), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "INSP_ITEM_POLICY", "table_comment": "巡检项策略(nmg)", "field_count": 11, "content_length": 276, "content": "表名: INSP_ITEM_POLICY(巡检项策略(nmg))\n字段: ID(主键), POLICY_ID(巡检策略id)[BIGINT], INSP_TYPE(巡检类型：数据库、平台、服务、设备), INSP_SUB_TYPE(子类型：暂时不用), RECORD_GENERATE_POLICY(记录生成策略：全部、异常), RECORD_RETENTION_DAYS(记录保留天数), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "INSP_POLICY", "table_comment": "巡检策略（nmg）", "field_count": 9, "content_length": 158, "content": "表名: INSP_POLICY(巡检策略（nmg）)\n字段: ID(主键), NAME(名称), CRON(cron表达式), ENABLE(是否启用), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "FILE_INFO1", "table_comment": "文件信息(332)", "field_count": 13, "content_length": 275, "content": "表名: FILE_INFO1(文件信息(332))\n字段: ID(主键)<PK,NOT NULL>, FILE_NAME(文件名称), FILE_REMOTE_PATH(文件路径), FILE_DIGEST(文件摘要), FILE_BUSINESS(文件业务类型), FILE_SIZE(文件大小/字节)[BIGINT(32)], STATUS(文件状态), HMAC(完整性校验字段), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "upgrade_job", "table_comment": "升级作业表", "field_count": 11, "content_length": 297, "content": "表名: upgrade_job(升级作业表)\n字段: ID(主键)<PK,NOT NULL>, PARENT_ID(父级作业主键，回滚时关联升级记录), PACKAGE_ID(升级包主键，关联upgrade_package_info主键), DISPATCH_NODE(调度节点(ip)), JOB_TYPE(作业类型（0.升级 1.回滚）), JOB_STATUS(作业状态（0.等待中 1.执行中 2.执行成功 3执行失败）), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "upgrade_task", "table_comment": "升级任务表", "field_count": 11, "content_length": 291, "content": "表名: upgrade_task(升级任务表)\n字段: ID(主键)<PK,NOT NULL>, JOB_ID(升级作业主键)<NOT NULL>, WORK_NODE(工作节点(ip)), TASK_TYPE(任务类型（0.db 1.service）), TASK_STATUS(任务状态（0.等待中 1.执行中 2.执行成功 3执行失败）), TASK_ORDER(任务顺序，正序，数字小的先执行)[BIGINT], REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "upgrade_cluster_association", "table_comment": "集群-升级作业关联表", "field_count": 9, "content_length": 247, "content": "表名: upgrade_cluster_association(集群-升级作业关联表)\n字段: ID(主键)<PK,NOT NULL>, UPGRADE_JOB_ID(升级作业主键), CLUSTER_ID(集群主键(如果是密码子服务集群，则关联服务组主键)), CLUSTER_TYPE(集群类型(0平台、1-11密码子服务)), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "upgrade_package_info", "table_comment": "升级包信息表", "field_count": 12, "content_length": 320, "content": "表名: upgrade_package_info(升级包信息表)\n字段: ID(主键)<PK,NOT NULL>, PACKAGE_NAME(升级包名称), SOURCE_VERSION(升级前版本), TARGET_VERSION(升级后版本), FILE_DIGEST(升级包摘要), PACKAGE_TYPE(升级包类型（0.平台 1-11按照service_type表划分）), UPLOAD_STATUS(上传状态(0.上传中 1上传成功 2上传失败 3.使用中)), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "product_version", "table_comment": "产品版本表", "field_count": 11, "content_length": 249, "content": "表名: product_version(产品版本表)\n字段: ID(主键)<PK,NOT NULL>, PRODUCT_CODE(产品简称), PRODUCT_NAME(产品名称), PRODUCT_VERSION(产品版本), SERVER_TYPE_ID(服务类型(0平台，1-xxx其他密码子服务)), SORT_NUM(排序), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "cluster_version", "table_comment": "集群版本表", "field_count": 9, "content_length": 209, "content": "表名: cluster_version(集群版本表)\n字段: ID(主键)<PK,NOT NULL>, CLUSTER_TYPE(集群类型(0.平台，1-xx密码子服务)), CLUSTER_ID(集群id), CLUSTER_VERSION(集群版本), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "HOST_MACHINE", "table_comment": "宿主机信息（V3.3.3）", "field_count": 15, "content_length": 301, "content": "表名: HOST_MACHINE(宿主机信息（V3.3.3）)\n字段: ID(ID), IP(IP), NAME(宿主机名称), CPU_COUNT(cpu核数)[INT(255)], MEMORY_GB(内存大小)[INT(255)], DISK_GB(硬盘大小), OS(操作系统), CPU_ARCH(cpu架构), REMOTE_PORT(管控端口)[INT(255)], REGION_ID(区域ID)[BIGINT(255)], REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DEVICE_INFO", "table_comment": "设备信息(1030,V3.3.4)", "field_count": 43, "content_length": 1452, "content": "表名: DEVICE_INFO(设备信息(1030,V3.3.4))\n字段: DEVICE_ID(设备ID)[BIGINT]<PK,NOT NULL>, DEVICE_SELF_ID(设备内部ID)[VARCHAR(100)], DEVICE_NAME(设备名称)[VARCHAR(100)]<NOT NULL>, VENDOR_ID(厂商ID)[BIGINT]<NOT NULL>, DEVICE_TYPE_ID(设备类型ID)[BIGINT]<NOT NULL>, FAMILY_TYPE(设备物理类型：1：云密码机；2：物理机；3：虚拟机)[INT], HCCS_DEVICE_ID(所属宿主机ID（虚拟机使用）)[BIGINT], HCCS_VSM_TOTAL(可创建虚拟机总数)[INT], HCCS_VSM_USABLE(可创建虚拟机数)[INT], HCCS_VSM_USED(已创建数量)[INT], DEVICE_GROUP_ID(设备组ID)[BIGINT], MGT_IP(管理IP)[VARCHAR(40)]<NOT NULL>, MGT_PORT(管理端口)[INT]<NOT NULL>, BUSI_IP(服务IP)[VARCHAR(40)]<NOT NULL>, BUSI_PORT(服务端口)[INT]<NOT NULL>, CONNECT_PASSWORD(服务连接密码)[VARCHAR(200)], CONNECT_PROTOCOL(协议（HTTP、HTTPS）)[VARCHAR(40)], DEVICE_VERSION(设备版本号)[VARCHAR(100)], DEVICE_SERIALNUM(设备序列号)[VARCHAR(100)], REGION_ID(区域ID), WEB_URL(设备管理平台URL), PUBLIC_KEY(公钥)[TEXT], PUBLIC_KEY_FINGER(公钥指纹)[TEXT], DEVICE_WEIGHT(设备权重)[DECIMAL], AUTH_CODE(授权码)[VARCHAR(200)], VSM_RESOURCE(资源分配;虚机资源分配), HMAC(HMAC)[VARCHAR(200)], CLOUD_TOKEN(云机回调token)[VARCHAR], MANAGEMENT_STATUS_ID(使用状态ID)[BIGINT], OPER_STATUS(操作状态ID)[BIGINT], MASTER_KEY_FLAG(是否生成主密钥(1是，0否);默认0)[INT]<DEFAULT:0>, INVALID_FLAG(是否作废;默认为0)[TEXT], REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR], CONNECT_AUTH_CODE(服务连接密码)[VARCHAR(200)], RUN_STATUS(运行状态：0：未运行，1：运行中，2：获取出错)[INT], BUSI_PORT_EXTEND(扩展端口)[INT]<NOT NULL,DEFAULT:8008>, CONTROL_IP(总控机IP), CONTROL_NAME(总控机账号), CONTROL_PASSWORD(总控机密码)", "type": "entity", "source": "json_file"}, {"table_name": "HW_LICENSE_APPLY", "table_comment": "华为许可证申请表（V3.4.0）", "field_count": 11, "content_length": 299, "content": "表名: HW_LICENSE_APPLY(华为许可证申请表（V3.4.0）)\n字段: ID(主键)<PK,NOT NULL>, LICENSE_TYPE_ID(许可规格ID)<NOT NULL>, APPLY_TYPE(申请类型;1：激活；2：扩容)<NOT NULL>, LICENSE_APPLY_INFO(许可内容)<NOT NULL>, APPLY_STATUS(申请状态;1：申请中；2：已导入), HMAC(完整性校验值), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "DIC_HW_LICENSE_TYPE", "table_comment": "华为许可规格(V3.4.0)", "field_count": 5, "content_length": 183, "content": "表名: DIC_HW_LICENSE_TYPE(华为许可规格(V3.4.0))\n字段: ID(主键)<PK,NOT NULL>, LICENSE_TYPE_NAME(许可规格名称)<NOT NULL>, LIMIT_TYPE(许可限制类型;1：应用；2：密码服务)<NOT NULL>, LIMIT_NUM(许可限制数量)<NOT NULL>, REMARK(备注)", "type": "entity", "source": "json_file"}, {"table_name": "HW_LICENSE_INFO", "table_comment": "华为许可证表(V3.4.0)", "field_count": 11, "content_length": 296, "content": "表名: HW_LICENSE_INFO(华为许可证表(V3.4.0))\n字段: ID(主键)<PK,NOT NULL>, LICENSE_APPLY_ID(申请许可记录ID)<NOT NULL>, LICENSE_TYPE_ID(许可规格ID)<NOT NULL>, APPLY_TYPE(申请类型;1：激活；2：扩容)<NOT NULL>, LICENSE_INFO(许可内容)<NOT NULL>, HMAC(完整性校验值), REMARK(备注), CREATE_BY(创建人), CREATE_TIME(创建时间), UPDATE_BY(更新人), UPDATE_TIME(更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "WHITELIST", "table_comment": "白名单(V3.4.0)", "field_count": 12, "content_length": 383, "content": "表名: WHITELIST(白名单(V3.4.0))\n字段: WHITE_LIST_ID(白名单id;白名单id)[BIGINT(255)]<PK,NOT NULL>, CLIENT_CODE(客户端标识;客户端标识)<NOT NULL>, IP(IP;IP)<NOT NULL>, ACCESS_PORT(接入端口;接入端口), UNIT(单位名称;单位名称), OPEN_STATUS(状态，0：开启，1：关闭;状态，0：开启，1：关闭), IP_TYPE(白名单类型，1：普通ip，2网关ip;白名单类型，1：普通ip，2网关ip)[BIGINT(4)], REMARK(备注;备注), CREATE_BY(创建人;创建人), CREATE_TIME(创建时间;创建时间), UPDATE_BY(更新人;更新人), UPDATE_TIME(更新时间;更新时间)", "type": "entity", "source": "json_file"}, {"table_name": "USER_ROOT_CA", "table_comment": "用户根证书(V3.4.0)", "field_count": 16, "content_length": 503, "content": "表名: USER_ROOT_CA(用户根证书(V3.4.0))\n字段: ID[BIGINT]<PK,NOT NULL>, CERT_TYPE(证书类型;1 ukey证书)[VARCHAR], CERT_FORMAT(证书格式;证书类型 x509)[VARCHAR], START_TIME(证书起始时间;证书起始时间)[VARCHAR], END_TIME(证书过期时间;证书过期时间)[VARCHAR], SERIAL(序列号;证书序列号), SUBJECT_DN(使用者;使用者), ISSUER(颁发者;颁发者)[VARCHAR], PUBLIC_KEY(证书公钥;证书公钥)[VARCHAR], VERIFY_CERT(验签证书（BASE64编码）;验签证书（BASE64编码）)[VARCHAR], VERSION(版本;版本)[VARCHAR], REMARK(备注)[VARCHAR], CREATE_BY(创建人)[BIGINT], CREATE_TIME(创建时间)[VARCHAR], UPDATE_BY(更新人)[BIGINT], UPDATE_TIME(更新时间)[VARCHAR]", "type": "entity", "source": "json_file"}]