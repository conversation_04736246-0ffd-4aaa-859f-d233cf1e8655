
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>流程图测试结果1</title>
    <style>
        .flowchart-container {
            position: relative;
            min-height: 400px;
            background: #f9f9f9;
            padding: 20px;
            overflow: auto;
            border: 1px solid #ddd;
            margin: 20px;
        }
        
        .participant-box {
            position: absolute;
            top: 20px;
            width: 120px;
            height: 60px;
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border: 2px solid #2196f3;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #1976d2;
            text-align: center;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .interaction {
            position: absolute;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .arrow {
            position: relative;
            width: 100%;
            height: 2px;
            background: #333;
        }
        
        .arrow-right::after {
            content: '';
            position: absolute;
            right: -8px;
            top: -4px;
            width: 0;
            height: 0;
            border-left: 8px solid #333;
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
        }
        
        .arrow-left::after {
            content: '';
            position: absolute;
            left: -8px;
            top: -4px;
            width: 0;
            height: 0;
            border-right: 8px solid #333;
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
        }
        
        .dashed-arrow {
            background: repeating-linear-gradient(
                to right,
                #333 0px,
                #333 5px,
                transparent 5px,
                transparent 10px
            );
        }
        
        .message {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <h1>流程图测试结果</h1>
    <h2>测试用例1：用户登录流程</h2>
    <div class="flowchart-container">
        <div class="participant-box" data-id="User" style="left: 50px;">
            用户
        </div>
        
        <div class="participant-box" data-id="System" style="left: 230px;">
            系统
        </div>
        
        <div class="participant-box" data-id="DB" style="left: 410px;">
            数据库
        </div>
        
        <div class="interaction" style="top: 120px; left: 110px; width: 180px;">
            <div class="arrow solid-arrow arrow-right"></div>
            <div class="message">提交登录请求</div>
        </div>
        
        <div class="interaction" style="top: 180px; left: 290px; width: 0px;">
            <div class="arrow solid-arrow arrow-left"></div>
            <div class="message">输入参数验证(E)</div>
        </div>
        
        <div class="interaction" style="top: 240px; left: 290px; width: 180px;">
            <div class="arrow solid-arrow arrow-right"></div>
            <div class="message">验证登录信息(R)</div>
        </div>
        
        <div class="interaction" style="top: 300px; left: 290px; width: 180px;">
            <div class="arrow solid-arrow arrow-right"></div>
            <div class="message">保存登录信息(W)</div>
        </div>
        
        <div class="interaction" style="top: 360px; left: 110px; width: 180px;">
            <div class="arrow solid-arrow arrow-left"></div>
            <div class="message">返回登录结果(X)</div>
        </div>
        </div>
    
    <h2>测试用例2：数据查询流程</h2>
    <div class="flowchart-container">
        <div class="participant-box" data-id="User" style="left: 50px;">
            用户
        </div>
        
        <div class="participant-box" data-id="System" style="left: 230px;">
            系统
        </div>
        
        <div class="participant-box" data-id="Cache" style="left: 410px;">
            缓存
        </div>
        
        <div class="participant-box" data-id="DB" style="left: 590px;">
            数据库
        </div>
        
        <div class="interaction" style="top: 120px; left: 110px; width: 180px;">
            <div class="arrow solid-arrow arrow-right"></div>
            <div class="message">发起查询请求</div>
        </div>
        
        <div class="interaction" style="top: 180px; left: 290px; width: 180px;">
            <div class="arrow solid-arrow arrow-right"></div>
            <div class="message">检查缓存数据(R)</div>
        </div>
        
        <div class="interaction" style="top: 240px; left: 290px; width: 360px;">
            <div class="arrow solid-arrow arrow-right"></div>
            <div class="message">查询数据库(R)</div>
        </div>
        
        <div class="interaction" style="top: 300px; left: 290px; width: 180px;">
            <div class="arrow solid-arrow arrow-right"></div>
            <div class="message">更新缓存(W)</div>
        </div>
        
        <div class="interaction" style="top: 360px; left: 110px; width: 180px;">
            <div class="arrow solid-arrow arrow-left"></div>
            <div class="message">返回查询结果(X)</div>
        </div>
        </div>
</body>
</html>
        