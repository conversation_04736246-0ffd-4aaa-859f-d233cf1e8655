#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试多文件和JSON格式支持
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from knowledge_base import knowledge_base

def test_multi_files_support():
    """测试多文件和JSON格式支持"""
    print("=== 多文件和JSON格式支持测试 ===\n")
    
    # 1. 检查知识库状态
    print("1. 知识库状态检查")
    print(f"   - 知识库启用: {'是' if knowledge_base.enabled else '否'}")
    print(f"   - 功能文档数量: {len(knowledge_base.function_docs)}")
    print(f"   - 数据实体数量: {len(knowledge_base.entity_docs)}")
    print()
    
    # 2. 检查功能文档来源
    print("2. 功能文档来源分析")
    if knowledge_base.function_docs:
        sources = {}
        for doc in knowledge_base.function_docs:
            source = doc.get('source', 'unknown')
            sources[source] = sources.get(source, 0) + 1
        
        for source, count in sources.items():
            print(f"   - {source}: {count} 条")
        
        # 显示不同来源的示例
        print("\n   示例文档:")
        shown_sources = set()
        for doc in knowledge_base.function_docs[:10]:
            source = doc.get('source', 'unknown')
            if source not in shown_sources:
                print(f"   [{source}] {doc['title'][:50]}...")
                shown_sources.add(source)
    else:
        print("   - 未找到功能文档")
    print()
    
    # 3. 检查数据实体来源
    print("3. 数据实体来源分析")
    if knowledge_base.entity_docs:
        sources = {}
        for doc in knowledge_base.entity_docs:
            source = doc.get('source', 'unknown')
            sources[source] = sources.get(source, 0) + 1
        
        for source, count in sources.items():
            print(f"   - {source}: {count} 条")
        
        # 显示不同来源的示例
        print("\n   示例实体:")
        shown_sources = set()
        for doc in knowledge_base.entity_docs[:10]:
            source = doc.get('source', 'unknown')
            if source not in shown_sources:
                table_name = doc.get('table_name', 'Unknown')
                field_count = len(doc.get('fields', []))
                print(f"   [{source}] {table_name} ({field_count} 字段)")
                shown_sources.add(source)
    else:
        print("   - 未找到数据实体")
    print()
    
    # 4. 测试JSON格式解析效果
    print("4. JSON格式解析效果测试")
    json_entities = [doc for doc in knowledge_base.entity_docs if doc.get('source') == 'json_file']
    if json_entities:
        print(f"   - JSON实体数量: {len(json_entities)}")
        
        # 显示一个详细的JSON实体示例
        sample_entity = json_entities[0]
        print(f"   - 示例实体: {sample_entity['table_name']}")
        print(f"     表注释: {sample_entity.get('table_comment', 'N/A')}")
        print(f"     字段数量: {len(sample_entity.get('fields', []))}")
        
        # 显示前几个字段的详细信息
        fields = sample_entity.get('fields', [])[:3]
        for field in fields:
            print(f"     - {field['name']}: {field['type']} ({field['comment']})")
    else:
        print("   - 未找到JSON格式的实体")
    print()
    
    # 5. 测试搜索功能
    print("5. 多源搜索功能测试")
    test_queries = ["用户", "密码", "配置", "证书"]
    
    for query in test_queries:
        results = knowledge_base.search_knowledge(query)
        print(f"   查询 '{query}': 找到 {len(results)} 个结果")
        
        # 按来源分组显示结果
        sources = {}
        for result in results:
            source = result.get('source', 'unknown')
            if source not in sources:
                sources[source] = []
            sources[source].append(result)
        
        for source, source_results in sources.items():
            print(f"     [{source}]: {len(source_results)} 个")
            for result in source_results[:2]:  # 只显示前2个
                title = result.get('title', result.get('table_name', 'Unknown'))
                print(f"       - {title[:40]}... (相似度: {result['similarity']:.3f})")
    print()
    
    # 6. 测试上下文生成
    print("6. 多源上下文生成测试")
    context = knowledge_base.get_context_for_module(
        "用户管理",
        "用户权限管理", 
        "用户信息管理",
        "管理用户的基本信息，包括用户ID、用户名、密码等"
    )
    
    if context:
        print("   生成的上下文:")
        lines = context.split('\n')
        for line in lines[:15]:  # 只显示前15行
            print(f"   {line}")
        if len(lines) > 15:
            print(f"   ... (还有 {len(lines) - 15} 行)")
    else:
        print("   未生成上下文")

if __name__ == "__main__":
    test_multi_files_support()
