import os
from typing import List, Dict, Any, Tuple
from collections import defaultdict

class ModuleAllocator:
    """二级模块分配器"""
    
    @staticmethod
    def allocate_modules_to_threads(level_2_modules: List[str], thread_count: int) -> List[List[str]]:
        """
        将二级模块分配到线程
        
        Args:
            level_2_modules: 二级模块列表
            thread_count: 线程数量
            
        Returns:
            每个线程分配的模块列表
        """
        if not level_2_modules:
            return []
        
        # 如果线程数大于等于模块数，每个模块一个线程
        if thread_count >= len(level_2_modules):
            return [[module] for module in level_2_modules]
        
        # 计算每个线程应该分配的模块数
        modules_per_thread = len(level_2_modules) // thread_count
        remaining_modules = len(level_2_modules) % thread_count
        
        allocations = []
        start_idx = 0
        
        for i in range(thread_count):
            # 前面的线程多分配一个模块（如果有余数）
            current_count = modules_per_thread + (1 if i < remaining_modules else 0)
            end_idx = start_idx + current_count
            
            allocations.append(level_2_modules[start_idx:end_idx])
            start_idx = end_idx
        
        return allocations
    
    @staticmethod
    def group_data_by_level2(df_data: List[Dict[str, Any]], level_2_name: str) -> Dict[str, List[Dict[str, Any]]]:
        """
        按二级模块分组数据
        
        Args:
            df_data: DataFrame转换的数据列表
            level_2_name: 二级模块列名
            
        Returns:
            按二级模块分组的数据字典
        """
        grouped_data = defaultdict(list)
        
        for row in df_data:
            level_2_module = str(row.get(level_2_name, ''))
            if level_2_module and level_2_module.strip().lower() not in ['nan', 'none', '']:
                grouped_data[level_2_module].append(row)
        
        return dict(grouped_data)
    
    @staticmethod
    def get_optimal_thread_count(level_2_count: int, max_threads: int = None) -> int:
        """
        获取最优线程数
        
        Args:
            level_2_count: 二级模块数量
            max_threads: 最大线程数限制
            
        Returns:
            最优线程数
        """
        if max_threads is None:
            max_threads = os.cpu_count() or 4
        
        # 线程数不能超过模块数
        optimal_threads = min(level_2_count, max_threads)
        
        # 至少1个线程
        return max(1, optimal_threads)
