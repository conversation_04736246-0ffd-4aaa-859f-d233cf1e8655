#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试汇总建议生成功能
"""

import sys
import os
import json

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cosmic_validator import CosmicValidator
import config

def test_summary_generation():
    """测试汇总建议生成功能"""
    print("开始测试汇总建议生成功能...")
    
    # 创建校验器实例
    validator = CosmicValidator(config)
    
    # 模拟合并后的校验结果
    merged_validation = {
        "batch_count": 2,
        "summary": {
            "total_issues_found": 5,
            "total_records": 100,
            "severity_statistics": {
                "counts": {"高": 2, "中": 2, "低": 1},
                "percentages": {"高": 40.0, "中": 40.0, "低": 20.0}
            },
            "all_findings": [
                {
                    "module_path": "模块1/子模块1/功能1",
                    "function_process": "过程1",
                    "issue_type": "数据组聚合",
                    "severity": "高",
                    "issue_description": "数据组命名不规范"
                },
                {
                    "module_path": "模块2/子模块2/功能2",
                    "function_process": "过程2",
                    "issue_type": "重复计数",
                    "severity": "中",
                    "issue_description": "存在重复计数"
                }
            ],
            "all_recommendations": [
                "优先解决数据组命名问题",
                "检查重复计数情况"
            ]
        }
    }
    
    # 模拟大模型调用，专门返回汇总建议
    def mock_summary_llm_call(prompt, data):
        """模拟汇总建议的大模型调用"""
        print(f"调用大模型生成汇总建议...")
        print(f"提示词包含关键字: {'汇总建议' in prompt}")
        
        return json.dumps({
            "priority_recommendations": [
                "优先解决高严重程度的数据组聚合问题，这类问题影响CFP计算准确性",
                "其次处理中等严重程度的重复计数问题，避免功能点重复统计",
                "建立统一的数据组命名规范，确保跨模块一致性"
            ],
            "common_patterns": [
                "数据组命名不规范是最常见的问题，占总问题的40%",
                "重复计数问题主要出现在批量操作和分页查询中",
                "缺少统一的业务实体定义标准"
            ],
            "improvement_measures": [
                "制定并执行数据组命名规范文档",
                "建立代码审查机制，重点检查数据组定义",
                "开发自动化检测工具，识别重复计数问题",
                "定期进行COSMIC方法培训，提高团队规范意识"
            ],
            "best_practices": [
                "使用'业务实体+操作类型'的数据组命名模式",
                "对于分页查询，将分页信息与主数据组合并计数",
                "建立数据组字典，统一管理所有数据组定义",
                "在功能设计阶段就考虑COSMIC拆解规范"
            ],
            "severity_analysis": {
                "高": "主要涉及数据组聚合问题，直接影响CFP计算的准确性，需要立即处理",
                "中": "重复计数问题会导致功能点估算偏高，影响项目规模评估",
                "低": "格式和命名问题，不影响功能但需要规范化"
            }
        }, ensure_ascii=False)
    
    # 替换大模型调用方法
    validator._call_llm_for_validation = mock_summary_llm_call
    
    try:
        # 直接测试汇总建议生成
        summary_suggestions = validator._generate_summary_suggestions(merged_validation)
        
        if summary_suggestions:
            print("汇总建议生成成功！")
            print(json.dumps(summary_suggestions, ensure_ascii=False, indent=2))
            
            # 保存结果
            with open("debug/test_summary_suggestions.json", 'w', encoding='utf-8') as f:
                json.dump(summary_suggestions, f, ensure_ascii=False, indent=2)
            print("汇总建议已保存到: debug/test_summary_suggestions.json")
        else:
            print("汇总建议生成失败！")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_summary_generation()
