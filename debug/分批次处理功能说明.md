# COSMIC校验器分批次处理功能说明

## 功能概述

已成功实现了COSMIC校验器的分批次处理功能，当数据量过大时（超过50K tokens），系统会自动进行分批次处理，确保每个批次都能被大模型正确处理。

## 核心特性

### 1. 自动触发机制
- **触发条件**：当预估token数量超过50,000时自动触发
- **计算方式**：数据大小（字符数）× 1.5 = 预估token数
- **智能判断**：根据数据格式选择相应的分批策略

### 2. CSV格式分批次处理
- **每个批次都带header**：确保每个批次都是完整的CSV格式
- **批次大小计算**：目标30K tokens/批次，约200行数据/批次
- **数据完整性**：保持原始CSV结构，不进行格式转换
- **批次信息**：每个批次包含详细的批次索引和数据范围信息

### 3. JSON格式分批次处理
- **保留原始数据结构**：维持功能过程的层次化结构
- **按功能过程分批**：以功能过程为单位进行分批
- **结构完整性**：每个批次保持完整的JSON数据结构
- **批次大小动态调整**：根据数据复杂度调整批次大小

## 实现细节

### 核心方法

1. **`_process_large_data_in_batches()`**
   - 分批次处理的入口方法
   - 根据数据格式选择相应的处理策略

2. **`_process_csv_in_batches()`**
   - CSV专用分批次处理
   - 确保每个批次都包含完整的header
   - 生成批次调试文件

3. **`_process_json_in_batches()`**
   - JSON专用分批次处理
   - 保留原始数据结构
   - 按功能过程进行分批

4. **批次结果合并方法**
   - `_merge_csv_batch_results()`：合并CSV批次结果
   - `_merge_json_batch_results()`：合并JSON批次结果
   - `_merge_validation_results()`：统一的结果合并逻辑

### 批次数据结构

#### CSV批次数据
```json
{
  "data_format": "CSV",
  "batch_info": {
    "batch_index": 1,
    "total_batches": 6,
    "batch_size": 200,
    "data_range": "第1行到第200行"
  },
  "summary": { /* 原始数据摘要 */ },
  "csv_content": "header\ndata1\ndata2...",
  "validation_focus": { /* 校验重点 */ }
}
```

#### JSON批次数据
```json
{
  "data_format": "JSON",
  "batch_info": {
    "batch_index": 1,
    "total_batches": 3,
    "batch_size": 10,
    "process_range": "功能过程 1 到 10"
  },
  "summary": { /* 原始数据摘要 */ },
  "function_processes": { /* 功能过程数据 */ },
  "validation_focus": { /* 校验重点 */ }
}
```

## 测试验证

### 测试场景
1. **CSV分批次处理**：1177行数据分为6个批次
2. **JSON分批次处理**：310个功能过程分为多个批次
3. **批次文件生成**：每个批次生成独立的调试文件

### 测试结果
- ✅ CSV格式：每个批次都包含完整的header
- ✅ JSON格式：保留原始数据结构
- ✅ 批次信息：准确记录批次索引和数据范围
- ✅ 调试文件：生成完整的批次调试信息

### 生成的调试文件
```
debug/csv_batch_1_input.json   - CSV第1批次数据
debug/csv_batch_2_input.json   - CSV第2批次数据
...
debug/json_batch_1_input.json  - JSON第1批次数据
debug/json_batch_2_input.json  - JSON第2批次数据
...
```

## 批次结果合并

### 合并策略
1. **成功批次统计**：记录成功处理的批次数量
2. **失败批次处理**：记录失败批次的详细信息
3. **结果聚合**：尝试合并各批次的校验结果
4. **统计信息**：提供整体的处理统计

### 合并结果结构
```json
{
  "input_summary": { /* 输入数据摘要 */ },
  "batch_processing_info": {
    "total_batches": 6,
    "successful_batches": 6,
    "failed_batches": 0,
    "processing_method": "CSV分批次处理，每批次包含header"
  },
  "batch_results": [ /* 各批次详细结果 */ ],
  "merged_validation_result": { /* 合并后的校验结果 */ },
  "timestamp": "2025-07-27 18:30:00"
}
```

## 优势特点

1. **自动化处理**：无需手动干预，自动检测并触发分批处理
2. **格式保持**：CSV保持header，JSON保持结构完整性
3. **可追溯性**：每个批次都有详细的调试信息
4. **容错性**：单个批次失败不影响其他批次处理
5. **结果完整性**：提供完整的批次处理统计和合并结果

## 使用方式

分批次处理功能完全透明，用户使用方式不变：

```bash
# 正常使用，系统自动判断是否需要分批处理
python cosmic_validator.py large_data.csv
python cosmic_validator.py large_data.json
```

当数据量超过阈值时，系统会自动输出分批处理信息：
```
数据量过大，进行分批次处理...
CSV数据将分为 6 个批次处理，每批次约 200 行
处理批次 1/6 (第1-200行)...
批次调试数据已保存到: debug/csv_batch_1_input.json
...
```

## 配置参数

- **Token阈值**：50,000 tokens（可在代码中调整）
- **CSV批次大小**：约200行/批次（基于30K tokens目标）
- **JSON批次大小**：动态计算（基于功能过程数量）
- **调试文件路径**：debug/目录下

这个分批次处理功能确保了COSMIC校验器能够处理任意大小的数据文件，同时保持数据格式的完整性和处理结果的准确性。
