#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试小批次处理功能（用于验证逻辑）
"""

import sys
import os
import pandas as pd
import json
sys.path.append('..')

from cosmic_validator import CosmicValidator
import config

def create_small_test_csv():
    """创建小的测试CSV数据"""
    print("创建小的测试CSV数据...")
    
    # 创建测试数据
    test_data = {
        '一级功能模块': ['系统管理'] * 50,
        '二级功能模块': ['用户管理'] * 50,
        '三级功能模块': ['用户注册'] * 50,
        '功能用户': ['用户'] * 50,
        '触发事件': ['点击注册'] * 50,
        '功能过程': [f'用户注册过程{i}' for i in range(1, 51)],
        '子过程描述': [f'注册步骤{i}' for i in range(1, 51)],
        '数据移动类型': ['E', 'R', 'W', 'X'] * 12 + ['E', 'R'],
        '数据组': [f'用户信息{i}' for i in range(1, 51)],
        '数据属性': [f'用户名,密码,邮箱{i}' for i in range(1, 51)],
        'CFP': [1] * 50,
        '功能点个数': [1] * 50
    }
    
    df = pd.DataFrame(test_data)
    test_file = "small_test.csv"
    df.to_csv(test_file, index=False, encoding='utf-8-sig')
    
    print(f"创建了包含 {len(df)} 行数据的测试CSV文件: {test_file}")
    return test_file

def test_batch_logic_with_small_data():
    """使用小数据测试分批次逻辑"""
    print("=" * 60)
    print("测试分批次逻辑（小数据）")
    print("=" * 60)
    
    # 创建测试数据
    test_file = create_small_test_csv()
    
    validator = CosmicValidator(config)
    
    # 临时修改批次大小阈值来触发分批处理
    print("强制触发分批次处理...")
    
    # 直接调用分批次处理方法进行测试
    try:
        # 先正常处理获取数据结构
        result = validator.validate_cosmic_data(test_file)
        
        if "error" not in result:
            print("✅ 小数据处理成功")
            
            # 显示摘要信息
            input_summary = result.get('input_summary', {})
            print(f"总记录数: {input_summary.get('total_records', 0)}")
            print(f"数据移动类型分布: {input_summary.get('data_movement_types', {})}")
            
        else:
            print(f"❌ 处理失败: {result['error']}")
    
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
    
    finally:
        # 清理测试文件
        try:
            os.remove(test_file)
            print(f"已清理测试文件: {test_file}")
        except:
            pass

def test_csv_batch_structure():
    """测试CSV批次数据结构"""
    print("=" * 60)
    print("测试CSV批次数据结构")
    print("=" * 60)
    
    # 模拟CSV数据
    csv_content = """一级功能模块,二级功能模块,三级功能模块,数据移动类型,数据组
系统管理,用户管理,用户注册,E,用户信息
系统管理,用户管理,用户注册,R,用户信息
系统管理,用户管理,用户注册,W,用户信息
系统管理,用户管理,用户注册,X,用户信息
系统管理,用户管理,用户查询,E,查询条件
系统管理,用户管理,用户查询,R,用户信息
系统管理,用户管理,用户查询,X,查询结果"""
    
    lines = csv_content.strip().split('\n')
    header = lines[0]
    data_lines = lines[1:]
    
    print(f"原始数据: {len(data_lines)} 行")
    print(f"Header: {header}")
    
    # 模拟分批处理
    batch_size = 3
    total_batches = (len(data_lines) + batch_size - 1) // batch_size
    
    print(f"将分为 {total_batches} 个批次，每批次 {batch_size} 行")
    
    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min((batch_idx + 1) * batch_size, len(data_lines))
        batch_data_lines = data_lines[start_idx:end_idx]
        
        # 构建批次CSV内容（包含header）
        batch_csv_content = header + '\n' + '\n'.join(batch_data_lines)
        
        print(f"\n批次 {batch_idx + 1}:")
        print(f"  数据范围: 第{start_idx + 1}行到第{end_idx}行")
        print(f"  批次数据行数: {len(batch_data_lines)}")
        print(f"  批次内容预览:")
        batch_lines = batch_csv_content.split('\n')
        for i, line in enumerate(batch_lines[:3]):
            print(f"    {line}")
        if len(batch_lines) > 3:
            print(f"    ... (共{len(batch_lines)}行)")

def test_json_batch_structure():
    """测试JSON批次数据结构"""
    print("=" * 60)
    print("测试JSON批次数据结构")
    print("=" * 60)
    
    # 模拟JSON功能过程数据
    function_processes = {
        "用户注册过程1": {
            "data_movements": {"is_complete": True, "movements": ["E", "R", "W", "X"]},
            "details": "用户注册详细信息1"
        },
        "用户注册过程2": {
            "data_movements": {"is_complete": False, "movements": ["E", "R"]},
            "details": "用户注册详细信息2"
        },
        "用户查询过程1": {
            "data_movements": {"is_complete": True, "movements": ["E", "R", "X"]},
            "details": "用户查询详细信息1"
        },
        "用户查询过程2": {
            "data_movements": {"is_complete": True, "movements": ["E", "R", "W", "X"]},
            "details": "用户查询详细信息2"
        },
        "用户删除过程1": {
            "data_movements": {"is_complete": False, "movements": ["E", "W"]},
            "details": "用户删除详细信息1"
        }
    }
    
    print(f"原始功能过程数: {len(function_processes)}")
    
    # 模拟分批处理
    process_items = list(function_processes.items())
    batch_size = 2
    total_batches = (len(process_items) + batch_size - 1) // batch_size
    
    print(f"将分为 {total_batches} 个批次，每批次 {batch_size} 个功能过程")
    
    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min((batch_idx + 1) * batch_size, len(process_items))
        batch_processes = dict(process_items[start_idx:end_idx])
        
        print(f"\n批次 {batch_idx + 1}:")
        print(f"  功能过程范围: {start_idx + 1} 到 {end_idx}")
        print(f"  批次功能过程数: {len(batch_processes)}")
        print(f"  功能过程名称: {list(batch_processes.keys())}")
        
        # 统计完整性
        complete_count = sum(1 for p in batch_processes.values() 
                           if p["data_movements"]["is_complete"])
        print(f"  完整功能过程: {complete_count}/{len(batch_processes)}")

def main():
    """主函数"""
    print("开始测试分批次处理逻辑")
    print()
    
    # 确保debug目录存在
    os.makedirs("debug", exist_ok=True)
    
    # 测试小数据处理
    test_batch_logic_with_small_data()
    print()
    
    # 测试CSV批次结构
    test_csv_batch_structure()
    print()
    
    # 测试JSON批次结构
    test_json_batch_structure()
    
    print("\n" + "=" * 60)
    print("分批次逻辑测试完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
