#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的COSMIC校验器功能
支持CSV和JSON格式输入
"""

import sys
import os
sys.path.append('..')

from cosmic_validator import CosmicValidator
import config

def test_csv_validation():
    """测试CSV格式校验"""
    print("=" * 60)
    print("测试CSV格式校验")
    print("=" * 60)
    
    validator = CosmicValidator(config)
    
    # 测试CSV文件
    csv_file = "../output-sft.csv"
    if not os.path.exists(csv_file):
        print(f"错误：CSV测试文件 {csv_file} 不存在")
        return False
    
    print(f"开始校验CSV文件: {csv_file}")
    result = validator.validate_cosmic_data(csv_file)
    
    if "error" in result:
        print(f"CSV校验失败: {result['error']}")
        return False
    
    print("CSV校验成功！")
    
    # 显示摘要信息
    input_summary = result.get('input_summary', {})
    print(f"总记录数: {input_summary.get('total_records', 0)}")
    print(f"数据移动类型分布: {input_summary.get('data_movement_types', {})}")
    
    # 保存结果
    validator.save_validation_result(result, "debug/csv_validation_result.json")
    validator.save_validation_report(result, "debug/csv_validation_report.txt")
    
    return True

def test_json_validation():
    """测试JSON格式校验"""
    print("=" * 60)
    print("测试JSON格式校验")
    print("=" * 60)
    
    validator = CosmicValidator(config)
    
    # 测试JSON文件
    json_file = "../output-sft.json"
    if not os.path.exists(json_file):
        print(f"错误：JSON测试文件 {json_file} 不存在")
        return False
    
    print(f"开始校验JSON文件: {json_file}")
    result = validator.validate_cosmic_data(json_file)
    
    if "error" in result:
        print(f"JSON校验失败: {result['error']}")
        return False
    
    print("JSON校验成功！")
    
    # 显示摘要信息
    input_summary = result.get('input_summary', {})
    print(f"总记录数: {input_summary.get('total_records', 0)}")
    print(f"数据移动类型分布: {input_summary.get('data_movement_types', {})}")
    
    # 显示功能过程摘要
    fp_summary = result.get('function_process_summary', {})
    if fp_summary:
        print(f"功能过程: 总数={fp_summary.get('total_processes', 0)}, "
              f"完整={fp_summary.get('complete_processes', 0)}, "
              f"不完整={fp_summary.get('incomplete_processes', 0)}")
    
    # 保存结果
    validator.save_validation_result(result, "debug/json_validation_result.json")
    validator.save_validation_report(result, "debug/json_validation_report.txt")
    
    return True

def test_file_format_detection():
    """测试文件格式检测"""
    print("=" * 60)
    print("测试文件格式检测")
    print("=" * 60)
    
    validator = CosmicValidator(config)
    
    # 测试不支持的格式
    test_cases = [
        ("test.txt", "不支持的文件格式"),
        ("test.xlsx", "不支持的文件格式"),
        ("nonexistent.csv", "文件不存在")
    ]
    
    for file_path, expected_error in test_cases:
        print(f"测试文件: {file_path}")
        result = validator.validate_cosmic_data(file_path)
        
        if "error" in result:
            print(f"  预期错误: {result['error']}")
        else:
            print(f"  意外成功: {result}")
        print()

def compare_results():
    """比较CSV和JSON校验结果"""
    print("=" * 60)
    print("比较CSV和JSON校验结果")
    print("=" * 60)
    
    import json
    
    # 读取结果文件
    csv_result_file = "debug/csv_validation_result.json"
    json_result_file = "debug/json_validation_result.json"
    
    if not os.path.exists(csv_result_file) or not os.path.exists(json_result_file):
        print("错误：结果文件不存在，请先运行校验测试")
        return
    
    with open(csv_result_file, 'r', encoding='utf-8') as f:
        csv_result = json.load(f)
    
    with open(json_result_file, 'r', encoding='utf-8') as f:
        json_result = json.load(f)
    
    # 比较摘要信息
    csv_summary = csv_result.get('input_summary', {})
    json_summary = json_result.get('input_summary', {})
    
    print("数据摘要比较:")
    print(f"CSV总记录数: {csv_summary.get('total_records', 0)}")
    print(f"JSON总记录数: {json_summary.get('total_records', 0)}")
    
    print(f"CSV数据移动类型: {csv_summary.get('data_movement_types', {})}")
    print(f"JSON数据移动类型: {json_summary.get('data_movement_types', {})}")
    
    # 比较校验结果长度
    csv_validation = csv_result.get('raw_validation_result', '')
    json_validation = json_result.get('raw_validation_result', '')
    
    print(f"CSV校验结果长度: {len(csv_validation)} 字符")
    print(f"JSON校验结果长度: {len(json_validation)} 字符")

def main():
    """主函数"""
    print("开始测试新的COSMIC校验器功能")
    print("支持CSV和JSON格式输入")
    print()
    
    # 确保debug目录存在
    os.makedirs("debug", exist_ok=True)
    
    # 测试CSV格式
    csv_success = test_csv_validation()
    print()
    
    # 测试JSON格式
    json_success = test_json_validation()
    print()
    
    # 测试文件格式检测
    test_file_format_detection()
    
    # 比较结果
    if csv_success and json_success:
        compare_results()
    
    print("=" * 60)
    print("测试完成！")
    print("请查看debug目录下的结果文件：")
    print("- csv_validation_result.json")
    print("- csv_validation_report.txt")
    print("- json_validation_result.json")
    print("- json_validation_report.txt")
    print("=" * 60)

if __name__ == "__main__":
    main()
