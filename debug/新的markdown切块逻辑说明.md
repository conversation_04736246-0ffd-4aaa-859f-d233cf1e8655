# 新的Markdown切块逻辑说明

## 概述

重新设计了用户手册知识库构建的切块逻辑，按照markdown的层次结构进行智能切块，提高知识检索的准确性和相关性。

## 切块规则

### 1. 基本原则
- 按照markdown的语法，构建层次结构
- 每个子块的层级为：`config.py`中的`MARKDOWN_CHILD_PATTERN`参数，默认为4个# (四级标题)
- 如果上级标题内没有目标层级的子标题，则该上级标题作为单独的子块
- 父标题与它的第1个子标题之间存在内容，则该父标题作为单独的子块

### 2. 具体规则

#### 规则1：目标层级标题直接作为切块
- 四级标题 (####) 直接作为切块
- 例如：`#### 3.1.1.1 子章节` → 独立切块

#### 规则2：无子标题的上级标题作为切块
- 如果三级标题下没有四级标题，则三级标题作为切块
- 如果二级标题下没有三级标题，则二级标题作为切块
- 以此类推

#### 规则3：有独立内容的父标题作为切块
- 父标题与第一个子标题之间有内容时，父标题作为单独切块
- 例如：
  ```markdown
  ### 3.1.1 子章节
  这是3.1.1的内容，应该作为单独的切块
  
  #### 3.1.1.1 子章节
  这是测试子块7
  ```
  结果：`3.1.1 子章节` 和 `3.1.1.1 子章节` 都是独立切块

#### 规则4：文档前言处理
- 文档开头没有标题的内容作为"文档前言"切块

## 示例

### 输入文档
```markdown
# 1.章节
这是测试子块1

## 1.1 子章节
这是测试子块2

## 1.2 子章节
这是测试子块3

# 2.章节

## 2.1 子章节
这是测试子块4

# 3.章节
这是测试子块5

## 3.1 子章节
这是测试子块6

### 3.1.1 子章节
这是3.1.1的内容，应该作为单独的切块

#### 3.1.1.1 子章节
这是测试子块7

#### 3.1.1.2 子章节
这是测试子块8
```

### 输出切块
1. **块1**: `1.章节` - 测试子块1
2. **块2**: `1.1 子章节` - 测试子块2
3. **块3**: `1.2 子章节` - 测试子块3
4. **块4**: `2.1 子章节` - 测试子块4 (跳过空的"2.章节")
5. **块5**: `3.章节` - 测试子块5
6. **块6**: `3.1 子章节` - 测试子块6
7. **块7**: `3.1.1 子章节` - 3.1.1的内容，应该作为单独的切块
8. **块8**: `3.1.1.1 子章节` - 测试子块7
9. **块9**: `3.1.1.2 子章节` - 测试子块8

## 实际效果

### 用户手册统计
- 检测到 402 个section
- 文档前言: 1 个
- 一级标题 (#): 3 个
- 二级标题 (##): 23 个
- 三级标题 (###): 100 个
- **四级标题 (####): 275 个**

### 切块结果
- 总共生成 376 个功能说明切块
- 275个四级标题 + 101个其他层级标题（无子标题或有独立内容）
- 平均内容长度合理，提高了检索精度

## 配置参数

### config.py中的相关配置
```python
MARKDOWN_CHILD_PATTERN = '####'  # 子级分段正则表达式模式（四级标题）
```

- 可以通过修改`MARKDOWN_CHILD_PATTERN`来调整目标切块层级
- 例如：设置为`'###'`则以三级标题为目标切块层级

## 优势

1. **智能层次识别**：根据markdown结构智能识别切块边界
2. **内容完整性**：确保每个切块都有实际内容
3. **灵活配置**：可通过配置调整目标切块层级
4. **避免冗余**：跳过没有内容的标题
5. **保持上下文**：父标题的独立内容不会丢失

## 技术实现

### 核心方法
- `_parse_markdown_hierarchy()`: 解析markdown层次结构
- `_build_chunks_from_sections()`: 根据规则构建切块

### 算法流程
1. 解析所有标题和内容到sections
2. 确定目标切块层级
3. 遍历sections，应用切块规则
4. 生成最终切块列表

这个新的切块逻辑大大提高了知识库的质量和检索效果。
