{"data_format": "CSV", "batch_info": {"batch_index": 1, "total_batches": 1, "batch_size": 121, "data_range": "第1行到第121行"}, "summary": {"total_records": 120, "columns": ["一级功能模块", "二级功能模块", "三级功能模块", "预估工作量\n（人天）", "功能用户", "触发事件", "功能过程", "子过程描述", "数据移动类型", "数据组", "数据属性", "CFP"], "data_movement_types": {"R": 35, "E": 33, "X": 27, "W": 25}, "cfp_distribution": {"1": 120}, "level1_modules": ["密码资产数据管理"], "level2_modules": ["密码资产数据管理"], "level3_modules": ["密码服务数据库新增", "密码服务数据库列表", "密码服务数据库模式列表", "密码服务数据库模式删除", "密码服务数据库模式查询", "密码服务数据库模式新增", "API网关列表", "API网关初始化", "API网关新增", "API网关编辑", "API网关删除", "路由管理列表", "路由管理详情", "设备类型展示", "设备类型初始化", "设备类型新增", "设备类型编辑", "设备类型停用", "设备类型启用", "设备类型删除", "监控信息配置查看", "监控信息配置", "密码设备集群列表", "密码设备集群新增", "密码设备集群编辑"]}, "csv_content": "一级功能模块,二级功能模块,三级功能模块,\"预估工作量\n（人天）\",功能用户,触发事件,功能过程,子过程描述,数据移动类型,数据组,数据属性,CFP\n密码资产数据管理,密码资产数据管理,密码服务数据库新增,4.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码服务数据库管理页面点击新增按钮,新增密码服务数据库信息,选择数据库类型,E,数据库类型,数据库类型名称,1\n密码资产数据管理,密码资产数据管理,密码服务数据库新增,,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码服务数据库管理页面点击新增按钮,新增密码服务数据库信息,输入数据库IP和端口,E,数据库连接信息,IP地址、端口号,1\n密码资产数据管理,密码资产数据管理,密码服务数据库新增,,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码服务数据库管理页面点击新增按钮,新增密码服务数据库信息,输入管理员账号和密码,E,管理员凭证,管理员账号、管理员密码,1\n密码资产数据管理,密码资产数据管理,密码服务数据库新增,,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码服务数据库管理页面点击新增按钮,新增密码服务数据库信息,保存数据库信息,W,数据库信息,数据库名称、数据库类型、IP地址、端口号、管理员账号、管理员密码,1\n密码资产数据管理,密码资产数据管理,密码服务数据库列表,,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码服务数据库管理页面点击列表展示,展示密码服务数据库列表,输入分页信息,E,分页参数,页码、每页数量,1\n密码资产数据管理,密码资产数据管理,密码服务数据库列表,,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码服务数据库管理页面点击列表展示,展示密码服务数据库列表,读取数据库列表数据,R,数据库列表,数据库名称、数据库类型、实例库名称、IP地址、端口号、完整性校验状态,1\n密码资产数据管理,密码资产数据管理,密码服务数据库列表,,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码服务数据库管理页面点击列表展示,展示密码服务数据库列表,输出数据库列表,X,数据库列表,数据库名称、数据库类型、实例库名称、IP地址、端口号、完整性校验状态,1\n密码资产数据管理,密码资产数据管理,密码服务数据库列表,,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码服务数据库管理页面点击列表展示,展示密码服务数据库列表,输出分页信息,X,分页信息,总记录数、当前页码、总页数,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式列表,2.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码服务数据库模式管理页面点击列表展示,展示密码服务数据库模式列表,输入查询条件,E,查询参数,数据库名称、模式名称,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式列表,,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码服务数据库模式管理页面点击列表展示,展示密码服务数据库模式列表,读取数据库模式列表,R,数据库模式列表,模式名称、所属数据库、创建时间,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式删除,,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码服务数据库模式管理页面点击删除按钮,删除密码服务数据库模式,选择待删除模式,E,模式信息,模式名称、所属数据库,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式删除,,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码服务数据库模式管理页面点击删除按钮,删除密码服务数据库模式,执行模式删除操作,W,数据库模式,模式名称、所属数据库,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式查询,,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码服务数据库模式管理页面点击查询按钮,查询密码服务数据库模式,输入模式名称,E,查询参数,模式名称,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式查询,,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码服务数据库模式管理页面点击查询按钮,查询密码服务数据库模式,读取模式详细信息,R,数据库模式,模式名称、所属数据库、创建时间、表结构,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式新增,,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码服务数据库模式管理页面点击新增按钮,新增密码服务数据库模式,输入模式名称和描述,E,模式信息,模式名称、描述,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式新增,,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码服务数据库模式管理页面点击新增按钮,新增密码服务数据库模式,保存模式信息,W,数据库模式,模式名称、所属数据库、描述,1\n密码资产数据管理,密码资产数据管理,API网关列表,4.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在API网关管理页面点击列表展示,展示API网关列表,输入分页信息,E,分页参数,页码、每页数量,1\n密码资产数据管理,密码资产数据管理,API网关列表,,发起者：管理员，接收者：密码资产数据管理系统,管理员在API网关管理页面点击列表展示,展示API网关列表,读取网关列表数据,R,API网关列表,网关名称、所属区域、标识、类型、IP地址、业务端口、管理端口,1\n密码资产数据管理,密码资产数据管理,API网关列表,,发起者：管理员，接收者：密码资产数据管理系统,管理员在API网关管理页面点击列表展示,展示API网关列表,输出网关列表,X,API网关列表,网关名称、所属区域、标识、类型、IP地址、业务端口、管理端口,1\n密码资产数据管理,密码资产数据管理,API网关列表,,发起者：管理员，接收者：密码资产数据管理系统,管理员在API网关管理页面点击列表展示,展示API网关列表,输出分页信息,X,分页信息,总记录数、当前页码、总页数,1\n密码资产数据管理,密码资产数据管理,API网关初始化,3.0,发起者：系统，接收者：密码资产数据管理系统,密码服务平台部署成功后触发API网关初始化,初始化API网关信息,读取平台部署配置,R,部署配置,网关类型、IP地址、端口,1\n密码资产数据管理,密码资产数据管理,API网关初始化,,发起者：系统，接收者：密码资产数据管理系统,密码服务平台部署成功后触发API网关初始化,初始化API网关信息,生成默认网关信息,E,网关信息,网关名称、类型、IP地址、端口,1\n密码资产数据管理,密码资产数据管理,API网关初始化,,发起者：系统，接收者：密码资产数据管理系统,密码服务平台部署成功后触发API网关初始化,初始化API网关信息,保存初始化网关数据,W,API网关,网关名称、类型、IP地址、端口,1\n密码资产数据管理,密码资产数据管理,API网关新增,,发起者：管理员，接收者：密码资产数据管理系统,管理员在API网关管理页面点击新增按钮,新增API网关信息,输入网关基本信息,E,网关信息,网关名称、所属需求、标识、类型、管理端口,1\n密码资产数据管理,密码资产数据管理,API网关新增,,发起者：管理员，接收者：密码资产数据管理系统,管理员在API网关管理页面点击新增按钮,新增API网关信息,验证网关信息,R,网关信息,标识唯一性校验,1\n密码资产数据管理,密码资产数据管理,API网关新增,,发起者：管理员，接收者：密码资产数据管理系统,管理员在API网关管理页面点击新增按钮,新增API网关信息,保存网关信息,W,API网关,网关名称、所属需求、标识、类型、管理端口,1\n密码资产数据管理,密码资产数据管理,API网关编辑,,发起者：管理员，接收者：密码资产数据管理系统,管理员在API网关管理页面点击删除按钮,删除API网关信息,选择待删除网关,E,网关信息,网关标识,1\n密码资产数据管理,密码资产数据管理,API网关编辑,,发起者：管理员，接收者：密码资产数据管理系统,管理员在API网关管理页面点击删除按钮,删除API网关信息,验证网关依赖关系,R,依赖关系,关联服务数量,1\n密码资产数据管理,密码资产数据管理,API网关编辑,,发起者：管理员，接收者：密码资产数据管理系统,管理员在API网关管理页面点击删除按钮,删除API网关信息,执行网关删除操作,W,API网关,网关标识,1\n密码资产数据管理,密码资产数据管理,密码服务数据库新增,,发起者：管理员，接收者：密码资产管理系统,管理员在批量创建页面点击批量创建按钮,批量创建虚拟密码机,输入批量创建参数,E,批量创建参数,数量、资源规格、网络配置,1\n密码资产数据管理,密码资产数据管理,密码服务数据库新增,,发起者：管理员，接收者：密码资产管理系统,管理员在批量创建页面点击批量创建按钮,批量创建虚拟密码机,调用云密码机0088标准接口,X,创建请求,虚拟机规格、网络参数,1\n密码资产数据管理,密码资产数据管理,密码服务数据库新增,,发起者：管理员，接收者：密码资产管理系统,管理员在批量创建页面点击批量创建按钮,批量创建虚拟密码机,接收创建结果,R,创建响应,虚拟机ID、状态,1\n密码资产数据管理,密码资产数据管理,密码服务数据库新增,,发起者：管理员，接收者：密码资产管理系统,管理员在批量创建页面点击批量创建按钮,批量创建虚拟密码机,保存虚拟机信息,W,虚拟密码机信息,名称、IP地址、资源分配,1\n密码资产数据管理,密码资产数据管理,密码服务数据库新增,,发起者：管理员，接收者：密码资产管理系统,管理员在批量创建页面点击批量创建按钮,批量创建虚拟密码机,更新网络配置,W,网络配置,子网、安全组,1\n密码资产数据管理,密码资产数据管理,密码服务数据库新增,,发起者：管理员，接收者：密码资产管理系统,管理员在批量创建页面点击批量创建按钮,批量创建虚拟密码机,生成操作日志,W,操作日志,操作时间、操作人,1\n密码资产数据管理,密码资产数据管理,密码服务数据库新增,,发起者：管理员，接收者：密码资产管理系统,管理员在批量创建页面点击批量创建按钮,批量创建虚拟密码机,验证资源配额,R,资源配额,CPU、内存、存储,1\n密码资产数据管理,密码资产数据管理,密码服务数据库新增,,发起者：管理员，接收者：密码资产管理系统,管理员在批量创建页面点击批量创建按钮,批量创建虚拟密码机,检查网络连通性,R,网络状态,子网可用性、路由表,1\n密码资产数据管理,密码资产数据管理,密码服务数据库新增,,发起者：管理员，接收者：密码资产管理系统,管理员在批量创建页面点击批量创建按钮,批量创建虚拟密码机,发送创建确认通知,X,通知消息,创建结果、虚拟机ID,1\n密码资产数据管理,密码资产数据管理,密码服务数据库新增,,发起者：管理员，接收者：密码资产管理系统,管理员在批量创建页面点击批量创建按钮,批量创建虚拟密码机,更新虚拟机状态,W,虚拟机状态,运行状态、健康检查,1\n密码资产数据管理,密码资产数据管理,密码服务数据库列表,,发起者：管理员，接收者：密码资产管理系统,管理员访问虚拟密码机列表页面,展示虚拟密码机列表,查询分页信息,E,分页参数,页码、单页数量,1\n密码资产数据管理,密码资产数据管理,密码服务数据库列表,,发起者：管理员，接收者：密码资产管理系统,管理员访问虚拟密码机列表页面,展示虚拟密码机列表,读取虚拟密码机列表,R,虚拟密码机列表,名称、IP、状态、资源分配,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式列表,,发起者：管理员，接收者：密码资产管理系统,管理员在列表页面输入查询条件,查询虚拟密码机,输入查询条件,E,查询参数,名称、主机、管理IP、服务IP、设备类型,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式列表,,发起者：管理员，接收者：密码资产管理系统,管理员在列表页面输入查询条件,查询虚拟密码机,读取匹配的虚拟密码机,R,虚拟密码机列表,名称、IP、状态、设备类型,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式删除,,发起者：管理员，接收者：密码资产管理系统,管理员点击创建虚拟密码机按钮,创建虚拟密码机,选择云密码机模板,E,云密码机模板,模板名称、规格,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式删除,,发起者：管理员，接收者：密码资产管理系统,管理员点击创建虚拟密码机按钮,创建虚拟密码机,调用创建接口并保存配置,W,虚拟密码机配置,名称、IP、资源分配,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式查询,,发起者：管理员，接收者：密码资产管理系统,管理员点击虚拟密码机详情按钮,展示虚拟密码机详情,读取虚拟机基础信息,R,虚拟机基础信息,名称、IP、创建时间,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式查询,,发起者：管理员，接收者：密码资产管理系统,管理员点击虚拟密码机详情按钮,展示虚拟密码机详情,读取资源分配详情,R,资源分配,CPU、内存、存储,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式查询,,发起者：管理员，接收者：密码资产管理系统,管理员点击虚拟密码机详情按钮,展示虚拟密码机详情,读取网络配置信息,R,网络配置,子网、安全组,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式查询,,发起者：管理员，接收者：密码资产管理系统,管理员点击虚拟密码机详情按钮,展示虚拟密码机详情,读取运行状态信息,R,运行状态,运行状态、健康检查,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式查询,,发起者：管理员，接收者：密码资产管理系统,管理员点击虚拟密码机详情按钮,展示虚拟密码机详情,生成详情展示数据,X,详情数据,综合信息,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式新增,,发起者：管理员，接收者：密码资产管理系统,管理员在编辑页面修改虚拟密码机信息,编辑虚拟密码机,输入新名称和连接密码,E,编辑参数,名称、连接密码,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式新增,,发起者：管理员，接收者：密码资产管理系统,管理员在编辑页面修改虚拟密码机信息,编辑虚拟密码机,验证连接密码有效性,R,验证结果,密码状态,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式新增,,发起者：管理员，接收者：密码资产管理系统,管理员在编辑页面修改虚拟密码机信息,编辑虚拟密码机,更新虚拟密码机信息,W,虚拟密码机信息,名称、连接密码,1\n密码资产数据管理,密码资产数据管理,密码服务数据库模式新增,,发起者：管理员，接收者：密码资产管理系统,管理员在编辑页面修改虚拟密码机信息,编辑虚拟密码机,下发配置到密码服务,X,配置指令,更新参数,1\n密码资产数据管理,密码资产数据管理,API网关列表,,发起者：管理员，接收者：密码资产管理系统,管理员点击删除虚拟密码机按钮,删除虚拟密码机,确认删除操作,E,确认信息,确认标识,1\n密码资产数据管理,密码资产数据管理,API网关列表,,发起者：管理员，接收者：密码资产管理系统,管理员点击删除虚拟密码机按钮,删除虚拟密码机,调用删除接口,X,删除请求,虚拟机ID,1\n密码资产数据管理,密码资产数据管理,API网关列表,,发起者：管理员，接收者：密码资产管理系统,管理员点击删除虚拟密码机按钮,删除虚拟密码机,更新虚拟机列表,W,虚拟机列表,删除状态,1\n密码资产数据管理,密码资产数据管理,API网关初始化,,发起者：管理员，接收者：密码资产管理系统,管理员点击启动虚拟密码机按钮,启动虚拟密码机,发送启动指令,X,操作指令,启动命令,1\n密码资产数据管理,密码资产数据管理,API网关初始化,,发起者：管理员，接收者：密码资产管理系统,管理员点击启动虚拟密码机按钮,启动虚拟密码机,接收启动结果,R,操作结果,启动状态,1\n密码资产数据管理,密码资产数据管理,API网关初始化,,发起者：管理员，接收者：密码资产管理系统,管理员点击启动虚拟密码机按钮,启动虚拟密码机,更新虚拟机状态,W,虚拟机状态,运行状态,1\n密码资产数据管理,密码资产数据管理,API网关新增,,发起者：管理员，接收者：密码资产管理系统,管理员点击停止虚拟密码机按钮,停止虚拟密码机,发送停止指令,X,操作指令,停止命令,1\n密码资产数据管理,密码资产数据管理,API网关新增,,发起者：管理员，接收者：密码资产管理系统,管理员点击停止虚拟密码机按钮,停止虚拟密码机,接收停止结果,R,操作结果,停止状态,1\n密码资产数据管理,密码资产数据管理,API网关新增,,发起者：管理员，接收者：密码资产管理系统,管理员点击停止虚拟密码机按钮,停止虚拟密码机,更新虚拟机状态,W,虚拟机状态,运行状态,1\n密码资产数据管理,密码资产数据管理,API网关编辑,,发起者：管理员，接收者：密码资产管理系统,管理员点击重启虚拟密码机按钮,重启虚拟密码机,发送重启指令,X,操作指令,重启命令,1\n密码资产数据管理,密码资产数据管理,API网关编辑,,发起者：管理员，接收者：密码资产管理系统,管理员点击重启虚拟密码机按钮,重启虚拟密码机,接收重启结果,R,操作结果,重启状态,1\n密码资产数据管理,密码资产数据管理,API网关编辑,,发起者：管理员，接收者：密码资产管理系统,管理员点击重启虚拟密码机按钮,重启虚拟密码机,更新虚拟机状态,W,虚拟机状态,运行状态,1\n密码资产数据管理,密码资产数据管理,API网关删除,,发起者：管理员，接收者：密码资产管理系统,管理员点击强制删除虚拟密码机按钮,强制删除虚拟密码机,输入强制删除标识,E,强制删除参数,强制标识,1\n密码资产数据管理,密码资产数据管理,API网关删除,,发起者：管理员，接收者：密码资产管理系统,管理员点击强制删除虚拟密码机按钮,强制删除虚拟密码机,调用强制删除接口,X,强制删除请求,虚拟机ID,1\n密码资产数据管理,密码资产数据管理,API网关删除,,发起者：管理员，接收者：密码资产管理系统,管理员点击强制删除虚拟密码机按钮,强制删除虚拟密码机,更新虚拟机列表,W,虚拟机列表,删除状态,1\n密码资产数据管理,密码资产数据管理,路由管理列表,4.0,发起者：管理员，接收者：密码资产管理系统,管理员点击生成虚机影像按钮,生成虚机影像,输入影像生成参数,E,影像参数,影像名称、存储位置,1\n密码资产数据管理,密码资产数据管理,路由管理列表,,发起者：管理员，接收者：密码资产管理系统,管理员点击生成虚机影像按钮,生成虚机影像,调用生成接口,X,生成请求,虚拟机ID,1\n密码资产数据管理,密码资产数据管理,路由管理列表,,发起者：管理员，接收者：密码资产管理系统,管理员点击生成虚机影像按钮,生成虚机影像,接收生成结果,R,生成结果,影像ID、状态,1\n密码资产数据管理,密码资产数据管理,路由管理列表,,发起者：管理员，接收者：密码资产管理系统,管理员点击生成虚机影像按钮,生成虚机影像,保存影像信息,W,虚机影像,影像名称、存储路径,1\n密码资产数据管理,密码资产数据管理,路由管理详情,,发起者：管理员，接收者：密码资产管理系统,管理员点击下载虚机影像按钮,下载虚机影像,选择虚机影像,E,影像选择,影像ID,1\n密码资产数据管理,密码资产数据管理,路由管理详情,,发起者：管理员，接收者：密码资产管理系统,管理员点击下载虚机影像按钮,下载虚机影像,调用下载接口,X,下载请求,影像ID,1\n密码资产数据管理,密码资产数据管理,路由管理详情,,发起者：管理员，接收者：密码资产管理系统,管理员点击下载虚机影像按钮,下载虚机影像,接收影像文件,R,影像文件,文件内容,1\n密码资产数据管理,密码资产数据管理,路由管理详情,,发起者：管理员，接收者：密码资产管理系统,管理员点击下载虚机影像按钮,下载虚机影像,生成下载链接,X,下载链接,URL地址,1\n密码资产数据管理,密码资产数据管理,设备类型展示,,发起者：管理员，接收者：密码资产管理系统,管理员点击导入虚机影像按钮,导入虚机影像,上传影像文件,E,影像文件,文件内容,1\n密码资产数据管理,密码资产数据管理,设备类型展示,,发起者：管理员，接收者：密码资产管理系统,管理员点击导入虚机影像按钮,导入虚机影像,解析影像文件,R,影像元数据,影像规格、配置,1\n密码资产数据管理,密码资产数据管理,设备类型展示,,发起者：管理员，接收者：密码资产管理系统,管理员点击导入虚机影像按钮,导入虚机影像,调用导入接口,X,导入请求,影像配置,1\n密码资产数据管理,密码资产数据管理,设备类型展示,,发起者：管理员，接收者：密码资产管理系统,管理员点击导入虚机影像按钮,导入虚机影像,更新影像列表,W,虚机影像,导入状态,1\n密码资产数据管理,密码资产数据管理,设备类型初始化,,发起者：管理员，接收者：密码资产管理系统,管理员访问物理密码机列表页面,展示物理密码机列表,查询分页信息,E,分页参数,页码、单页数量,1\n密码资产数据管理,密码资产数据管理,设备类型初始化,,发起者：管理员，接收者：密码资产管理系统,管理员访问物理密码机列表页面,展示物理密码机列表,读取物理密码机列表,R,物理密码机列表,名称、厂商、设备类型、管理IP,1\n密码资产数据管理,密码资产数据管理,设备类型新增,,发起者：管理员，接收者：密码资产管理系统,管理员点击新建物理密码机按钮,新建物理密码机,输入设备基本信息,E,设备信息,名称、厂商、设备类型,1\n密码资产数据管理,密码资产数据管理,设备类型新增,,发起者：管理员，接收者：密码资产管理系统,管理员点击新建物理密码机按钮,新建物理密码机,输入网络配置信息,E,网络配置,管理IP、管理端口,1\n密码资产数据管理,密码资产数据管理,设备类型新增,,发起者：管理员，接收者：密码资产管理系统,管理员点击新建物理密码机按钮,新建物理密码机,验证设备可用性,R,验证结果,连接状态,1\n密码资产数据管理,密码资产数据管理,设备类型新增,,发起者：管理员，接收者：密码资产管理系统,管理员点击新建物理密码机按钮,新建物理密码机,调用注册接口,X,注册请求,设备信息,1\n密码资产数据管理,密码资产数据管理,设备类型新增,,发起者：管理员，接收者：密码资产管理系统,管理员点击新建物理密码机按钮,新建物理密码机,保存设备信息,W,物理密码机信息,名称、IP、厂商,1\n密码资产数据管理,密码资产数据管理,设备类型编辑,,发起者：管理员，接收者：密码资产管理系统,管理员点击编辑物理密码机按钮,编辑物理密码机,输入修改后的名称和备注,E,编辑参数,名称、备注,1\n密码资产数据管理,密码资产数据管理,设备类型编辑,,发起者：管理员，接收者：密码资产管理系统,管理员点击编辑物理密码机按钮,编辑物理密码机,验证修改内容,R,验证结果,名称唯一性,1\n密码资产数据管理,密码资产数据管理,设备类型编辑,,发起者：管理员，接收者：密码资产管理系统,管理员点击编辑物理密码机按钮,编辑物理密码机,更新设备信息,W,物理密码机信息,名称、备注,1\n密码资产数据管理,密码资产数据管理,设备类型停用,2.0,发起者：管理员，接收者：密码资产管理系统,管理员点击删除物理密码机按钮,删除物理密码机,确认删除操作,E,确认信息,确认标识,1\n密码资产数据管理,密码资产数据管理,设备类型停用,,发起者：管理员，接收者：密码资产管理系统,管理员点击删除物理密码机按钮,删除物理密码机,调用删除接口,X,删除请求,设备ID,1\n密码资产数据管理,密码资产数据管理,设备类型停用,,发起者：管理员，接收者：密码资产管理系统,管理员点击删除物理密码机按钮,删除物理密码机,更新设备列表,W,物理密码机列表,删除状态,1\n密码资产数据管理,密码资产数据管理,设备类型启用,3.0,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机详情按钮,展示物理密码机详情,读取设备基础信息,R,设备基础信息,名称、厂商、设备类型,1\n密码资产数据管理,密码资产数据管理,设备类型启用,,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机详情按钮,展示物理密码机详情,读取网络配置信息,R,网络配置,管理IP、管理端口,1\n密码资产数据管理,密码资产数据管理,设备类型启用,,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机详情按钮,展示物理密码机详情,读取版本信息,R,版本信息,软件版本、固件版本,1\n密码资产数据管理,密码资产数据管理,设备类型启用,,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机详情按钮,展示物理密码机详情,读取安全状态,R,安全状态,完整性校验、序列号,1\n密码资产数据管理,密码资产数据管理,设备类型启用,,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机详情按钮,展示物理密码机详情,生成详情展示数据,X,详情数据,综合信息,1\n密码资产数据管理,密码资产数据管理,设备类型删除,,发起者：管理员，接收者：密码资产管理系统,管理员点击强制删除物理密码机按钮,强制删除物理密码机,输入强制删除标识,E,强制删除参数,强制标识,1\n密码资产数据管理,密码资产数据管理,设备类型删除,,发起者：管理员，接收者：密码资产管理系统,管理员点击强制删除物理密码机按钮,强制删除物理密码机,调用强制删除接口,X,强制删除请求,设备ID,1\n密码资产数据管理,密码资产数据管理,设备类型删除,,发起者：管理员，接收者：密码资产管理系统,管理员点击强制删除物理密码机按钮,强制删除物理密码机,更新设备列表,W,物理密码机列表,删除状态,1\n密码资产数据管理,密码资产数据管理,监控信息配置查看,4.0,发起者：管理员，接收者：密码资产管理系统,管理员点击管理页面跳转按钮,跳转到设备管理页面,读取设备管理地址,R,管理地址,URL地址,1\n密码资产数据管理,密码资产数据管理,监控信息配置查看,,发起者：管理员，接收者：密码资产管理系统,管理员点击管理页面跳转按钮,跳转到设备管理页面,生成跳转链接,X,跳转链接,URL地址,1\n密码资产数据管理,密码资产数据管理,监控信息配置查看,,发起者：管理员，接收者：密码资产管理系统,管理员点击管理页面跳转按钮,跳转到设备管理页面,打开管理页面,X,页面请求,跳转指令,1\n密码资产数据管理,密码资产数据管理,监控信息配置,3.0,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥创建按钮,创建保护主密钥,选择设备类型,E,设备类型,服务器密码机、虚拟机类型,1\n密码资产数据管理,密码资产数据管理,监控信息配置,,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥创建按钮,创建保护主密钥,输入密钥参数,E,密钥参数,密钥长度、算法类型,1\n密码资产数据管理,密码资产数据管理,监控信息配置,,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥创建按钮,创建保护主密钥,调用创建接口,X,创建请求,密钥参数,1\n密码资产数据管理,密码资产数据管理,监控信息配置,,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥创建按钮,创建保护主密钥,接收创建结果,R,创建结果,密钥ID、状态,1\n密码资产数据管理,密码资产数据管理,监控信息配置,,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥创建按钮,创建保护主密钥,保存密钥信息,W,保护主密钥,密钥ID、设备ID,1\n密码资产数据管理,密码资产数据管理,密码设备集群列表,4.0,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥同步按钮,同步保护主密钥,选择目标设备,E,目标设备,设备ID,1\n密码资产数据管理,密码资产数据管理,密码设备集群列表,,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥同步按钮,同步保护主密钥,读取现有密钥信息,R,保护主密钥,密钥ID、状态,1\n密码资产数据管理,密码资产数据管理,密码设备集群列表,,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥同步按钮,同步保护主密钥,调用同步接口,X,同步请求,密钥ID,1\n密码资产数据管理,密码资产数据管理,密码设备集群列表,,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥同步按钮,同步保护主密钥,更新同步状态,W,保护主密钥,同步状态,1\n密码资产数据管理,密码资产数据管理,密码设备集群新增,3.0,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥备份按钮,备份保护主密钥,选择设备,E,设备选择,设备ID,1\n密码资产数据管理,密码资产数据管理,密码设备集群新增,,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥备份按钮,备份保护主密钥,调用备份接口,X,备份请求,设备ID,1\n密码资产数据管理,密码资产数据管理,密码设备集群新增,,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥备份按钮,备份保护主密钥,接收备份文件,R,备份文件,加密密钥文件,1\n密码资产数据管理,密码资产数据管理,密码设备集群编辑,,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥还原按钮,还原保护主密钥,上传备份文件,E,备份文件,加密密钥文件,1\n密码资产数据管理,密码资产数据管理,密码设备集群编辑,,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥还原按钮,还原保护主密钥,解析备份文件,R,备份元数据,密钥ID、加密算法,1\n密码资产数据管理,密码资产数据管理,密码设备集群编辑,,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥还原按钮,还原保护主密钥,调用还原接口,X,还原请求,密钥数据,1"}