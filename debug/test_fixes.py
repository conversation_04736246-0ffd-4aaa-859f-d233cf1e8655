#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web UI修复功能

测试以下修复：
1. Mermaid时序图渲染支持
2. 页面内Markdown渲染
3. 一级模块功能需求描述
"""

import os
import sys
import requests
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_mermaid_support():
    """测试Mermaid时序图支持"""
    print("1. 测试Mermaid时序图支持...")
    
    # 创建一个包含Mermaid时序图的测试markdown文件
    test_markdown = """# 测试文档

## 时序图测试

```mermaid
sequenceDiagram
    participant User as 用户
    participant System as 系统
    participant DB as 数据库
    
    User->>System: 配置上报路径
    System->>System: 输入配置信息(E)
    System->>DB: 校验路径重复性(R)
    DB-->>System: 返回校验结果
    System->>DB: 保存配置信息(W)
    System->>User: 返回配置结果(X)
```

这是一个测试时序图。
"""
    
    test_file = "debug/test_mermaid.md"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_markdown)
    
    try:
        # 测试渲染API
        response = requests.get(f"http://localhost:5000/api/render/{test_file}", timeout=10)
        if response.status_code == 200:
            html_content = response.text
            if 'mermaid' in html_content.lower() and 'sequenceDiagram' in html_content:
                print("✅ Mermaid时序图支持正常")
                return True
            else:
                print("❌ Mermaid时序图支持异常")
                return False
        else:
            print(f"❌ 渲染API调用失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Mermaid测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)

def test_in_page_rendering():
    """测试页面内渲染功能"""
    print("2. 测试页面内渲染功能...")
    
    try:
        # 获取主页面
        response = requests.get("http://localhost:5000", timeout=10)
        if response.status_code == 200:
            html_content = response.text
            # 检查是否包含页面内查看按钮
            if 'viewInPageBtn' in html_content and '页面内查看' in html_content:
                print("✅ 页面内渲染按钮已添加")
                
                # 检查是否包含文档查看器区域
                if 'documentViewer' in html_content and 'document-viewer' in html_content:
                    print("✅ 文档查看器区域已添加")
                    
                    # 检查是否包含相关JavaScript函数
                    if 'viewDocumentInPage' in html_content and 'hideDocumentViewer' in html_content:
                        print("✅ 页面内渲染JavaScript函数已添加")
                        return True
                    else:
                        print("❌ 缺少页面内渲染JavaScript函数")
                        return False
                else:
                    print("❌ 缺少文档查看器区域")
                    return False
            else:
                print("❌ 缺少页面内查看按钮")
                return False
        else:
            print(f"❌ 主页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 页面内渲染测试失败: {e}")
        return False

def test_level1_description():
    """测试一级模块功能需求描述"""
    print("3. 测试一级模块功能需求描述...")
    
    try:
        # 检查doc_generator.py是否包含相关修改
        with open('doc_generator.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if '必须为一级模块生成功能需求描述' in content:
            print("✅ doc_generator.py已添加一级模块描述约束")
            
            if '该一级模块包含的二级模块' in content and '该一级模块包含的三级模块' in content:
                print("✅ 已添加模块名称收集逻辑")
                
                if '请根据以上二级、三级模块名称，为该一级模块生成综合性的功能需求描述' in content:
                    print("✅ 已添加一级模块描述生成指导")
                    return True
                else:
                    print("❌ 缺少一级模块描述生成指导")
                    return False
            else:
                print("❌ 缺少模块名称收集逻辑")
                return False
        else:
            print("❌ doc_generator.py缺少一级模块描述约束")
            return False
    except Exception as e:
        print(f"❌ 一级模块描述测试失败: {e}")
        return False

def test_prompt_updates():
    """测试提示词更新"""
    print("4. 测试提示词更新...")
    
    try:
        # 检查doc_prompt.md是否包含用户的修改
        with open('doc_prompt.md', 'r', encoding='utf-8') as f:
            content = f.read()
            
        checks = [
            ('顺序列出各三级模块名称及时序图，序号采用：1,2,3,4，如下：', '时序图序号格式'),
            ('- 1. {三级模块名称} - 时序图', '时序图列表格式'),
            ('- ***功能简介***', '功能简介格式'),
            ('- ***功能要求***', '功能要求格式'),
            ('不含数据组、数据属性、数据移动等内容', '子过程描述约束')
        ]
        
        all_passed = True
        for check_text, check_name in checks:
            if check_text in content:
                print(f"✅ {check_name}更新正确")
            else:
                print(f"❌ {check_name}更新缺失")
                all_passed = False
        
        return all_passed
    except Exception as e:
        print(f"❌ 提示词更新测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=== Web UI 修复功能测试 ===")
    print()
    
    # 检查Web服务是否运行
    print("检查Web服务状态...")
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        if response.status_code == 200:
            print("✅ Web服务正在运行")
        else:
            print(f"❌ Web服务响应异常: {response.status_code}")
            print("请确保运行了: python web_ui.py")
            return
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Web服务")
        print("请先启动Web服务: python web_ui.py")
        return
    except Exception as e:
        print(f"❌ Web服务检查失败: {e}")
        return
    
    print()
    
    # 运行测试
    tests = [
        test_mermaid_support,
        test_in_page_rendering,
        test_level1_description,
        test_prompt_updates
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        if test_func():
            passed += 1
        print()
    
    # 总结
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有修复功能测试通过！")
        print("\n修复内容总结:")
        print("1. ✅ 修复了Mermaid时序图渲染支持")
        print("2. ✅ 添加了页面内Markdown渲染功能")
        print("3. ✅ 增加了一级模块功能需求描述生成")
        print("4. ✅ 更新了提示词格式")
        print("\n现在可以正常使用Web UI的所有功能！")
    else:
        print("❌ 部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
