# 功能需求文档生成器使用说明

## 概述

功能需求文档生成器是基于COSMIC功能拆解结果生成功能需求文档的工具。它能够：

1. 根据COSMIC功能拆解的CSV数据生成结构化的功能需求文档
2. 自动生成Mermaid语法的关键时序图
3. 结合知识库信息生成详细的需求描述
4. 支持分批次处理，避免单次请求过大
5. 支持配置起始序号，灵活适应不同文档结构

## 文件结构

```
cosmic/
├── requirement_generator.py          # 主要生成器文件
├── 功能需求生成提示词.md              # 系统提示词文件
├── config.py                        # 配置文件（包含生成器配置）
├── debug/
│   ├── test_requirement_generator.py # 测试脚本
│   └── 功能需求文档_*.md             # 生成的文档文件
```

## 配置说明

在 `config.py` 中的 `REQUIREMENT_GENERATOR_CONFIG` 配置项：

```python
REQUIREMENT_GENERATOR_CONFIG = {
    # 基本配置
    "default_start_number": "2",                             # 默认起始序号
    "max_subprocess_per_batch": 50,                          # 每批次最大子过程数
    "prompt_file": "功能需求生成提示词.md",                    # 系统提示词文件
    "output_dir": "debug",                                   # 输出目录
    
    # 文档格式配置
    "include_toc": True,                                     # 是否包含目录
    "include_timestamp": True,                               # 是否包含时间戳
    "mermaid_theme": "default",                              # Mermaid图表主题
    
    # 数据列名映射（根据实际CSV文件调整）
    "column_mapping": {
        "level1": "一级功能模块",
        "level2": "二级功能模块", 
        "level3": "三级功能模块",
        "function": "功能过程",
        "user": "功能用户",
        "trigger": "触发事件",
        "subprocess": "子过程描述",
        "data_movement": "数据移动类型",
        "data_group": "数据组",
        "data_attribute": "数据属性"
    }
}
```

## 使用方法

### 1. 基本使用

```bash
# 使用默认配置生成文档
python requirement_generator.py

# 测试模式（小批次）
python requirement_generator.py test
```

### 2. 编程方式使用

```python
from requirement_generator import RequirementGenerator

# 创建生成器
generator = RequirementGenerator(
    start_number="2",           # 起始序号
    max_subprocess_per_batch=50 # 每批次最大子过程数
)

# 生成文档
output_file = generator.generate_requirements_document("output.csv")
print(f"生成的文档: {output_file}")
```

### 3. 自定义配置

```python
# 自定义起始序号
generator = RequirementGenerator(start_number="3.1")  # 从3.1.1开始

# 自定义批次大小
generator = RequirementGenerator(max_subprocess_per_batch=30)
```

## 输入数据格式

输入的CSV文件应包含以下列：

| 列名 | 说明 | 示例 |
|------|------|------|
| 一级功能模块 | 一级模块名称 | 用户管理 |
| 二级功能模块 | 二级模块名称 | 用户账户管理 |
| 三级功能模块 | 三级模块名称 | 用户注册 |
| 功能过程 | 功能过程名称 | 用户注册 |
| 功能用户 | 功能用户描述 | 发起者：新用户，接收者：系统 |
| 触发事件 | 触发事件描述 | 用户注册请求 |
| 子过程描述 | 子过程详细描述 | 输入用户注册信息 |
| 数据移动类型 | E/R/W/X | E |
| 数据组 | 数据组名称 | 用户注册信息 |
| 数据属性 | 数据属性列表 | 用户名、密码、邮箱 |

## 输出文档格式

生成的Markdown文档结构如下：

```markdown
## 2.1 一级功能需求 （对应一级模块）
### 2.1.1 二级功能需求 （对应二级模块）
#### ******* 三级功能需求 （对应三级模块）
##### *******.1 功能过程 （对应功能过程）
###### *******.1.1 关键时序图

```mermaid
sequenceDiagram
    participant User as 功能用户
    participant System as 系统
    participant DB as 数据库
    
    User->>System: 触发事件描述
    System->>System: 子过程1描述
    System->>DB: 子过程2描述(R/W)
    DB-->>System: 返回数据
    System->>User: 子过程N描述(输出)
```

###### *******.1.2 需求描述

该功能用于[功能目的]。当[触发条件]时，系统执行以下处理流程：

1. **输入处理**：[描述输入数据和验证逻辑]
2. **业务处理**：[描述核心业务逻辑]
3. **数据操作**：[描述数据读取、写入操作]
4. **输出结果**：[描述输出内容和格式]

涉及的主要数据包括：[列举关键数据组和属性]

业务规则：[描述重要的业务约束和规则]
```

## 知识库集成

生成器会自动使用知识库信息来增强需求描述：

1. **用户手册信息**：理解功能的具体业务场景和操作流程
2. **数据库实体信息**：确保数据组和数据属性的准确性
3. **上下文相关性**：根据模块名称和功能过程自动检索相关信息

## 批次处理

为了避免单次请求过大，生成器支持分批次处理：

- 默认每批次最多50个子过程
- 自动按三级模块分组
- 保持序号连续性
- 支持自定义批次大小

## 序号管理

支持灵活的序号配置：

| 起始序号 | 生成结果 | 说明 |
|----------|----------|------|
| "2" | 2.1, 2.2, ... | 从2.1开始 |
| "3" | 3.1, 3.2, ... | 从3.1开始 |
| "3.1" | 3.1.1, 3.1.2, ... | 从3.1.1开始 |
| "4.2" | 4.2.1, 4.2.2, ... | 从4.2.1开始 |

## 测试和调试

### 运行测试

```bash
# 运行完整测试
python debug/test_requirement_generator.py

# 运行小批次测试
python requirement_generator.py test
```

### 调试输出

生成的文档会保存在 `debug/` 目录下，文件名格式：
```
功能需求文档_{输入文件名}_{时间戳}.md
```

### 常见问题

1. **CSV文件列名不匹配**：修改 `config.py` 中的 `column_mapping`
2. **知识库未初始化**：确保知识库文件存在并正确配置
3. **API限制**：调整批次大小或增加延时
4. **序号错误**：检查起始序号格式是否正确

## 扩展功能

### 自定义提示词

修改 `功能需求生成提示词.md` 文件来自定义生成逻辑：

- 调整时序图样式
- 修改需求描述模板
- 添加特定业务规则

### 自定义输出格式

在 `RequirementGenerator` 类中修改：

- `format_batch_for_llm()`: 调整输入格式
- `generate_batch_requirements()`: 修改生成逻辑
- 添加后处理步骤

## 注意事项

1. **数据质量**：确保输入CSV数据完整且格式正确
2. **知识库**：充分利用知识库信息提高生成质量
3. **批次大小**：根据API限制调整批次大小
4. **序号连续性**：确保跨批次的序号正确递增
5. **文档审查**：生成后需要人工审查和完善

## 更新日志

- v1.0: 基本功能实现，支持COSMIC数据转换为功能需求文档
- 支持知识库集成和分批次处理
- 支持灵活的序号配置和自定义格式
