#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分批次校验功能
"""

import sys
import os
import pandas as pd
import json

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cosmic_validator import CosmicValidator
import config

def create_test_data():
    """创建测试数据"""
    test_data = []
    
    # 创建一些测试记录
    for i in range(50):  # 创建50条记录用于测试
        record = {
            "一级功能模块": f"模块{i//10 + 1}",
            "二级功能模块": f"子模块{i//5 + 1}",
            "三级功能模块": f"功能{i + 1}",
            "功能用户": "管理员",
            "触发事件": f"事件{i + 1}",
            "功能过程": f"过程{i + 1}",
            "子过程描述": f"子过程描述{i + 1}",
            "数据移动类型": ["E", "R", "W", "X"][i % 4],
            "数据组": f"数据组{i + 1}",
            "CFP": 1.0
        }
        test_data.append(record)
    
    # 保存为CSV文件
    df = pd.DataFrame(test_data)
    test_file = "debug/test_cosmic_data.csv"
    df.to_csv(test_file, index=False, encoding='utf-8-sig')
    print(f"测试数据已保存到: {test_file}")
    return test_file

def test_batch_validation():
    """测试分批次校验功能"""
    print("开始测试分批次校验功能...")
    
    # 创建测试数据
    test_file = create_test_data()
    
    # 创建校验器实例
    validator = CosmicValidator(config)
    
    # 模拟校验结果（因为没有真实的API）
    def mock_llm_call(prompt, data):
        """模拟大模型调用"""
        # 检查是否是汇总建议请求
        if "汇总建议" in prompt or "综合性的汇总建议" in prompt:
            return json.dumps({
                "priority_recommendations": [
                    "优先解决高严重程度的数据组聚合问题，这类问题影响CFP计算准确性",
                    "其次处理中等严重程度的重复计数问题，避免功能点重复统计",
                    "建立统一的数据组命名规范，确保跨模块一致性"
                ],
                "common_patterns": [
                    "数据组命名不规范是最常见的问题，占总问题的50%",
                    "重复计数问题主要出现在批量操作和分页查询中",
                    "缺少统一的业务实体定义标准"
                ],
                "improvement_measures": [
                    "制定并执行数据组命名规范文档",
                    "建立代码审查机制，重点检查数据组定义",
                    "开发自动化检测工具，识别重复计数问题",
                    "定期进行COSMIC方法培训，提高团队规范意识"
                ],
                "best_practices": [
                    "使用'业务实体+操作类型'的数据组命名模式",
                    "对于分页查询，将分页信息与主数据组合并计数",
                    "建立数据组字典，统一管理所有数据组定义",
                    "在功能设计阶段就考虑COSMIC拆解规范"
                ],
                "severity_analysis": {
                    "高": "主要涉及数据组聚合问题，直接影响CFP计算的准确性，需要立即处理",
                    "中": "重复计数问题会导致功能点估算偏高，影响项目规模评估",
                    "低": "格式和命名问题，不影响功能但需要规范化"
                }
            }, ensure_ascii=False)
        else:
            # 普通校验请求
            return json.dumps({
                "overall_assessment": {
                    "total_records": 50,
                    "compliance_rate": "85.0%",
                    "major_issues_count": 5,
                    "minor_issues_count": 10
                },
                "detailed_findings": [
                    {
                        "module_path": "模块1/子模块1/功能1",
                        "function_process": "过程1",
                        "subprocess_description": "子过程描述1",
                        "issue_type": "数据组聚合",
                        "severity": "高",
                        "current_value": "数据组1",
                        "issue_description": "数据组命名不规范",
                        "suggested_fix": "使用标准命名规范",
                        "example": "标准数据组名称"
                    },
                    {
                        "module_path": "模块1/子模块2/功能2",
                        "function_process": "过程2",
                        "subprocess_description": "子过程描述2",
                        "issue_type": "重复计数",
                        "severity": "中",
                        "current_value": "数据组2",
                        "issue_description": "存在重复计数",
                        "suggested_fix": "合并重复操作",
                        "example": "合并后的操作"
                    }
                ],
                "summary_recommendations": [
                    "优先解决数据组命名问题",
                    "检查重复计数情况"
                ]
            }, ensure_ascii=False)
    
    # 替换大模型调用方法
    validator._call_llm_for_validation = mock_llm_call
    
    try:
        # 执行校验
        result = validator.validate_cosmic_data(test_file, "check_prompt.md")
        
        # 保存结果
        validator.save_validation_result(result, "debug/test_validation_result.json")
        validator.save_validation_report(result, "debug/test_validation_report.txt")
        
        print("测试完成！")
        print(f"结果已保存到: debug/test_validation_result.json")
        print(f"报告已保存到: debug/test_validation_report.txt")
        
        # 显示关键信息
        if "merged_validation_result" in result:
            merged = result["merged_validation_result"]
            summary = merged.get("summary", {})
            severity_stats = summary.get("severity_statistics", {})
            
            print("\n严重程度统计:")
            counts = severity_stats.get("counts", {})
            percentages = severity_stats.get("percentages", {})
            for severity in ['高', '中', '低']:
                count = counts.get(severity, 0)
                percentage = percentages.get(severity, 0)
                print(f"  {severity}严重程度: {count}个 ({percentage}%)")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_batch_validation()
