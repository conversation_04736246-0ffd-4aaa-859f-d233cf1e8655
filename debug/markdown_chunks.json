[{"title": "文档前言", "content_length": 27, "content_preview": "密码服务平台管理端\n\n用户手册\n\n（无区域多租户模式）", "type": "function", "source": "user_manual"}, {"title": "版权声明", "content_length": 185, "content_preview": "版权所有 ©三未信安科技股份有限公司 2023 保留一切权利（包括但不限于修订、最终解释权）。\n\n本文档由三未信安科技股份有限公司编写，仅用于用户和合作伙伴阅读。本公司依中华人民共和国著作权法，享有及保留一切著作之专属权利。未经本公司书面许可，任何单位和个人不得以任何方式或形式对文档内任何部分或全部内容进行擅自摘抄、增删、复制、仿制、备份和修改，并不得以任何形式传播。", "type": "function", "source": "user_manual"}, {"title": "特别提示", "content_length": 397, "content_preview": "由于产品版本升级或其他原因，本文档内容会不定期更新，更新的内容会在本文档发行新版本时予以印刷。本文档仅用于为最终用户提供信息或使用指导，文档中的陈述、信息和建议不构成任何明示或暗示的担保。您所购买产品的硬件配置、功能、特性或服务等应受本公司商业合同和条款约束。本文档中描述的硬件配置、功能、特性或服务可能不在您的购买或使用范围之内。任何情况下，本公司均不对（包括但不限于）最终用户或任何第三方因使用本...", "type": "function", "source": "user_manual"}, {"title": "目录", "content_length": 2040, "content_preview": "1. 产品介绍.1.1. 产品简介 .1.2. 产品功能 .  \n2. 基本概念.  \n3. 功能模块介绍 .3.1. 平台首页 .3.1.1. 平台资源统计 . . 93.1.2. 密码服务统计 . 103.1.3. 密码设备统计 . 113.1.4. 密评统计 . 123.1.5. 工单统计 . 133.1.6. 告警统计 . 133.1.7. 应用调用次数 . 143.2. 许可授权管理 ....", "type": "function", "source": "user_manual"}, {"title": "1. 产品介绍", "content_length": 279, "content_preview": "## 1.1.产品简介\n\n密码作为信息安全防护的重要手段，在关键信息系统和基础网络防护中发挥着不可替代的重要作用。密码服务平台从服务的视角来进行设计，提供了直接满足用户业务需求的密码服务，其具有灵活的架构基础来支撑服务的多样性和可扩展性。\n\n## 1.2.产品功能\n\n密码服务管理平台以密码设备为基础，提供设备以及服务的集中统一管理方式，包含租户管理、密码设备管理、密码服务管理、用户管理等功能。通过...", "type": "function", "source": "user_manual"}, {"title": "2. 基本概念", "content_length": 146, "content_preview": "密码机：提供密码基础运算功能的服务器硬件产品或云密码机创建的虚拟密码机。设备主密钥：虚拟服务器密码机的密钥产生时由设备内部的主密钥进行加密后产生，同一个租户内的虚拟机的主密钥需保持一致才可以保持同一个密钥在租户内所有设备上进行正常的密码运算。没有主密钥的设备，不允许分配给租户和进行业务运算。", "type": "function", "source": "user_manual"}, {"title": "3. 功能模块介绍 (第1部分)", "content_length": 7995, "content_preview": "## 3.1.平台大屏\n\n平台大屏对密码设备、密码服务、应用、密评、密码监控信息、告警信息等进行了总体展示于统计\n\n![](images/da291914db0f67864c86d0c0ae9e40232a23befa3e37d1b3a29f71ace76300c1.jpg)\n```text\ntext文本框内的内容是视觉模型对图片的识别结果：\n这是一张密码服务平台监控大屏的截图，显示了多个监控和统...", "type": "function", "source": "user_manual"}, {"title": "3. 功能模块介绍 (第2部分)", "content_length": 7851, "content_preview": "1. **vsm_10_20_17_114_443**\n   - CPU使用率：6%\n   - 内存使用率：34%\n   - 磁盘使用率：8%\n\n2. **vsm_10_20_17_115_443**\n   - CPU使用率：0%\n   - 内存使用率：0%\n   - 磁盘使用率：0%\n\n3. **vsm_10_20_17_112_443**\n   - CPU使用率：6%\n   - 内存使用率：3...", "type": "function", "source": "user_manual"}, {"title": "3. 功能模块介绍 (第3部分)", "content_length": 4592, "content_preview": "![](images/51559808ef37b311a9229222606b8beb844be0cfe21fa341f4bf1fc76dd0190d.jpg)\n```text\ntext文本框内的内容是视觉模型对图片的识别结果：\n这是一张显示许可证申请状态的网页截图。\n```\n  \n密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)\n\n#### 3.3.1.1. 申请许可\n\n申请许可时，...", "type": "function", "source": "user_manual"}, {"title": "3.4.租户管理 (第1部分)", "content_length": 7854, "content_preview": "## 3.4.1. 租户审核\n\n系统操作员登录平台，打开租户管理下待审核租户菜单，查看待审核的租户信息。点击待审核租户对象操作列“审核”按钮，弹出租户审核弹出框，选择是否通过和输入审核意见。审核通过：为租户选择区域、使用的数据库、单位名称、是否支持专享服务，点击“确定”按钮，通过租户。\n\n审核拒绝：输入不通过的审核意见。\n\n![](images/10b62b25b989b3521e8d0b2378...", "type": "function", "source": "user_manual"}, {"title": "3.4.租户管理 (第2部分)", "content_length": 3957, "content_preview": "![](images/1ebcbce689f87bdf9962f995679bdd085374c3c26ba945e429436d98881b83ee.jpg)\n```text\ntext文本框内的内容是视觉模型对图片的识别结果：\n这张图片显示了一个计算机界面，具体是一个管理租户信息的系统页面。页面上有一个表格，列出了多个租户的信息，包括租户标识、租户名称、机构名称、业务、应用数量、服务数量和设备数...", "type": "function", "source": "user_manual"}, {"title": "3.5.设备管理 (第1部分)", "content_length": 7864, "content_preview": "设备管理负责管理平台的所有设备信息，包括云密码机、虚拟密码机、物理密码机、服务一体机等，并负责设备组的信息管理\n\n## 3.5.1. 云密码机管理\n\n### *******. 云密码机列表\n\n云密码机列表页面中，在搜索框中输入名称和管理 IP，可以模糊查询云密码机列表。\n\n![](images/1baaaa7a2f204e6d4b3e7daa97883f7785443aafe4d18942fc79...", "type": "function", "source": "user_manual"}, {"title": "3.5.设备管理 (第2部分)", "content_length": 7921, "content_preview": "![](images/fb4713f2c99faade818f04d92a8a1ad4faed221a80989e1cde6d2b23aa3a68dc.jpg)\n```text\ntext文本框内的内容是视觉模型对图片的识别结果：\n这是一张关于批量创建虚拟机的配置界面截图。界面上有多个输入框和选择框，用于设置虚拟机的相关参数。具体包括：\n\n1. 设备类型：当前选择的是“云服务器密码机_V5.2.7”...", "type": "function", "source": "user_manual"}, {"title": "3.5.设备管理 (第3部分)", "content_length": 7743, "content_preview": "在界面底部有两个按钮：“取消”和“确定”，用户可以点击“确定”来保存所填写的信息，或者点击“取消”来放弃当前操作。\n```\n  \n图 3-4- 32 编辑物理密码机页面\n\n### 3.5.3.4. 删除物理密码机\n\n在密码机信息列表中可以删除已经注册到系统中的设备信息。\n\n![](images/619cdb3a523ed7a9d51ad0507205ea8b426e1dbe98ca60cb6e27...", "type": "function", "source": "user_manual"}, {"title": "3.5.设备管理 (第4部分)", "content_length": 6655, "content_preview": "1. **设备类型名称**：一个输入框，提示用户输入设备类型名称。\n2. **所属厂商**：一个下拉菜单，当前选择的是“三未信安科技股份有限公司”。\n3. **管理规范**：一个下拉菜单，提示用户选择管理规范。\n4. **接口协议**：两个单选按钮，分别表示HTTPS和HTTP。\n5. **虚拟机镜像类型**：一个下拉菜单，提示用户选择虚拟机镜像类型。\n6. **资源配置**：一个下拉菜单，提示用...", "type": "function", "source": "user_manual"}, {"title": "3.6.服务管理 (第1部分)", "content_length": 7944, "content_preview": "服务管理负责维护与服务相关的模块，是密码服务平台的核心模块\n\n## 3.6.1. 服务管理\n\n以系统操作员身份登录系统，在服务管理中可查看各个服务的详细信息，并完成服务添加、编辑、启动、停止、删除等操作。\n\n### 3.6.1.1. 服务列表\n\n打开服务管理菜单，页面展示服务信息列表。\n\n![](images/325d5efec3c40a36cfd0c52ef6838f7c96e23c7ea974...", "type": "function", "source": "user_manual"}, {"title": "3.6.服务管理 (第2部分)", "content_length": 7861, "content_preview": "![](images/f0a858aea36dc6818d6a4c000354b69430e57e4473f2ebc01fcb4c50f481d3a1.jpg)\n```text\ntext文本框内的内容是视觉模型对图片的识别结果：\n这是一张关于新增数据库的界面截图，界面上有多个输入框和选择框，用于填写数据库的相关信息。具体包括：\n\n1. 数据库名称：需要输入数据库的名称。\n2. 区域：需要从下拉菜单...", "type": "function", "source": "user_manual"}, {"title": "3.6.服务管理 (第3部分)", "content_length": 4788, "content_preview": "表格中列出了六个镜像的信息：\n1. image-ccsp-secdb-openeuler-x86：数据库加密服务，镜像版本3.4.0.2，文件大小674MB，状态为启用。\n2. image-ccsp-sms-openeuler-x86：协同签名服务，镜像版本4.6.5.1，文件大小561MB，状态为启用。\n3. image-ccsp-secauth-openeuler-x86：动态令牌服务，镜像版...", "type": "function", "source": "user_manual"}, {"title": "3.7.用户管理 (第1部分)", "content_length": 7964, "content_preview": "系统操作员具有用户管理的权限，系统管理员具有流程审核的权限，使用系统初始化时添加的系统操作员登录平台后进行用户管理功能操作，使用系统初始化时添加的系统管理员登录平台后进行流程审核功能操作。\n\n## 3.7.1. 人员管理配置\n\n### 3.7.1.1. 登录配置展示\n\n系统管理员登录，点击系统管理-》人员管理配置，打开登录配置菜单，页面展示登录配置参数。\n\n![](images/a1e0943fa...", "type": "function", "source": "user_manual"}, {"title": "3.7.用户管理 (第2部分)", "content_length": 2013, "content_preview": "此外，页面顶部有一个搜索框和重置按钮，方便管理员快速查找特定的注册记录。页面底部显示了当前页码和总页数，以及导航按钮，以便于分页浏览所有的注册记录。\n```\n  \n密码服务平台管理端用户手册 v3.4.0(无区域多租户模式)  \n图 3-6-13 审核用户注册\n\n### 3.7.4. 单位管理\n\n### 3.7.4.1. 单位信息列表\n\n单位管理功能在系统支持组织管理时显示，进入单位管理功能，显示...", "type": "function", "source": "user_manual"}, {"title": "3.8.系统管理 (第1部分)", "content_length": 7922, "content_preview": "系统管理员具有设置口令黑名单、人员管理配置、系统配置等操作权限。\n\n## 3.8.1. 口令黑名单\n\n### 3.8.1.1. 弱口令配置列表展示\n\n打开口令黑名单菜单，页面展示弱口令列表。\n\n![](images/c80079abb81dd3c8f4c2b2ee4cff70cdb74da2ae93e659379249f7813cf2b5aa.jpg)\n```text\ntext文本框内的内容是视觉...", "type": "function", "source": "user_manual"}, {"title": "3.8.系统管理 (第2部分)", "content_length": 7142, "content_preview": "![](images/ad4a699b59829c75980105eac786515dcd5b4e1cbf2a194cc55bc895ebf8e0fb.jpg)\n```text\ntext文本框内的内容是视觉模型对图片的识别结果：\n这是一张关于新建SYSLOG服务的配置界面截图。在界面上，有多个输入框和选项供用户填写和选择，以完成SYSLOG服务的设置。具体包括：\n\n1. **日志上报类型**：这是...", "type": "function", "source": "user_manual"}, {"title": "3.9.日志管理", "content_length": 1226, "content_preview": "## 3.9.1. 管理日志\n\n### *******. 操作日志查询\n\n![](images/1f4739fdf284ad21e0a2576a0a43f31daa42b65513c09c20997230da22846f8a.jpg)\n```text\ntext文本框内的内容是视觉模型对图片的识别结果：\n这张图片显示的是一个操作审计日志的界面，记录了不同功能模块下的操作内容、操作人、请求IP、操作状...", "type": "function", "source": "user_manual"}, {"title": "3.10. 监控告警", "content_length": 5928, "content_preview": "## 3.10.1. 告警信息\n\n以系统操作员身份登录系统，在监控告警-告警信息中可以查看告警信息。\n\n![](images/52bf53a77416ebee2b8669f67836272fb3615308985acf311b496242f0f46c38.jpg)\n```text\ntext文本框内的内容是视觉模型对图片的识别结果：\n这张图片显示了一个告警信息的管理界面，具体包含以下内容：\n\n1. ...", "type": "function", "source": "user_manual"}, {"title": "3.11. 密码产品 (第1部分)", "content_length": 7490, "content_preview": "以系统操作员身份登录系统，可以对密码产品进行管理。\n\n## 3.11.1. 产品信息\n\n在产品信息中可查看各个产品的详细信息，并完成产品的上架、下架、新增、删除、预览、编辑等操作。\n\n### ********. 产品上架和下架\n\n点击列表中对应产品的上架开关，可以将产品状态设置为上架或下架。只有上架的产品才能对其服务进行配额管理。\n\n![](images/8c0bcde6139990735ea54...", "type": "function", "source": "user_manual"}, {"title": "3.11. 密码产品 (第2部分)", "content_length": 2800, "content_preview": "<html><body><table><tr><td colspan=\"2\">批量已读</td><td colspan=\"6\">批量删除</td></tr><tr><td>□</td><td colspan=\"2\">消息来源</td><td>标题</td><td>内容</td><td>是否已读</td><td>创建时间</td><td>操作</td></tr><tr><td>√</td><td c...", "type": "function", "source": "user_manual"}, {"title": "3.13. 工单管理", "content_length": 2767, "content_preview": "以系统操作员身份登录系统，可以对工单进行管理。\n\n## 3.13.1. 待处理工单\n\n系统操作员登录之后，点击界面左边菜单栏工单管理->待处理工单按钮，进入待处理工单管理界面。\n\n问题描述 工单类型 所属租户 Q查询号 工单编号 工单类型 所属租户 问题描述 提交时间 状态 操作\n\n### 3.13.1.1. 处理工单\n\n点击对应工单（状态为“待处理“）的查看按钮进入工单详情界面，再点击右上角的开...", "type": "function", "source": "user_manual"}, {"title": "3.14. 密评管理 (第1部分)", "content_length": 7953, "content_preview": "## 3.14.1. 密码知识库\n\n### 3.14.1.1. 密码知识库列表\n\n点击密码知识库，展示密码知识库列表，界面显示密码知识库名称，所属分类、文件类型、文件大小、备注、创建时间、是否支持租户访问（租户不显示），可根据知识库名称模糊查询，也可以选择知识库分类进行分类查询。\n\n![](images/79e4d005504aa0d3bfd1bdf170cc05da8dfea7db76ecd1a...", "type": "function", "source": "user_manual"}, {"title": "3.14. 密评管理 (第2部分)", "content_length": 7815, "content_preview": "![](images/06949960b98ee67f4a5aaf9f2e5ef22f089813964e93d95e84f925ba8548950e.jpg)\n```text\ntext文本框内的内容是视觉模型对图片的识别结果：\n这是一张折线图，标题为“密评单位统计”。图表中有两条折线，分别代表“已注册单位数”和“开始密改单位数”。横轴表示日期，从9月13日到9月19日；纵轴表示单位数量，数值范围...", "type": "function", "source": "user_manual"}, {"title": "3.14. 密评管理 (第3部分)", "content_length": 7882, "content_preview": "![](images/05e2363a3d9b21f978c71765602d0138900ac632fc1664d86119d482e74f01be.jpg)\n```text\ntext文本框内的内容是视觉模型对图片的识别结果：\n这张图片显示了一个管理界面，主要用于管理和编辑不同级别的默认模板。界面上方有一个搜索框，用户可以输入模板名称进行查询，并有“查询”和“重置”按钮。在搜索框下方，有一个表格...", "type": "function", "source": "user_manual"}, {"title": "3.14. 密评管理 (第4部分)", "content_length": 1781, "content_preview": "![](images/fc855cabb6f5a5baed03ae7fd6ab992d7377db958efef4b0216d712c18d45de6.jpg)\n```text\ntext文本框内的内容是视觉模型对图片的识别结果：\n这是一张显示“新建交付模板”界面的截图。界面上有以下内容：\n\n1. **业务类型**：这是一个下拉菜单，提示用户“请选择业务类型”，表示用户需要从下拉菜单中选择一个业务类...", "type": "function", "source": "user_manual"}, {"title": "3.16. 密评基础数据 (第1部分)", "content_length": 7909, "content_preview": "以系统操作员登录系统，能够进行密评基础数据的管理。\n\n## 3.16.1. 密评机构管理\n\n系统操作员登录后，点击界面左边菜单栏密评基础数据->密评机构管理按钮，进入密评机构管理界面。\n\n### 3.16.1.1. 密评机构列表\n\n打开密评机构管理菜单，页面展示密评机构信息列表。\n\n![](images/7bf1478543d74290ee2c661bc6758508738ea83d3056891...", "type": "function", "source": "user_manual"}, {"title": "3.16. 密评基础数据 (第2部分)", "content_length": 7940, "content_preview": "![](images/d38fa6afa6f20159224c9052a220eea16b7ff695e5321dfb7e0264d0063e1336.jpg)\n```text\ntext文本框内的内容是视觉模型对图片的识别结果：\n这张图片显示的是一个计算机软件界面，具体来说是一个用于创建或编辑“密评要求”的窗口。这个窗口包含几个输入字段和按钮：\n\n1. **密评要求类型**：这是一个下拉菜单，用户...", "type": "function", "source": "user_manual"}, {"title": "3.16. 密评基础数据 (第3部分)", "content_length": 4847, "content_preview": "![](images/39727593f16eed5da74fffd5cce90268e06d90df31faff9e4096bde55d24783c.jpg)\n```text\ntext文本框内的内容是视觉模型对图片的识别结果：\n这张图片显示的是一个计算机界面，具体来说是一个弹出的对话框，标题为“新建密评条目改进建议”。这个对话框包含两个主要部分：\n\n1. **改进建议**：这是一个文本输入框，提...", "type": "function", "source": "user_manual"}, {"title": "3.17. 计量分析", "content_length": 725, "content_preview": "## 3.17.1. 计量信息\n\n### 3.17.1.1. 计量信息列表\n\n系统操作员登录，点击计量分析下的计量信息，列表展示统计到的业务信息以及流量等信息。该信息是一段时间内业务调用的展示，包括调用次数、总流量、流速峰值等信息。统计维度可按照分钟、小时、天、月查询。\n\n![](images/141d9372fb398e2dbc72729dd96b2d0df285d3c58c898f0d1058...", "type": "function", "source": "user_manual"}, {"title": "3.18. 数据备份 (第1部分)", "content_length": 7907, "content_preview": "系统操作员登录平台可对平台数据库进行自动或手动的备份，备份可整库备份、以服务组为单位备份。提供了数据的自动备份、自动清理、手动备份、备份还原等功能，当前版本仅支持达梦以及 openguass。\n\n基本概念：\n\n1、物理备份\n\n① 物理备份是指对数据库的操作系统物理文件（数据文件、控制文件、日志文件等）的备份；\n\n② 物理备份的备份范围仅支持整库备份；  \n③ 物理备份支持全量备份及增量备份。全量备...", "type": "function", "source": "user_manual"}, {"title": "3.18. 数据备份 (第2部分)", "content_length": 3207, "content_preview": "系统操作员登录，点击数据备份-》还原记录。界面显示还原记录列表，展示自动备份和手动备份执行还原操作的数据库以及状态等信息。其中，“还原服务组”指该次还原涉及到哪些服务组；“还原内容”指该次还原具体还原了哪些数据库模式。\n\n![](images/0c07c1bdadf54306e4ca1dc1774076f825e2d10cda93af4bc3345d078b590ad8.jpg)\n```text\n...", "type": "function", "source": "user_manual"}, {"title": "3.19. 在线巡检", "content_length": 3499, "content_preview": "在线巡检可配置一定的巡检策略，生成巡检报告。报告中可展示对平台使用的数据库、服务组、设备组信息检测结果。\n\n## 3.18.1. 巡检策略\n\n### 3.18.1.1. 巡检策略列表\n\n系统操作员登录，点击在线巡检-》巡检策略，界面展示配置的巡检策略，包括策略名称、报告保留天数、巡检类型、下次执行时间等信息。\n\n![](images/08c5f870677d6b48475bf8e3d9381877...", "type": "function", "source": "user_manual"}, {"title": "3.20. 升级管理", "content_length": 2174, "content_preview": "升级管理可对多节点平台的升级包进行在线升级，主要步骤包括升级包上传、升级、回滚操作。\n\n## 3.20.1. 升级包管理\n\n### 3.20.1.1. 升级包列表\n\n系统操作员登录，点击升级管理-》升级包管理，列表展示上传的升级包信息，包括升级包的名称、升级前后的版本变化等信息。\n\n![](images/d4e94288656fb3521175fffaf3373c32284548fee6b7b37...", "type": "function", "source": "user_manual"}, {"title": "3.21. 平台灾备租户信息迁移", "content_length": 1982, "content_preview": "灾备租户信息迁移功能目的在于尽可能减少人为迁移工作量，该迁移操作涉及到的迁移范围如下图：\n\n![](images/517f339777100dea943cb54316d78e7a583c44ecb081364162513cad459984f6.jpg)\n```text\ntext文本框内的内容是视觉模型对图片的识别结果：\n这张图片展示了一个流程图，描述了从申请导入许可到服务站点部署及取消注册的整个过...", "type": "function", "source": "user_manual"}, {"title": "公司介绍", "content_length": 860, "content_preview": "三未信安科技股份有限公司（股票代码：688489）成立于 2008 年，是国内主要的密码基础设施提供商，专注于密码技术的研究创新和核心产品的开发、销售及服务，为用户提供全面的商用密码产品和整体解决方案。\n\n三未信安具备从密码芯片、密码板卡、密码整机到密码系统的完整密码产品体系和信创密码建设能力，当前已有五十多款产品取得了国家商用密码产品认证证书，是商用密码产品种类最齐全的公司之一。典型产品包括密码...", "type": "function", "source": "user_manual"}]