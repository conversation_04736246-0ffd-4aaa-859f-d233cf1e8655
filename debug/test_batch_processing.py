#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分批次处理功能
"""

import sys
import os
import pandas as pd
import json
sys.path.append('..')

from cosmic_validator import CosmicValidator
import config

def create_large_csv_test_data():
    """创建大量CSV测试数据"""
    print("创建大量CSV测试数据...")
    
    # 读取原始数据
    original_file = "../output-sft.csv"
    if not os.path.exists(original_file):
        print(f"原始文件 {original_file} 不存在")
        return None
    
    df = pd.read_csv(original_file, encoding='utf-8-sig')
    
    # 复制数据以创建大数据集（复制5倍）
    large_df = pd.concat([df] * 5, ignore_index=True)
    
    # 保存大数据集
    large_csv_file = "large_test_data.csv"
    large_df.to_csv(large_csv_file, index=False, encoding='utf-8-sig')
    
    print(f"创建了包含 {len(large_df)} 行数据的大CSV文件: {large_csv_file}")
    return large_csv_file

def create_large_json_test_data():
    """创建大量JSON测试数据"""
    print("创建大量JSON测试数据...")
    
    # 读取原始数据
    original_file = "../output-sft.json"
    if not os.path.exists(original_file):
        print(f"原始文件 {original_file} 不存在")
        return None
    
    with open(original_file, 'r', encoding='utf-8') as f:
        original_data = json.load(f)
    
    # 复制数据以创建大数据集（复制3倍）
    large_data = original_data * 3
    
    # 保存大数据集
    large_json_file = "large_test_data.json"
    with open(large_json_file, 'w', encoding='utf-8') as f:
        json.dump(large_data, f, ensure_ascii=False, indent=2)
    
    print(f"创建了包含 {len(large_data)} 条记录的大JSON文件: {large_json_file}")
    return large_json_file

def test_csv_batch_processing():
    """测试CSV分批次处理"""
    print("=" * 60)
    print("测试CSV分批次处理")
    print("=" * 60)
    
    # 创建大数据文件
    large_csv_file = create_large_csv_test_data()
    if not large_csv_file:
        return False
    
    validator = CosmicValidator(config)
    
    print(f"开始分批次处理CSV文件: {large_csv_file}")
    result = validator.validate_cosmic_data(large_csv_file)
    
    if "error" in result:
        print(f"CSV分批次处理失败: {result['error']}")
        return False
    
    print("CSV分批次处理成功！")
    
    # 显示批次处理信息
    batch_info = result.get('batch_processing_info', {})
    print(f"总批次数: {batch_info.get('total_batches', 0)}")
    print(f"成功批次: {batch_info.get('successful_batches', 0)}")
    print(f"失败批次: {batch_info.get('failed_batches', 0)}")
    print(f"处理方法: {batch_info.get('processing_method', '未知')}")
    
    # 保存结果
    validator.save_validation_result(result, "debug/csv_batch_result.json")
    validator.save_validation_report(result, "debug/csv_batch_report.txt")
    
    # 清理测试文件
    try:
        os.remove(large_csv_file)
        print(f"已清理测试文件: {large_csv_file}")
    except:
        pass
    
    return True

def test_json_batch_processing():
    """测试JSON分批次处理"""
    print("=" * 60)
    print("测试JSON分批次处理")
    print("=" * 60)
    
    # 创建大数据文件
    large_json_file = create_large_json_test_data()
    if not large_json_file:
        return False
    
    validator = CosmicValidator(config)
    
    print(f"开始分批次处理JSON文件: {large_json_file}")
    result = validator.validate_cosmic_data(large_json_file)
    
    if "error" in result:
        print(f"JSON分批次处理失败: {result['error']}")
        return False
    
    print("JSON分批次处理成功！")
    
    # 显示批次处理信息
    batch_info = result.get('batch_processing_info', {})
    print(f"总批次数: {batch_info.get('total_batches', 0)}")
    print(f"成功批次: {batch_info.get('successful_batches', 0)}")
    print(f"失败批次: {batch_info.get('failed_batches', 0)}")
    print(f"处理方法: {batch_info.get('processing_method', '未知')}")
    
    # 显示功能过程摘要
    fp_summary = result.get('function_process_summary', {})
    if fp_summary:
        print(f"功能过程: 总数={fp_summary.get('total_processes', 0)}, "
              f"完整={fp_summary.get('complete_processes', 0)}, "
              f"不完整={fp_summary.get('incomplete_processes', 0)}")
    
    # 保存结果
    validator.save_validation_result(result, "debug/json_batch_result.json")
    validator.save_validation_report(result, "debug/json_batch_report.txt")
    
    # 清理测试文件
    try:
        os.remove(large_json_file)
        print(f"已清理测试文件: {large_json_file}")
    except:
        pass
    
    return True

def test_batch_size_calculation():
    """测试批次大小计算"""
    print("=" * 60)
    print("测试批次大小计算")
    print("=" * 60)
    
    # 模拟不同大小的数据
    test_cases = [
        {"lines": 1000, "expected_batches": "约5批次"},
        {"lines": 5000, "expected_batches": "约25批次"},
        {"lines": 10000, "expected_batches": "约50批次"}
    ]
    
    for case in test_cases:
        lines = case["lines"]
        batch_size = max(100, 30000 // 150)  # 与实际代码相同的计算逻辑
        batches = (lines + batch_size - 1) // batch_size  # 向上取整
        
        print(f"数据行数: {lines}, 批次大小: {batch_size}, 预计批次数: {batches}")

def check_batch_files():
    """检查生成的批次文件"""
    print("=" * 60)
    print("检查生成的批次文件")
    print("=" * 60)
    
    batch_files = []
    for file in os.listdir('.'):
        if file.startswith('csv_batch_') or file.startswith('json_batch_'):
            batch_files.append(file)
    
    if batch_files:
        print(f"找到 {len(batch_files)} 个批次文件:")
        for file in sorted(batch_files):
            file_size = os.path.getsize(file)
            print(f"  {file} ({file_size} 字节)")
    else:
        print("没有找到批次文件")

def main():
    """主函数"""
    print("开始测试分批次处理功能")
    print("注意：此测试会创建大量数据，可能需要较长时间")
    print()
    
    # 确保debug目录存在
    os.makedirs("debug", exist_ok=True)
    
    # 测试批次大小计算
    test_batch_size_calculation()
    print()
    
    # 测试CSV分批次处理
    csv_success = test_csv_batch_processing()
    print()
    
    # 测试JSON分批次处理
    json_success = test_json_batch_processing()
    print()
    
    # 检查生成的批次文件
    check_batch_files()
    
    print("=" * 60)
    print("分批次处理测试完成！")
    if csv_success and json_success:
        print("✅ 所有测试通过")
    else:
        print("❌ 部分测试失败")
    print("请查看debug目录下的结果文件")
    print("=" * 60)

if __name__ == "__main__":
    main()
