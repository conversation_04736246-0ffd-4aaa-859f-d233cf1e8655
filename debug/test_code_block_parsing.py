#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试代码块内标题解析修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from knowledge_base import KnowledgeBase

def test_code_block_parsing():
    """测试代码块内标题解析修复"""
    
    # 创建知识库实例
    kb = KnowledgeBase()
    
    # 读取测试文件
    test_file = "debug/test_code_block_fix.md"
    with open(test_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("原始内容:")
    print("=" * 50)
    print(content)
    print("=" * 50)
    
    # 测试修复后的切块逻辑
    print("\n测试修复后的标题解析:")
    print("-" * 50)
    
    chunks = kb._parse_markdown_hierarchy(content)
    
    print(f"总共生成了 {len(chunks)} 个切块:")
    print()
    
    for i, chunk in enumerate(chunks, 1):
        print(f"块{i}: {chunk['title']}")
        print(f"内容预览: {chunk['content'].strip()[:100]}...")
        print("-" * 30)
    
    # 验证不应该出现的标题
    chunk_titles = [chunk['title'] for chunk in chunks]
    
    print("\n验证结果:")
    print("-" * 30)
    
    # 这些不应该被识别为标题
    false_titles = ['基础信息', '详细配置', '这不是标题，只是注释', '这也不是标题']
    
    for false_title in false_titles:
        if false_title in chunk_titles:
            print(f"❌ 错误：'{false_title}' 被错误识别为标题")
        else:
            print(f"✅ 正确：'{false_title}' 没有被识别为标题")
    
    # 这些应该被识别为标题
    true_titles = ['*******. 查看设备详情', '另一个测试章节', '真正的三级标题', '真正的四级标题']
    
    for true_title in true_titles:
        if true_title in chunk_titles:
            print(f"✅ 正确：'{true_title}' 被正确识别为标题")
        else:
            print(f"❌ 错误：'{true_title}' 没有被识别为标题")

if __name__ == "__main__":
    test_code_block_parsing()
