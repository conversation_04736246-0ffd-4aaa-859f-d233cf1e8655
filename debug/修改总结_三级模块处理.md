# 系统修改总结：从二级模块改为三级模块处理

## 修改概述

根据用户需求，将系统处理逻辑从按二级模块为单位调用大模型，修改为按三级模块为单位调用大模型。同时适配了新的Excel文件格式，该文件新增了"功能过程"列，将原来的三级模块进行了合并，原来的三级模块降级为"功能过程"。

## 文件结构变化

### 原始文件结构
- 列名：`['一级功能模块', '二级功能模块', '三级功能模块', '功能描述', '预估工作量（人天）']`
- 处理单位：二级模块
- 总行数：289行

### 调整后文件结构  
- 列名：`['一级功能模块', '二级功能模块', '三级功能模块', '功能过程', '功能描述', '预估工作量\n（人天）']`
- 处理单位：三级模块
- 总行数：291行
- 新增：功能过程列

## 主要修改内容

### 1. 修改处理逻辑（main.py）

#### 变量调整
- 将 `current_level_2` 改为 `current_level_3` 作为主要分组标识
- 新增 `current_function_process_list` 存储功能过程列表
- 更新列名变量：`func_name` → `func_process_name`

#### 分组逻辑调整
```python
# 原逻辑：按二级模块分组
if (current_level_2 is not None) and (level_2_module != current_level_2 or level_1_module != current_level_1):

# 新逻辑：按三级模块分组  
if (current_level_3 is not None) and (level_3_module != current_level_3 or level_2_module != current_level_2 or level_1_module != current_level_1):
```

#### 批处理简化
- 移除了原来的BATCH_COUNT批处理逻辑
- 现在每个三级模块的所有功能过程一次性处理
- 简化了知识库上下文获取逻辑

#### 结果处理优化
- 在结果中正确添加功能过程信息
- 更新了输出列的顺序，包含功能过程列

### 2. 优化系统提示词（prompt.md）

#### 任务描述更新
- 明确说明现在的处理单位是三级模块
- 强调每个三级模块包含多个功能过程
- 更新了输入输出格式说明

#### 示例更新
- 提供了包含功能过程的完整示例
- 展示了新的数据结构和处理方式

### 3. 配置文件调整

#### 列名映射更新
```python
# 更新预估工作量列名（包含换行符）
estimated_workload_name = "预估工作量\n（人天）"

# 新增功能过程列名
func_process_name = "功能过程"
```

## 处理流程变化

### 原流程（按二级模块）
1. 读取Excel文件
2. 按二级模块分组
3. 每个二级模块下的所有三级模块批量处理
4. 调用大模型进行功能拆解
5. 处理结果并输出

### 新流程（按三级模块）
1. 读取Excel文件（包含功能过程列）
2. 按三级模块分组
3. 每个三级模块下的所有功能过程一次性处理
4. 调用大模型进行功能拆解
5. 处理结果并输出（包含功能过程信息）

## 输出格式变化

### 新增列
- 在原有列的基础上新增了"功能过程"列
- 列顺序：`[一级功能模块, 二级功能模块, 三级功能模块, 功能过程, 功能描述, 预估工作量, 功能用户, 触发事件, 功能过程, 子过程描述, 数据移动类型, 数据组, 数据属性, CFP]`

### 数据映射
- 每个功能过程对应一个或多个功能拆解结果
- 功能过程信息正确映射到输出结果中

## 测试验证

### 测试文件
1. `debug/test_file_format.py` - 测试文件读取和数据处理逻辑
2. `debug/test_full_process.py` - 测试完整处理流程

### 测试结果
- ✅ 文件读取正常，所有必需列都存在
- ✅ 按三级模块分组处理正常
- ✅ 数据结构转换正确
- ✅ 输出格式符合预期

## 兼容性说明

- 系统仍然支持原有的知识库功能
- 保持了原有的限流和错误处理机制
- 输出格式向后兼容，只是新增了功能过程列

## 使用说明

1. 确保使用调整后的Excel文件：`附件4：中国移动河南公司2025年移动爱家终端管理平台研发项目-软件功能清单-调整.xlsx`
2. 运行 `python main.py` 即可开始处理
3. 系统会按三级模块为单位调用大模型进行功能拆解
4. 输出结果包含完整的功能过程信息

## 注意事项

1. 新文件中的"预估工作量"列名包含换行符，已在代码中正确处理
2. 功能过程信息会正确映射到每个拆解结果中
3. 系统提示词已更新，更好地指导大模型理解新的数据结构
4. 建议在正式运行前先用小规模数据测试
