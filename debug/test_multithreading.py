#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多线程功能测试脚本
"""

import sys
import os
import time
import pandas as pd
from collections import defaultdict

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import THREAD_COUNT, MAX_THREAD_COUNT

def test_thread_safe_collector():
    """测试线程安全的结果收集器"""
    print("=== 测试线程安全的结果收集器 ===")
    
    from debug.thread_safe_collector import ThreadSafeResultCollector
    
    collector = ThreadSafeResultCollector()
    
    # 注册模块顺序
    modules = ["模块A", "模块B", "模块C"]
    for module in modules:
        collector.register_module_order(module)
    
    # 添加结果（模拟乱序添加）
    collector.add_results([{"data": "C1"}, {"data": "C2"}], "模块C")
    collector.add_results([{"data": "A1"}, {"data": "A2"}], "模块A")
    collector.add_results([{"data": "B1"}], "模块B")
    
    # 获取排序后的结果
    sorted_results = collector.get_sorted_results()
    
    print(f"结果数量: {collector.get_results_count()}")
    print("排序后的结果:")
    for i, result in enumerate(sorted_results):
        print(f"  {i+1}: {result}")
    
    # 验证顺序是否正确
    expected_order = ["A1", "A2", "B1", "C1", "C2"]
    actual_order = [r["data"] for r in sorted_results]
    
    if actual_order == expected_order:
        print("✓ 顺序测试通过")
    else:
        print(f"✗ 顺序测试失败: 期望 {expected_order}, 实际 {actual_order}")

def test_module_allocator():
    """测试模块分配器"""
    print("\n=== 测试模块分配器 ===")
    
    from debug.module_allocator import ModuleAllocator
    
    modules = ["模块A", "模块B", "模块C", "模块D", "模块E"]
    
    # 测试不同线程数的分配
    for thread_count in [1, 2, 3, 4, 6]:
        allocations = ModuleAllocator.allocate_modules_to_threads(modules, thread_count)
        print(f"线程数 {thread_count}: {allocations}")
    
    # 测试最优线程数计算
    for module_count in [1, 3, 5, 8, 12]:
        optimal = ModuleAllocator.get_optimal_thread_count(module_count, 4)
        print(f"模块数 {module_count}, 最优线程数: {optimal}")

def test_config():
    """测试配置"""
    print("\n=== 测试配置 ===")
    print(f"配置的线程数: {THREAD_COUNT}")
    print(f"最大线程数: {MAX_THREAD_COUNT}")

def create_test_data():
    """创建测试数据"""
    print("\n=== 创建测试数据 ===")
    
    test_data = []
    
    # 创建3个二级模块，每个模块有2-3个三级模块
    for i in range(1, 4):
        level_2 = f"二级模块{i}"
        for j in range(1, 3):
            level_3 = f"三级模块{i}-{j}"
            for k in range(1, 3):
                test_data.append({
                    "一级功能模块": "一级模块A",
                    "二级功能模块": level_2,
                    "三级功能模块": level_3,
                    "功能过程": f"功能过程{i}-{j}-{k}",
                    "功能描述": f"描述{i}-{j}-{k}",
                    "预估工作量（人天）": "1"
                })
    
    # 保存测试数据
    df = pd.DataFrame(test_data)
    test_file = "debug/test_multithreading_data.xlsx"
    df.to_excel(test_file, index=False)
    print(f"测试数据已保存到: {test_file}")
    
    return test_file

def test_data_grouping():
    """测试数据分组"""
    print("\n=== 测试数据分组 ===")
    
    # 创建测试数据
    test_file = create_test_data()
    
    # 读取数据
    df = pd.read_excel(test_file)
    print(f"读取数据: {len(df)} 行")
    
    # 按二级模块分组
    grouped_by_level2 = defaultdict(list)
    level_2_order = {}
    order_counter = 0
    
    for idx, row in df.iterrows():
        level_2_module = str(row["二级功能模块"])
        level_3_module = str(row["三级功能模块"])
        
        # 记录二级模块的顺序
        if level_2_module not in level_2_order:
            level_2_order[level_2_module] = order_counter
            order_counter += 1
            
        grouped_by_level2[level_2_module].append(row.to_dict())
    
    # 显示分组结果
    level_2_modules = sorted(grouped_by_level2.keys(), key=lambda x: level_2_order[x])
    print(f"发现 {len(level_2_modules)} 个二级模块:")
    for module in level_2_modules:
        print(f"  {module}: {len(grouped_by_level2[module])} 行数据")

if __name__ == "__main__":
    print("多线程功能测试开始...")
    
    test_config()
    test_thread_safe_collector()
    test_module_allocator()
    test_data_grouping()
    
    print("\n测试完成!")
