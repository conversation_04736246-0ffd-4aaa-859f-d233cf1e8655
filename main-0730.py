import requests
import re
import json
import pandas as pd
from collections import OrderedDict
from config import ENDPOINT_URL, MODEL_NAME, API_KEY, API_QPM, API_TPM, BATCH_COUNT,TEST_LEVEL2_NAME, TEST_LEVEL3_NAME,KNOWLEDGE_BASE_ENABLED,PROMPT_PATH
from csv_2_xls import process_csv_to_excel
from knowledge_base import knowledge_base
import time
import math  # 用于token估算

# 全局限流状态变量
qpm_counter = 0
tpm_counter = 0
window_start_time = time.time()

def call_LLM(prompt, user_input, knowledge_context="", endpoint_url = ENDPOINT_URL, model_name = MODEL_NAME):
    global qpm_counter, tpm_counter, window_start_time  # 使用全局限流变量
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}"
    }

    # 如果有知识库上下文，将其添加到系统提示词中
    enhanced_prompt = prompt
    if knowledge_context:
        enhanced_prompt = f"{prompt}\n\n{knowledge_context}"

    # 构造消息体
    messages = [
        {"role": "system", "content": enhanced_prompt},
        {"role": "user", "content": user_input}
    ]
    stream = True
    data = {
        "model": model_name,
        "messages": messages,
        "temperature": 0.3,
        "stream": stream,
        "enable_thinking": True
    }

    # 新增限流逻辑
    current_time = time.time()
    # 重置窗口
    if current_time - window_start_time >= 60:
        qpm_counter = 0
        tpm_counter = 0
        window_start_time = current_time
    
    # 输入token估算（中文按2token/字符，英文按1token/单词）
    input_text = prompt + user_input
    input_token_count = math.ceil(len(input_text) * 1.5)  # 粗略估算
    
    # QPM/TPM等待逻辑
    while qpm_counter >= API_QPM or tpm_counter + input_token_count > API_TPM:
        sleep_time = max(60 - (current_time - window_start_time) + 0.1, 0)
        print(f"达到限流阈值，等待{sleep_time:.1f}秒...")
        time.sleep(sleep_time)
        current_time = time.time()
        if current_time - window_start_time >= 60:
            qpm_counter = 0
            tpm_counter = 0
            window_start_time = current_time
    
    # 更新计数器
    qpm_counter += 1
    tpm_counter += input_token_count
    
    content = ""
    with requests.post(endpoint_url, headers=headers, json=data, stream=stream) as response:
        try:
            response.raise_for_status()
        except requests.exceptions.HTTPError as e:
            # 打印错误状态码和响应体
            print(f"HTTP Error {response.status_code}: {e}")
            print(f"Response Body: {response.text[:100]}")
            raise 
        for line in response.iter_lines():
            if line:
                decoded_line = line.decode('utf-8')
                if decoded_line.startswith("data: "):
                    try:
                        chunk = json.loads(decoded_line[6:])  # 去除"data: "前缀并解析
                        delta_content = chunk["choices"][0]["delta"].get("content", "")
                        if delta_content:                            
                            content += delta_content
                    except json.JSONDecodeError:
                        continue
    
    # 输出token估算
    output_token_count = math.ceil(len(content) * 1.5)
    tpm_counter += output_token_count
    
    return content

def extract_json_from_content(content):
    """
    从大模型返回的 content 字符串中提取并解析 JSON 代码块
    """
    # 用正则表达式提取 ```json ... ``` 之间的内容
    # print(f"解析json: {content}")
    match = re.search(r"``json\s*(.*?)\s*```", content, re.DOTALL)
    if match:
        json_str = match.group(1)
        try:
            # 解析为 Python 对象
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            print("JSON解析失败：", e)
            return None
    else:
        try:
            if '</think>' in content:
                content = content.split('</think>')[-1]
            content = content.replace('```json','').replace('```json','')
            content= content.strip()
            content_json = json.loads(content)
            return content_json
        except:
            pass
        print(f"未找到JsoN代码块：{content}")
        return None


def flatten_results_with_subprocess(all_items):
    """
    输入：all_items为包含模块信息和大模型返回的所有item（每个item有'子过程'字段，值为列表）
    输出：每个子过程一行的扁平化结果，所有父级信息每行都保留
    """
    flat_results = []
    for item in all_items:
        subprocess_list = item.get('子过程', [])
        # 拷贝除'子过程'外的所有字段
        base_info = {k: v for k, v in item.items() if k != '子过程'}
        for subprocess in subprocess_list:
            row = base_info.copy()
            row.update(subprocess)  # 子过程的key直接变成列
            flat_results.append(row)
    return flat_results

def llm_cfp(level_1, level_2, level_3, function_process_list, description_list, user_inputs, all_results):
    # 先检查3级模块
    if TEST_LEVEL3_NAME and level_3 != TEST_LEVEL3_NAME:
        return
    if TEST_LEVEL2_NAME and level_2 != TEST_LEVEL2_NAME:
        return
    if not user_inputs:
        print(f"!!!!【{level_1}-{level_2}-{level_3}】内容空，跳过!!!!")
        return
    
    print(f"开始处理:【{level_1}-{level_2}-{level_3}】......")
    batch_user_input = "\n".join([f"{i+1}. {input}" for i, input in enumerate(user_inputs)])
    # 获取知识库上下文
    knowledge_context = ""
    if KNOWLEDGE_BASE_ENABLED and knowledge_base.enabled:
        # 为当前三级模块获取知识库上下文
        query_parts = function_process_list
        query = level_3 +" , " + " ".join([part for part in query_parts if part and str(part).strip()])
        context = knowledge_base.get_context_for_module( query)
        if context:
            knowledge_context = f"### {level_3} 相关上下文:\n{context}"
            #print(f"获取知识库上下文: {current_level_3}")

    # 可重试3次大模型调用
    for i in range(3):
        try:            
            result = call_LLM(prompt, batch_user_input, knowledge_context)
            #print(json.dumps(result, ensure_ascii=False, indent=4))
            if result:
                result = extract_json_from_content(result)
            if isinstance(result, list):
                break
            print(f"第{i+1}次大模型结果解析失败，正在重试...")
        except Exception as e:
            print(f"第{i+1}次大模型调用失败，正在重试...: {e}")
        time.sleep(1)
    if not result or not isinstance(result, list):
        print(f"未能正确解析到结果，请检查大模型返回的格式是否正确.")
        return
    # 每个元素是 {"三级功能模块名称": [功能过程列表]}
    for module_dict in result:
        if isinstance(module_dict, dict):
            # 遍历每个三级模块
            for module_name, function_processes in module_dict.items():
                # 使用当前三级模块的信息
                level_3 = current_level_3
                func_desc = current_function_description
                est_work = current_estimated_workload

                # 处理该模块下的每个功能过程
                if isinstance(function_processes, list):
                    for i, process in enumerate(function_processes):
                        ordered_item = OrderedDict()
                        ordered_item[level_1_name] = current_level_1
                        ordered_item[level_2_name] = current_level_2
                        ordered_item[level_3_name] = level_3
                        # 添加对应的功能过程信息
                        if i < len(current_function_process_list):
                            ordered_item[func_process_name] = current_function_process_list[i]
                        else:
                            ordered_item[func_process_name] = ""
                        ordered_item[function_description_name] = func_desc
                        ordered_item[estimated_workload_name] = est_work
                        # 添加功能过程的所有字段
                        for k, v in process.items():
                            ordered_item[k] = v
                        all_results.append(ordered_item)
def set_duplicate_modules_to_nan(df, module_cols, right_cols):
    """
    对module_cols做全局去重，对right_cols只在同一个三级模块下做去重。
    """
    # 1. 先对module_cols做全局去重
    for col in module_cols:
        df[col] = df[col].where(df[col] != df[col].shift(), None)
    # 2. 对right_cols在同一个三级模块下做去重
    level_3_col = module_cols[-1]  # 假设最后一个是三级模块
    for col in right_cols:
        if col in df.columns:
            df[col] = df.groupby(level_3_col, group_keys=False)[col].apply(lambda x: x.where(x != x.shift(), None))
    return df
def move_column(result_df, col_to_move, after_col)-> pd.DataFrame:
    cols = result_df.columns.tolist()
    if after_col in cols and col_to_move in cols:
        # 执行列移动逻辑
        trigger_idx = cols.index(after_col)  # 找到触发事件列位置
        cols.insert(trigger_idx , cols.pop(cols.index(col_to_move)))  # 移动列
        return result_df[cols]  # 保持原有列顺序
    else:
        print("缺少必要列，请检查数据结构")
        return result_df

if __name__ == "__main__":
    excel_file = "附件2：功能清单-2025.xlsx"
    level_1_name, level_2_name, level_3_name, func_process_name = "一级功能模块", "二级功能模块","三级功能模块","功能过程"
    function_description_name = "功能描述"
    estimated_workload_name = "预估工作量（人天）"  # 注意：调整后的文件中有换行符
    sheet_name = 1
    header = 0

    with open(PROMPT_PATH, 'r', encoding='utf-8') as f:
        prompt = f.read()
    
    df = pd.read_excel(excel_file, sheet_name = sheet_name, header = header)
    
    # 列名替换逻辑 : 预估工作量列中有回车符
    # df.columns = [col if not re.search(r'预估工作量', col) else estimated_workload_name for col in df.columns]
    
    # 向后填充一级/二级模块列      
    df[[level_1_name, level_2_name, level_3_name]] = df[[level_1_name, level_2_name, level_3_name]].ffill()

    all_results = []
    user_inputs = []
    current_level_3 = None
    current_level_2 = None
    current_level_1 = None
    current_function_process_list = []  # 新增：存储功能过程列表
    current_function_description_list = []
    current_estimated_workload_list = []
    current_function_description = None
    current_estimated_workload = None
    count = 0  # 计数器

    for idx, row in df.iterrows():
        level_1_module = str(row[level_1_name])
        level_2_module = str(row[level_2_name])
        level_3_module = str(row[level_3_name])
        func_process = str(row[func_process_name])  # 这里是功能过程
        function_description = str(row[function_description_name])
        estimated_workload = str(row[estimated_workload_name])
        # 跳过无效三级模块
        if pd.isna(level_3_module) or str(level_3_module).strip().lower() in ['nan', 'none', '']:
            continue

        # 如果遇到新的三级功能模块，先处理上一个
        if (current_level_3 is not None) and (level_3_module != current_level_3 or level_2_module != current_level_2 or level_1_module != current_level_1):
            llm_cfp(current_level_1, current_level_2, current_level_3, current_function_process_list, current_function_description_list, user_inputs, all_results)

            # 清空累计
            user_inputs = []
            current_function_process_list = []
            current_function_description_list = []
            current_estimated_workload_list = []
            #break


        # 累计当前三级功能模块下的功能过程
        user_input = f"{level_1_name}：{level_1_module}，{level_2_name}：{level_2_module}，{level_3_name}：{level_3_module}, {func_process_name}: {func_process}, {function_description_name}: {function_description}, {estimated_workload_name}: {estimated_workload}"
        user_inputs.append(user_input)

        current_function_process_list.append(func_process)
        current_function_description_list.append(function_description)
        current_estimated_workload_list.append(estimated_workload)

        current_level_3 = level_3_module
        current_level_2 = level_2_module
        current_level_1 = level_1_module
        current_function_description = function_description
        current_estimated_workload = estimated_workload

    # 循环结束后，处理最后一组
    llm_cfp(current_level_1, current_level_2, current_level_3, current_function_process_list, current_function_description_list, user_inputs, all_results)
    
    # all_results存储为json格式
    # json_results = json.dumps(all_results, ensure_ascii=False)
    # with open('output.json', 'w', encoding='utf-8') as f:
    #     f.write(json_results)
   
    
    flat_results = flatten_results_with_subprocess(all_results)
    result_df = pd.DataFrame(flat_results)
    module_cols = [level_1_name, level_2_name, level_3_name]
    right_cols = [func_process_name, function_description_name, estimated_workload_name, '功能用户', '触发事件', '功能过程']
    #result_df = set_duplicate_modules_to_nan(result_df, module_cols, right_cols)
    # 把"功能过程"列挪到“触发事件”后
    result_df = move_column(result_df, "功能过程","触发事件")
    #result_df = move_column(result_df, "预估工作量（人天）","功能过程")
    
    out_file = f"output_"+time.strftime("%m%d%H%M", time.localtime())+".csv"
    result_df.to_csv(out_file, index=False, encoding='utf-8-sig')
    process_csv_to_excel(out_file)