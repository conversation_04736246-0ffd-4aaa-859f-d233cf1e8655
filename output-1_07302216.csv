﻿一级功能模块,二级功能模块,三级功能模块,功能描述,预估工作量（人天）,功能用户,触发事件,功能过程,子过程描述,数据移动类型,数据组,数据属性,CFP
SDP签名验签模块,统一API,签名验签第三方应用模式认证,nan,8,发起者：用户 接收者：密码服务平台,第三方应用模式认证请求,签名验签第三方应用模式认证,输入第三方应用认证请求,E,第三方应用认证请求信息,应用ID、用户ID、请求时间、认证类型,1
SDP签名验签模块,统一API,签名验签第三方应用模式认证,nan,8,发起者：用户 接收者：密码服务平台,第三方应用模式认证请求,签名验签第三方应用模式认证,验证第三方应用权限,R,第三方应用权限信息,应用ID、权限状态、操作员ID、权限有效期,1
SDP签名验签模块,统一API,签名验签第三方应用模式认证,nan,8,发起者：用户 接收者：密码服务平台,第三方应用模式认证请求,签名验签第三方应用模式认证,获取用户证书信息,R,用户证书信息,证书名称、签名算法、签发者、使用者、开始日期、结束日期、状态,1
SDP签名验签模块,统一API,签名验签第三方应用模式认证,nan,8,发起者：用户 接收者：密码服务平台,第三方应用模式认证请求,签名验签第三方应用模式认证,生成签名请求数据,W,签名请求数据,签名算法、数据内容、时间戳、证书序列号,1
SDP签名验签模块,统一API,签名验签第三方应用模式认证,nan,8,发起者：用户 接收者：密码服务平台,第三方应用模式认证请求,签名验签第三方应用模式认证,执行签名操作并存储结果,W,签名结果信息,签名值、签名时间、签名状态、签名算法,1
SDP签名验签模块,统一API,签名验签第三方应用模式认证,nan,8,发起者：用户 接收者：密码服务平台,第三方应用模式认证请求,签名验签第三方应用模式认证,验证签名结果有效性,R,签名验证结果,验证结果、错误代码、验证时间、签名算法,1
SDP签名验签模块,统一API,签名验签第三方应用模式认证,nan,8,发起者：用户 接收者：密码服务平台,第三方应用模式认证请求,签名验签第三方应用模式认证,返回认证结果至第三方应用,X,认证结果信息,认证状态、错误信息、认证时间、签名结果,1
SDP签名验签模块,统一API,签名验签第三方应用模式认证,nan,8,发起者：用户 接收者：密码服务平台,第三方应用模式认证请求,签名验签第三方应用模式认证,记录认证操作日志,W,认证日志信息,操作员ID、认证时间、认证结果、应用ID、证书序列号,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,禁用证书,证书禁用,输入证书禁用请求,E,证书禁用请求,证书ID、操作员ID、禁用原因,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,禁用证书,证书禁用,读取证书状态信息,R,证书状态信息,证书ID、当前状态、结束日期,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,禁用证书,证书禁用,验证操作员权限,R,操作员权限信息,操作员ID、权限等级、操作范围,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,禁用证书,证书禁用,更新证书状态为禁用,W,证书状态更新记录,证书ID、原状态、新状态、更新时间,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,禁用证书,证书禁用,返回证书禁用结果,X,证书禁用反馈,证书ID、禁用状态、操作结果,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,禁用证书,证书禁用,记录证书禁用日志,W,证书操作日志,操作员ID、证书ID、操作类型、操作时间,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,下载证书,证书下载,输入证书下载请求,E,证书下载请求,证书ID、下载类型、用户ID,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,下载证书,证书下载,读取证书内容信息,R,证书内容信息,证书ID、证书数据、加密算法,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,下载证书,证书下载,验证证书下载权限,R,用户权限信息,用户ID、证书ID、访问权限,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,下载证书,证书下载,生成证书下载文件,W,证书下载文件,证书ID、文件格式、下载路径,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,下载证书,证书下载,返回证书下载链接,X,证书下载响应,下载URL、证书名称、文件大小,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,下载证书,证书下载,记录证书下载日志,W,证书操作日志,用户ID、证书ID、下载时间、IP地址,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,更新证书状态,证书状态更新,输入证书状态变更请求,E,证书状态变更请求,证书ID、目标状态、变更原因,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,更新证书状态,证书状态更新,读取当前证书状态,R,证书状态信息,证书ID、当前状态、有效期,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,更新证书状态,证书状态更新,验证状态变更合法性,R,状态规则信息,当前状态、目标状态、允许操作,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,更新证书状态,证书状态更新,执行证书状态变更,W,证书状态变更记录,证书ID、原状态、新状态、变更时间,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,更新证书状态,证书状态更新,返回状态变更结果,X,证书状态反馈,证书ID、新状态、操作结果,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,更新证书状态,证书状态更新,记录状态变更日志,W,证书操作日志,操作员ID、证书ID、变更类型、变更时间,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,查询证书信息,证书信息查询,输入证书查询条件,E,证书查询请求,证书ID、查询字段、过滤条件,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,查询证书信息,证书信息查询,读取证书基础信息,R,证书基础信息,证书ID、证书名称、签发者,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,查询证书信息,证书信息查询,读取证书有效期信息,R,证书有效期信息,证书ID、开始日期、结束日期,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,查询证书信息,证书信息查询,读取证书使用状态,R,证书状态信息,证书ID、当前状态、禁用原因,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,查询证书信息,证书信息查询,返回证书查询结果,X,证书查询结果,证书ID、证书名称、状态、有效期,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,查询证书信息,证书信息查询,记录证书查询日志,W,证书操作日志,用户ID、证书ID、查询时间、查询条件,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,导入用户证书,用户证书导入,上传用户证书文件,E,证书上传文件,文件格式、证书内容、上传时间,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,导入用户证书,用户证书导入,解析证书文件内容,R,证书解析信息,证书ID、签名算法、签发者,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,导入用户证书,用户证书导入,验证证书有效性,R,证书验证信息,证书ID、有效期、签发机构,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,导入用户证书,用户证书导入,存储用户证书信息,W,用户证书记录,证书ID、用户ID、证书内容,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,导入用户证书,用户证书导入,返回证书导入结果,X,证书导入反馈,证书ID、导入状态、错误信息,1
SDP签名验签模块,证书有效性验证,签名验签用户证书管理,nan,30,密码发起者：用户 接收者：密码服务平台,导入用户证书,用户证书导入,记录证书导入日志,W,证书操作日志,操作员ID、证书ID、导入时间、文件大小,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书禁用,证书禁用,输入证书禁用请求,E,证书禁用请求,证书ID、操作员ID、禁用原因,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书禁用,证书禁用,读取证书状态信息,R,证书状态信息,证书ID、当前状态、签发者、有效期,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书禁用,证书禁用,验证操作员权限,R,操作员权限信息,操作员ID、权限等级、操作范围,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书禁用,证书禁用,更新证书状态为禁用,W,证书状态更新记录,证书ID、新状态、更新时间、操作员ID,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书禁用,证书禁用,返回证书禁用结果,X,证书禁用响应,证书ID、操作结果、错误代码,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书禁用,证书禁用,记录证书禁用日志,W,证书操作日志,操作类型、证书ID、操作员ID、操作时间,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书下载,证书下载,输入证书下载请求,E,证书下载请求,证书ID、下载格式、下载类型,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书下载,证书下载,读取证书内容信息,R,证书内容信息,证书ID、证书内容、签名算法、密钥算法,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书下载,证书下载,验证证书有效性,R,证书有效性信息,证书ID、当前时间、有效期、状态,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书下载,证书下载,生成证书下载文件,W,证书下载文件,证书ID、文件内容、文件格式、生成时间,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书下载,证书下载,返回证书下载链接,X,证书下载响应,下载链接、文件名、文件大小,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书下载,证书下载,记录证书下载日志,W,证书操作日志,操作类型、证书ID、操作员ID、下载时间,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书查询,证书查询,输入证书查询条件,E,证书查询请求,查询类型、证书名称、签发者、状态,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书查询,证书查询,读取证书列表信息,R,证书列表信息,证书ID、证书名称、签发者、状态、有效期,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书查询,证书查询,过滤符合条件的证书,R,证书过滤条件,证书名称、签发者、状态、开始日期,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书查询,证书查询,返回证书查询结果,X,证书查询结果,证书ID、证书名称、签发者、状态、有效期,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书查询,证书查询,记录证书查询日志,W,证书操作日志,操作类型、查询条件、操作员ID、查询时间,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书状态更新,证书状态更新,输入证书状态更新请求,E,证书状态更新请求,证书ID、目标状态、更新原因,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书状态更新,证书状态更新,读取当前证书状态,R,证书状态信息,证书ID、当前状态、签发者、有效期,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书状态更新,证书状态更新,验证状态变更合法性,R,状态变更规则,当前状态、目标状态、允许变更路径,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书状态更新,证书状态更新,更新证书状态记录,W,证书状态更新记录,证书ID、新状态、更新时间、操作员ID,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书状态更新,证书状态更新,返回状态更新结果,X,证书状态更新响应,证书ID、操作结果、错误代码,1
SDP签名验签模块,证书有效性验证,签名验签应用证书管理,nan,28,密码发起者：用户 接收者：密码服务平台,证书状态更新,证书状态更新,记录状态变更日志,W,证书操作日志,操作类型、证书ID、旧状态、新状态,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS1签名验证请求,RSA-PKCS1签名验证,输入RSA-PKCS1签名验证请求数据,E,RSA-PKCS1签名请求,待验证数据内容、数字签名值、签名算法标识、公钥证书ID,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS1签名验证请求,RSA-PKCS1签名验证,读取公钥证书信息,R,证书信息,证书别名、证书类型(RSA2048/SM2)、签名算法、公钥值、证书有效期,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS1签名验证请求,RSA-PKCS1签名验证,验证签名算法匹配性,R,算法配置,支持算法列表、算法版本、密钥长度要求,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS1签名验证请求,RSA-PKCS1签名验证,执行RSA-PKCS1签名验证运算,R,运算参数,模数N、指数E、签名哈希值、填充模式,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS1签名验证请求,RSA-PKCS1签名验证,校验证书有效性,R,证书状态,证书状态(有效/吊销)、CRL签发时间、证书用途,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS1签名验证请求,RSA-PKCS1签名验证,生成签名验证结果,W,验证结果,验证状态(通过/失败)、错误代码、验证时间戳,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS1签名验证请求,RSA-PKCS1签名验证,输出签名验证结果,X,验证结果展示,验证状态、错误描述、证书信息摘要,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS1签名验证请求,RSA-PKCS1签名验证,记录签名验证操作日志,W,操作日志,操作用户ID、操作时间、验证结果、请求参数摘要,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS1签名验证请求,RSA-PKCS1签名验证,校验数据完整性,R,数据校验规则,数据长度限制、字段格式要求、必填字段校验,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS1签名验证请求,RSA-PKCS1签名验证,验证公钥证书链,R,证书链信息,中间证书ID、根证书指纹、证书路径长度,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS1签名验证请求,RSA-PKCS1签名验证,检查时间戳有效性,R,时间戳验证,系统时间、证书生效时间、签名时间,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS1签名验证请求,RSA-PKCS1签名验证,生成验证结果摘要,W,摘要信息,摘要算法、摘要值、摘要生成时间,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS1签名验证请求,RSA-PKCS1签名验证,输出验证结果摘要,X,摘要展示,摘要算法、摘要值、验证状态,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS1签名验证请求,RSA-PKCS1签名验证,记录验证结果摘要日志,W,摘要日志,摘要算法、摘要值、验证结果、记录时间,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS1签名验证请求,RSA-PKCS1签名验证,校验签名格式规范性,R,格式校验规则,签名长度、编码格式、填充方式,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS1签名验证请求,RSA-PKCS1签名验证,输出格式校验结果,X,格式校验反馈,格式校验状态、错误位置、修复建议,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS7功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS7签名验证请求,RSA-PKCS7签名验证,输入RSA-PKCS7签名验证请求,E,RSA-PKCS7签名验证请求,签名数据、公钥证书ID、验证算法类型、时间戳,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS7功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS7签名验证请求,RSA-PKCS7签名验证,读取公钥证书信息,R,公钥证书信息,证书别名、签名算法、公钥模数、证书有效期,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS7功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS7签名验证请求,RSA-PKCS7签名验证,验证签名数据格式,R,签名格式校验规则,ASN.1结构、PKCS7标准、签名算法匹配性,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS7功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS7签名验证请求,RSA-PKCS7签名验证,执行RSA-PKCS7验签运算,R,验签运算参数,签名值、摘要算法、填充模式、密钥长度,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS7功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS7签名验证请求,RSA-PKCS7签名验证,校验证书有效性,R,证书有效性校验,证书状态、信任链完整性、CRL吊销状态,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS7功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS7签名验证请求,RSA-PKCS7签名验证,生成验签结果日志,W,验签操作日志,操作用户、验签时间、证书ID、结果状态,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS7功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS7签名验证请求,RSA-PKCS7签名验证,输出RSA-PKCS7验签结果,X,验签结果反馈,验证状态、错误代码、证书信息摘要,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS7功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS7签名验证请求,RSA-PKCS7签名验证,记录验签性能指标,W,验签性能数据,处理耗时、CPU占用率、内存消耗量,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS7功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS7签名验证请求,RSA-PKCS7签名验证,验证时间戳有效性,R,时间戳验证规则,时间戳算法、签名时间、系统当前时间,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS7功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS7签名验证请求,RSA-PKCS7签名验证,校验签名数据完整性,R,数据完整性校验,原始数据哈希值、签名数据哈希值、哈希算法,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS7功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS7签名验证请求,RSA-PKCS7签名验证,验证证书链信任关系,R,证书链验证,根证书ID、中间证书ID、证书路径长度,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS7功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS7签名验证请求,RSA-PKCS7签名验证,生成验签结果报告,W,验签结果报告,验证结论、证书详情、操作记录、系统时间,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS7功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS7签名验证请求,RSA-PKCS7签名验证,输出验签结果报告,X,验签报告展示,报告标题、验证状态、证书信息、操作时间,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS7功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS7签名验证请求,RSA-PKCS7签名验证,记录验签失败详情,W,验签失败日志,失败原因、错误代码、证书ID、操作用户,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS7功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS7签名验证请求,RSA-PKCS7签名验证,验证签名算法兼容性,R,算法兼容性规则,支持算法列表、证书算法、系统配置算法,1
SDP签名验签模块,签名验证,签名验签RSA-PKCS7功能,nan,16,发起者：用户 接收者：密码服务平台,发起RSA-PKCS7签名验证请求,RSA-PKCS7签名验证,输出算法兼容性检查结果,X,算法兼容性反馈,兼容性状态、建议算法、证书算法、系统算法,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起SM2-PKCS1签名验证,SM2-PKCS1签名验证,输入签名验证请求数据,E,签名验证请求,签名数据、原始数据、证书序列号、签名算法标识,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起SM2-PKCS1签名验证,SM2-PKCS1签名验证,读取证书信息,R,证书信息,证书类型、序列号、签名算法、密钥算法、使用者、开始日期、结束日期,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起SM2-PKCS1签名验证,SM2-PKCS1签名验证,验证证书有效性,R,证书状态信息,证书状态、签发者、使用者、有效期,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起SM2-PKCS1签名验证,SM2-PKCS1签名验证,执行SM2-PKCS1签名验证算法,R,签名验证参数,签名数据、原始数据、公钥信息、算法参数,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起SM2-PKCS1签名验证,SM2-PKCS1签名验证,生成签名验证结果,W,签名验证结果,验证状态、错误代码、验证时间、证书信息,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起SM2-PKCS1签名验证,SM2-PKCS1签名验证,输出签名验证结果,X,验证结果展示,验证状态、证书信息、错误描述、验证时间,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起SM2-PKCS1签名验证,SM2-PKCS1签名验证,记录签名验证日志,W,验证日志,操作员ID、验证时间、验证结果、证书序列号,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起SM2-PKCS1签名验证,SM2-PKCS1签名验证,校验签名算法兼容性,R,算法兼容性信息,支持算法列表、证书算法、请求算法,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起SM2-PKCS1签名验证,SM2-PKCS1签名验证,处理异常签名数据,W,异常处理记录,异常类型、错误码、处理时间、签名数据摘要,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起SM2-PKCS1签名验证,SM2-PKCS1签名验证,验证证书链完整性,R,证书链信息,根证书、中间证书、证书路径、签发关系,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起SM2-PKCS1签名验证,SM2-PKCS1签名验证,校验时间戳有效性,R,时间戳信息,签名时间、证书有效期、系统时间,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起SM2-PKCS1签名验证,SM2-PKCS1签名验证,生成验证结果摘要,W,结果摘要信息,验证状态摘要、关键参数摘要、证书信息摘要,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起SM2-PKCS1签名验证,SM2-PKCS1签名验证,输出验证结果PDF报告,X,PDF验证报告,报告标题、验证详情、证书信息、时间戳,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起SM2-PKCS1签名验证,SM2-PKCS1签名验证,更新证书使用统计,W,证书统计信息,证书使用次数、最近使用时间、验证结果统计,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起SM2-PKCS1签名验证,SM2-PKCS1签名验证,校验签名数据格式,R,数据格式规范,签名数据格式、原始数据格式、编码方式,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS1功能,nan,16,发起者：用户 接收者：密码服务平台,发起SM2-PKCS1签名验证,SM2-PKCS1签名验证,生成验证结果通知,X,验证通知信息,通知类型、验证结果、操作建议、证书状态,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS7功能,nan,16,密码发起者：用户 接收者：密码服务平台,证书下载请求,证书下载,输入证书下载请求参数,E,证书下载请求,证书类型、序列号、签名算法、密钥算法,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS7功能,nan,16,密码发起者：用户 接收者：密码服务平台,证书下载请求,证书下载,验证用户下载权限,R,用户权限信息,用户ID、权限等级、操作类型、证书类型,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS7功能,nan,16,密码发起者：用户 接收者：密码服务平台,证书下载请求,证书下载,读取证书内容并返回,X,证书下载内容,证书数据、签名算法、密钥算法、有效期,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS7功能,nan,16,密码发起者：用户 接收者：密码服务平台,证书下载请求,证书下载,记录证书下载操作日志,W,证书下载日志,操作时间、用户ID、证书类型、序列号,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS7功能,nan,16,密码发起者：用户 接收者：密码服务平台,签名生成请求,签名生成,输入签名生成请求数据,E,签名生成请求,待签名数据、私钥标识、签名算法,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS7功能,nan,16,密码发起者：用户 接收者：密码服务平台,签名生成请求,签名生成,读取用户私钥信息,R,用户私钥信息,私钥内容、密钥算法、有效期、用户ID,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS7功能,nan,16,密码发起者：用户 接收者：密码服务平台,签名生成请求,签名生成,执行SM2-PKCS7签名运算,W,签名结果数据,签名值、签名算法、时间戳、用户ID,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS7功能,nan,16,密码发起者：用户 接收者：密码服务平台,签名生成请求,签名生成,返回签名结果给用户,X,签名结果响应,签名数据、签名算法、验证状态、时间戳,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS7功能,nan,16,密码发起者：用户 接收者：密码服务平台,签名验证请求,签名验证,输入签名验证请求参数,E,签名验证请求,原始数据、签名值、公钥标识、签名算法,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS7功能,nan,16,密码发起者：用户 接收者：密码服务平台,签名验证请求,签名验证,读取对应公钥证书信息,R,公钥证书信息,公钥内容、证书状态、签发者、有效期,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS7功能,nan,16,密码发起者：用户 接收者：密码服务平台,签名验证请求,签名验证,执行SM2-PKCS7验证运算,W,验证结果数据,验证结果、签名算法、时间戳、证书状态,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS7功能,nan,16,密码发起者：用户 接收者：密码服务平台,签名验证请求,签名验证,返回验证结果信息,X,验证结果响应,验证状态、失败原因、签名算法、时间戳,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS7功能,nan,16,密码发起者：用户 接收者：密码服务平台,证书注销请求,证书注销,输入证书注销请求信息,E,证书注销请求,证书序列号、注销原因、用户ID,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS7功能,nan,16,密码发起者：用户 接收者：密码服务平台,证书注销请求,证书注销,验证证书有效性及注销权限,R,证书状态信息,证书状态、签发者、有效期、用户ID,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS7功能,nan,16,密码发起者：用户 接收者：密码服务平台,证书注销请求,证书注销,更新证书状态为已注销,W,证书状态更新,证书序列号、新状态、操作时间、操作员ID,1
SDP签名验签模块,签名验证,签名验签SM2-PKCS7功能,nan,16,密码发起者：用户 接收者：密码服务平台,证书注销请求,证书注销,返回证书注销结果,X,证书注销响应,注销状态、失败原因、证书序列号、操作时间,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,生成数字信封,数字信封生成,输入数字信封生成参数,E,数字信封生成请求,信封ID、签名算法、密钥索引、加密内容,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,生成数字信封,数字信封生成,验证用户权限及密钥有效性,R,用户密钥信息,用户ID、密钥状态、权限等级、密钥有效期,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,生成数字信封,数字信封生成,生成数字签名并封装信封,W,数字信封实体,信封ID、签名值、加密数据、生成时间,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,生成数字信封,数字信封生成,返回数字信封生成结果,X,数字信封响应,信封ID、生成状态、签名算法、生成时间,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,生成数字信封,数字信封生成,记录数字信封生成日志,W,操作日志,操作类型、操作时间、用户ID、信封ID,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,验证数字信封,数字信封验证,输入数字信封验证请求,E,数字信封验证请求,信封ID、验证算法、验证密钥,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,验证数字信封,数字信封验证,读取数字信封实体,R,数字信封实体,信封ID、签名值、加密数据、生成时间,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,验证数字信封,数字信封验证,执行数字签名验证,R,验证密钥信息,公钥证书、密钥算法、密钥有效期,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,验证数字信封,数字信封验证,返回数字信封验证结果,X,数字信封验证结果,验证状态、签名算法、验证时间、信封ID,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,验证数字信封,数字信封验证,记录数字信封验证日志,W,操作日志,操作类型、操作时间、用户ID、验证结果,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,查询数字信封,数字信封查询,输入数字信封查询条件,E,数字信封查询请求,查询时间范围、信封状态、用户ID,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,查询数字信封,数字信封查询,读取数字信封记录,R,数字信封记录,信封ID、生成时间、签名算法、验证状态,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,查询数字信封,数字信封查询,返回数字信封查询结果,X,数字信封查询结果,信封ID、生成时间、验证状态、签名算法,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,查询数字信封,数字信封查询,记录数字信封查询日志,W,操作日志,操作类型、操作时间、用户ID、查询条件,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,更新数字信封,数字信封更新,输入数字信封更新请求,E,数字信封更新请求,信封ID、更新字段、新值,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,更新数字信封,数字信封更新,读取原始数字信封,R,数字信封实体,信封ID、签名值、加密数据、生成时间,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,更新数字信封,数字信封更新,执行数字信封更新,W,数字信封实体,信封ID、更新字段、新值、更新时间,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,更新数字信封,数字信封更新,返回数字信封更新结果,X,数字信封更新响应,信封ID、更新状态、更新时间、操作人,1
SDP签名验签模块,签名验证,签名验签数字信封,nan,12,发起者：用户 接收者：密码服务平台,更新数字信封,数字信封更新,记录数字信封更新日志,W,操作日志,操作类型、操作时间、用户ID、更新内容,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,查询镜像列表,镜像列表查询,输入镜像查询条件,E,镜像查询条件,镜像名称、服务类型、查询项,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,查询镜像列表,镜像列表查询,读取镜像信息,R,镜像信息,镜像名称、镜像版本、服务类型、文件大小、创建时间,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,查询镜像列表,镜像列表查询,返回镜像列表展示,X,镜像列表展示信息,镜像名称、镜像版本、服务类型、状态、操作按钮,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,查询镜像列表,镜像列表查询,保存镜像查询记录,W,镜像查询记录,操作人ID、查询时间、查询条件、查询结果数量,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,启用镜像,镜像启用,输入镜像启用请求,E,镜像启用请求,镜像ID、操作人ID,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,启用镜像,镜像启用,验证镜像状态及权限,R,镜像状态信息,镜像状态、操作人权限、镜像类型,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,启用镜像,镜像启用,更新镜像启用状态,W,镜像状态更新,镜像ID、新状态、操作时间,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,启用镜像,镜像启用,返回启用结果,X,镜像启用结果,镜像名称、操作结果、状态变更时间,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,启用镜像,镜像启用,记录镜像启用日志,W,镜像操作日志,操作类型、操作人ID、镜像ID、操作时间,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,禁用镜像,镜像禁用,输入镜像禁用请求,E,镜像禁用请求,镜像ID、操作人ID,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,禁用镜像,镜像禁用,验证镜像状态及权限,R,镜像状态信息,镜像状态、操作人权限、镜像类型,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,禁用镜像,镜像禁用,更新镜像禁用状态,W,镜像状态更新,镜像ID、新状态、操作时间,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,禁用镜像,镜像禁用,返回禁用结果,X,镜像禁用结果,镜像名称、操作结果、状态变更时间,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,禁用镜像,镜像禁用,记录镜像禁用日志,W,镜像操作日志,操作类型、操作人ID、镜像ID、操作时间,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,删除镜像,镜像删除,输入镜像删除请求,E,镜像删除请求,镜像ID、操作人ID,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,删除镜像,镜像删除,验证镜像状态及权限,R,镜像状态信息,镜像状态、操作人权限、镜像类型,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,删除镜像,镜像删除,执行镜像删除操作,W,镜像删除记录,镜像ID、删除时间、操作人ID,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,删除镜像,镜像删除,返回删除结果,X,镜像删除结果,镜像名称、删除状态、操作时间,1
SDP签名验签模块,签名验证,签名验签服务镜像管理,nan,16,密码发起者：用户 接收者：密码服务平台,删除镜像,镜像删除,记录镜像删除日志,W,镜像操作日志,操作类型、操作人ID、镜像ID、操作时间,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组新增,服务组新增,输入服务组新增信息,E,服务组新增信息,服务组ID、服务组名称、服务组CODE、业务类型、KMS服务组ID,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组新增,服务组新增,校验服务组唯一性,R,服务组校验信息,服务组名称、服务组CODE、业务类型、KMS服务组ID,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组新增,服务组新增,保存服务组配置,W,服务组配置数据,服务组ID、服务组名称、服务组CODE、业务类型、KMS服务组ID、创建时间,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组新增,服务组新增,返回服务组新增结果,X,服务组新增结果,服务组ID、服务组名称、操作结果、错误代码,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组新增,服务组新增,记录服务组新增日志,W,服务组操作日志,操作类型、操作时间、操作用户、服务组ID、操作结果,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组修改,服务组修改,输入服务组修改信息,E,服务组修改条件,服务组ID、修改字段、新值,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组修改,服务组修改,读取原服务组配置,R,服务组原始数据,服务组ID、服务组名称、服务组CODE、业务类型、KMS服务组ID,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组修改,服务组修改,更新服务组配置,W,服务组更新数据,服务组ID、修改字段、新值、修改时间,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组修改,服务组修改,返回服务组修改结果,X,服务组修改结果,服务组ID、修改字段、操作结果、错误代码,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组修改,服务组修改,记录服务组修改日志,W,服务组操作日志,操作类型、操作时间、操作用户、服务组ID、修改内容,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组删除,服务组删除,发起服务组删除请求,E,服务组删除请求,服务组ID、删除原因,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组删除,服务组删除,校验服务组关联关系,R,服务组关联数据,服务组ID、租户绑定记录、设备关联状态,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组删除,服务组删除,执行服务组删除,W,服务组删除数据,服务组ID、删除时间、删除操作员,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组删除,服务组删除,返回服务组删除结果,X,服务组删除结果,服务组ID、操作结果、错误代码,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组删除,服务组删除,记录服务组删除日志,W,服务组操作日志,操作类型、操作时间、操作用户、服务组ID、删除原因,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组查询,服务组查询,输入服务组查询条件,E,服务组查询请求,服务组ID、服务组名称、业务类型,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组查询,服务组查询,读取服务组配置信息,R,服务组配置数据,服务组ID、服务组名称、服务组CODE、业务类型、KMS服务组ID,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组查询,服务组查询,返回服务组查询结果,X,服务组查询结果,服务组ID、服务组名称、业务类型、KMS服务组ID、创建时间,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组查询,服务组查询,记录服务组查询日志,W,服务组操作日志,操作类型、操作时间、操作用户、服务组ID、查询条件,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组绑定租户,服务组绑定租户,输入租户绑定请求,E,租户绑定请求,租户ID、服务组ID、业务类型,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组绑定租户,服务组绑定租户,校验租户与服务组关系,R,租户绑定校验数据,租户ID、服务组ID、业务类型、绑定状态,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组绑定租户,服务组绑定租户,保存租户绑定关系,W,租户绑定数据,租户ID、服务组ID、业务类型、绑定时间,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组绑定租户,服务组绑定租户,返回绑定操作结果,X,租户绑定结果,租户ID、服务组ID、操作结果、错误代码,1
SDP签名验签模块,签名验证,签名验签服务组管理,nan,16,发起者：用户 接收者：密码服务平台,服务组绑定租户,服务组绑定租户,记录租户绑定日志,W,服务组操作日志,操作类型、操作时间、操作用户、租户ID、服务组ID,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务创建,签名验签服务创建,输入签名验签服务创建信息,E,签名验签服务创建信息,服务名称、服务类型、有效期、服务描述,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务创建,签名验签服务创建,校验服务名称唯一性,R,签名验签服务校验信息,服务名称、服务类型、有效期,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务创建,签名验签服务创建,生成服务唯一标识,W,签名验签服务标识信息,服务ID、服务名称、创建时间,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务创建,签名验签服务创建,返回服务创建结果,X,签名验签服务创建结果,服务ID、服务名称、创建状态,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务创建,签名验签服务创建,记录服务创建日志,W,签名验签服务操作日志,操作员ID、服务ID、操作类型、操作时间,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务配置,签名验签服务配置,输入服务配置参数,E,签名验签服务配置参数,服务ID、配置项、配置值,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务配置,签名验签服务配置,读取服务当前配置,R,签名验签服务配置信息,服务ID、配置项、配置值,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务配置,签名验签服务配置,更新服务配置信息,W,签名验签服务配置更新,服务ID、配置项、新配置值,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务配置,签名验签服务配置,返回配置更新结果,X,签名验签服务配置结果,服务ID、配置项、更新状态,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务配置,签名验签服务配置,记录服务配置日志,W,签名验签服务操作日志,操作员ID、服务ID、操作类型、操作时间,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务启用,签名验签服务启用,输入服务启用请求,E,签名验签服务启用请求,服务ID、启用时间,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务启用,签名验签服务启用,验证服务状态,R,签名验签服务状态信息,服务ID、当前状态,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务启用,签名验签服务启用,更新服务状态为启用,W,签名验签服务状态更新,服务ID、新状态、启用时间,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务启用,签名验签服务启用,返回服务启用结果,X,签名验签服务启用结果,服务ID、启用状态、启用时间,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务启用,签名验签服务启用,记录服务启用日志,W,签名验签服务操作日志,操作员ID、服务ID、操作类型、操作时间,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务停用,签名验签服务停用,输入服务停用请求,E,签名验签服务停用请求,服务ID、停用原因,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务停用,签名验签服务停用,验证服务状态,R,签名验签服务状态信息,服务ID、当前状态,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务停用,签名验签服务停用,更新服务状态为停用,W,签名验签服务状态更新,服务ID、新状态、停用时间,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务停用,签名验签服务停用,返回服务停用结果,X,签名验签服务停用结果,服务ID、停用状态、停用时间,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务停用,签名验签服务停用,记录服务停用日志,W,签名验签服务操作日志,操作员ID、服务ID、操作类型、操作时间,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务续约,签名验签服务续约,输入服务续约请求,E,签名验签服务续约请求,服务ID、续约周期,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务续约,签名验签服务续约,验证服务授权类型,R,签名验签服务授权信息,服务ID、授权类型,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务续约,签名验签服务续约,更新服务有效期,W,签名验签服务有效期更新,服务ID、新有效期,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务续约,签名验签服务续约,返回服务续约结果,X,签名验签服务续约结果,服务ID、新有效期、续约状态,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务续约,签名验签服务续约,记录服务续约日志,W,签名验签服务操作日志,操作员ID、服务ID、操作类型、操作时间,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务删除,签名验签服务删除,输入服务删除请求,E,签名验签服务删除请求,服务ID、删除原因,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务删除,签名验签服务删除,验证服务状态,R,签名验签服务状态信息,服务ID、当前状态,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务删除,签名验签服务删除,删除服务记录,W,签名验签服务删除记录,服务ID、删除时间,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务删除,签名验签服务删除,返回服务删除结果,X,签名验签服务删除结果,服务ID、删除状态,1
SDP签名验签模块,签名验证,签名验签服务生命周期管理,nan,36,发起者：用户 接收者：密码服务平台,签名验签服务删除,签名验签服务删除,记录服务删除日志,W,签名验签服务操作日志,操作员ID、服务ID、操作类型、操作时间,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,证书模板新增,证书模板新增,输入证书模板新增信息,E,证书模板新增信息,模板ID、模板名称、签名算法、签发者、使用者,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,证书模板新增,证书模板新增,校验证书模板唯一性,R,证书模板校验信息,模板名称、签发者、使用者、状态,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,证书模板新增,证书模板新增,保存证书模板数据,W,证书模板数据,模板ID、模板名称、签名算法、签发者、使用者,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,证书模板新增,证书模板新增,返回证书模板新增结果,X,证书模板新增结果,模板ID、新增时间、操作结果,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,证书模板新增,证书模板新增,记录证书模板新增日志,W,证书模板操作日志,操作员ID、操作时间、操作类型、模板ID,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,认证服务配置,认证服务配置,输入认证服务配置参数,E,认证服务配置信息,服务ID、区域ID、服务名称、IP地址,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,认证服务配置,认证服务配置,读取现有认证服务配置,R,认证服务配置数据,服务ID、区域ID、服务名称、IP地址,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,认证服务配置,认证服务配置,更新认证服务配置,W,认证服务更新数据,服务ID、区域ID、服务名称、IP地址,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,认证服务配置,认证服务配置,返回认证服务配置结果,X,认证服务配置结果,服务ID、更新时间、操作结果,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,认证服务配置,认证服务配置,记录认证服务配置日志,W,认证服务操作日志,操作员ID、操作时间、服务ID、操作类型,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,巡检项管理,巡检项管理,输入巡检项配置信息,E,巡检项配置信息,巡检类型、子类型、启用状态,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,巡检项管理,巡检项管理,读取巡检项配置,R,巡检项配置数据,巡检类型、子类型、启用状态、备注,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,巡检项管理,巡检项管理,保存巡检项配置,W,巡检项配置数据,巡检类型、子类型、启用状态、备注,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,巡检项管理,巡检项管理,返回巡检项配置结果,X,巡检项配置结果,操作结果、配置时间、巡检类型,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,巡检项管理,巡检项管理,记录巡检项操作日志,W,巡检项操作日志,操作员ID、操作时间、巡检类型、操作类型,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,syslog服务器配置,syslog服务器配置,输入syslog服务器参数,E,syslog服务器配置信息,服务器IP、服务器端口、协议类型,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,syslog服务器配置,syslog服务器配置,读取现有syslog配置,R,syslog服务器配置数据,服务器IP、服务器端口、协议类型、启用状态,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,syslog服务器配置,syslog服务器配置,更新syslog服务器配置,W,syslog服务器更新数据,服务器IP、服务器端口、协议类型、启用状态,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,syslog服务器配置,syslog服务器配置,返回syslog配置结果,X,syslog配置结果,操作结果、配置时间、服务器IP,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,syslog服务器配置,syslog服务器配置,记录syslog配置日志,W,syslog操作日志,操作员ID、操作时间、服务器IP、操作类型,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,证书模板查询,证书模板查询,输入证书模板查询条件,E,证书模板查询条件,模板名称、签发者、状态,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,证书模板查询,证书模板查询,读取证书模板数据,R,证书模板数据,模板ID、模板名称、签名算法、签发者,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,证书模板查询,证书模板查询,返回证书模板查询结果,X,证书模板查询结果,模板ID、模板名称、签名算法、签发者,1
SDP签名验签模块,签名验证,签名验签服务数据库管理,nan,20,发起者：用户 接收者：密码服务平台,证书模板查询,证书模板查询,记录证书模板查询日志,W,证书模板操作日志,操作员ID、操作时间、操作类型、查询条件,1
SDP签名验签模块,签名验证,签名验签服务日志管理,nan,16,密码发起者：用户 接收者：密码服务平台,日志查询,日志查询,输入SIM操作日志查询条件,E,SIM操作日志查询条件,手机号、操作类型、日志类型、时间范围,1
SDP签名验签模块,签名验证,签名验签服务日志管理,nan,16,密码发起者：用户 接收者：密码服务平台,日志查询,日志查询,读取SIM操作日志数据,R,SIM操作日志数据,日志ID、手机号、操作类型、操作时间、HMAC状态,1
SDP签名验签模块,签名验证,签名验签服务日志管理,nan,16,密码发起者：用户 接收者：密码服务平台,日志查询,日志查询,返回日志查询结果,X,SIM操作日志查询结果,日志ID、手机号、操作类型、操作时间、审计状态,1
SDP签名验签模块,签名验证,签名验签服务日志管理,nan,16,密码发起者：用户 接收者：密码服务平台,日志查询,日志查询,记录日志查询操作,W,日志查询记录,操作人ID、查询时间、查询条件、查询结果数量,1
SDP签名验签模块,签名验证,签名验签服务日志管理,nan,16,密码发起者：用户 接收者：密码服务平台,日志审计,日志审计,输入批量审计请求,E,批量审计请求,日志ID列表、审计状态、操作人ID,1
SDP签名验签模块,签名验证,签名验签服务日志管理,nan,16,密码发起者：用户 接收者：密码服务平台,日志审计,日志审计,验证操作员审计权限,R,操作员权限信息,操作员ID、角色权限、审计模块权限,1
SDP签名验签模块,签名验证,签名验签服务日志管理,nan,16,密码发起者：用户 接收者：密码服务平台,日志审计,日志审计,更新日志审计状态,W,日志审计更新,日志ID、审计状态、审计时间、操作人ID,1
SDP签名验签模块,签名验证,签名验签服务日志管理,nan,16,密码发起者：用户 接收者：密码服务平台,日志审计,日志审计,返回审计结果,X,日志审计结果,日志ID、原状态、新状态、审计时间,1
SDP签名验签模块,签名验证,签名验签服务日志管理,nan,16,密码发起者：用户 接收者：密码服务平台,日志审计,日志审计,记录审计操作日志,W,审计操作记录,操作人ID、审计时间、操作类型、影响日志数量,1
SDP签名验签模块,签名验证,签名验签服务日志管理,nan,16,密码发起者：用户 接收者：密码服务平台,日志删除,日志删除,发起日志删除请求,E,日志删除请求,日志ID列表、删除原因、操作人ID,1
SDP签名验签模块,签名验证,签名验签服务日志管理,nan,16,密码发起者：用户 接收者：密码服务平台,日志删除,日志删除,验证日志删除权限,R,日志删除权限,操作员ID、日志ID、删除权限状态,1
SDP签名验签模块,签名验证,签名验签服务日志管理,nan,16,密码发起者：用户 接收者：密码服务平台,日志删除,日志删除,执行日志删除操作,W,日志删除记录,日志ID、删除时间、操作人ID、删除原因,1
SDP签名验签模块,签名验证,签名验签服务日志管理,nan,16,密码发起者：用户 接收者：密码服务平台,日志删除,日志删除,返回删除结果,X,日志删除结果,日志ID、删除状态、删除时间,1
SDP签名验签模块,签名验证,签名验签服务日志管理,nan,16,密码发起者：用户 接收者：密码服务平台,日志删除,日志删除,记录删除操作日志,W,日志删除操作记录,操作人ID、删除时间、删除日志数量、删除原因,1
SDP签名验签模块,签名验证,签名验签服务日志管理,nan,16,密码发起者：用户 接收者：密码服务平台,日志导出,日志导出,输入日志导出条件,E,日志导出请求,导出格式、时间范围、过滤条件,1
SDP签名验签模块,签名验证,签名验签服务日志管理,nan,16,密码发起者：用户 接收者：密码服务平台,日志导出,日志导出,生成日志导出文件,W,日志导出文件,文件ID、生成时间、文件大小、导出条件,1
SDP签名验签模块,签名验证,签名验签服务日志管理,nan,16,密码发起者：用户 接收者：密码服务平台,日志导出,日志导出,返回导出文件链接,X,日志导出结果,文件ID、下载链接、生成时间、文件类型,1
SDP签名验签模块,签名验证,签名验签服务日志管理,nan,16,密码发起者：用户 接收者：密码服务平台,日志导出,日志导出,记录导出操作日志,W,日志导出记录,操作人ID、导出时间、导出数量、文件类型,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,配置监控服务,监控服务配置,输入监控服务配置信息,E,监控服务配置信息,服务名称、IP地址、端口号、区域ID、认证方式ID,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,配置监控服务,监控服务配置,读取现有监控服务配置,R,监控服务配置,服务ID、区域ID、服务名称、IP地址、端口号,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,配置监控服务,监控服务配置,校验监控服务配置参数,R,SNMP认证方式,认证方式ID、认证方式名称、备注,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,配置监控服务,监控服务配置,保存监控服务配置,W,监控服务配置,服务ID、区域ID、服务名称、IP地址、端口号、认证方式ID,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,配置监控服务,监控服务配置,返回配置成功结果,X,配置结果,服务名称、配置状态、错误代码,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,检查服务状态,服务状态检查,发起服务状态查询请求,E,状态查询请求,服务ID、区域ID,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,检查服务状态,服务状态检查,读取监控服务运行状态,R,监控服务状态,服务ID、运行状态、最后心跳时间、CPU使用率,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,检查服务状态,服务状态检查,生成状态摘要信息,R,系统资源数据,内存占用、连接数、响应时间,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,检查服务状态,服务状态检查,返回服务状态详情,X,服务状态详情,服务名称、运行状态、资源使用情况、异常标识,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,检查服务状态,服务状态检查,记录状态检查日志,W,监控日志,操作时间、服务ID、检查结果、操作员ID,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,验证证书链,证书链验证监控,上传证书文件进行验证,E,证书验证请求,证书文件、验证类型、时间戳,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,验证证书链,证书链验证监控,解析证书链结构,R,证书链数据,证书序列号、颁发者、有效期、公钥算法,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,验证证书链,证书链验证监控,执行证书有效性检查,R,信任锚点,CA证书、信任策略、CRL分布点,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,验证证书链,证书链验证监控,生成证书验证报告,W,验证结果,验证状态、错误代码、完整证书链,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,验证证书链,证书链验证监控,输出验证结果详情,X,证书验证结果,验证状态、详细错误信息、证书路径,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,解析TLS报文,TLS报文解析监控,上传TLS抓包文件,E,TLS抓包数据,文件名称、抓包时间、协议版本,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,解析TLS报文,TLS报文解析监控,解析TLS握手过程,R,握手协议数据,客户端随机数、服务器证书、密钥交换算法,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,解析TLS报文,TLS报文解析监控,验证TLS会话完整性,R,会话参数,会话ID、加密套件、扩展字段,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,解析TLS报文,TLS报文解析监控,生成TLS分析报告,W,TLS分析结果,协议合规性、安全强度、异常标识,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,解析TLS报文,TLS报文解析监控,展示TLS解析结果,X,TLS解析详情,握手过程、证书验证状态、安全建议,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,生成监控告警,监控告警生成,设置告警阈值参数,E,告警规则,服务ID、CPU阈值、内存阈值,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,生成监控告警,监控告警生成,读取实时监控指标,R,监控指标,服务ID、CPU使用率、内存占用、连接数,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,生成监控告警,监控告警生成,执行告警条件判断,R,告警规则,服务ID、CPU阈值、内存阈值,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,生成监控告警,监控告警生成,生成告警事件记录,W,告警事件,告警时间、服务ID、告警类型、严重程度,1
SDP签名验签模块,签名验证,签名验签服务监控,nan,16,密码发起者：用户 接收者：密码服务平台,生成监控告警,监控告警生成,推送告警通知,X,告警通知,告警内容、通知渠道、接收人,1
SDP签名验签模块,签名验证,签名验签服务告警,nan,16,发起者：用户 接收者：密码服务平台,查询告警信息,告警信息查询,输入告警信息查询条件,E,告警信息查询请求,租户ID、区域ID、告警标识、告警源IP,1
SDP签名验签模块,签名验证,签名验签服务告警,nan,16,发起者：用户 接收者：密码服务平台,查询告警信息,告警信息查询,读取告警信息表数据,R,告警信息表,ID、租户ID、区域ID、告警标识、告警源IP,1
SDP签名验签模块,签名验证,签名验签服务告警,nan,16,发起者：用户 接收者：密码服务平台,查询告警信息,告警信息查询,返回告警信息查询结果,X,告警信息查询结果,告警类型、告警信息、所属租户、区域,1
SDP签名验签模块,签名验证,签名验签服务告警,nan,16,发起者：用户 接收者：密码服务平台,查询告警信息,告警信息查询,保存告警信息查询记录,W,告警查询日志,操作人ID、查询时间、查询条件,1
SDP签名验签模块,签名验证,签名验签服务告警,nan,16,发起者：用户 接收者：密码服务平台,查询告警历史,告警历史查询,输入告警历史查询条件,E,告警历史查询请求,租户名称、区域名称、告警标识,1
SDP签名验签模块,签名验证,签名验签服务告警,nan,16,发起者：用户 接收者：密码服务平台,查询告警历史,告警历史查询,读取告警历史表数据,R,告警历史表,ID、租户名称、区域名称、告警标识,1
SDP签名验签模块,签名验证,签名验签服务告警,nan,16,发起者：用户 接收者：密码服务平台,查询告警历史,告警历史查询,返回告警历史查询结果,X,告警历史查询结果,告警类型、处置状态、操作人、处置时间,1
SDP签名验签模块,签名验证,签名验签服务告警,nan,16,发起者：用户 接收者：密码服务平台,查询告警历史,告警历史查询,保存告警历史查询记录,W,告警历史查询日志,操作人ID、查询时间、查询条件,1
SDP签名验签模块,签名验证,签名验签服务告警,nan,16,发起者：用户 接收者：密码服务平台,新增告警信息,告警信息新增,输入告警信息新增数据,E,告警信息新增请求,告警标识、告警源ID、告警级别,1
SDP签名验签模块,签名验证,签名验签服务告警,nan,16,发起者：用户 接收者：密码服务平台,新增告警信息,告警信息新增,校验告警标识唯一性,R,告警标识校验数据,告警标识、主键约束,1
SDP签名验签模块,签名验证,签名验签服务告警,nan,16,发起者：用户 接收者：密码服务平台,新增告警信息,告警信息新增,写入告警信息表,W,告警信息表,ID、告警标识、告警源ID、告警级别,1
SDP签名验签模块,签名验证,签名验签服务告警,nan,16,发起者：用户 接收者：密码服务平台,新增告警信息,告警信息新增,返回告警新增结果,X,告警新增响应,新增状态、告警ID、错误信息,1
SDP签名验签模块,签名验证,签名验签服务告警,nan,16,发起者：用户 接收者：密码服务平台,修改告警信息,告警信息修改,输入告警信息修改参数,E,告警信息修改请求,告警ID、修改字段、新值,1
SDP签名验签模块,签名验证,签名验签服务告警,nan,16,发起者：用户 接收者：密码服务平台,修改告警信息,告警信息修改,读取原始告警信息,R,告警信息表,ID、告警标识、告警源ID,1
SDP签名验签模块,签名验证,签名验签服务告警,nan,16,发起者：用户 接收者：密码服务平台,修改告警信息,告警信息修改,更新告警信息表,W,告警信息表,ID、修改字段、更新时间,1
SDP签名验签模块,签名验证,签名验签服务告警,nan,16,发起者：用户 接收者：密码服务平台,修改告警信息,告警信息修改,返回告警修改结果,X,告警修改响应,修改状态、告警ID、错误信息,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,签名服务统计查询,签名服务统计查询,输入签名服务统计查询条件,E,签名服务统计查询条件,时间范围、服务类型、用户ID、签名次数,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,签名服务统计查询,签名服务统计查询,读取签名服务基础数据,R,签名服务基础数据,签名时间、签名算法、签名状态、用户ID,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,签名服务统计查询,签名服务统计查询,过滤签名服务数据,R,签名服务过滤数据,签名时间范围、服务类型匹配项、用户ID匹配项,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,签名服务统计查询,签名服务统计查询,计算签名服务统计指标,R,签名服务统计指标,总签名次数、成功次数、失败次数、平均耗时,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,签名服务统计查询,签名服务统计查询,生成签名服务统计结果,X,签名服务统计结果,统计时间、总签名次数、成功率、失败率,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,签名服务统计查询,签名服务统计查询,记录签名服务查询日志,W,签名服务查询日志,查询时间、操作用户、查询条件、结果数量,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,验签服务统计查询,验签服务统计查询,输入验签服务统计查询条件,E,验签服务统计查询条件,时间范围、验签类型、用户ID、验签次数,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,验签服务统计查询,验签服务统计查询,读取验签服务基础数据,R,验签服务基础数据,验签时间、验签算法、验签结果、用户ID,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,验签服务统计查询,验签服务统计查询,过滤验签服务数据,R,验签服务过滤数据,验签时间范围、服务类型匹配项、用户ID匹配项,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,验签服务统计查询,验签服务统计查询,计算验签服务统计指标,R,验签服务统计指标,总验签次数、成功次数、失败次数、平均耗时,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,验签服务统计查询,验签服务统计查询,生成验签服务统计结果,X,验签服务统计结果,统计时间、总验签次数、成功率、失败率,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,验签服务统计查询,验签服务统计查询,记录验签服务查询日志,W,验签服务查询日志,查询时间、操作用户、查询条件、结果数量,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,统计结果导出,统计结果导出,选择统计结果导出类型,E,统计结果导出类型,导出格式（CSV/Excel）、数据范围,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,统计结果导出,统计结果导出,读取待导出统计数据,R,待导出统计数据,签名/验签记录、时间戳、用户ID,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,统计结果导出,统计结果导出,转换统计结果格式,R,格式转换数据,字段映射规则、格式校验规则,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,统计结果导出,统计结果导出,生成导出文件,W,导出文件,文件名称、文件大小、生成时间,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,统计结果导出,统计结果导出,返回导出文件链接,X,导出文件链接,下载地址、文件类型、生成状态,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,统计结果导出,统计结果导出,记录导出操作日志,W,导出操作日志,操作时间、操作用户、导出类型、文件大小,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,生成统计报告,生成统计报告,配置统计报告参数,E,统计报告参数,报告周期、包含指标、输出格式,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,生成统计报告,生成统计报告,读取历史统计数据,R,历史统计数据,月度签名次数、季度验签成功率、年度趋势数据,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,生成统计报告,生成统计报告,生成统计图表,R,统计图表数据,柱状图配置、折线图配置、饼图配置,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,生成统计报告,生成统计报告,整合统计报告内容,R,统计报告内容,文本摘要、图表位置、数据附录,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,生成统计报告,生成统计报告,生成统计报告文件,W,统计报告文件,文件名称、生成时间、文件格式,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,生成统计报告,生成统计报告,返回报告预览信息,X,报告预览信息,预览链接、文件大小、生成状态,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,生成统计报告,生成统计报告,记录报告生成日志,W,报告生成日志,生成时间、操作用户、报告类型、文件大小,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,操作日志审计,操作日志审计,输入日志审计查询条件,E,日志审计查询条件,时间范围、操作类型、操作用户,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,操作日志审计,操作日志审计,读取操作日志数据,R,操作日志数据,操作时间、操作类型、操作详情、用户ID,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,操作日志审计,操作日志审计,过滤操作日志记录,R,过滤操作日志,时间匹配项、类型匹配项、用户匹配项,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,操作日志审计,操作日志审计,生成日志审计结果,X,日志审计结果,审计时间、记录数量、关键操作摘要,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,操作日志审计,操作日志审计,导出审计日志记录,W,审计日志导出,导出时间、导出格式、记录条数,1
SDP签名验签模块,签名验证,签名验签服务统计分析,nan,16,发起者：用户 接收者：密码服务平台,操作日志审计,操作日志审计,记录审计操作日志,W,审计操作日志,审计时间、操作用户、查询条件、导出状态,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,新增物理密码机,物理密码机新增,输入物理密码机新增信息,E,物理密码机新增请求,密码机名称、所属厂商、设备类型、管理IP、管理端口、业务IP、业务端口、授权码、连接密码、版本、序列号、备注,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,新增物理密码机,物理密码机新增,校验物理密码机IP唯一性,R,物理密码机IP校验信息,管理IP、业务IP、设备类型、厂商ID,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,新增物理密码机,物理密码机新增,校验设备序列号唯一性,R,物理密码机序列号校验信息,序列号、设备类型、厂商ID,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,新增物理密码机,物理密码机新增,写入物理密码机基础信息,W,物理密码机基础信息,密码机ID、密码机名称、厂商ID、设备类型ID、管理IP、管理端口、业务IP、业务端口、版本、序列号、创建时间,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,新增物理密码机,物理密码机新增,写入物理密码机认证信息,W,物理密码机认证信息,密码机ID、授权码、连接密码、认证状态、更新时间,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,新增物理密码机,物理密码机新增,返回物理密码机新增结果,X,物理密码机新增响应,密码机ID、操作结果、错误代码、错误描述,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,新增物理密码机,物理密码机新增,记录物理密码机新增日志,W,物理密码机操作日志,操作类型、操作员ID、操作时间、密码机ID、操作结果,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,查询物理密码机列表,物理密码机列表查询,输入物理密码机查询条件,E,物理密码机查询请求,厂商ID、设备类型ID、管理IP、状态、分页参数,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,查询物理密码机列表,物理密码机列表查询,读取物理密码机基础信息,R,物理密码机基础信息,密码机ID、密码机名称、厂商ID、设备类型ID、管理IP、管理端口、版本、序列号、创建时间,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,查询物理密码机列表,物理密码机列表查询,读取物理密码机状态信息,R,物理密码机状态信息,密码机ID、使用状态、连接状态、最后心跳时间,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,查询物理密码机列表,物理密码机列表查询,组合物理密码机完整信息,R,物理密码机完整信息,密码机ID、密码机名称、厂商名称、设备类型名称、管理IP、管理端口、使用状态、连接状态、版本、序列号,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,查询物理密码机列表,物理密码机列表查询,返回物理密码机列表结果,X,物理密码机列表响应,总记录数、当前页数据、分页参数、查询时间戳,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,查询物理密码机列表,物理密码机列表查询,记录物理密码机查询日志,W,物理密码机操作日志,操作类型、操作员ID、操作时间、查询条件摘要,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,修改物理密码机信息,物理密码机信息修改,输入物理密码机修改信息,E,物理密码机修改请求,密码机ID、修改字段、新值,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,修改物理密码机信息,物理密码机信息修改,读取物理密码机原始信息,R,物理密码机原始信息,密码机ID、密码机名称、厂商ID、设备类型ID、管理IP、管理端口、业务IP、业务端口、版本、序列号,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,修改物理密码机信息,物理密码机信息修改,校验物理密码机修改权限,R,物理密码机权限信息,密码机ID、操作员ID、操作权限类型,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,修改物理密码机信息,物理密码机信息修改,更新物理密码机基础信息,W,物理密码机基础信息,密码机ID、密码机名称、厂商ID、设备类型ID、管理IP、管理端口、业务IP、业务端口、版本、更新时间,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,修改物理密码机信息,物理密码机信息修改,返回物理密码机修改结果,X,物理密码机修改响应,密码机ID、操作结果、错误代码、错误描述,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,修改物理密码机信息,物理密码机信息修改,记录物理密码机修改日志,W,物理密码机操作日志,操作类型、操作员ID、操作时间、密码机ID、修改字段、旧值、新值,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,删除物理密码机,物理密码机删除,输入物理密码机删除请求,E,物理密码机删除请求,密码机ID、删除原因,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,删除物理密码机,物理密码机删除,读取物理密码机关联信息,R,物理密码机关联信息,密码机ID、关联业务系统数、关联证书数量,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,删除物理密码机,物理密码机删除,校验物理密码机删除权限,R,物理密码机权限信息,密码机ID、操作员ID、操作权限类型,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,删除物理密码机,物理密码机删除,标记物理密码机删除状态,W,物理密码机状态信息,密码机ID、删除状态、删除时间、删除操作员ID,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,删除物理密码机,物理密码机删除,返回物理密码机删除结果,X,物理密码机删除响应,密码机ID、操作结果、错误代码、错误描述,1
SDP签名验签模块,密码设备对接,签名验签物理密码机管理,nan,32,发起者：用户 接收者：密码服务平台,删除物理密码机,物理密码机删除,记录物理密码机删除日志,W,物理密码机操作日志,操作类型、操作员ID、操作时间、密码机ID、删除原因,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥生成,主密钥生成,输入主密钥生成请求,E,主密钥生成请求,设备ID、密钥类型、生成参数,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥生成,主密钥生成,读取设备类型配置信息,R,设备类型配置,设备类型ID、密钥算法、密钥长度,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥生成,主密钥生成,执行主密钥生成算法,W,主密钥数据,密钥值、生成时间、设备ID,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥生成,主密钥生成,返回主密钥生成结果,X,主密钥生成结果,密钥ID、生成状态、错误信息,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥生成,主密钥生成,记录主密钥生成日志,W,主密钥操作日志,操作类型、操作时间、操作员ID,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥备份,主密钥备份,输入主密钥备份请求,E,主密钥备份请求,密钥ID、备份类型、加密算法,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥备份,主密钥备份,读取主密钥数据,R,主密钥数据,密钥值、设备ID、生成时间,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥备份,主密钥备份,执行密钥加密备份,W,密钥备份记录,备份ID、加密密钥、备份时间,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥备份,主密钥备份,返回备份结果信息,X,备份结果,备份状态、备份ID、错误代码,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥备份,主密钥备份,记录备份操作日志,W,主密钥操作日志,操作类型、备份ID、操作员ID,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥恢复,主密钥恢复,输入主密钥恢复请求,E,主密钥恢复请求,备份ID、设备ID、恢复参数,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥恢复,主密钥恢复,读取密钥备份记录,R,密钥备份记录,加密密钥、备份时间、备份类型,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥恢复,主密钥恢复,执行密钥解密恢复,W,主密钥数据,密钥值、恢复时间、设备ID,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥恢复,主密钥恢复,返回恢复结果信息,X,恢复结果,恢复状态、密钥ID、错误信息,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥恢复,主密钥恢复,记录恢复操作日志,W,主密钥操作日志,操作类型、备份ID、操作员ID,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥更新,主密钥更新,输入主密钥更新请求,E,主密钥更新请求,旧密钥ID、新密钥参数,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥更新,主密钥更新,验证旧密钥有效性,R,主密钥数据,密钥值、设备ID、生成时间,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥更新,主密钥更新,生成新主密钥数据,W,主密钥数据,新密钥值、更新时间、设备ID,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥更新,主密钥更新,返回更新结果信息,X,更新结果,新密钥ID、更新状态、错误代码,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥更新,主密钥更新,记录更新操作日志,W,主密钥操作日志,操作类型、旧密钥ID、新密钥ID,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥查询,主密钥查询,输入主密钥查询条件,E,主密钥查询请求,设备ID、密钥类型、时间范围,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥查询,主密钥查询,读取主密钥信息,R,主密钥数据,密钥ID、密钥类型、生成时间,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥查询,主密钥查询,返回查询结果信息,X,主密钥查询结果,密钥列表、设备ID、密钥状态,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥查询,主密钥查询,记录查询操作日志,W,主密钥操作日志,操作类型、查询条件、操作员ID,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥删除,主密钥删除,输入主密钥删除请求,E,主密钥删除请求,密钥ID、删除原因,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥删除,主密钥删除,验证删除权限信息,R,用户权限数据,操作员ID、设备ID、权限级别,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥删除,主密钥删除,执行密钥删除操作,W,主密钥数据,密钥ID、删除时间、删除状态,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥删除,主密钥删除,返回删除结果信息,X,删除结果,删除状态、密钥ID、错误信息,1
SDP签名验签模块,密码设备对接,签名验签设备主密钥管理,nan,16,密码发起者：用户 接收者：密码服务平台,主密钥删除,主密钥删除,记录删除操作日志,W,主密钥操作日志,操作类型、密钥ID、操作员ID,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型新增,设备类型新增,输入设备类型新增信息,E,设备类型新增信息,设备类型名称、备注、是否默认,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型新增,设备类型新增,校验设备类型名称唯一性,R,设备类型校验信息,设备类型名称、设备类型ID、是否默认,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型新增,设备类型新增,保存设备类型新增数据,W,设备类型新增数据,设备类型ID、设备类型名称、备注、是否默认,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型新增,设备类型新增,返回设备类型新增结果,X,设备类型新增结果,设备类型ID、设备类型名称、新增状态,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型新增,设备类型新增,记录设备类型新增日志,W,设备类型操作日志,操作类型、操作时间、操作用户ID、设备类型ID,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型编辑,设备类型编辑,输入设备类型修改信息,E,设备类型修改信息,设备类型ID、设备类型名称、备注,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型编辑,设备类型编辑,读取设备类型原始数据,R,设备类型原始数据,设备类型ID、设备类型名称、备注、是否默认,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型编辑,设备类型编辑,更新设备类型数据,W,设备类型更新数据,设备类型ID、设备类型名称、备注、是否默认,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型编辑,设备类型编辑,返回设备类型修改结果,X,设备类型修改结果,设备类型ID、修改时间、修改状态,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型编辑,设备类型编辑,记录设备类型修改日志,W,设备类型操作日志,操作类型、操作时间、操作用户ID、设备类型ID,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型删除,设备类型删除,发起设备类型删除请求,E,设备类型删除请求,设备类型ID、删除原因,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型删除,设备类型删除,校验设备类型关联关系,R,设备类型关联数据,设备类型ID、关联设备数量、是否默认,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型删除,设备类型删除,执行设备类型删除,W,设备类型删除数据,设备类型ID、删除时间、删除状态,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型删除,设备类型删除,返回设备类型删除结果,X,设备类型删除结果,设备类型ID、删除状态、删除时间,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型删除,设备类型删除,记录设备类型删除日志,W,设备类型操作日志,操作类型、操作时间、操作用户ID、设备类型ID,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型查询,设备类型查询,输入设备类型查询条件,E,设备类型查询条件,设备类型名称、是否默认、查询时间范围,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型查询,设备类型查询,读取设备类型列表数据,R,设备类型列表,设备类型ID、设备类型名称、备注、是否默认,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型查询,设备类型查询,返回设备类型查询结果,X,设备类型查询结果,设备类型ID、设备类型名称、备注、是否默认,1
SDP签名验签模块,密码设备对接,签名验签设备类型管理,nan,16,密码发起者：用户 接收者：密码服务平台,设备类型查询,设备类型查询,记录设备类型查询日志,W,设备类型操作日志,操作类型、操作时间、操作用户ID、查询条件,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,创建签名验签设备组,用户创建签名验签设备组,输入签名验签设备组基础信息,E,签名验签设备组基础信息,设备组名称、设备组标识、设备组类型,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,创建签名验签设备组,用户创建签名验签设备组,校验设备组名称唯一性,R,设备组名称校验信息,设备组名称、设备组标识、创建时间,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,创建签名验签设备组,用户创建签名验签设备组,校验设备组标识唯一性,R,设备组标识校验信息,设备组标识、设备组名称、创建时间,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,创建签名验签设备组,用户创建签名验签设备组,保存签名验签设备组信息,W,签名验签设备组信息,设备组ID、设备组名称、设备组标识,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,创建签名验签设备组,用户创建签名验签设备组,返回设备组创建结果,X,设备组创建结果,设备组ID、创建状态、错误代码,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,创建签名验签设备组,用户创建签名验签设备组,记录设备组创建日志,W,设备组操作日志,操作类型、操作时间、操作用户,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,修改签名验签设备组,用户修改签名验签设备组,输入设备组修改参数,E,设备组修改参数,设备组ID、修改字段、新值,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,修改签名验签设备组,用户修改签名验签设备组,读取原设备组信息,R,设备组原始信息,设备组ID、设备组名称、设备组标识,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,修改签名验签设备组,用户修改签名验签设备组,校验修改后名称唯一性,R,设备组名称校验信息,设备组名称、设备组标识、创建时间,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,修改签名验签设备组,用户修改签名验签设备组,更新设备组信息,W,设备组更新信息,设备组ID、修改字段、修改后值,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,修改签名验签设备组,用户修改签名验签设备组,返回设备组修改结果,X,设备组修改结果,设备组ID、修改状态、错误代码,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,修改签名验签设备组,用户修改签名验签设备组,记录设备组修改日志,W,设备组操作日志,操作类型、操作时间、操作用户,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,删除签名验签设备组,用户删除签名验签设备组,输入设备组删除请求,E,设备组删除请求,设备组ID、删除原因,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,删除签名验签设备组,用户删除签名验签设备组,校验设备组关联关系,R,设备组关联校验信息,设备组ID、关联设备数、关联服务数,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,删除签名验签设备组,用户删除签名验签设备组,删除设备组信息,W,设备组删除信息,设备组ID、删除时间、删除用户,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,删除签名验签设备组,用户删除签名验签设备组,返回设备组删除结果,X,设备组删除结果,设备组ID、删除状态、错误代码,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,删除签名验签设备组,用户删除签名验签设备组,记录设备组删除日志,W,设备组操作日志,操作类型、操作时间、操作用户,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,查询签名验签设备组,用户查询签名验签设备组,输入设备组查询条件,E,设备组查询条件,查询字段、查询值、分页参数,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,查询签名验签设备组,用户查询签名验签设备组,读取设备组列表信息,R,设备组列表信息,设备组ID、设备组名称、设备组标识,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,查询签名验签设备组,用户查询签名验签设备组,返回设备组查询结果,X,设备组查询结果,设备组ID、设备组名称、设备组标识,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,查询签名验签设备组,用户查询签名验签设备组,记录设备组查询日志,W,设备组操作日志,操作类型、操作时间、操作用户,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,绑定租户到设备组,用户绑定租户到签名验签设备组,输入租户绑定参数,E,租户绑定参数,租户ID、设备组ID、共享模式,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,绑定租户到设备组,用户绑定租户到签名验签设备组,校验租户与设备组关系,R,租户绑定校验信息,租户ID、设备组ID、绑定状态,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,绑定租户到设备组,用户绑定租户到签名验签设备组,保存租户绑定关系,W,租户绑定关系,租户ID、设备组ID、绑定时间,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,绑定租户到设备组,用户绑定租户到签名验签设备组,返回租户绑定结果,X,租户绑定结果,租户ID、设备组ID、绑定状态,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,绑定租户到设备组,用户绑定租户到签名验签设备组,记录租户绑定日志,W,设备组操作日志,操作类型、操作时间、操作用户,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,解绑租户从设备组,用户解绑租户从签名验签设备组,输入租户解绑参数,E,租户解绑参数,租户ID、设备组ID,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,解绑租户从设备组,用户解绑租户从签名验签设备组,校验租户绑定关系,R,租户绑定校验信息,租户ID、设备组ID、绑定状态,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,解绑租户从设备组,用户解绑租户从签名验签设备组,删除租户绑定关系,W,租户绑定关系,租户ID、设备组ID、解绑时间,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,解绑租户从设备组,用户解绑租户从签名验签设备组,返回租户解绑结果,X,租户解绑结果,租户ID、设备组ID、解绑状态,1
SDP签名验签模块,密码设备对接,签名验签设备组管理,nan,32,密码发起者：用户 接收者：密码服务平台,解绑租户从设备组,用户解绑租户从签名验签设备组,记录租户解绑日志,W,设备组操作日志,操作类型、操作时间、操作用户,1
