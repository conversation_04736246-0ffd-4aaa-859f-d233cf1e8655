# COSMIC功能需求文档生成器 Web UI

基于COSMIC功能拆解数据生成功能需求文档的Web界面，支持Mermaid时序图在线渲染。

## 🌟 主要功能

1. **文件选择**: 通过下拉框选择项目目录下的xlsx或csv文件
2. **文档生成**: 调用doc_generator.py生成markdown格式的功能需求文档
3. **在线渲染**: 支持Markdown和Mermaid时序图的在线渲染
4. **重新生成**: 支持重新生成已存在的markdown文档
5. **文件管理**: 自动检查对应md文件是否存在，提供下载功能

## 🚀 快速开始

### 方法1: 使用启动脚本（推荐）

```bash
python start_web_ui.py
```

启动脚本会自动：
- 检查Python版本和依赖
- 安装缺失的依赖包
- 检查项目文件完整性
- 启动Web服务器
- 自动打开浏览器

### 方法2: 手动启动

1. **安装依赖**
```bash
pip install flask flask-cors markdown pygments pandas openpyxl requests
```

2. **启动Web服务器**
```bash
python web_ui.py
```

3. **访问Web界面**
打开浏览器访问: http://localhost:5000

## 📁 目录结构

```
cosmic/
├── web_ui.py              # Web应用主文件
├── start_web_ui.py        # 启动脚本
├── doc_generator.py       # 文档生成器
├── config.py              # 配置文件
├── doc_prompt.md          # 优化后的提示词
├── templates/
│   └── index.html         # Web界面模板
├── debug/
│   └── test_web_ui.py     # Web UI测试脚本
├── *.xlsx                 # Excel数据文件
├── *.csv                  # CSV数据文件
└── *_功能需求文档.md      # 生成的文档
```

## 🎯 使用流程

1. **准备数据文件**
   - 将COSMIC功能拆解的xlsx或csv文件放在项目根目录
   - 确保文件包含必要的列：一级功能模块、二级功能模块、三级功能模块、功能过程等

2. **启动Web服务**
   - 运行 `python start_web_ui.py`
   - 或手动运行 `python web_ui.py`

3. **选择文件**
   - 在Web界面的下拉框中选择要处理的文件
   - 查看文件信息和Markdown状态

4. **生成文档**
   - 点击"生成文档"按钮
   - 等待文档生成完成（可能需要几分钟）

5. **查看结果**
   - 点击"查看文档"在新窗口中查看渲染结果
   - 支持Mermaid时序图的在线渲染
   - 可以下载生成的markdown文件

## 📋 新的文档结构

根据用户需求，文档按照以下结构组织：

```markdown
## 2.1 一级功能需求 （对应一级模块）
(该一级模块的功能简介，描述)

### 2.1.1 关键时序图/业务逻辑图
(顺序列出各三级模块名称及时序图)

#### ******* 三级模块时序图
```mermaid
sequenceDiagram
    participant User as 功能用户
    participant System as 系统
    participant DB as 数据库
    
    User->>System: 触发事件
    System->>DB: 数据操作
    DB-->>System: 返回结果
    System->>User: 输出结果
```

### 2.1.2 功能需求描述
(详细描述该一级模块的功能)

#### ******* 二级功能需求 （对应二级模块）

##### *******.1 三级功能需求 （对应三级模块）
{三级模块名称} 包含如下功能：
{顺序列出该三级模块所包含的各功能过程名称}

###### *******.1.1 功能过程 （对应功能过程）
***功能简介***
{功能过程名称}
***功能要求***
{顺序列出各子过程描述列的名称}
```

## ⚙️ 配置说明

### 主要配置项（config.py）

- `REQUIREMENT_GENERATOR_CONFIG`: 文档生成器配置
  - `default_start_number`: 默认起始序号（如"2"）
  - `max_subprocess_per_batch`: 每批次最大子过程数
  - `prompt_file`: 系统提示词文件路径

### 提示词优化

- 只为三级模块生成时序图
- 功能过程不生成时序图，只生成功能描述
- 从三级模块逐层向上组织文档内容

## 🔧 测试功能

运行测试脚本验证功能：

```bash
python debug/test_web_ui.py
```

测试内容包括：
- 文件列表API
- 文档生成API
- 文档渲染功能
- Mermaid时序图支持

## 🎨 界面特性

- **响应式设计**: 支持桌面和移动设备
- **实时状态**: 显示文件信息和处理状态
- **进度提示**: 生成过程中显示加载动画
- **错误处理**: 友好的错误提示和处理
- **Mermaid支持**: 完整的时序图渲染支持

## 📝 注意事项

1. **文件格式**: 确保xlsx/csv文件包含必要的列名
2. **网络连接**: Mermaid渲染需要CDN资源，确保网络连接正常
3. **生成时间**: 大文件可能需要较长时间生成，请耐心等待
4. **浏览器兼容**: 推荐使用Chrome、Firefox等现代浏览器

## 🐛 故障排除

### 常见问题

1. **依赖缺失**
   ```bash
   pip install -r requirements.txt
   ```

2. **端口占用**
   - 修改web_ui.py中的端口号
   - 或终止占用5000端口的进程

3. **文档生成失败**
   - 检查数据文件格式
   - 查看控制台错误信息
   - 确认API配置正确

4. **Mermaid不显示**
   - 检查网络连接
   - 确认浏览器支持JavaScript
   - 查看浏览器控制台错误

## 📞 技术支持

如遇问题，请检查：
1. Python版本 >= 3.7
2. 所有依赖已正确安装
3. 数据文件格式正确
4. 网络连接正常

---

🎉 享受使用COSMIC功能需求文档生成器 Web UI！
