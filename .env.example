# COSMIC功能需求文档生成器配置文件

# 基本配置
BATCH_COUNT=30
PROMPT_PATH=prompt.md

# 嵌入模型配置
EMBEDDING_MODEL=embed
EMBEDDING_API_BASE=https://ai.secsign.online:38080
EMBEDDING_API_KEY=sk-JpT9PZnwGKDerAIyxmqavr0hEc98aBVnXzLB41fIlCcVVQRB

# 大模型配置
ENDPOINT_URL=https://ai.secsign.online:3003/v1/chat/completions
MODEL_NAME=qwen3-32b
API_KEY=sk-JpT9PZnwGKDerAIyxmqavr0hEc98aBVnXzLB41fIlCcVVQRB
API_QPM=60
API_TPM=100000

# 测试配置
TEST_LEVEL2_NAME=
TEST_LEVEL3_NAME=

# 知识库配置
MARKDOWN_MANUAL_PATH=docs/集省一体平台及服务操作手册.md,docs/三未信安密码服务平台管理端用户手册v3.4.0(无区域多租户模式).md
SQL_FILE_PATH=docs/ccsp_data.sql,docs/V3.4.pdma.json
KNOWLEDGE_BASE_ENABLED=true
KNOWLEDGE_BASE_TOP_K=5
KNOWLEDGE_BASE_SIMILARITY_THRESHOLD=0.1
KNOWLEDGE_BASE_CACHE_DIR=debug/knowledge_cache

# 分块处理配置
CHUNK_PROCESSING_ENABLED=true
MARKDOWN_CHILD_PATTERN=####
SQL_CHUNK_BY_TABLE=true
MAX_CHUNK_SIZE=20480
MIN_CHUNK_SIZE=100
EMBEDDING_BATCH_SIZE=3

# COSMIC校验功能配置
CHECK_ENDPOINT_URL=https://ai.secsign.online:3003/v1/chat/completions
CHECK_MODEL_NAME=qwen3-32b
CHECK_API_KEY=sk-JpT9PZnwGKDerAIyxmqavr0hEc98aBVnXzLB41fIlCcVVQRB
CHECK_API_QPM=
CHECK_API_TPM=
CHECK_EXCLUDED_FIELDS=预估工作量（人天）,功能描述
CHECK_OUTPUT_DIR=debug
CHECK_INPUT_DEBUG_FILE=cosmic_validation_input.json
CHECK_RESULT_FILE=cosmic_validation_result.json
CHECK_PROMPT_FILE=check_prompt.md
CHECK_BATCH_COUNT=500

# 功能需求文档生成配置
REQUIREMENT_DEFAULT_START_NUMBER=2
REQUIREMENT_MAX_SUBPROCESS_PER_BATCH=100
REQUIREMENT_PROMPT_FILE=doc_prompt.md
REQUIREMENT_OUTPUT_DIR=debug
REQUIREMENT_INCLUDE_TOC=true
REQUIREMENT_INCLUDE_TIMESTAMP=true
REQUIREMENT_MERMAID_THEME=default

# 数据列名映射
REQUIREMENT_COLUMN_LEVEL1=一级功能模块
REQUIREMENT_COLUMN_LEVEL2=二级功能模块
REQUIREMENT_COLUMN_LEVEL3=三级功能模块
REQUIREMENT_COLUMN_FUNCTION=功能过程
REQUIREMENT_COLUMN_USER=功能用户
REQUIREMENT_COLUMN_TRIGGER=触发事件
REQUIREMENT_COLUMN_SUBPROCESS=子过程描述
REQUIREMENT_COLUMN_DATA_MOVEMENT=数据移动类型
REQUIREMENT_COLUMN_DATA_GROUP=数据组
REQUIREMENT_COLUMN_DATA_ATTRIBUTE=数据属性
