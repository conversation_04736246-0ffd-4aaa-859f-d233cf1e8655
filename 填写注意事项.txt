1、文件命名格式：如项目较小，项目名称：COSMIC软件评估功能点拆分表.xlsx；如项目较大，项目名称-一级/二级/三级功能模块序号XXX：COSMIC软件评估功能点拆分表.xlsx（其中XXX代表模块编号，请与需求说明书保持一致）
2、附件5里必含“功能时序图”、“功能拆分表”两张Sheet，Sheet表的名字不允许更改
3、提交材料时，A列应为空白。当评审过程中需求发生变更时，请根据增删改的内容在A列进行对应标注
4、请务必将“子过程描述”填写在J列。不允许增加列
5、功能点拆分表填写完成后，请仅保留评估内容，删除作为示范的案例、定义、填写说明
6.功能点拆分应基于功能时序图
7.数据跨层交互是重要的判定依据
8.静态判断不作为功能点
9.使用了开源软件或配置已有功能作为实现途径，配置过程不能作为功能点
10.建议至少一个三级功能模块提供一个时序图
11.子系统是指如果存在一个项目分为多个开发商开发，或者多个独立部署的系统，那么每个独立的软件系统就是本项目的一个子系统，不存在这类情况就留空
