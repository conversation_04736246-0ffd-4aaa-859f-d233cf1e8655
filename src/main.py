import requests
import re
import json
import pandas as pd
from collections import OrderedDict
from config import ENDPOINT_URL, MODEL_NAME, API_KEY, API_QPM, API_TPM, BATCH_COUNT,TEST_LEVEL2_NAME, TEST_LEVEL3_NAME,KNOWLEDGE_BASE_ENABLED,PROMPT_PATH, THREAD_COUNT, MAX_THREAD_COUNT
from csv_2_xls import process_csv_to_excel
from knowledge_base import knowledge_base
import time
import math  # 用于token估算
import threading
import os
import llm_util

from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict


def flatten_results_with_subprocess(all_items):
    """
    输入：all_items为包含模块信息和大模型返回的所有item（每个item有'子过程'字段，值为列表）
    输出：每个子过程一行的扁平化结果，所有父级信息每行都保留
    """
    flat_results = []
    for item in all_items:
        subprocess_list = item.get('子过程', [])
        # 拷贝除'子过程'外的所有字段
        base_info = {k: v for k, v in item.items() if k != '子过程'}
        for subprocess in subprocess_list:
            row = base_info.copy()
            row.update(subprocess)  # 子过程的key直接变成列
            flat_results.append(row)
    return flat_results

def llm_cfp(level_1, level_2, level_3, function_process_list, description_list, user_inputs, all_results,
           level_1_name="一级功能模块", level_2_name="二级功能模块", level_3_name="三级功能模块",
           func_process_name="功能过程", function_description_name="功能描述", estimated_workload_name="预估工作量（人天）"):
    # 先检查3级模块
    if TEST_LEVEL3_NAME and level_3 != TEST_LEVEL3_NAME:
        return
    if TEST_LEVEL2_NAME and level_2 != TEST_LEVEL2_NAME:
        return
    if not user_inputs:
        print(f"!!!!【{level_1}-{level_2}-{level_3}】内容空，跳过!!!!")
        return
    
    print(f"开始处理:【{level_1}-{level_2}-{level_3}】......")
    batch_user_input = "\n".join([f"{i+1}. {input}" for i, input in enumerate(user_inputs)])
    # 获取知识库上下文
    knowledge_context = ""
    if KNOWLEDGE_BASE_ENABLED and knowledge_base.enabled:
        # 为当前三级模块获取知识库上下文
        query_parts = function_process_list
        query = level_3 +" , " + " ".join([part for part in query_parts if part and str(part).strip()])
        context = knowledge_base.get_context_for_module( query)
        if context:
            knowledge_context = f"### {level_3} 相关上下文:\n{context}"
            #print(f"获取知识库上下文: {current_level_3}")

    # 可重试3次大模型调用
    for i in range(3):
        try:            
            result = llm_util.call_LLM(prompt, batch_user_input, knowledge_context)
            #print(json.dumps(result, ensure_ascii=False, indent=4))
            if result:
                result = llm_util.extract_json_from_content(result)
            if isinstance(result, list):
                break
            print(f"第{i+1}次大模型结果解析失败，正在重试...")
        except Exception as e:
            print(f"第{i+1}次大模型调用失败，正在重试...: {e}")
        time.sleep(1)
    if not result or not isinstance(result, list):
        print(f"未能正确解析到结果，请检查大模型返回的格式是否正确.")
        return
    # 每个元素是 {"三级功能模块名称": [功能过程列表]}
    for module_dict in result:
        if isinstance(module_dict, dict):
            # 遍历每个三级模块
            for _, function_processes in module_dict.items():
                # 使用传入的参数信息
                func_desc = description_list[0] if description_list else ""
                est_work = ""  # 预估工作量暂时不使用

                # 处理该模块下的每个功能过程
                if isinstance(function_processes, list):
                    for i, process in enumerate(function_processes):
                        ordered_item = OrderedDict()
                        ordered_item[level_1_name] = level_1
                        ordered_item[level_2_name] = level_2
                        ordered_item[level_3_name] = level_3
                        # 添加对应的功能过程信息
                        if i < len(function_process_list):
                            ordered_item[func_process_name] = function_process_list[i]
                        else:
                            ordered_item[func_process_name] = ""
                        ordered_item[function_description_name] = func_desc
                        ordered_item[estimated_workload_name] = est_work
                        # 添加功能过程的所有字段
                        for k, v in process.items():
                            ordered_item[k] = v
                        all_results.append(ordered_item)

def process_level2_module_group(level_2_module, grouped_data, level_1_name, level_2_name, level_3_name,
                               func_process_name, function_description_name, estimated_workload_name):
    """
    处理单个二级模块的所有数据

    Args:
        level_2_module: 二级模块名称
        grouped_data: 该二级模块下的所有数据
        其他参数: 列名和提示词等配置

    Returns:
        该二级模块处理后的结果列表
    """
    print(f"[线程{threading.current_thread().name}] 开始处理二级模块: {level_2_module}")

    module_results = []
    user_inputs = []
    current_level_3 = None
    current_level_2 = None
    current_level_1 = None
    current_function_process_list = []
    current_function_description_list = []
    current_estimated_workload_list = []


    # 按原始顺序处理该二级模块下的所有行
    for row in grouped_data:
        level_1_module = str(row[level_1_name])
        level_2_module_row = str(row[level_2_name])
        level_3_module = str(row[level_3_name])
        func_process = str(row[func_process_name])
        function_description = str(row[function_description_name])
        estimated_workload = str(row[estimated_workload_name])

        # 跳过无效三级模块
        if pd.isna(level_3_module) or str(level_3_module).strip().lower() in ['nan', 'none', '']:
            continue

        # 如果遇到新的三级功能模块，先处理上一个
        if (current_level_3 is not None) and (level_3_module != current_level_3 or level_2_module_row != current_level_2 or level_1_module != current_level_1):
            llm_cfp(current_level_1, current_level_2, current_level_3, current_function_process_list,
                   current_function_description_list, user_inputs, module_results,
                   level_1_name, level_2_name, level_3_name, func_process_name,
                   function_description_name, estimated_workload_name)

            # 清空累计
            user_inputs = []
            current_function_process_list = []
            current_function_description_list = []
            current_estimated_workload_list = []

        # 累计当前三级功能模块下的功能过程
        user_input = f"{level_1_name}：{level_1_module}，{level_2_name}：{level_2_module_row}，{level_3_name}：{level_3_module}, {func_process_name}: {func_process}, {function_description_name}: {function_description}, {estimated_workload_name}: {estimated_workload}"
        user_inputs.append(user_input)

        current_function_process_list.append(func_process)
        current_function_description_list.append(function_description)
        current_estimated_workload_list.append(estimated_workload)

        current_level_3 = level_3_module
        current_level_2 = level_2_module_row
        current_level_1 = level_1_module


    # 处理最后一组
    if current_level_3 is not None:
        llm_cfp(current_level_1, current_level_2, current_level_3, current_function_process_list,
               current_function_description_list, user_inputs, module_results,
               level_1_name, level_2_name, level_3_name, func_process_name,
               function_description_name, estimated_workload_name)

    print(f"[线程{threading.current_thread().name}] 完成二级模块: {level_2_module}, 处理了 {len(module_results)} 个结果")
    return level_2_module, module_results
def set_duplicate_modules_to_nan(df, module_cols, right_cols):
    """
    对module_cols做全局去重，对right_cols只在同一个三级模块下做去重。
    """
    # 1. 先对module_cols做全局去重
    for col in module_cols:
        df[col] = df[col].where(df[col] != df[col].shift(), None)
    # 2. 对right_cols在同一个三级模块下做去重
    level_3_col = module_cols[-1]  # 假设最后一个是三级模块
    for col in right_cols:
        if col in df.columns:
            df[col] = df.groupby(level_3_col, group_keys=False)[col].apply(lambda x: x.where(x != x.shift(), None))
    return df
def move_column(result_df, col_to_move, after_col)-> pd.DataFrame:
    cols = result_df.columns.tolist()
    if after_col in cols and col_to_move in cols:
        # 执行列移动逻辑
        trigger_idx = cols.index(after_col)  # 找到触发事件列位置
        cols.insert(trigger_idx , cols.pop(cols.index(col_to_move)))  # 移动列
        return result_df[cols]  # 保持原有列顺序
    else:
        print("缺少必要列，请检查数据结构")
        return result_df

def get_optimal_thread_count(level_2_count):
    """获取最优线程数"""
    if THREAD_COUNT > 0:
        # 使用配置的线程数，但不能超过模块数
        optimal_threads = min(THREAD_COUNT, level_2_count)
    else:
        # 自动根据CPU核心数确定
        cpu_count = os.cpu_count() or 4
        optimal_threads = min(cpu_count, level_2_count)

    # 应用最大线程数限制
    optimal_threads = min(optimal_threads, MAX_THREAD_COUNT)

    # 至少1个线程
    return max(1, optimal_threads)

if __name__ == "__main__":
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1:
        excel_file = sys.argv[1] # COSMIC数据文件
    else:
        excel_file = "data/附件2：功能清单-2025-all.xlsx"
    level_1_name, level_2_name, level_3_name, func_process_name = "一级功能模块", "二级功能模块","三级功能模块","功能过程"
    function_description_name = "功能描述"
    estimated_workload_name = "预估工作量（人天）"  # 注意：调整后的文件中有换行符
    sheet_name = 1
    header = 0

    with open(PROMPT_PATH, 'r', encoding='utf-8') as f:
        prompt = f.read()

    df = pd.read_excel(excel_file, sheet_name = sheet_name, header = header)

    # 列名替换逻辑 : 预估工作量列中有回车符
    # df.columns = [col if not re.search(r'预估工作量', col) else estimated_workload_name for col in df.columns]

    # 向后填充一级/二级模块列
    df[[level_1_name, level_2_name, level_3_name]] = df[[level_1_name, level_2_name, level_3_name]].ffill()

    # 按二级模块分组数据
    grouped_by_level2 = defaultdict(list)
    level_2_order = {}  # 记录二级模块的原始顺序
    order_counter = 0

    for idx, row in df.iterrows():
        level_2_module = str(row[level_2_name])
        level_3_module = str(row[level_3_name])

        # 跳过无效三级模块
        if pd.isna(level_3_module) or str(level_3_module).strip().lower() in ['nan', 'none', '']:
            continue

        # 记录二级模块的顺序
        if level_2_module not in level_2_order:
            level_2_order[level_2_module] = order_counter
            order_counter += 1

        grouped_by_level2[level_2_module].append(row.to_dict())

    # 获取所有二级模块列表（按原始顺序）
    level_2_modules = sorted(grouped_by_level2.keys(), key=lambda x: level_2_order[x])

    print(f"发现 {len(level_2_modules)} 个二级模块: {level_2_modules}")

    # 确定线程数
    thread_count = get_optimal_thread_count(len(level_2_modules))
    print(f"使用 {thread_count} 个线程进行处理")

    # 多线程处理
    all_results = []
    start_time = time.time()

    with ThreadPoolExecutor(max_workers=thread_count, thread_name_prefix="CosmicWorker") as executor:
        # 提交任务
        future_to_module = {}
        for level_2_module in level_2_modules:
            future = executor.submit(
                process_level2_module_group,
                level_2_module,
                grouped_by_level2[level_2_module],
                level_1_name, level_2_name, level_3_name,
                func_process_name, function_description_name, estimated_workload_name
            )
            future_to_module[future] = level_2_module

        # 收集结果（按原始顺序）
        module_results = {}
        for future in as_completed(future_to_module):
            level_2_module = future_to_module[future]
            try:
                module_name, results = future.result()
                module_results[module_name] = results
                print(f"完成模块 {module_name}: {len(results)} 个结果")
            except Exception as exc:
                print(f"模块 {level_2_module} 处理失败: {exc}")
                module_results[level_2_module] = []

        # 按原始顺序合并结果
        for level_2_module in level_2_modules:
            if level_2_module in module_results:
                all_results.extend(module_results[level_2_module])

    processing_time = time.time() - start_time
    print(f"多线程处理完成，耗时: {processing_time:.2f}秒，共处理 {len(all_results)} 个结果")
    
    # all_results存储为json格式
    # json_results = json.dumps(all_results, ensure_ascii=False)
    # with open('output.json', 'w', encoding='utf-8') as f:
    #     f.write(json_results)
   
    
    flat_results = flatten_results_with_subprocess(all_results)
    result_df = pd.DataFrame(flat_results)
    module_cols = [level_1_name, level_2_name, level_3_name]
    right_cols = [func_process_name, function_description_name, estimated_workload_name, '功能用户', '触发事件', '功能过程']
    #result_df = set_duplicate_modules_to_nan(result_df, module_cols, right_cols)
    # 把"功能过程"列挪到“触发事件”后
    result_df = move_column(result_df, "功能过程","触发事件")
    #result_df = move_column(result_df, "预估工作量（人天）","功能过程")    
    
    out_file = f"{excel_file}_output_"+time.strftime("%m%d%H%M", time.localtime())+".csv"
    result_df.to_csv(out_file, index=False, encoding='utf-8-sig')
    process_csv_to_excel(out_file)