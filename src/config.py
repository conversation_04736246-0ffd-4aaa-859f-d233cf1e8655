# 每批次最多的三级模块数量
BATCH_COUNT = 30

DATA_DIR = "data/"
# 多线程配置
THREAD_COUNT = 6  # 线程数量，设置为0时自动根据CPU核心数确定
MAX_THREAD_COUNT = 8  # 最大线程数限制

PROMPT_PATH="prompt/prompt.md"
# 嵌入模型配置
EMBEDDING_MODEL="embed"  # 嵌入模型
#EMBEDDING_API_BASE="http://10.10.43.64:38080"
EMBEDDING_API_BASE = "https://ai.secsign.online:38080"
EMBEDDING_API_KEY = "sk-JpT9PZnwGKDerAIyxmqavr0hEc98aBVnXzLB41fIlCcVVQRB"

# LEVEL2_NAME = "密码资产数据管理"
TEST_LEVEL2_NAME = "" #总部一级平台对接
TEST_LEVEL3_NAME = "" #"密钥及生命周期管理"
#ENDPOINT_URL="http://10.10.43.64:3300/v1/chat/completions"
ENDPOINT_URL="https://ai.secsign.online:3003/v1/chat/completions"
#ENDPOINT_URL="http://10.20.45.180:3100/v1/chat/completions"
MODEL_NAME="qwen3-32b"
API_KEY="sk-JpT9PZnwGKDerAIyxmqavr0hEc98aBVnXzLB41fIlCcVVQRB"

# 阿里云
# ENDPOINT_URL="https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
# ## ENDPOINT_URL="https://10.10.43.64:3000/compatible-mode/v1/chat/completions"
# MODEL_NAME="qwen3-coder-plus-2025-07-22"
# API_KEY="sk-1d06e75d7fd94338b5b32cf8f9099651"
# 每分钟的调用次数限制
API_QPM=600
# 每分钟处理的文本token数量
API_TPM=1000000

# openrouter
# ENDPOINT_URL="https://openrouter.ai/api/v1/chat/completions"
# MODEL_NAME="moonshotai/kimi-k2:free"
# API_KEY="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"
# # 每分钟的调用次数限制
# API_QPM=1
# # 每分钟处理的文本token数量
# API_TPM=1000000

# 知识库相关配置
KNOWLEDGE_BASE_ENABLED = True  # 是否启用知识库功能
KNOWLEDGE_BASE_TOP_K = 5  # 检索返回的最相关结果数量
KNOWLEDGE_BASE_SIMILARITY_THRESHOLD = 0.1  # 相似度阈值，低于此值的结果将被过滤
KNOWLEDGE_BASE_CACHE_DIR = "data/gansu_knowledge"  # 知识库缓存目录
# 新增知识库配置 - 支持多个文件，逗号分隔
# MARKDOWN_MANUAL_PATH = "debug/test_manual.md"  # 临时使用测试文件
# 文件路径格式： 文件路径名:markdown的子标题
MARKDOWN_MANUAL_PATH = "docs/甘肃移动/SHM1812软件密码模块产品用户手册-外部公开.md:#,docs/甘肃移动/SZT1701数字证书认证系统用户手册v5.0.1.2.md:####,docs/甘肃移动/协同签名服务 用户手册V4.6.8.md:###,docs/甘肃移动/密码软算法技术白皮书-外部公开.md:###,docs/甘肃移动/密码软算法接口文档（C）V1.14.md:###,docs/甘肃移动/密码软算法用户手册-外部公开.md:#,docs/甘肃移动/签名验签使用手册-ccsp-3.4.0:###,docs/河南移动手册/三未信安密码服务平台管理端用户手册v3.4.0(无区域多租户模式).md:####"
SQL_FILE_PATH = "docs/V3.4.pdma.json"  # 支持SQL和JSON格式

# 分块处理配置
CHUNK_PROCESSING_ENABLED = True  # 是否启用分块处理
MARKDOWN_CHILD_PATTERN = '####'  # 子级分段正则表达式模式（四级标题）
SQL_CHUNK_BY_TABLE = True  # SQL文件是否按数据表分块
MAX_CHUNK_SIZE = 20480  # 单个分块的最大字符数（20KB），超过此大小会进一步分割
MIN_CHUNK_SIZE = 100  # 单个分块的最小字符数，小于此大小会合并到相邻分块
EMBEDDING_BATCH_SIZE = 3  # 向量化处理的批次大小，避免超过模型限制

# COSMIC校验功能配置
# 校验专用的大模型配置（如果不设置，则使用默认配置）
CHECK_ENDPOINT_URL = "https://ai.secsign.online:3003/v1/chat/completions"  # 校验专用API端点，None表示使用默认ENDPOINT_URL
CHECK_MODEL_NAME = "qwen3-32b"    # 校验专用模型名称，None表示使用默认MODEL_NAME
CHECK_API_KEY = "sk-JpT9PZnwGKDerAIyxmqavr0hEc98aBVnXzLB41fIlCcVVQRB"       # 校验专用API密钥，None表示使用默认API_KEY
CHECK_API_QPM = None       # 校验专用QPM限制，None表示使用默认API_QPM
CHECK_API_TPM = None       # 校验专用TPM限制，None表示使用默认API_TPM

# 校验功能特定配置
CHECK_EXCLUDED_FIELDS = [   # 在校验时需要去除的字段列表
    '预估工作量（人天）',      # 去除预估工作量字段，减少提示词使用
    '功能描述',            # 可选：去除功能描述字段
]

# 校验结果输出配置
CHECK_OUTPUT_DIR = "data/docs"                                    # 校验结果输出目录
CHECK_INPUT_DEBUG_FILE = "cosmic_validation_input.json"      # 输入数据调试文件名
CHECK_RESULT_FILE = "cosmic_validation_result.json"          # 校验结果文件名
CHECK_PROMPT_FILE = "prompt/check_prompt.md"                        # 校验提示词文件名
CHECK_BATCH_COUNT = 500

# 功能需求文档生成配置
REQUIREMENT_GENERATOR_CONFIG = {
    # 基本配置
    "default_start_number": "2",                             # 默认起始序号
    "max_subprocess_per_batch": 200,                          # 每批次最大子过程数
    "prompt_file": "prompt/doc_prompt.md",                    # 系统提示词文件
    #"output_dir": "debug",                                   # 输出目录

    # 文档格式配置
    "include_toc": True,                                     # 是否包含目录
    "include_timestamp": True,                               # 是否包含时间戳
    "mermaid_theme": "default",                              # Mermaid图表主题

    # 多线程配置
    "max_threads": 4,                                        # 最大线程数

    # 数据列名映射（根据实际CSV文件调整）
    "column_mapping": {
        "level1": "一级功能模块",
        "level2": "二级功能模块",
        "level3": "三级功能模块",
        "function": "功能过程",
        "user": "功能用户",
        "trigger": "触发事件",
        "subprocess": "子过程描述",
        "data_movement": "数据移动类型",
        "data_group": "数据组",
        "data_attribute": "数据属性"
    }
}