说明,"OPEX-需求名称
CAPEX-子系统",一级模块,二级模块,三级模块,功能用户,"OPEX-功能用户需求
CAPEX-可留空",触发事件,功能过程,子过程描述,数据移动类型,数据组,数据属性,CFP,CFP评估核定,备注
定义,"OPEX：与需求管理平台中上报的需求名称对应
CAPEX：一个项目分为多个开发商开发，或者多个独立部署的系统，那么每个独立的软件系统就是本项目的一个子系统，不存在这类情况就留空",本次需求需要改造的本项目（或子系统）中已有的一级业务功能名称，或者本次需求新增的本项目（或子系统）的一级业务功能名称,本次需求需要改造的本项目（或子系统）中已有的二级业务功能名称，或者本次需求新增的本项目（或子系统）的二级业务功能名称,本次需求需要改造的本项目（或子系统）中已有的三级业务功能名称，或者本次需求新增的本项目（或子系统）的三级业务功能名称,一个（类）用户是软件块的功能性用户需求中数据的发送者或者预期的接收者。,用户需求的子集。这些需求以任务和服务的形式描述软件做什么。,待度量软件的功能性用户需求中可识别的一个事件，此事件使得一个或多个软件功能用户产生一个或多个数据组，每个数据组随后被一个触发输入所移动。,"1、体现了待度量软件的功能性用户需求基本部件的一组数据移动，该功能处理在该FUR中是独一无二的，并能独立于该FUR的其他功能处理被定义。
2、一个功能处理可能只有一个触发输入。每个功能处理在接受到由其触发输入数据移动所移动的一个数据组后，开始进行处理。
3、一个功能处理的所有数据移动的集合是满足其FUR的触发输入所有可能的响应所需的集合。","1、每个功能处理由一系列子过程组成。
2、一个子处理可以是一个数据移动或者数据运算。",COSMIC规定的四种数据移动类型。包括：输入（E）输出（X）读（R）写（W）,一个唯一的、非空的、无序的数据属性的集合。,一个数据属性是一个已识别的数据组中最小的信息单元。,表示数据移动的规模。,评定结果,"填写规则如下：
送审可以缺省此项，评估必须填写此项

“新增”：CFP值为1；

“复用”：CFP值为1/3；

“不计数”：CFP值为0；"
填写说明,"来源于需求文档。
（选填）","来源于需求文档。应与功能架构图对应，每个项目应保持其功能模块划分的持续性
（必填）","来源于需求文档。应与功能架构图对应，每个项目应保持其功能模块划分的持续性
（必填）","来源于需求文档。应与功能架构图对应，每个项目应保持其功能模块划分的持续性
（必填）","只包含数据发起者和数据接受者。（功能用户可能是自然人、系统、程序、模块）若数据发起者有多个，要求拆分为1对1 的进行填写。
（必填）","主：发起者
谓：操作
宾：事务
（必填）","操作+对象
对象+被操作
（必填）","主：发起者
谓：子操作
宾：对应事务
（必填）","谓：下一级子操作
宾：事务
(必填)",四选一（必填）,"识别对象的新域名
(必填)","子过程可识别的字段
(必填，可填写部分属性)",送审值：每一个数据移动表示1个CFP,"评估值：
如果机器初审，人工复审认定本行结果为“新增”，则填写为1；
如果机器初审，人工复审认定本行结果为“复用”，则填写为1/3；
如果机器初审，人工复审认定本行结果为“不计数”，则填写为0；
","机器初审、人工复审都需要填写评估认定理由
填写规则如下：

“新增”：CFP值为1：可空

“复用”：CFP值为1/3：详细描述清楚复合国标/行标/企标/院标等技术判定依据

“不计数”：CFP值为0：详细描述清楚复合国标/行标/企标/院标等技术判定依据"
案例,客户端校园之春活动,,,,发起者：个人用户接收者：客户端,个人用户评论活动内容,输入评论,个人用户新增评论,用户输入评论内容,E,评论信息,"编号,用户ID,目标编号,目标类型,内容,创建时间,是否可用,审核时间,审核人,审核原因,审核状态,回复编号,回复人ID,来源",1,0,
,,,,,,,,,查询敏感词过滤信息,R,敏感词信息,编号，敏感词内容，时间,1,1,
,,,,,,,,,查询先审后发机制配置,R,审核配置信息,配置代码，配置内容，配置时间，审核方式,1,1,
,,,,,,,,,保存用户评论,W,活动评论,"编号,用户ID,目标编号,目标类型,内容,创建时间,是否可用,审核时间,审核人,审核原因,审核状态,回复编号,回复人ID,来源",0,0,
,,,,,,,,,提示用户评论是否成功,X,活动评论,"编号,用户ID,目标编号,目标类型,内容,创建时间,是否可用,审核时间,审核人,审核原因,审核状态,回复编号,回复人ID,来源",1,1/3,
