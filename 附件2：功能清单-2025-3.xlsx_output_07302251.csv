﻿一级功能模块,二级功能模块,三级功能模块,功能描述,预估工作量（人天）,功能用户,触发事件,功能过程,子过程描述,数据移动类型,数据组,数据属性,CFP
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM3哈希,nan,5,发起者：用户 接收者：密码服务平台,执行SM3哈希计算,SM3哈希计算,输入SM3哈希计算原始数据,E,SM3哈希输入数据,原始数据内容、数据长度、数据编码格式,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM3哈希,nan,5,发起者：用户 接收者：密码服务平台,执行SM3哈希计算,SM3哈希计算,读取SM3算法配置参数,R,SM3算法配置,哈希块大小、填充方式、输出格式,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM3哈希,nan,5,发起者：用户 接收者：密码服务平台,执行SM3哈希计算,SM3哈希计算,执行SM3哈希计算,R,SM3计算中间状态,初始哈希值、消息分块状态、轮次计数,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM3哈希,nan,5,发起者：用户 接收者：密码服务平台,执行SM3哈希计算,SM3哈希计算,输出SM3哈希结果,X,SM3哈希输出结果,哈希值、计算时间戳、算法标识,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM3哈希,nan,5,发起者：用户 接收者：密码服务平台,执行SM3哈希计算,SM3哈希计算,记录SM3哈希计算日志,W,SM3计算日志,操作用户ID、计算耗时、输入数据摘要,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM3-HMAC,nan,5,发起者：用户 接收者：密码服务平台,生成SM3-HMAC,SM3-HMAC生成,输入用户身份信息和待认证数据,E,用户身份认证请求,用户ID、密钥、待认证数据内容,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM3-HMAC,nan,5,发起者：用户 接收者：密码服务平台,生成SM3-HMAC,SM3-HMAC生成,读取用户安全扩展信息,R,用户安全扩展信息,用户ID、HMAC、最后一次活跃时间,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM3-HMAC,nan,5,发起者：用户 接收者：密码服务平台,生成SM3-HMAC,SM3-HMAC生成,执行SM3-HMAC算法计算,W,HMAC计算结果,HMAC值、计算时间戳、算法类型,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM3-HMAC,nan,5,发起者：用户 接收者：密码服务平台,生成SM3-HMAC,SM3-HMAC生成,更新用户安全扩展信息,W,用户安全扩展更新,用户ID、新HMAC值、更新时间,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM3-HMAC,nan,5,发起者：用户 接收者：密码服务平台,生成SM3-HMAC,SM3-HMAC生成,返回HMAC认证结果,X,HMAC认证响应,HMAC值、认证状态、时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2密钥生成,nan,5,密码发起者：用户 接收者：密码服务平台,用户请求生成SM2密钥对,SM2密钥对生成,接收SM2密钥生成请求,E,密钥生成请求,用户ID、密钥算法类型(SM2)、密钥用途、密钥长度,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2密钥生成,nan,5,密码发起者：用户 接收者：密码服务平台,用户请求生成SM2密钥对,SM2密钥对生成,读取密钥存储配置参数,R,密钥存储配置,密钥存储路径、加密方式、访问权限、租户标识,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2密钥生成,nan,5,密码发起者：用户 接收者：密码服务平台,用户请求生成SM2密钥对,SM2密钥对生成,执行SM2密钥对生成算法,W,SM2密钥对,私钥数据、公钥数据、密钥生成时间、密钥状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2密钥生成,nan,5,密码发起者：用户 接收者：密码服务平台,用户请求生成SM2密钥对,SM2密钥对生成,写入密钥统计信息,W,密钥统计记录,密钥算法类型、密钥状态、租户标识、服务ID,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2密钥生成,nan,5,密码发起者：用户 接收者：密码服务平台,用户请求生成SM2密钥对,SM2密钥对生成,返回密钥生成结果,X,密钥生成响应,密钥ID、生成状态、公钥摘要、生成时间,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2密钥生成,nan,5,密码发起者：用户 接收者：密码服务平台,用户请求生成SM2密钥对,SM2密钥对生成,记录密钥生成操作日志,W,系统操作日志,操作类型、操作用户、操作时间、操作结果,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2签名,nan,5,密码发起者：用户 接收者：密码服务平台,用户发起SM2签名请求,SM2签名处理,输入SM2签名请求信息,E,SM2签名请求,用户ID、签名数据内容、证书ID、签名算法类型,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2签名,nan,5,密码发起者：用户 接收者：密码服务平台,用户发起SM2签名请求,SM2签名处理,读取SM2证书信息,R,SM2证书信息,证书ID、证书类型(SM2/RSA)、签名算法(SM3WithSM2)、证书有效期、证书状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2签名,nan,5,密码发起者：用户 接收者：密码服务平台,用户发起SM2签名请求,SM2签名处理,执行SM2签名运算,W,SM2签名结果,签名值(Signature)、签名时间戳、签名算法标识、证书指纹,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2签名,nan,5,密码发起者：用户 接收者：密码服务平台,用户发起SM2签名请求,SM2签名处理,写入签名操作日志,W,密码操作日志,操作类型(Sign)、操作用户、操作时间、操作结果状态、签名数据哈希,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2签名,nan,5,密码发起者：用户 接收者：密码服务平台,用户发起SM2签名请求,SM2签名处理,返回SM2签名结果,X,SM2签名响应,签名结果状态、签名值Base64编码、证书序列号、签名算法版本,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2验签,nan,5,发起者：用户 接收者：密码服务平台,发起SM2验签请求,SM2验签处理,输入SM2验签请求信息,E,验签请求信息,签名数据、消息摘要、公钥ID、算法版本,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2验签,nan,5,发起者：用户 接收者：密码服务平台,发起SM2验签请求,SM2验签处理,读取SM2公钥信息,R,公钥信息,公钥值、密钥算法、密钥ID、密钥状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2验签,nan,5,发起者：用户 接收者：密码服务平台,发起SM2验签请求,SM2验签处理,执行SM2验签算法,R,验签参数,签名算法参数、消息哈希值、公钥参数,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2验签,nan,5,发起者：用户 接收者：密码服务平台,发起SM2验签请求,SM2验签处理,输出验签结果,X,验签结果,验证状态、错误代码、验签时间,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2验签,nan,5,发起者：用户 接收者：密码服务平台,发起SM2验签请求,SM2验签处理,记录验签操作日志,W,验签日志,操作员ID、验签时间、验证结果、请求参数摘要,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2加密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM2加密请求,客户端软件密码模块SM2加密,输入SM2加密请求数据,E,SM2加密输入数据,明文数据、加密算法参数（密钥ID）、用户ID,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2加密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM2加密请求,客户端软件密码模块SM2加密,读取SM2加密密钥信息,R,SM2加密密钥信息,密钥ID、密钥值、密钥状态（启用/禁用）,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2加密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM2加密请求,客户端软件密码模块SM2加密,执行SM2加密算法运算,W,SM2加密结果,密文数据、加密时间戳、加密算法版本,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2加密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM2加密请求,客户端软件密码模块SM2加密,返回SM2加密结果,X,SM2加密输出结果,密文数据、加密状态（成功/失败）、错误代码,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2加密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM2加密请求,客户端软件密码模块SM2加密,记录SM2加密操作日志,W,SM2加密日志,操作用户、操作时间、加密结果状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2解密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM2解密请求,客户端软件密码模块SM2解密,输入SM2解密请求信息,E,SM2解密请求信息,密文内容、密钥ID、请求时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2解密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM2解密请求,客户端软件密码模块SM2解密,读取SM2密钥信息进行验证,R,SM2密钥信息,密钥ID、公钥参数、私钥参数、密钥有效期,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2解密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM2解密请求,客户端软件密码模块SM2解密,执行SM2解密算法运算,W,SM2解密运算参数,密文长度、密钥类型、解密算法版本,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2解密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM2解密请求,客户端软件密码模块SM2解密,输出SM2解密结果,X,SM2解密结果,明文内容、解密状态码、解密完成时间,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM2解密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM2解密请求,客户端软件密码模块SM2解密,记录SM2解密操作日志,W,SM2解密日志,操作员ID、解密请求时间、解密结果状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4对称密钥生成,nan,5,密码发起者：用户 接收者：密码服务平台,请求生成SM4对称密钥,SM4对称密钥生成,输入SM4密钥生成请求参数,E,SM4密钥生成请求,密钥长度、密钥用途、用户ID,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4对称密钥生成,nan,5,密码发起者：用户 接收者：密码服务平台,请求生成SM4对称密钥,SM4对称密钥生成,验证密钥生成参数合法性,R,系统配置信息,密钥长度限制、用户权限等级、算法支持状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4对称密钥生成,nan,5,密码发起者：用户 接收者：密码服务平台,请求生成SM4对称密钥,SM4对称密钥生成,执行SM4算法生成对称密钥,W,SM4密钥数据,密钥值、生成时间、密钥ID,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4对称密钥生成,nan,5,密码发起者：用户 接收者：密码服务平台,请求生成SM4对称密钥,SM4对称密钥生成,存储生成的SM4密钥到安全存储区,W,密钥存储记录,密钥ID、存储位置、存储时间,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4对称密钥生成,nan,5,密码发起者：用户 接收者：密码服务平台,请求生成SM4对称密钥,SM4对称密钥生成,返回SM4密钥生成结果,X,SM4密钥生成结果,密钥ID、生成状态、生成时间,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-ECB加密,nan,5,发起者：用户 接收者：密码服务平台,用户请求SM4-ECB加密,执行SM4-ECB加密,输入SM4-ECB加密请求信息,E,加密请求信息,数据明文、加密算法标识（SM4-ECB）、密钥ID,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-ECB加密,nan,5,发起者：用户 接收者：密码服务平台,用户请求SM4-ECB加密,执行SM4-ECB加密,读取加密密钥信息,R,密钥信息,密钥ID、密钥值、密钥状态（启用/禁用）,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-ECB加密,nan,5,发起者：用户 接收者：密码服务平台,用户请求SM4-ECB加密,执行SM4-ECB加密,执行SM4-ECB加密运算,W,加密结果,密文数据、加密时间戳、加密算法版本,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-ECB加密,nan,5,发起者：用户 接收者：密码服务平台,用户请求SM4-ECB加密,执行SM4-ECB加密,返回加密结果展示信息,X,加密结果展示信息,密文数据、加密状态（成功/失败）、加密耗时,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-ECB加密,nan,5,发起者：用户 接收者：密码服务平台,用户请求SM4-ECB加密,执行SM4-ECB加密,记录加密操作日志,W,加密日志,操作员ID、加密时间、加密算法、密钥ID、加密结果,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CBC加密,nan,5,发起者：用户 接收者：密码服务平台,执行SM4-CBC加密,执行SM4-CBC加密,输入SM4-CBC加密参数,E,加密输入参数,密钥、初始化向量(IV)、明文数据,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CBC加密,nan,5,发起者：用户 接收者：密码服务平台,执行SM4-CBC加密,执行SM4-CBC加密,读取加密配置信息,R,加密配置信息,算法模式、填充方式、加密强度,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CBC加密,nan,5,发起者：用户 接收者：密码服务平台,执行SM4-CBC加密,执行SM4-CBC加密,执行SM4-CBC加密运算,W,加密中间结果,加密状态、密文块、加密时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CBC加密,nan,5,发起者：用户 接收者：密码服务平台,执行SM4-CBC加密,执行SM4-CBC加密,输出SM4-CBC加密结果,X,加密输出结果,密文数据、加密算法标识、加密结果状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CBC加密,nan,5,发起者：用户 接收者：密码服务平台,执行SM4-CBC加密,执行SM4-CBC加密,记录加密操作日志,W,加密操作日志,操作员ID、加密时间、密文长度、加密结果,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CTR加密,nan,5,密码发起者：用户 接收者：密码服务平台,用户请求SM4-CTR加密,执行SM4-CTR加密,输入SM4加密请求信息,E,SM4加密请求信息,明文数据、密钥标识符、CTR初始向量、加密模式,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CTR加密,nan,5,密码发起者：用户 接收者：密码服务平台,用户请求SM4-CTR加密,执行SM4-CTR加密,读取加密参数配置,R,加密参数配置,CTR初始向量长度、密钥长度、加密算法版本,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CTR加密,nan,5,密码发起者：用户 接收者：密码服务平台,用户请求SM4-CTR加密,执行SM4-CTR加密,执行SM4-CTR加密运算,W,加密结果数据,密文数据、加密时间戳、加密状态码,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CTR加密,nan,5,密码发起者：用户 接收者：密码服务平台,用户请求SM4-CTR加密,执行SM4-CTR加密,返回加密结果展示信息,X,加密结果展示信息,密文数据、加密耗时、加密算法标识,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CTR加密,nan,5,密码发起者：用户 接收者：密码服务平台,用户请求SM4-CTR加密,执行SM4-CTR加密,记录加密操作日志,W,加密操作日志,操作员ID、加密时间、加密数据量、加密结果状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-GCM加密,nan,5,发起者：用户 接收者：密码服务平台,SM4-GCM加密处理,SM4-GCM加密处理,输入SM4-GCM加密请求数据,E,加密请求数据,待加密数据内容、加密算法类型、密钥标识符、数据标识符,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-GCM加密,nan,5,发起者：用户 接收者：密码服务平台,SM4-GCM加密处理,SM4-GCM加密处理,读取加密算法配置参数,R,算法配置信息,SM4-GCM算法模式、分组长度、认证标签长度、密钥长度,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-GCM加密,nan,5,发起者：用户 接收者：密码服务平台,SM4-GCM加密处理,SM4-GCM加密处理,执行SM4-GCM加密运算,W,加密中间结果,加密密文数据、认证标签、初始化向量、加密时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-GCM加密,nan,5,发起者：用户 接收者：密码服务平台,SM4-GCM加密处理,SM4-GCM加密处理,输出SM4-GCM加密结果,X,加密结果数据,加密密文、认证标签、加密算法标识、加密状态码,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-GCM加密,nan,5,发起者：用户 接收者：密码服务平台,SM4-GCM加密处理,SM4-GCM加密处理,记录加密操作日志,W,加密操作日志,操作用户ID、加密数据标识、加密时间、加密结果状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CCM加密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CCM加密请求,SM4-CCM加密处理,输入SM4-CCM加密请求参数,E,SM4-CCM加密请求参数,加密数据内容、密钥标识符、加密模式（CCM）、认证数据长度,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CCM加密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CCM加密请求,SM4-CCM加密处理,读取SM4-CCM加密配置信息,R,加密算法配置信息,算法版本、密钥存储路径、认证标签长度、加密模式参数,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CCM加密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CCM加密请求,SM4-CCM加密处理,执行SM4-CCM加密算法运算,W,加密运算中间结果,加密密文数据、认证标签、加密时间戳、算法状态码,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CCM加密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CCM加密请求,SM4-CCM加密处理,输出SM4-CCM加密结果,X,加密结果返回数据,加密后数据、认证标签、加密算法标识、操作结果状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CCM加密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CCM加密请求,SM4-CCM加密处理,记录SM4-CCM加密操作日志,W,加密操作日志记录,操作用户ID、加密时间、算法参数、加密数据摘要、操作结果,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFB加密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-OFB加密请求,执行SM4-OFB加密操作,输入SM4-OFB加密请求信息,E,SM4-OFB加密请求,明文数据、加密密钥、初始向量(IV),1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFB加密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-OFB加密请求,执行SM4-OFB加密操作,读取SM4算法配置参数,R,SM4算法配置,算法类型、分组模式(OFB)、块大小、填充方式,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFB加密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-OFB加密请求,执行SM4-OFB加密操作,生成SM4-OFB密钥流,W,SM4密钥流,密钥流数据、加密时间戳、加密状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFB加密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-OFB加密请求,执行SM4-OFB加密操作,执行SM4-OFB加密运算,W,SM4加密结果,密文数据、加密算法标识、加密时间,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFB加密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-OFB加密请求,执行SM4-OFB加密操作,输出SM4-OFB加密结果,X,SM4加密响应,密文数据、加密状态、加密时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CFB加密,nan,5,发起者：用户 接收者：密码服务平台,加密请求,SM4-CFB加密处理,输入SM4-CFB加密请求参数,E,加密请求参数,加密算法类型、明文数据、密钥ID、加密模式,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CFB加密,nan,5,发起者：用户 接收者：密码服务平台,加密请求,SM4-CFB加密处理,读取SM4加密密钥信息,R,密钥存储信息,密钥ID、密钥值、密钥有效期、加密算法类型,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CFB加密,nan,5,发起者：用户 接收者：密码服务平台,加密请求,SM4-CFB加密处理,执行SM4-CFB加密算法,W,加密中间结果,加密状态码、加密时间戳、加密算法版本,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CFB加密,nan,5,发起者：用户 接收者：密码服务平台,加密请求,SM4-CFB加密处理,输出SM4-CFB加密结果,X,加密响应数据,密文数据、加密时间戳、加密算法标识,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CFB加密,nan,5,发起者：用户 接收者：密码服务平台,加密请求,SM4-CFB加密处理,记录加密操作日志,W,加密操作日志,操作用户ID、加密时间、加密结果状态、密钥ID,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OCB加密,nan,5,发起者：用户 接收者：密码服务平台,SM4-OCB加密,SM4-OCB加密,输入SM4-OCB加密请求信息,E,SM4-OCB加密请求信息,明文数据、加密密钥、初始向量(IV),1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OCB加密,nan,5,发起者：用户 接收者：密码服务平台,SM4-OCB加密,SM4-OCB加密,读取SM4-OCB加密配置参数,R,加密配置参数,密钥长度、OCB模式参数、加密算法版本,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OCB加密,nan,5,发起者：用户 接收者：密码服务平台,SM4-OCB加密,SM4-OCB加密,执行SM4-OCB加密运算,W,加密中间结果,加密密文、认证标签、运算时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OCB加密,nan,5,发起者：用户 接收者：密码服务平台,SM4-OCB加密,SM4-OCB加密,输出SM4-OCB加密结果,X,加密输出结果,密文数据、认证标签、加密状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OCB加密,nan,5,发起者：用户 接收者：密码服务平台,SM4-OCB加密,SM4-OCB加密,记录加密操作日志,W,加密操作日志,操作用户ID、加密时间、加密结果状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-XTS加密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-XTS加密请求,SM4-XTS加密处理,输入SM4-XTS加密请求信息,E,SM4-XTS加密请求信息,加密数据、密钥ID、加密模式、数据长度,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-XTS加密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-XTS加密请求,SM4-XTS加密处理,读取加密密钥信息,R,加密密钥信息,密钥值、密钥ID、密钥类型、密钥有效期,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-XTS加密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-XTS加密请求,SM4-XTS加密处理,执行SM4-XTS加密算法,R,SM4-XTS算法参数,加密模式参数、分组大小、初始向量,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-XTS加密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-XTS加密请求,SM4-XTS加密处理,输出SM4-XTS加密结果,X,SM4-XTS加密结果,加密数据、加密时间戳、加密状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-XTS加密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-XTS加密请求,SM4-XTS加密处理,记录加密操作日志,W,加密操作日志,操作员ID、加密时间、加密数据量、加密结果,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-BC加密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM4-BC加密请求,客户端软件密码模块SM4-BC加密,输入SM4加密请求信息,E,SM4加密请求信息,明文数据、加密密钥、加密模式（ECB/CBC）、加密算法版本,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-BC加密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM4-BC加密请求,客户端软件密码模块SM4-BC加密,读取加密配置参数,R,加密配置信息,支持算法列表、密钥长度限制、默认加密模式、系统时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-BC加密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM4-BC加密请求,客户端软件密码模块SM4-BC加密,执行SM4-BC加密运算,W,加密结果数据,密文数据、加密时间、加密算法标识、加密状态码,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-BC加密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM4-BC加密请求,客户端软件密码模块SM4-BC加密,返回加密结果展示信息,X,加密结果展示信息,密文数据、加密耗时、加密算法详情、操作结果状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-BC加密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM4-BC加密请求,客户端软件密码模块SM4-BC加密,记录加密操作日志,W,加密操作日志,操作员ID、加密时间、加密算法类型、加密结果状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFBNLF加密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-OFBNLF加密请求,SM4-OFBNLF加密处理,输入SM4-OFBNLF加密请求数据,E,SM4加密请求信息,明文数据、密钥ID、加密模式参数、初始化向量,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFBNLF加密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-OFBNLF加密请求,SM4-OFBNLF加密处理,验证加密参数有效性,R,加密参数验证信息,密钥有效性状态、模式支持状态、初始化向量合法性,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFBNLF加密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-OFBNLF加密请求,SM4-OFBNLF加密处理,执行SM4-OFBNLF加密算法,W,SM4加密结果,密文数据、加密状态码、加密时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFBNLF加密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-OFBNLF加密请求,SM4-OFBNLF加密处理,返回加密结果至客户端,X,加密结果展示信息,密文数据、加密算法标识、加密耗时,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFBNLF加密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-OFBNLF加密请求,SM4-OFBNLF加密处理,记录加密操作日志,W,加密操作日志,操作用户ID、加密算法类型、加密结果状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CMAC生成,nan,5,发起者：用户 接收者：密码服务平台,用户请求生成SM4-CMAC,SM4-CMAC生成,输入SM4-CMAC生成请求参数,E,SM4-CMAC生成请求,密钥标识符、消息数据、算法参数、时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CMAC生成,nan,5,发起者：用户 接收者：密码服务平台,用户请求生成SM4-CMAC,SM4-CMAC生成,读取SM4加密算法配置信息,R,SM4算法配置,加密模式、分组大小、填充方式、算法版本,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CMAC生成,nan,5,发起者：用户 接收者：密码服务平台,用户请求生成SM4-CMAC,SM4-CMAC生成,执行SM4-CMAC算法计算,W,SM4-CMAC计算结果,CMAC值、计算状态、错误代码、算法耗时,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CMAC生成,nan,5,发起者：用户 接收者：密码服务平台,用户请求生成SM4-CMAC,SM4-CMAC生成,输出SM4-CMAC生成结果,X,SM4-CMAC响应数据,CMAC值、请求ID、生成时间、验证状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CMAC生成,nan,5,发起者：用户 接收者：密码服务平台,用户请求生成SM4-CMAC,SM4-CMAC生成,记录SM4-CMAC生成操作日志,W,密码操作日志,操作类型、用户标识、操作时间、结果状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-ECB解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-ECB解密请求,执行SM4-ECB解密操作,输入SM4-ECB解密请求信息,E,SM4-ECB解密请求,密文数据、密钥标识符、算法参数,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-ECB解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-ECB解密请求,执行SM4-ECB解密操作,读取加密密钥信息,R,SM4加密密钥,密钥ID、密钥值、密钥状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-ECB解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-ECB解密请求,执行SM4-ECB解密操作,执行SM4-ECB解密算法,W,SM4解密结果,明文数据、解密时间戳、解密状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-ECB解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-ECB解密请求,执行SM4-ECB解密操作,验证解密结果完整性,R,解密校验规则,校验算法、校验字段、校验阈值,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-ECB解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-ECB解密请求,执行SM4-ECB解密操作,返回SM4-ECB解密响应,X,SM4解密响应,明文数据、操作结果、响应时间,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CBC解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-CBC解密请求,SM4-CBC解密,输入SM4-CBC解密请求参数,E,SM4-CBC解密请求参数,密文数据、密钥标识符、IV向量、算法模式,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CBC解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-CBC解密请求,SM4-CBC解密,验证解密参数有效性,R,SM4-CBC解密验证规则,密钥长度校验规则、IV长度校验规则、算法模式合法性,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CBC解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-CBC解密请求,SM4-CBC解密,读取SM4密钥信息,R,SM4密钥信息,密钥值、密钥ID、密钥有效期、密钥使用状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CBC解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-CBC解密请求,SM4-CBC解密,输出SM4-CBC解密结果,X,SM4-CBC解密结果,明文数据、解密状态码、解密时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CBC解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-CBC解密请求,SM4-CBC解密,记录SM4-CBC解密日志,W,SM4-CBC解密日志,操作员ID、解密时间、密文哈希值、解密结果状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CTR解密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM4-CTR解密请求,SM4-CTR解密,输入SM4-CTR解密请求参数,E,SM4-CTR解密请求,加密数据、密钥ID、CTR初始向量,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CTR解密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM4-CTR解密请求,SM4-CTR解密,验证密钥有效性及CTR参数,R,密钥验证信息,密钥ID、密钥状态、CTR值范围、密钥算法类型,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CTR解密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM4-CTR解密请求,SM4-CTR解密,执行SM4-CTR解密算法处理,R,加密数据块,加密数据分块、密钥材料、CTR计数器值,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CTR解密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM4-CTR解密请求,SM4-CTR解密,输出SM4-CTR解密结果,X,解密结果,明文数据、解密状态、操作时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CTR解密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM4-CTR解密请求,SM4-CTR解密,记录SM4-CTR解密操作日志,W,密码操作日志,操作员ID、解密时间、密钥ID、操作结果,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-GCM解密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM4-GCM解密请求,SM4-GCM解密处理,输入SM4-GCM解密请求数据,E,SM4-GCM解密请求数据,密文数据、密钥标识符、初始化向量(IV)、算法版本,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-GCM解密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM4-GCM解密请求,SM4-GCM解密处理,读取密钥存储信息并验证解密参数,R,密钥验证信息,密钥状态、密钥有效期、IV有效性、算法兼容性,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-GCM解密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM4-GCM解密请求,SM4-GCM解密处理,执行SM4-GCM解密算法运算,W,解密中间结果,解密密钥缓存、解密状态码、解密时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-GCM解密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM4-GCM解密请求,SM4-GCM解密处理,输出SM4-GCM解密结果,X,SM4-GCM解密结果,明文数据、解密状态、解密时间、校验结果,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-GCM解密,nan,5,发起者：用户 接收者：密码服务平台,用户发起SM4-GCM解密请求,SM4-GCM解密处理,记录SM4-GCM解密操作日志,W,解密操作日志,操作时间、用户标识、解密结果、密钥ID、请求IP,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CCM解密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CCM解密请求,SM4-CCM解密,输入SM4-CCM解密请求参数,E,SM4-CCM解密请求,加密数据、密钥、初始化向量(IV),1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CCM解密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CCM解密请求,SM4-CCM解密,验证密钥与初始化向量有效性,R,密钥验证信息,密钥长度、IV长度、算法匹配标识,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CCM解密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CCM解密请求,SM4-CCM解密,读取加密数据存储内容,R,加密数据实体,加密数据长度、加密时间戳、数据校验码,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CCM解密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CCM解密请求,SM4-CCM解密,执行SM4-CCM解密算法,W,解密中间结果,解密状态码、解密耗时、解密数据缓存,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CCM解密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CCM解密请求,SM4-CCM解密,输出SM4-CCM解密结果,X,解密响应数据,明文数据、解密时间戳、操作结果标识,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFB解密,nan,5,发起者：用户 接收者：密码服务平台,用户请求SM4-OFB解密,SM4-OFB解密,输入SM4-OFB解密请求参数,E,SM4-OFB解密请求,密文数据、密钥ID、初始向量(IV)、算法模式参数,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFB解密,nan,5,发起者：用户 接收者：密码服务平台,用户请求SM4-OFB解密,SM4-OFB解密,读取加密密钥存储信息,R,密钥存储信息,密钥ID、密钥值、密钥有效期、密钥算法类型,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFB解密,nan,5,发起者：用户 接收者：密码服务平台,用户请求SM4-OFB解密,SM4-OFB解密,执行SM4-OFB解密算法运算,W,SM4-OFB解密结果,明文数据、解密状态码、解密时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFB解密,nan,5,发起者：用户 接收者：密码服务平台,用户请求SM4-OFB解密,SM4-OFB解密,输出SM4-OFB解密结果,X,SM4-OFB解密响应,明文数据、解密状态码、解密时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFB解密,nan,5,发起者：用户 接收者：密码服务平台,用户请求SM4-OFB解密,SM4-OFB解密,记录SM4-OFB解密操作日志,W,密码操作日志,操作类型、操作时间、用户ID、解密状态码,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CFB解密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CFB解密请求,客户端软件密码模块SM4-CFB解密,输入SM4-CFB解密请求参数,E,SM4-CFB解密请求信息,密文数据、密钥标识符、IV向量、算法模式,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CFB解密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CFB解密请求,客户端软件密码模块SM4-CFB解密,读取SM4加密密钥信息,R,SM4密钥存储信息,密钥值、密钥类型、密钥有效期、密钥状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CFB解密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CFB解密请求,客户端软件密码模块SM4-CFB解密,执行SM4-CFB解密算法,R,SM4-CFB算法参数,块大小、填充方式、加密模式、初始向量,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CFB解密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CFB解密请求,客户端软件密码模块SM4-CFB解密,输出SM4-CFB解密结果,X,SM4-CFB解密明文,明文数据、解密时间戳、操作状态码,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CFB解密,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CFB解密请求,客户端软件密码模块SM4-CFB解密,记录SM4-CFB解密操作日志,W,密码操作日志,操作类型、操作时间、操作用户、解密结果,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OCB解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-OCB解密请求,SM4-OCB解密处理,输入SM4-OCB解密请求数据,E,SM4-OCB解密请求,密文数据、密钥标识符、算法参数、请求时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OCB解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-OCB解密请求,SM4-OCB解密处理,读取SM4-OCB解密密钥,R,SM4-OCB密钥信息,密钥ID、密钥值、密钥状态、密钥创建时间,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OCB解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-OCB解密请求,SM4-OCB解密处理,执行SM4-OCB解密算法,W,SM4-OCB解密中间状态,解密阶段标识、算法状态码、临时密钥缓存,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OCB解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-OCB解密请求,SM4-OCB解密处理,输出SM4-OCB解密结果,X,SM4-OCB解密响应,明文数据、解密状态码、处理时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OCB解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-OCB解密请求,SM4-OCB解密处理,记录SM4-OCB解密操作日志,W,SM4-OCB解密日志,操作用户ID、解密请求ID、解密结果状态、操作时间,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-XTS解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-XTS解密请求,SM4-XTS解密处理,输入SM4-XTS解密请求信息,E,SM4-XTS解密请求信息,加密数据、密钥标识符、初始化向量(IV)、算法参数,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-XTS解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-XTS解密请求,SM4-XTS解密处理,读取SM4-XTS算法配置参数,R,SM4-XTS算法配置,块大小、加密模式、填充方式、算法版本,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-XTS解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-XTS解密请求,SM4-XTS解密处理,执行SM4-XTS解密运算,W,SM4-XTS解密中间结果,解密状态码、解密数据块、校验结果,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-XTS解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-XTS解密请求,SM4-XTS解密处理,输出SM4-XTS解密结果,X,SM4-XTS解密输出数据,明文数据、解密时间戳、操作状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-XTS解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-XTS解密请求,SM4-XTS解密处理,记录SM4-XTS解密操作日志,W,SM4-XTS解密日志,操作用户ID、解密时间、密钥标识符、操作结果,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-BC解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-BC解密请求,SM4-BC解密处理,输入SM4-BC解密请求,E,SM4-BC解密请求,加密数据、密钥ID、解密模式参数,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-BC解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-BC解密请求,SM4-BC解密处理,读取SM4密钥信息,R,SM4密钥信息,密钥ID、密钥值、密钥有效期、密钥状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-BC解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-BC解密请求,SM4-BC解密处理,执行SM4-BC解密算法,W,SM4解密中间结果,解密数据块、解密状态码、解密时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-BC解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-BC解密请求,SM4-BC解密处理,输出SM4-BC解密结果,X,SM4解密输出数据,明文数据、解密结果状态、解密耗时,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-BC解密,nan,5,发起者：用户 接收者：密码服务平台,发起SM4-BC解密请求,SM4-BC解密处理,记录SM4-BC解密日志,W,SM4解密操作日志,操作用户ID、解密时间、解密数据量、操作结果,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFBNLF解密,nan,5,发起者：用户 接收者：密码服务平台,用户请求SM4-OFBNLF解密,执行SM4-OFBNLF解密,输入SM4-OFBNLF解密请求信息,E,解密请求信息,加密数据、密钥标识符、算法参数、请求时间,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFBNLF解密,nan,5,发起者：用户 接收者：密码服务平台,用户请求SM4-OFBNLF解密,执行SM4-OFBNLF解密,读取SM4-OFBNLF算法配置参数,R,算法配置参数,算法模式、分组大小、填充方式、协议版本,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFBNLF解密,nan,5,发起者：用户 接收者：密码服务平台,用户请求SM4-OFBNLF解密,执行SM4-OFBNLF解密,验证解密密钥有效性,R,密钥信息,密钥ID、密钥值、密钥状态、密钥有效期,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFBNLF解密,nan,5,发起者：用户 接收者：密码服务平台,用户请求SM4-OFBNLF解密,执行SM4-OFBNLF解密,执行SM4-OFBNLF解密运算,W,解密结果数据,明文数据、解密时间、操作员ID、解密状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-OFBNLF解密,nan,5,发起者：用户 接收者：密码服务平台,用户请求SM4-OFBNLF解密,执行SM4-OFBNLF解密,返回SM4-OFBNLF解密结果,X,解密结果展示信息,明文数据、解密耗时、错误代码、操作记录,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CMAC验证,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CMAC验证请求,SM4-CMAC验证,输入SM4-CMAC验证请求信息,E,SM4-CMAC验证请求信息,验证数据、密钥ID、时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CMAC验证,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CMAC验证请求,SM4-CMAC验证,读取SM4-CMAC密钥信息,R,SM4-CMAC密钥信息,密钥ID、密钥值、密钥状态,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CMAC验证,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CMAC验证请求,SM4-CMAC验证,执行SM4-CMAC算法验证,W,SM4-CMAC验证结果,验证状态、错误代码、时间戳,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CMAC验证,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CMAC验证请求,SM4-CMAC验证,输出SM4-CMAC验证结果,X,SM4-CMAC验证结果展示,验证状态、错误描述、操作时间,1
客户端软件密码模块,部分签名和合成,客户端软件密码模块SM4-CMAC验证,nan,5,密码发起者：用户 接收者：密码服务平台,发起SM4-CMAC验证请求,SM4-CMAC验证,记录SM4-CMAC验证日志,W,SM4-CMAC验证日志,操作员ID、验证结果、日志时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举设备,nan,4,发起者：用户 接收者：密码服务平台,设备枚举请求,设备枚举查询,输入设备枚举查询条件,E,设备枚举查询条件,查询参数、用户ID、设备类型过滤器,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举设备,nan,4,发起者：用户 接收者：密码服务平台,设备枚举请求,设备枚举查询,读取客户端设备枚举信息,R,设备枚举信息,设备ID、设备名称、设备类型、设备状态,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举设备,nan,4,发起者：用户 接收者：密码服务平台,设备枚举请求,设备枚举查询,返回设备枚举结果列表,X,设备枚举结果,设备列表、枚举时间、查询状态,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举设备,nan,4,发起者：用户 接收者：密码服务平台,设备枚举请求,设备枚举查询,记录设备枚举操作日志,W,设备枚举日志,操作员ID、操作时间、枚举结果状态,1
客户端软件密码模块,PIN管理,客户端软件密码模块连接设备,nan,4,发起者：用户 接收者：密码服务平台,用户发起设备连接请求,建立设备连接,输入设备连接参数,E,设备连接请求参数,设备ID、设备类型、IP地址、端口号,1
客户端软件密码模块,PIN管理,客户端软件密码模块连接设备,nan,4,发起者：用户 接收者：密码服务平台,用户发起设备连接请求,建立设备连接,读取设备类型配置信息,R,设备类型配置,设备类型ID、设备类型名称、默认标志、监控类型,1
客户端软件密码模块,PIN管理,客户端软件密码模块连接设备,nan,4,发起者：用户 接收者：密码服务平台,用户发起设备连接请求,建立设备连接,验证设备连接合法性,R,设备权限验证信息,设备ID、用户权限、连接状态、认证令牌,1
客户端软件密码模块,PIN管理,客户端软件密码模块连接设备,nan,4,发起者：用户 接收者：密码服务平台,用户发起设备连接请求,建立设备连接,建立设备通信通道,W,设备连接记录,设备ID、连接时间、IP地址、连接状态,1
客户端软件密码模块,PIN管理,客户端软件密码模块连接设备,nan,4,发起者：用户 接收者：密码服务平台,用户发起设备连接请求,建立设备连接,返回设备连接结果,X,设备连接响应,设备ID、连接状态、错误代码、会话ID,1
客户端软件密码模块,PIN管理,客户端软件密码模块断开设备,nan,4,发起者：用户 接收者：密码服务平台,用户请求断开设备,客户端软件密码模块断开设备,输入断开设备请求信息,E,断开设备请求信息,设备ID、操作员ID、断开原因,1
客户端软件密码模块,PIN管理,客户端软件密码模块断开设备,nan,4,发起者：用户 接收者：密码服务平台,用户请求断开设备,客户端软件密码模块断开设备,读取设备当前状态信息,R,设备状态信息,设备ID、当前状态、设备类型,1
客户端软件密码模块,PIN管理,客户端软件密码模块断开设备,nan,4,发起者：用户 接收者：密码服务平台,用户请求断开设备,客户端软件密码模块断开设备,更新设备状态为断开,W,设备状态更新信息,设备ID、新状态、更新时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块断开设备,nan,4,发起者：用户 接收者：密码服务平台,用户请求断开设备,客户端软件密码模块断开设备,返回断开结果并记录日志,X,断开结果信息,设备ID、操作结果、操作时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块获取设备信息,nan,4,发起者：用户 接收者：密码服务平台,用户请求获取设备信息,获取设备信息,输入设备信息查询请求,E,设备信息查询请求,设备ID、用户ID、查询时间戳,1
客户端软件密码模块,PIN管理,客户端软件密码模块获取设备信息,nan,4,发起者：用户 接收者：密码服务平台,用户请求获取设备信息,获取设备信息,读取设备类型配置信息,R,设备类型配置,设备类型ID、设备类型名称、默认标志位,1
客户端软件密码模块,PIN管理,客户端软件密码模块获取设备信息,nan,4,发起者：用户 接收者：密码服务平台,用户请求获取设备信息,获取设备信息,读取设备基础信息与网络信息,R,设备详情信息,设备名称、所属主机、所属租户、IP地址、网络状态,1
客户端软件密码模块,PIN管理,客户端软件密码模块获取设备信息,nan,4,发起者：用户 接收者：密码服务平台,用户请求获取设备信息,获取设备信息,返回设备信息响应数据,X,设备信息响应,设备名称、设备类型、所属租户、IP地址、查询结果状态,1
客户端软件密码模块,PIN管理,客户端软件密码模块设备认证,nan,4,密码发起者：用户 接收者：密码服务平台,设备认证请求,设备认证处理,输入设备认证请求信息,E,设备认证请求,设备ID、认证方式、设备类型、认证时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块设备认证,nan,4,密码发起者：用户 接收者：密码服务平台,设备认证请求,设备认证处理,读取设备类型配置信息,R,设备类型信息,设备类型ID、设备类型名称、默认标志,1
客户端软件密码模块,PIN管理,客户端软件密码模块设备认证,nan,4,密码发起者：用户 接收者：密码服务平台,设备认证请求,设备认证处理,验证SNMP认证方式有效性,R,SNMP认证方式,认证方式ID、认证方式名称、同步状态,1
客户端软件密码模块,PIN管理,客户端软件密码模块设备认证,nan,4,密码发起者：用户 接收者：密码服务平台,设备认证请求,设备认证处理,输出设备认证结果,X,设备认证响应,认证状态、认证方式、设备类型、认证时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块修改PIN,nan,4,发起者：用户 接收者：密码服务平台,修改PIN,修改PIN,输入PIN修改请求信息,E,PIN修改请求,旧PIN、新PIN、确认新PIN、用户ID,1
客户端软件密码模块,PIN管理,客户端软件密码模块修改PIN,nan,4,发起者：用户 接收者：密码服务平台,修改PIN,修改PIN,验证旧PIN有效性,R,用户当前PIN信息,用户ID、当前PIN、状态、创建时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块修改PIN,nan,4,发起者：用户 接收者：密码服务平台,修改PIN,修改PIN,保存新PIN到密码模块,W,PIN修改记录,用户ID、新PIN、修改时间、操作员ID,1
客户端软件密码模块,PIN管理,客户端软件密码模块修改PIN,nan,4,发起者：用户 接收者：密码服务平台,修改PIN,修改PIN,返回PIN修改结果,X,PIN修改结果,用户ID、修改状态、错误信息,1
客户端软件密码模块,PIN管理,客户端软件密码模块校验PIN,nan,4,密码发起者：用户 接收者：密码服务平台,用户输入PIN进行校验,客户端软件密码模块校验PIN,输入PIN校验请求信息,E,PIN输入信息,用户ID、PIN码、请求时间戳,1
客户端软件密码模块,PIN管理,客户端软件密码模块校验PIN,nan,4,密码发起者：用户 接收者：密码服务平台,用户输入PIN进行校验,客户端软件密码模块校验PIN,读取存储的PIN信息,R,存储的PIN信息,用户ID、加密后的PIN码、加密算法类型,1
客户端软件密码模块,PIN管理,客户端软件密码模块校验PIN,nan,4,密码发起者：用户 接收者：密码服务平台,用户输入PIN进行校验,客户端软件密码模块校验PIN,返回PIN校验结果,X,PIN校验结果,校验状态、错误代码、响应时间戳,1
客户端软件密码模块,PIN管理,客户端软件密码模块校验PIN,nan,4,密码发起者：用户 接收者：密码服务平台,用户输入PIN进行校验,客户端软件密码模块校验PIN,记录PIN校验日志,W,PIN校验日志,用户ID、校验时间、校验结果、操作员ID,1
客户端软件密码模块,PIN管理,客户端软件密码模块解锁PIN,nan,4,发起者：用户 接收者：密码服务平台,用户提交解锁PIN请求,处理解锁PIN请求,输入解锁PIN信息,E,解锁PIN输入信息,用户ID、输入的PIN码、请求时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块解锁PIN,nan,4,发起者：用户 接收者：密码服务平台,用户提交解锁PIN请求,处理解锁PIN请求,读取用户存储的PIN信息,R,用户PIN信息,用户ID、加密存储的PIN、状态、最后修改时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块解锁PIN,nan,4,发起者：用户 接收者：密码服务平台,用户提交解锁PIN请求,处理解锁PIN请求,更新用户解锁状态,W,用户解锁状态,用户ID、解锁时间、状态、操作员ID,1
客户端软件密码模块,PIN管理,客户端软件密码模块解锁PIN,nan,4,发起者：用户 接收者：密码服务平台,用户提交解锁PIN请求,处理解锁PIN请求,返回解锁结果,X,解锁结果信息,结果状态、解锁时间、用户ID、操作结果描述,1
客户端软件密码模块,PIN管理,客户端软件密码模块重置PIN,nan,4,发起者：用户 接收者：密码服务平台,用户请求重置PIN,PIN重置处理,输入旧PIN验证信息,E,旧PIN验证请求,用户ID、旧PIN、验证时间戳,1
客户端软件密码模块,PIN管理,客户端软件密码模块重置PIN,nan,4,发起者：用户 接收者：密码服务平台,用户请求重置PIN,PIN重置处理,读取用户存储的PIN信息,R,用户PIN存储信息,用户ID、加密旧PIN、PIN状态、创建时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块重置PIN,nan,4,发起者：用户 接收者：密码服务平台,用户请求重置PIN,PIN重置处理,验证旧PIN有效性,R,PIN验证规则,PIN长度限制、加密算法、重试次数限制,1
客户端软件密码模块,PIN管理,客户端软件密码模块重置PIN,nan,4,发起者：用户 接收者：密码服务平台,用户请求重置PIN,PIN重置处理,输入并保存新PIN,E,新PIN输入信息,用户ID、新PIN、确认PIN、操作时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块重置PIN,nan,4,发起者：用户 接收者：密码服务平台,用户请求重置PIN,PIN重置处理,加密存储新PIN,W,用户PIN更新记录,用户ID、加密新PIN、更新时间、操作员ID,1
客户端软件密码模块,PIN管理,客户端软件密码模块重置PIN,nan,4,发起者：用户 接收者：密码服务平台,用户请求重置PIN,PIN重置处理,返回PIN重置结果,X,PIN重置反馈,操作结果代码、重置时间、用户ID,1
客户端软件密码模块,PIN管理,客户端软件密码模块重置PIN,nan,4,发起者：用户 接收者：密码服务平台,用户请求重置PIN,PIN重置处理,记录PIN重置操作日志,W,密码操作日志,操作类型、用户ID、操作时间、IP地址、结果状态,1
客户端软件密码模块,PIN管理,客户端软件密码模块创建应用,nan,4,发起者：用户 接收者：密码服务平台,创建应用请求,应用创建,输入应用基础信息,E,应用创建信息,应用ID、应用名称、应用类型、所属产品ID,1
客户端软件密码模块,PIN管理,客户端软件密码模块创建应用,nan,4,发起者：用户 接收者：密码服务平台,创建应用请求,应用创建,校验应用唯一性及关联产品,R,应用校验信息,应用ID、产品ID、产品类型、存储路径,1
客户端软件密码模块,PIN管理,客户端软件密码模块创建应用,nan,4,发起者：用户 接收者：密码服务平台,创建应用请求,应用创建,持久化应用配置信息,W,应用配置数据,应用ID、证书名称、证书内容、创建时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块创建应用,nan,4,发起者：用户 接收者：密码服务平台,创建应用请求,应用创建,返回应用创建结果,X,应用创建响应,应用ID、创建状态、错误代码、操作时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举应用,nan,4,发起者：用户 接收者：密码服务平台,枚举值新增,枚举值新增,输入枚举值新增信息,E,枚举值新增信息,枚举分组标识、枚举值名称、枚举值代码,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举应用,nan,4,发起者：用户 接收者：密码服务平台,枚举值新增,枚举值新增,校验枚举分组标识有效性,R,枚举分组信息,枚举分组标识、分组名称、分组状态,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举应用,nan,4,发起者：用户 接收者：密码服务平台,枚举值新增,枚举值新增,保存枚举值到数据库,W,枚举值存储信息,枚举分组标识、枚举值名称、枚举值代码、创建时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举应用,nan,4,发起者：用户 接收者：密码服务平台,枚举值新增,枚举值新增,返回新增结果,X,枚举值新增结果,枚举值ID、枚举值名称、操作结果,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举应用,nan,4,发起者：用户 接收者：密码服务平台,枚举值修改,枚举值修改,输入枚举值修改信息,E,枚举值修改条件,枚举值ID、修改后的枚举值名称、枚举值代码,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举应用,nan,4,发起者：用户 接收者：密码服务平台,枚举值修改,枚举值修改,读取原始枚举值信息,R,原始枚举值信息,枚举值ID、原枚举值名称、原枚举值代码,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举应用,nan,4,发起者：用户 接收者：密码服务平台,枚举值修改,枚举值修改,更新枚举值到数据库,W,枚举值更新信息,枚举值ID、新枚举值名称、新枚举值代码、修改时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举应用,nan,4,发起者：用户 接收者：密码服务平台,枚举值修改,枚举值修改,返回修改结果,X,枚举值修改结果,枚举值ID、新枚举值名称、操作结果,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举应用,nan,4,发起者：用户 接收者：密码服务平台,枚举值删除,枚举值删除,输入枚举值删除请求,E,枚举值删除请求,枚举值ID、删除原因,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举应用,nan,4,发起者：用户 接收者：密码服务平台,枚举值删除,枚举值删除,校验枚举值关联状态,R,枚举值关联信息,枚举值ID、关联模块数量、关联状态,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举应用,nan,4,发起者：用户 接收者：密码服务平台,枚举值删除,枚举值删除,执行枚举值删除操作,W,枚举值删除记录,枚举值ID、删除时间、操作员ID,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举应用,nan,4,发起者：用户 接收者：密码服务平台,枚举值删除,枚举值删除,返回删除结果,X,枚举值删除结果,枚举值ID、删除状态、操作结果,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举应用,nan,4,发起者：用户 接收者：密码服务平台,枚举值查询,枚举值查询,输入枚举值查询条件,E,枚举值查询请求,枚举分组标识、查询关键字,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举应用,nan,4,发起者：用户 接收者：密码服务平台,枚举值查询,枚举值查询,读取枚举值列表,R,枚举值列表,枚举值ID、枚举值名称、枚举值代码、分组标识,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举应用,nan,4,发起者：用户 接收者：密码服务平台,枚举值查询,枚举值查询,返回查询结果,X,枚举值查询结果,枚举值ID、枚举值名称、枚举值代码、分组标识,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举应用,nan,4,发起者：用户 接收者：密码服务平台,枚举值查询,枚举值查询,记录查询日志,W,枚举值查询日志,操作员ID、查询时间、查询条件,1
客户端软件密码模块,PIN管理,客户端软件密码模块删除应用,nan,4,发起者：用户 接收者：密码服务平台,删除应用请求,客户端软件密码模块删除应用,输入应用删除请求信息,E,应用删除请求,应用ID、用户ID、删除原因、删除时间戳,1
客户端软件密码模块,PIN管理,客户端软件密码模块删除应用,nan,4,发起者：用户 接收者：密码服务平台,删除应用请求,客户端软件密码模块删除应用,验证用户删除权限,R,用户权限信息,用户ID、权限级别、操作类型、关联应用ID,1
客户端软件密码模块,PIN管理,客户端软件密码模块删除应用,nan,4,发起者：用户 接收者：密码服务平台,删除应用请求,客户端软件密码模块删除应用,执行应用删除操作,W,应用删除信息,应用ID、删除状态、删除时间、操作员ID,1
客户端软件密码模块,PIN管理,客户端软件密码模块删除应用,nan,4,发起者：用户 接收者：密码服务平台,删除应用请求,客户端软件密码模块删除应用,返回删除操作结果,X,删除结果信息,操作结果、应用ID、删除时间、错误代码,1
客户端软件密码模块,PIN管理,客户端软件密码模块删除应用,nan,4,发起者：用户 接收者：密码服务平台,删除应用请求,客户端软件密码模块删除应用,记录应用删除日志,W,应用删除日志,操作员ID、操作时间、操作结果、应用ID、删除原因,1
客户端软件密码模块,PIN管理,客户端软件密码模块打开应用,nan,4,发起者：用户 接收者：密码服务平台,应用启动请求,应用启动认证处理,输入应用登录认证信息,E,应用登录认证信息,用户名、密码、应用ID,1
客户端软件密码模块,PIN管理,客户端软件密码模块打开应用,nan,4,发起者：用户 接收者：密码服务平台,应用启动请求,应用启动认证处理,读取应用登录证书信息,R,APP_AUTH_CERT,证书名称、证书内容、应用ID,1
客户端软件密码模块,PIN管理,客户端软件密码模块打开应用,nan,4,发起者：用户 接收者：密码服务平台,应用启动请求,应用启动认证处理,验证应用登录口令有效性,R,APP_AUTH_CODE,口令、是否作废、应用ID,1
客户端软件密码模块,PIN管理,客户端软件密码模块打开应用,nan,4,发起者：用户 接收者：密码服务平台,应用启动请求,应用启动认证处理,记录应用登录日志,W,应用登录日志,用户ID、登录时间、登录结果、IP地址,1
客户端软件密码模块,PIN管理,客户端软件密码模块关闭应用,nan,4,发起者：用户 接收者：密码服务平台,用户请求关闭密码模块应用,密码模块应用关闭处理,接收密码模块关闭请求,E,密码模块关闭请求,用户ID、应用标识符、关闭请求时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块关闭应用,nan,4,发起者：用户 接收者：密码服务平台,用户请求关闭密码模块应用,密码模块应用关闭处理,验证用户关闭权限,R,用户权限信息,用户ID、操作权限类型、密码模块状态,1
客户端软件密码模块,PIN管理,客户端软件密码模块关闭应用,nan,4,发起者：用户 接收者：密码服务平台,用户请求关闭密码模块应用,密码模块应用关闭处理,更新密码模块运行状态,W,密码模块状态记录,应用标识符、新状态值、状态更新时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块关闭应用,nan,4,发起者：用户 接收者：密码服务平台,用户请求关闭密码模块应用,密码模块应用关闭处理,返回关闭操作结果,X,密码模块关闭响应,操作结果代码、状态描述、异常信息,1
客户端软件密码模块,PIN管理,客户端软件密码模块创建容器,nan,4,发起者：用户 接收者：密码服务平台,用户请求创建容器,创建容器实例,输入容器创建请求信息,E,容器创建请求,容器ID、镜像名称、容器配置参数,1
客户端软件密码模块,PIN管理,客户端软件密码模块创建容器,nan,4,发起者：用户 接收者：密码服务平台,用户请求创建容器,创建容器实例,读取容器镜像元数据,R,容器镜像信息,镜像ID、镜像版本、镜像存储路径,1
客户端软件密码模块,PIN管理,客户端软件密码模块创建容器,nan,4,发起者：用户 接收者：密码服务平台,用户请求创建容器,创建容器实例,写入容器实例配置信息,W,容器实例配置,容器ID、容器状态、容器创建时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块创建容器,nan,4,发起者：用户 接收者：密码服务平台,用户请求创建容器,创建容器实例,返回容器创建结果,X,容器创建响应,容器ID、容器状态、错误代码,1
客户端软件密码模块,PIN管理,客户端软件密码模块删除容器,nan,4,发起者：用户 接收者：密码服务平台,删除容器请求,容器删除,输入容器删除请求信息,E,容器删除请求,容器ID、操作员ID、删除原因,1
客户端软件密码模块,PIN管理,客户端软件密码模块删除容器,nan,4,发起者：用户 接收者：密码服务平台,删除容器请求,容器删除,验证操作员删除权限,R,操作员权限信息,操作员ID、权限等级、容器ID,1
客户端软件密码模块,PIN管理,客户端软件密码模块删除容器,nan,4,发起者：用户 接收者：密码服务平台,删除容器请求,容器删除,执行容器数据删除操作,W,容器删除结果,容器ID、删除状态、删除时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块删除容器,nan,4,发起者：用户 接收者：密码服务平台,删除容器请求,容器删除,返回容器删除结果,X,容器删除结果展示,容器ID、删除状态、操作员ID,1
客户端软件密码模块,PIN管理,客户端软件密码模块删除容器,nan,4,发起者：用户 接收者：密码服务平台,删除容器请求,容器删除,记录容器删除日志,W,容器删除日志,操作员ID、容器ID、删除时间、删除结果,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举容器,nan,4,发起者：用户 接收者：密码服务平台,枚举容器管理,枚举容器管理,输入枚举容器管理请求,E,枚举容器管理请求,操作类型（查询/编辑）、枚举ID、枚举名称,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举容器,nan,4,发起者：用户 接收者：密码服务平台,枚举容器管理,枚举容器管理,读取枚举容器信息,R,枚举容器信息,枚举ID、枚举名称、枚举值、分组标识、创建时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举容器,nan,4,发起者：用户 接收者：密码服务平台,枚举容器管理,枚举容器管理,执行枚举容器操作,W,枚举容器操作结果,操作类型、枚举ID、更新后枚举值、操作状态,1
客户端软件密码模块,PIN管理,客户端软件密码模块枚举容器,nan,4,发起者：用户 接收者：密码服务平台,枚举容器管理,枚举容器管理,返回枚举容器操作结果,X,枚举容器响应数据,操作结果状态、枚举ID、枚举名称、更新时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块打开容器,nan,4,密码发起者：用户 接收者：密码服务平台,打开容器,打开容器,输入容器打开请求,E,容器打开请求,容器ID、用户ID、操作时间戳,1
客户端软件密码模块,PIN管理,客户端软件密码模块打开容器,nan,4,密码发起者：用户 接收者：密码服务平台,打开容器,打开容器,验证用户权限并读取容器配置,R,用户权限信息,用户ID、权限级别、容器访问权限,1
客户端软件密码模块,PIN管理,客户端软件密码模块打开容器,nan,4,密码发起者：用户 接收者：密码服务平台,打开容器,打开容器,校验容器状态并加载加密密钥,R,容器配置信息,容器ID、加密算法类型、密钥存储路径,1
客户端软件密码模块,PIN管理,客户端软件密码模块打开容器,nan,4,密码发起者：用户 接收者：密码服务平台,打开容器,打开容器,返回容器打开结果并记录操作日志,X,容器操作结果,容器ID、打开状态、异常代码,1
客户端软件密码模块,PIN管理,客户端软件密码模块关闭容器,nan,4,发起者：用户 接收者：密码服务平台,请求关闭容器,容器关闭处理,输入容器关闭请求信息,E,容器关闭请求信息,容器ID、用户ID、请求时间、关闭原因,1
客户端软件密码模块,PIN管理,客户端软件密码模块关闭容器,nan,4,发起者：用户 接收者：密码服务平台,请求关闭容器,容器关闭处理,验证用户关闭容器权限,R,用户权限信息,用户ID、操作权限、容器访问权限、有效期,1
客户端软件密码模块,PIN管理,客户端软件密码模块关闭容器,nan,4,发起者：用户 接收者：密码服务平台,请求关闭容器,容器关闭处理,执行容器状态更新操作,W,容器状态更新信息,容器ID、新状态、操作时间、操作类型,1
客户端软件密码模块,PIN管理,客户端软件密码模块关闭容器,nan,4,发起者：用户 接收者：密码服务平台,请求关闭容器,容器关闭处理,返回容器关闭结果,X,容器关闭结果信息,容器ID、操作结果、错误代码、处理时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块获得容器类型,nan,4,发起者：用户 接收者：密码服务平台,请求容器类型信息,获取容器类型信息,输入容器类型查询请求,E,容器类型查询请求,请求ID、用户ID、查询时间戳,1
客户端软件密码模块,PIN管理,客户端软件密码模块获得容器类型,nan,4,发起者：用户 接收者：密码服务平台,请求容器类型信息,获取容器类型信息,读取容器类型配置数据,R,容器类型配置信息,容器类型名称、容器版本、容器描述,1
客户端软件密码模块,PIN管理,客户端软件密码模块获得容器类型,nan,4,发起者：用户 接收者：密码服务平台,请求容器类型信息,获取容器类型信息,返回容器类型响应结果,X,容器类型响应数据,容器类型列表、查询状态、响应时间,1
客户端软件密码模块,PIN管理,客户端软件密码模块获得容器类型,nan,4,发起者：用户 接收者：密码服务平台,请求容器类型信息,获取容器类型信息,记录容器类型查询日志,W,容器类型操作日志,操作员ID、操作时间、查询结果状态,1
客户端软件密码模块,证书存储,客户端软件密码模块导入数字证书,nan,4,发起者：用户 接收者：密码服务平台,用户导入数字证书,数字证书导入,输入数字证书信息,E,数字证书信息,证书名称、证书内容、证书类型,1
客户端软件密码模块,证书存储,客户端软件密码模块导入数字证书,nan,4,发起者：用户 接收者：密码服务平台,用户导入数字证书,数字证书导入,验证证书有效性,R,证书校验信息,证书名称、证书内容、证书类型,1
客户端软件密码模块,证书存储,客户端软件密码模块导入数字证书,nan,4,发起者：用户 接收者：密码服务平台,用户导入数字证书,数字证书导入,写入证书到存储,W,证书存储信息,证书名称、证书内容、证书类型、存储时间,1
客户端软件密码模块,证书存储,客户端软件密码模块导入数字证书,nan,4,发起者：用户 接收者：密码服务平台,用户导入数字证书,数字证书导入,返回证书导入结果,X,证书导入结果,证书名称、导入状态、错误信息,1
客户端软件密码模块,证书存储,客户端软件密码模块导出数字证书,nan,4,发起者：用户 接收者：密码服务平台,用户请求导出数字证书,数字证书导出,输入数字证书导出请求,E,数字证书导出请求,用户ID、证书序列号、导出时间,1
客户端软件密码模块,证书存储,客户端软件密码模块导出数字证书,nan,4,发起者：用户 接收者：密码服务平台,用户请求导出数字证书,数字证书导出,读取用户认证证书信息,R,用户认证证书信息,证书内容、证书序列号、用户ID,1
客户端软件密码模块,证书存储,客户端软件密码模块导出数字证书,nan,4,发起者：用户 接收者：密码服务平台,用户请求导出数字证书,数字证书导出,生成并导出数字证书文件,X,导出的数字证书,证书内容、证书序列号、导出时间,1
客户端软件密码模块,证书存储,客户端软件密码模块导出数字证书,nan,4,发起者：用户 接收者：密码服务平台,用户请求导出数字证书,数字证书导出,记录证书导出操作日志,W,证书导出日志,操作员ID、导出时间、证书序列号,1
客户端软件密码模块,密钥生成,客户端软件密码模块生成随机数,nan,4,发起者：用户 接收者：密码服务平台,用户请求生成随机数,生成随机数,输入随机数生成请求参数,E,随机数生成请求,随机数长度、生成类型标识、用户ID,1
客户端软件密码模块,密钥生成,客户端软件密码模块生成随机数,nan,4,发起者：用户 接收者：密码服务平台,用户请求生成随机数,生成随机数,读取生成类型配置信息,R,生成类型配置,生成类型名称、生成类型值、是否对称算法,1
客户端软件密码模块,密钥生成,客户端软件密码模块生成随机数,nan,4,发起者：用户 接收者：密码服务平台,用户请求生成随机数,生成随机数,生成并保存随机数,W,生成的随机数,随机数值、生成时间戳、用户ID,1
客户端软件密码模块,密钥生成,客户端软件密码模块生成随机数,nan,4,发起者：用户 接收者：密码服务平台,用户请求生成随机数,生成随机数,返回随机数生成结果,X,随机数生成结果,随机数值、生成状态、生成时间,1
客户端软件密码模块,密钥生成,客户端软件密码模块生成ECC签名密钥对,nan,4,发起者：用户 接收者：密码服务平台,生成ECC签名密钥对请求,生成ECC签名密钥对,输入ECC密钥对生成请求参数,E,ECC密钥对生成请求,密钥长度、算法类型、用户ID,1
客户端软件密码模块,密钥生成,客户端软件密码模块生成ECC签名密钥对,nan,4,发起者：用户 接收者：密码服务平台,生成ECC签名密钥对请求,生成ECC签名密钥对,校验ECC密钥生成参数有效性,R,ECC密钥生成参数校验信息,密钥长度范围、算法类型白名单、用户权限,1
客户端软件密码模块,密钥生成,客户端软件密码模块生成ECC签名密钥对,nan,4,发起者：用户 接收者：密码服务平台,生成ECC签名密钥对请求,生成ECC签名密钥对,执行ECC密钥对生成运算,W,ECC密钥对存储信息,私钥数据、公钥数据、生成时间戳,1
客户端软件密码模块,密钥生成,客户端软件密码模块生成ECC签名密钥对,nan,4,发起者：用户 接收者：密码服务平台,生成ECC签名密钥对请求,生成ECC签名密钥对,返回ECC密钥对生成结果,X,ECC密钥对生成结果,私钥返回值、公钥返回值、生成状态码,1
客户端软件密码模块,密钥生成,客户端软件密码模块生成外部ECC签名密钥对,nan,4,发起者：用户 接收者：密码服务平台,生成外部ECC签名密钥对请求,生成外部ECC签名密钥对,输入密钥生成请求信息,E,密钥生成请求信息,密钥类型(ECC)、密钥长度、用途(签名)、用户ID,1
客户端软件密码模块,密钥生成,客户端软件密码模块生成外部ECC签名密钥对,nan,4,发起者：用户 接收者：密码服务平台,生成外部ECC签名密钥对请求,生成外部ECC签名密钥对,验证用户权限及密钥生成条件,R,用户权限信息,用户ID、权限等级、操作权限(密钥生成)、密钥用途限制,1
客户端软件密码模块,密钥生成,客户端软件密码模块生成外部ECC签名密钥对,nan,4,发起者：用户 接收者：密码服务平台,生成外部ECC签名密钥对请求,生成外部ECC签名密钥对,生成ECC签名密钥对并持久化存储,W,生成的ECC密钥对,公钥数据、私钥数据、生成时间戳、密钥ID,1
客户端软件密码模块,密钥生成,客户端软件密码模块生成外部ECC签名密钥对,nan,4,发起者：用户 接收者：密码服务平台,生成外部ECC签名密钥对请求,生成外部ECC签名密钥对,返回密钥生成结果及元数据,X,密钥生成结果,公钥数据、私钥数据、生成状态(成功/失败)、密钥ID,1
客户端软件密码模块,密钥生成,客户端软件密码模块导入ECC加密密钥对,nan,4,发起者：用户 接收者：密码服务平台,用户导入ECC加密密钥对,用户导入ECC加密密钥对,输入ECC加密密钥对信息,E,ECC密钥对输入信息,公钥数据、私钥数据、密钥对ID,1
客户端软件密码模块,密钥生成,客户端软件密码模块导入ECC加密密钥对,nan,4,发起者：用户 接收者：密码服务平台,用户导入ECC加密密钥对,用户导入ECC加密密钥对,验证密钥对格式和有效性,R,密钥验证规则,密钥格式要求、算法类型、密钥长度,1
客户端软件密码模块,密钥生成,客户端软件密码模块导入ECC加密密钥对,nan,4,发起者：用户 接收者：密码服务平台,用户导入ECC加密密钥对,用户导入ECC加密密钥对,将密钥对写入安全存储,W,ECC密钥对存储信息,加密公钥、加密私钥、密钥对ID、存储时间,1
客户端软件密码模块,密钥生成,客户端软件密码模块导入ECC加密密钥对,nan,4,发起者：用户 接收者：密码服务平台,用户导入ECC加密密钥对,用户导入ECC加密密钥对,返回密钥导入结果,X,密钥导入结果,密钥对ID、导入状态、错误代码,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC签名,nan,4,发起者：用户 接收者：密码服务平台,用户发起ECC签名请求,执行ECC签名操作,输入ECC签名请求信息,E,ECC签名请求信息,用户ID、待签名数据、时间戳、签名算法版本,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC签名,nan,4,发起者：用户 接收者：密码服务平台,用户发起ECC签名请求,执行ECC签名操作,读取用户权限信息进行签名权限校验,R,用户权限信息,用户ID、权限状态、签名有效期、算法白名单,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC签名,nan,4,发起者：用户 接收者：密码服务平台,用户发起ECC签名请求,执行ECC签名操作,执行ECC签名算法并生成签名值,W,ECC签名结果,签名值、签名时间、用户ID、算法标识,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC签名,nan,4,发起者：用户 接收者：密码服务平台,用户发起ECC签名请求,执行ECC签名操作,返回ECC签名结果给用户,X,ECC签名结果,签名值、签名状态、时间戳、验证说明,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC验签,nan,4,发起者：用户 接收者：密码服务平台,ECC证书验签请求,ECC证书验签,输入ECC证书验签请求信息,E,ECC验签请求信息,证书内容、签名数据、公钥参数、验签算法类型,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC验签,nan,4,发起者：用户 接收者：密码服务平台,ECC证书验签请求,ECC证书验签,读取证书信息并验证有效性,R,证书信息,证书内容、证书有效期、签发者信息、证书状态,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC验签,nan,4,发起者：用户 接收者：密码服务平台,ECC证书验签请求,ECC证书验签,执行ECC验签算法并保存结果,W,验签结果,验签状态、错误代码、验签时间戳、签名验证结果,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC验签,nan,4,发起者：用户 接收者：密码服务平台,ECC证书验签请求,ECC证书验签,返回ECC证书验签结果展示,X,验签结果展示信息,验签状态、详细错误信息、验签时间、证书指纹,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC生成并导出会话密钥,nan,4,发起者：用户 接收者：密码服务平台,生成并导出会话密钥,生成并导出会话密钥,输入会话密钥生成请求,E,会话密钥生成请求,用户ID、密钥长度、生成时间,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC生成并导出会话密钥,nan,4,发起者：用户 接收者：密码服务平台,生成并导出会话密钥,生成并导出会话密钥,生成ECC会话密钥,W,ECC会话密钥,密钥数据、密钥类型、生成时间,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC生成并导出会话密钥,nan,4,发起者：用户 接收者：密码服务平台,生成并导出会话密钥,生成并导出会话密钥,导出会话密钥为字节数据,W,导出密钥数据,密钥数据、密钥类型、导出时间,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC生成并导出会话密钥,nan,4,发起者：用户 接收者：密码服务平台,生成并导出会话密钥,生成并导出会话密钥,返回导出会话密钥结果,X,导出结果,密钥数据、状态、时间戳,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC外来公钥加密,nan,4,发起者：用户 接收者：密码服务平台,发起ECC外来公钥加密请求,执行ECC外来公钥加密,输入ECC外来公钥加密请求信息,E,ECC加密请求信息,待加密数据内容、外来公钥标识符、加密算法参数,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC外来公钥加密,nan,4,发起者：用户 接收者：密码服务平台,发起ECC外来公钥加密请求,执行ECC外来公钥加密,读取外来公钥信息,R,ECC公钥数据,公钥值、公钥格式、公钥长度、公钥算法版本,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC外来公钥加密,nan,4,发起者：用户 接收者：密码服务平台,发起ECC外来公钥加密请求,执行ECC外来公钥加密,执行ECC加密运算并生成密文,W,ECC加密结果,加密后的密文数据、加密时间戳、加密状态标识,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC外来公钥加密,nan,4,发起者：用户 接收者：密码服务平台,发起ECC外来公钥加密请求,执行ECC外来公钥加密,返回ECC加密结果,X,ECC加密响应数据,密文数据、加密算法标识、加密操作结果状态,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC外来私钥解密,nan,4,密码发起者：用户 接收者：密码服务平台,发起ECC外来私钥解密请求,客户端软件密码模块ECC外来私钥解密,输入ECC外来私钥解密请求,E,ECC外来私钥解密请求,私钥密文、加密算法类型、密钥标识符,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC外来私钥解密,nan,4,密码发起者：用户 接收者：密码服务平台,发起ECC外来私钥解密请求,客户端软件密码模块ECC外来私钥解密,读取解密参数信息,R,解密参数信息,解密算法参数、密钥存储位置、解密密钥ID,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC外来私钥解密,nan,4,密码发起者：用户 接收者：密码服务平台,发起ECC外来私钥解密请求,客户端软件密码模块ECC外来私钥解密,执行ECC私钥解密运算,W,解密后的私钥信息,私钥明文、解密时间、解密状态,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC外来私钥解密,nan,4,密码发起者：用户 接收者：密码服务平台,发起ECC外来私钥解密请求,客户端软件密码模块ECC外来私钥解密,输出解密结果展示信息,X,解密结果展示信息,私钥明文、解密状态、错误信息,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC外来私钥签名,nan,4,发起者：用户 接收者：密码服务平台,外来私钥签名请求,ECC外来私钥签名处理,输入外来私钥签名请求,E,外来私钥签名请求,私钥ID、签名数据、用户ID、请求时间,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC外来私钥签名,nan,4,发起者：用户 接收者：密码服务平台,外来私钥签名请求,ECC外来私钥签名处理,读取加密判定密钥信息,R,加密判定密钥信息,私钥ID、密钥类型、发送方区域ID、租户ID,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC外来私钥签名,nan,4,发起者：用户 接收者：密码服务平台,外来私钥签名请求,ECC外来私钥签名处理,执行ECC签名算法运算,W,ECC签名结果,签名数据、签名时间、私钥ID、算法版本,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC外来私钥签名,nan,4,发起者：用户 接收者：密码服务平台,外来私钥签名请求,ECC外来私钥签名处理,输出签名结果及状态,X,签名结果响应,签名数据、操作状态、错误代码、响应时间,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC外来公钥验签,nan,4,发起者：用户 接收者：密码服务平台,ECC外来公钥验签请求,ECC外来公钥验签,输入ECC外来公钥验签请求,E,ECC外来公钥验签请求,公钥数据、签名数据、时间戳、请求ID,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC外来公钥验签,nan,4,发起者：用户 接收者：密码服务平台,ECC外来公钥验签请求,ECC外来公钥验签,读取加密判定密钥信息,R,加密判定密钥信息,密钥类型、发送方区域ID、密钥状态、密钥有效期,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC外来公钥验签,nan,4,发起者：用户 接收者：密码服务平台,ECC外来公钥验签请求,ECC外来公钥验签,执行ECC公钥验签操作,W,验签结果日志,验签结果状态、错误代码、操作时间、请求ID,1
客户端软件密码模块,密钥拆分,客户端软件密码模块ECC外来公钥验签,nan,4,发起者：用户 接收者：密码服务平台,ECC外来公钥验签请求,ECC外来公钥验签,返回ECC公钥验签结果,X,ECC公钥验签结果,验签结果状态、错误信息、验签时间、请求ID,1
客户端软件密码模块,密钥拆分,客户端软件密码模块导出公钥,nan,4,密码发起者：用户 接收者：密码服务平台,用户请求导出公钥,用户导出公钥,输入公钥导出请求,E,公钥导出请求,用户ID、导出时间戳、公钥标识符,1
客户端软件密码模块,密钥拆分,客户端软件密码模块导出公钥,nan,4,密码发起者：用户 接收者：密码服务平台,用户请求导出公钥,用户导出公钥,验证用户权限并读取公钥数据,R,用户权限信息,用户ID、权限等级、操作类型、密钥访问权限,1
客户端软件密码模块,密钥拆分,客户端软件密码模块导出公钥,nan,4,密码发起者：用户 接收者：密码服务平台,用户请求导出公钥,用户导出公钥,生成并存储公钥文件,W,公钥文件,公钥内容、导出时间、加密算法标识、文件校验码,1
客户端软件密码模块,密钥拆分,客户端软件密码模块导出公钥,nan,4,密码发起者：用户 接收者：密码服务平台,用户请求导出公钥,用户导出公钥,返回公钥导出结果,X,导出结果信息,导出状态、文件路径、导出时间、操作员ID,1
客户端软件密码模块,密钥拆分,客户端软件密码模块导入会话密钥,nan,4,发起者：用户 接收者：密码服务平台,导入会话密钥,会话密钥导入,输入会话密钥导入信息,E,会话密钥导入请求,密钥ID、密钥值、加密算法、发送方区域ID,1
客户端软件密码模块,密钥拆分,客户端软件密码模块导入会话密钥,nan,4,发起者：用户 接收者：密码服务平台,导入会话密钥,会话密钥导入,校验密钥合法性及权限,R,加密判定密钥校验信息,密钥类型、发送方区域ID、租户ID、密钥状态,1
客户端软件密码模块,密钥拆分,客户端软件密码模块导入会话密钥,nan,4,发起者：用户 接收者：密码服务平台,导入会话密钥,会话密钥导入,写入会话密钥至加密存储表,W,会话密钥存储信息,密钥ID、密钥值、加密算法、存储时间,1
客户端软件密码模块,密钥拆分,客户端软件密码模块导入会话密钥,nan,4,发起者：用户 接收者：密码服务平台,导入会话密钥,会话密钥导入,返回会话密钥导入结果,X,会话密钥导入响应,密钥ID、导入状态、错误代码、操作时间,1
客户端软件密码模块,证书解析,客户端软件密码模块明文导入会话密钥,nan,4,发起者：用户 接收者：密码服务平台,用户导入密钥文件,明文导入会话密钥,输入密钥导入请求信息,E,密钥导入请求,租户ID、密钥文件路径、操作员ID、导入时间戳,1
客户端软件密码模块,证书解析,客户端软件密码模块明文导入会话密钥,nan,4,发起者：用户 接收者：密码服务平台,用户导入密钥文件,明文导入会话密钥,验证租户权限并读取密钥资源配额,R,租户密钥资源信息,租户ID、可用密钥数量、证书数量、区域ID,1
客户端软件密码模块,证书解析,客户端软件密码模块明文导入会话密钥,nan,4,发起者：用户 接收者：密码服务平台,用户导入密钥文件,明文导入会话密钥,解析并写入明文会话密钥,W,会话密钥存储记录,密钥值、密钥类型、加密算法、存储位置、密钥状态,1
客户端软件密码模块,证书解析,客户端软件密码模块明文导入会话密钥,nan,4,发起者：用户 接收者：密码服务平台,用户导入密钥文件,明文导入会话密钥,返回密钥导入结果,X,密钥导入响应,操作结果代码、密钥ID、导入时间、错误信息,1
客户端软件密码模块,证书解析,客户端软件密码模块加密初始化,nan,4,发起者：用户 接收者：密码服务平台,加密初始化请求,客户端软件密码模块加密初始化,输入加密初始化参数,E,加密初始化参数,算法类型、密钥长度、初始化向量,1
客户端软件密码模块,证书解析,客户端软件密码模块加密初始化,nan,4,发起者：用户 接收者：密码服务平台,加密初始化请求,客户端软件密码模块加密初始化,读取加密配置信息,R,加密配置信息,加密算法版本、密钥存储路径、加密策略,1
客户端软件密码模块,证书解析,客户端软件密码模块加密初始化,nan,4,发起者：用户 接收者：密码服务平台,加密初始化请求,客户端软件密码模块加密初始化,验证加密参数有效性,R,加密参数校验规则,参数格式校验、密钥强度校验、算法兼容性校验,1
客户端软件密码模块,证书解析,客户端软件密码模块加密初始化,nan,4,发起者：用户 接收者：密码服务平台,加密初始化请求,客户端软件密码模块加密初始化,执行加密模块初始化,W,加密模块状态信息,初始化状态、加密上下文ID、密钥加载结果,1
客户端软件密码模块,证书解析,客户端软件密码模块加密初始化,nan,4,发起者：用户 接收者：密码服务平台,加密初始化请求,客户端软件密码模块加密初始化,返回加密初始化结果,X,加密初始化响应,初始化状态码、加密上下文标识、错误描述,1
客户端软件密码模块,证书解析,客户端软件密码模块加密初始化,nan,4,发起者：用户 接收者：密码服务平台,加密初始化请求,客户端软件密码模块加密初始化,记录加密初始化日志,W,加密操作日志,操作时间、操作用户、初始化结果、加密上下文ID,1
客户端软件密码模块,证书解析,客户端软件密码模块单组数据加密,nan,4,发起者：用户 接收者：密码服务平台,用户请求单组数据加密,执行单组数据加密,输入单组数据加密请求,E,加密请求信息,原始数据内容、加密算法类型、密钥标识符,1
客户端软件密码模块,证书解析,客户端软件密码模块单组数据加密,nan,4,发起者：用户 接收者：密码服务平台,用户请求单组数据加密,执行单组数据加密,读取加密参数配置,R,加密参数配置,算法参数集、密钥存储路径、IV生成规则,1
客户端软件密码模块,证书解析,客户端软件密码模块单组数据加密,nan,4,发起者：用户 接收者：密码服务平台,用户请求单组数据加密,执行单组数据加密,执行加密算法运算,W,加密结果数据,加密后数据、加密时间戳、加密状态码,1
客户端软件密码模块,证书解析,客户端软件密码模块单组数据加密,nan,4,发起者：用户 接收者：密码服务平台,用户请求单组数据加密,执行单组数据加密,返回加密结果至客户端,X,加密响应信息,加密数据块、加密算法标识、操作结果状态,1
客户端软件密码模块,证书解析,客户端软件密码模块多组数据加密,nan,4,发起者：用户 接收者：密码服务平台,发起多组数据加密请求,处理多组数据加密请求,输入多组数据加密请求信息,E,加密请求信息,请求ID、数据组列表、加密算法类型、加密模式,1
客户端软件密码模块,证书解析,客户端软件密码模块多组数据加密,nan,4,发起者：用户 接收者：密码服务平台,发起多组数据加密请求,处理多组数据加密请求,读取加密配置参数,R,加密配置信息,算法类型、密钥ID、加密模式、IV值,1
客户端软件密码模块,证书解析,客户端软件密码模块多组数据加密,nan,4,发起者：用户 接收者：密码服务平台,发起多组数据加密请求,处理多组数据加密请求,执行多组数据加密操作,W,加密结果数据,加密数据块、请求ID、加密时间戳,1
客户端软件密码模块,证书解析,客户端软件密码模块多组数据加密,nan,4,发起者：用户 接收者：密码服务平台,发起多组数据加密请求,处理多组数据加密请求,输出加密结果及状态信息,X,加密响应信息,加密状态、加密数据标识、操作时间,1
客户端软件密码模块,证书解析,客户端软件密码模块结束加密,nan,4,发起者：用户 接收者：密码服务平台,用户请求结束加密,客户端软件密码模块结束加密,输入结束加密请求信息,E,结束加密请求信息,用户ID、请求时间、加密会话ID,1
客户端软件密码模块,证书解析,客户端软件密码模块结束加密,nan,4,发起者：用户 接收者：密码服务平台,用户请求结束加密,客户端软件密码模块结束加密,读取用户权限信息,R,用户权限信息,用户ID、权限级别、加密权限状态,1
客户端软件密码模块,证书解析,客户端软件密码模块结束加密,nan,4,发起者：用户 接收者：密码服务平台,用户请求结束加密,客户端软件密码模块结束加密,更新加密状态为结束,W,加密状态更新信息,加密会话ID、状态（结束）、结束时间,1
客户端软件密码模块,证书解析,客户端软件密码模块结束加密,nan,4,发起者：用户 接收者：密码服务平台,用户请求结束加密,客户端软件密码模块结束加密,返回加密结束结果,X,加密结束结果,操作结果、错误信息、结束时间,1
客户端软件密码模块,证书解析,客户端软件密码模块解密初始化,nan,4,发起者：用户 接收者：密码服务平台,客户端发起解密初始化,客户端解密初始化,输入解密初始化参数,E,解密初始化参数,证书路径、密钥ID、协议类型、初始化模式,1
客户端软件密码模块,证书解析,客户端软件密码模块解密初始化,nan,4,发起者：用户 接收者：密码服务平台,客户端发起解密初始化,客户端解密初始化,读取证书和密钥信息,R,证书和密钥信息,证书内容、密钥内容、证书有效期、密钥算法类型,1
客户端软件密码模块,证书解析,客户端软件密码模块解密初始化,nan,4,发起者：用户 接收者：密码服务平台,客户端发起解密初始化,客户端解密初始化,初始化密码模块配置,W,密码模块配置,配置参数、初始化状态、配置时间戳,1
客户端软件密码模块,证书解析,客户端软件密码模块解密初始化,nan,4,发起者：用户 接收者：密码服务平台,客户端发起解密初始化,客户端解密初始化,返回初始化结果,X,初始化结果,初始化状态、错误代码、响应时间,1
客户端软件密码模块,证书解析,客户端软件密码模块单组数据解密,nan,4,发起者：用户 接收者：密码服务平台,单组数据解密请求,单组数据解密,输入单组数据解密请求参数,E,解密请求数据,密文数据、算法类型标识、密钥标识符、数据分组标识,1
客户端软件密码模块,证书解析,客户端软件密码模块单组数据解密,nan,4,发起者：用户 接收者：密码服务平台,单组数据解密请求,单组数据解密,读取密钥信息及算法参数,R,密钥信息,密钥ID、密钥值、算法参数、密钥有效期、使用状态,1
客户端软件密码模块,证书解析,客户端软件密码模块单组数据解密,nan,4,发起者：用户 接收者：密码服务平台,单组数据解密请求,单组数据解密,执行分组解密算法处理,W,解密结果数据,明文数据、解密状态码、解密时间戳、算法执行日志,1
客户端软件密码模块,证书解析,客户端软件密码模块单组数据解密,nan,4,发起者：用户 接收者：密码服务平台,单组数据解密请求,单组数据解密,返回解密结果及状态信息,X,解密响应数据,明文数据、操作状态、错误代码、解密耗时,1
客户端软件密码模块,证书解析,客户端软件密码模块多组数据解密,nan,4,密码发起者：用户 接收者：密码服务平台,用户发起多组数据解密请求,多组数据解密,输入多组加密数据及解密参数,E,多组加密数据,数据组ID、加密算法类型、加密数据内容、数据分组标识,1
客户端软件密码模块,证书解析,客户端软件密码模块多组数据解密,nan,4,密码发起者：用户 接收者：密码服务平台,用户发起多组数据解密请求,多组数据解密,读取解密密钥信息,R,解密密钥信息,密钥ID、密钥值、算法类型、密钥有效期、租户标识,1
客户端软件密码模块,证书解析,客户端软件密码模块多组数据解密,nan,4,密码发起者：用户 接收者：密码服务平台,用户发起多组数据解密请求,多组数据解密,执行分组解密算法处理,W,解密结果数据,解密数据内容、解密状态、解密时间戳、分组标识,1
客户端软件密码模块,证书解析,客户端软件密码模块多组数据解密,nan,4,密码发起者：用户 接收者：密码服务平台,用户发起多组数据解密请求,多组数据解密,返回解密结果展示信息,X,解密结果展示信息,解密状态、解密数据摘要、操作时间、分组数量,1
客户端软件密码模块,证书解析,客户端软件密码模块结束解密,nan,4,密码发起者：用户 接收者：密码服务平台,结束解密操作,结束解密流程,输入结束解密请求,E,结束解密请求,解密任务ID、用户ID、解密状态标识,1
客户端软件密码模块,证书解析,客户端软件密码模块结束解密,nan,4,密码发起者：用户 接收者：密码服务平台,结束解密操作,结束解密流程,读取用户安全信息验证权限,R,用户安全信息,用户ID、HMAC完整性校验值、最后一次活跃时间,1
客户端软件密码模块,证书解析,客户端软件密码模块结束解密,nan,4,密码发起者：用户 接收者：密码服务平台,结束解密操作,结束解密流程,更新解密任务状态并记录日志,W,解密任务状态记录,解密任务ID、结束时间、操作结果、用户ID,1
客户端软件密码模块,证书解析,客户端软件密码模块结束解密,nan,4,密码发起者：用户 接收者：密码服务平台,结束解密操作,结束解密流程,返回解密结束响应,X,解密结束响应,解密任务ID、操作结果代码、结束时间戳,1
客户端软件密码模块,证书解析,客户端软件密码模块杂凑初始化,nan,4,密码发起者：用户 接收者：密码服务平台,用户请求初始化杂凑算法,杂凑初始化,输入杂凑初始化参数,E,杂凑初始化参数,算法类型、密钥长度、初始化向量,1
客户端软件密码模块,证书解析,客户端软件密码模块杂凑初始化,nan,4,密码发起者：用户 接收者：密码服务平台,用户请求初始化杂凑算法,杂凑初始化,读取杂凑算法配置,R,杂凑算法配置,算法版本、默认参数、安全策略,1
客户端软件密码模块,证书解析,客户端软件密码模块杂凑初始化,nan,4,密码发起者：用户 接收者：密码服务平台,用户请求初始化杂凑算法,杂凑初始化,执行杂凑初始化并保存,W,杂凑初始化结果,状态码、初始化时间、算法实例ID,1
客户端软件密码模块,证书解析,客户端软件密码模块杂凑初始化,nan,4,密码发起者：用户 接收者：密码服务平台,用户请求初始化杂凑算法,杂凑初始化,返回初始化结果,X,初始化结果展示,状态信息、详细参数、操作时间,1
客户端软件密码模块,证书解析,客户端软件密码模块单组数据杂凑,nan,4,密码发起者：用户 接收者：密码服务平台,发起单组数据杂凑请求,执行单组数据杂凑处理,输入单组数据杂凑请求,E,单组数据杂凑请求,数据内容、算法类型、请求时间戳,1
客户端软件密码模块,证书解析,客户端软件密码模块单组数据杂凑,nan,4,密码发起者：用户 接收者：密码服务平台,发起单组数据杂凑请求,执行单组数据杂凑处理,读取杂凑算法配置参数,R,杂凑算法配置,算法名称、参数集、版本号,1
客户端软件密码模块,证书解析,客户端软件密码模块单组数据杂凑,nan,4,密码发起者：用户 接收者：密码服务平台,发起单组数据杂凑请求,执行单组数据杂凑处理,执行数据杂凑计算,W,杂凑处理结果,哈希值、计算时间、处理状态,1
客户端软件密码模块,证书解析,客户端软件密码模块单组数据杂凑,nan,4,密码发起者：用户 接收者：密码服务平台,发起单组数据杂凑请求,执行单组数据杂凑处理,返回杂凑结果响应,X,杂凑结果响应,哈希值、响应时间、操作员ID,1
客户端软件密码模块,证书解析,客户端软件密码模块多组数据杂凑,nan,4,发起者：用户 接收者：密码服务平台,用户提交多组数据进行杂凑处理,多组数据杂凑处理,输入多组数据杂凑请求,E,多组数据输入信息,数据组ID、数据内容、数据格式,1
客户端软件密码模块,证书解析,客户端软件密码模块多组数据杂凑,nan,4,发起者：用户 接收者：密码服务平台,用户提交多组数据进行杂凑处理,多组数据杂凑处理,读取多组数据原始内容,R,多组数据读取信息,数据组ID、数据内容、数据格式,1
客户端软件密码模块,证书解析,客户端软件密码模块多组数据杂凑,nan,4,发起者：用户 接收者：密码服务平台,用户提交多组数据进行杂凑处理,多组数据杂凑处理,执行多组数据杂凑算法处理,W,杂凑处理结果,杂凑值、处理时间、数据组ID,1
客户端软件密码模块,证书解析,客户端软件密码模块多组数据杂凑,nan,4,发起者：用户 接收者：密码服务平台,用户提交多组数据进行杂凑处理,多组数据杂凑处理,输出杂凑结果至客户端,X,杂凑结果输出信息,杂凑值、数据组ID、处理状态,1
客户端软件密码模块,证书解析,客户端软件密码模块结束杂凑,nan,4,密码发起者：用户 接收者：密码服务平台,结束杂凑计算请求,结束杂凑计算,输入结束杂凑计算请求参数,E,结束杂凑请求数据,用户ID、杂凑算法类型、输入数据内容,1
客户端软件密码模块,证书解析,客户端软件密码模块结束杂凑,nan,4,密码发起者：用户 接收者：密码服务平台,结束杂凑计算请求,结束杂凑计算,读取用户安全信息扩展表,R,用户安全信息,用户ID、HMAC密钥、最后一次活跃时间,1
客户端软件密码模块,证书解析,客户端软件密码模块结束杂凑,nan,4,密码发起者：用户 接收者：密码服务平台,结束杂凑计算请求,结束杂凑计算,执行杂凑计算并生成结果,W,杂凑计算结果,用户ID、杂凑值、计算时间戳,1
客户端软件密码模块,证书解析,客户端软件密码模块结束杂凑,nan,4,密码发起者：用户 接收者：密码服务平台,结束杂凑计算请求,结束杂凑计算,输出杂凑计算结果,X,杂凑结果展示信息,用户ID、杂凑值、计算状态,1
客户端软件密码模块,证书解析,客户端软件密码模块Mac初始化,nan,4,发起者：用户 接收者：密码服务平台,Mac初始化请求,Mac初始化,输入Mac初始化参数,E,Mac初始化参数,初始化参数ID、密钥、算法类型、时间戳,1
客户端软件密码模块,证书解析,客户端软件密码模块Mac初始化,nan,4,发起者：用户 接收者：密码服务平台,Mac初始化请求,Mac初始化,读取Mac配置规则,R,Mac配置规则,规则ID、算法约束、密钥长度、版本号,1
客户端软件密码模块,证书解析,客户端软件密码模块Mac初始化,nan,4,发起者：用户 接收者：密码服务平台,Mac初始化请求,Mac初始化,执行Mac初始化操作,W,Mac初始化结果,初始化状态、操作时间、错误代码,1
客户端软件密码模块,证书解析,客户端软件密码模块Mac初始化,nan,4,发起者：用户 接收者：密码服务平台,Mac初始化请求,Mac初始化,返回Mac初始化响应,X,Mac初始化响应,响应状态、初始化详情、操作员ID,1
客户端软件密码模块,证书解析,客户端软件密码模块单组数据Mac,nan,4,发起者：用户 接收者：密码服务平台,生成单组数据Mac,生成单组数据Mac,输入单组数据Mac请求,E,单组数据Mac请求,数据内容、密钥ID、算法类型、请求时间,1
客户端软件密码模块,证书解析,客户端软件密码模块单组数据Mac,nan,4,发起者：用户 接收者：密码服务平台,生成单组数据Mac,生成单组数据Mac,读取密钥信息,R,密钥信息,密钥ID、密钥值、密钥算法、密钥状态,1
客户端软件密码模块,证书解析,客户端软件密码模块单组数据Mac,nan,4,发起者：用户 接收者：密码服务平台,生成单组数据Mac,生成单组数据Mac,生成并存储Mac数据,W,生成的Mac数据,Mac值、原始数据哈希、生成时间、密钥ID,1
客户端软件密码模块,证书解析,客户端软件密码模块单组数据Mac,nan,4,发起者：用户 接收者：密码服务平台,生成单组数据Mac,生成单组数据Mac,返回Mac结果响应,X,Mac结果响应,Mac值、生成状态、错误代码、响应时间,1
客户端软件密码模块,证书解析,客户端软件密码模块多组数据Mac,nan,4,发起者：用户 接收者：密码服务平台,多组数据Mac生成,多组数据Mac生成,输入多组数据及密钥参数,E,多组数据输入参数,数据组ID、加密算法类型、密钥标识符,1
客户端软件密码模块,证书解析,客户端软件密码模块多组数据Mac,nan,4,发起者：用户 接收者：密码服务平台,多组数据Mac生成,多组数据Mac生成,读取加密算法配置信息,R,加密算法配置,算法名称、密钥长度、MAC生成模式,1
客户端软件密码模块,证书解析,客户端软件密码模块多组数据Mac,nan,4,发起者：用户 接收者：密码服务平台,多组数据Mac生成,多组数据Mac生成,执行多组数据MAC计算,W,MAC计算结果,MAC值、计算时间戳、数据组哈希值,1
客户端软件密码模块,证书解析,客户端软件密码模块多组数据Mac,nan,4,发起者：用户 接收者：密码服务平台,多组数据Mac生成,多组数据Mac生成,输出MAC验证结果,X,MAC验证响应,验证状态、错误代码、MAC匹配标识,1
客户端软件密码模块,证书解析,客户端软件密码模块结束Mac,nan,4,密码发起者：用户 接收者：密码服务平台,生成MAC请求,客户端MAC生成,输入MAC生成请求参数,E,MAC生成请求,用户ID、消息内容、加密算法类型,1
客户端软件密码模块,证书解析,客户端软件密码模块结束Mac,nan,4,密码发起者：用户 接收者：密码服务平台,生成MAC请求,客户端MAC生成,读取用户安全扩展信息,R,用户安全信息,用户ID、HMAC密钥、最后一次活跃时间,1
客户端软件密码模块,证书解析,客户端软件密码模块结束Mac,nan,4,密码发起者：用户 接收者：密码服务平台,生成MAC请求,客户端MAC生成,执行MAC生成算法,W,MAC生成结果,MAC值、生成时间、算法标识,1
客户端软件密码模块,证书解析,客户端软件密码模块结束Mac,nan,4,密码发起者：用户 接收者：密码服务平台,生成MAC请求,客户端MAC生成,返回MAC生成结果,X,MAC响应数据,MAC值、状态码、错误信息,1
客户端软件密码模块,证书解析,客户端软件密码模块结束Mac,nan,4,密码发起者：用户 接收者：密码服务平台,验证MAC请求,客户端MAC验证,输入MAC验证请求参数,E,MAC验证请求,用户ID、消息内容、待验证MAC值,1
客户端软件密码模块,证书解析,客户端软件密码模块结束Mac,nan,4,密码发起者：用户 接收者：密码服务平台,验证MAC请求,客户端MAC验证,读取用户安全扩展信息,R,用户安全信息,用户ID、HMAC密钥、最后一次活跃时间,1
客户端软件密码模块,证书解析,客户端软件密码模块结束Mac,nan,4,密码发起者：用户 接收者：密码服务平台,验证MAC请求,客户端MAC验证,执行MAC验证算法,W,MAC验证结果,验证状态、时间戳、算法标识,1
客户端软件密码模块,证书解析,客户端软件密码模块结束Mac,nan,4,密码发起者：用户 接收者：密码服务平台,验证MAC请求,客户端MAC验证,返回MAC验证结果,X,MAC验证响应,验证状态、错误代码、详细日志,1
客户端软件密码模块,证书解析,客户端软件密码模块关闭密码对象句柄,nan,4,发起者：用户 接收者：密码服务平台,关闭密码对象句柄请求,关闭密码对象句柄,输入密码对象句柄关闭请求,E,密码对象句柄关闭请求,句柄ID、操作用户ID、请求时间,1
客户端软件密码模块,证书解析,客户端软件密码模块关闭密码对象句柄,nan,4,发起者：用户 接收者：密码服务平台,关闭密码对象句柄请求,关闭密码对象句柄,验证密码对象句柄有效性,R,密码对象句柄状态信息,句柄ID、当前状态、关联证书ID,1
客户端软件密码模块,证书解析,客户端软件密码模块关闭密码对象句柄,nan,4,发起者：用户 接收者：密码服务平台,关闭密码对象句柄请求,关闭密码对象句柄,执行密码对象句柄关闭操作,W,密码对象句柄关闭结果,句柄ID、关闭状态、操作时间,1
客户端软件密码模块,证书解析,客户端软件密码模块关闭密码对象句柄,nan,4,发起者：用户 接收者：密码服务平台,关闭密码对象句柄请求,关闭密码对象句柄,返回句柄关闭状态信息,X,密码对象句柄关闭反馈,句柄ID、操作结果、异常代码,1
