# 密码软算法模块(C)接口文档 V1.14

![](images/baac0eae04526c028433e5b4d9b761311d54b0340cd135d0617126d6ee15bcf9.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片的背景是由不同深浅的蓝色组成的几何图形，给人一种现代和科技感。在图片的中央，有两行文字，第一行是“河南信安科技发展有限公司”，第二行是“2019年5月”。这些文字可能是某个报告、文档或演示文稿的标题页，表明内容与河南信安科技发展有限公司在2019年5月的相关活动或信息有关。整体设计简洁明了，突出了公司的名称和日期。
```


### 版权声明

版权所有 $\circledcirc$ 三未信安科技股份有限公司 2024 保留一切权利（包括但不限于修订、最终解释权）。

本文档由三未信安科技股份有限公司编写，仅用于用户和合作伙伴阅读。本公司依中华人民共和国著作权法，享有及保留一切著作之专属权利。未经本公司书面许可，任何单位和个人不得以任何方式或形式对文档内任何部分或全部内容进行擅自摘抄、增删、复制仿制、备份和修改，并不得以任何形式传播。

### 特别提示

由于产品版本升级或其他原因，本文档内容会不定期更新，更新的内容会在本文档发行新版本时予以印刷。本文档仅用于为最终用户提供信息或使用指导，文档中的陈述、信息和建议不构成任何明示或暗示的担保。您所购买产品的硬件配置、功能、特性或服务等应受本公司商业合同和条款约束。本文档中描述的硬件配置、功能、特性或服务可能不在您的购买或使用范围之内。任何情况下，本公司均不对（包括但不限于）最终用户或任何第三方因使用本文档而造成的直接和间接损失或损害负责。

### 联系我们

感谢您使用我们的产品，如果您对我们的产品有什么意见和建议，可以通过电话、传真或电子邮件等方式向我们反馈。

电话：400-00-90196  
邮箱：<EMAIL>  
网址：www.sansec.com.cn  
公司地址、各地分公司与办事处地址请前往官方网站查阅。

# 目录

1 算法密钥管理.. .4  
1.1 实例化接口.. ...6  
1.1.1 新建密钥.... .6  
1.1.2 释放密钥... ...6  
1.1.3 新建 SM2 相关信息结构体.... .6  
1.1.4 释放 SM2 相关信息结构体.. ..6  
1.2 密钥管理接口.. ..6  
1.2.1 密钥初始化... ..6  
1.2.2 生成密钥.. ..7  
1.2.3 导出密钥为字节数据... 7  
1.2.4 导入字节数据为密钥.. 7  
1.2.5 导出密钥至文件. ..8  
1.2.6 由文件导入密钥. ..8  
1.3 辅助接口.. ...9  
1.3.1 其他操作. 9  
2 非对称算法.. ....9  
2.1 实例化接口.... ...11  
2.1.1 新建密钥运算结构体.. ..11  
2.1.2 释放密钥运算结构体.... ..11  
2.2 运算通用接口. ..12  
2.2.1 非对称运算初始化. ....12  
2.3 签名验签接口... ..12  
2.3.1 签名运算.. ....12  
2.3.2 验签运算. ..12  
2.3.3 签名运算-输入预处理数据.. ..13  
2.3.4 验签运算-输入预处理数据.. ..13  
2.4 加解密接口..... ...14  
2.4.1 加密运算.... .14  
2.4.2 解密运算.... .14  
2.5 辅助接口. ....14  
2.5.1 其他操作（迭代一不涉及） .14  
3 对称算法.. ...15  
3.1 实例化接口.. .19  
3.1.1 新建密钥运算结构体.. ..19  
3.1.2 释放密钥运算结构体... ...19  
3.2 运算接口. ..19  
3.2.1 对称运算初始化. ....19  
3.2.2 加密运算. .20  
3.2.3 解密运算.. ....20  
3.2.4 三段式加解密.. .20

******* 三段式加密初始化. .20  
******* 三段式带缓冲加密. ..21  
******* 三段式结束缓冲加密. ..21  
******* 三段式 / 两段式无缓冲加密. ..21  
******* 三段式解密初始化... ..22  
******* 三段式带缓冲解密.. ..22  
******* 三段式结束缓冲解密. ...22  
******* 三段式 / 两段式无缓冲解密. ..23  
3.3 辅助接口.. ..23  
3.3.1 其他操作.... ..23  
4 摘要算法. ...24  
4.1 初始化接口.. ...24  
4.1.1 新建摘要运算结构体.. ...24  
4.1.2 释放摘要运算结构体... ..24  
4.2 运算接口.. ..25  
4.2.1 一步式摘要. ..25  
4.2.2 三段式摘要.. ...25  
4.2.2.1 三段式摘要初始化. ..25  
4.2.2.2 三段式读取数据. .25  
4.2.2.3 三段式完成摘要.. ..26  
5 消息认证码算法.. ..26  
5.1 初始化接口.. ...27  
5.1.1 新建 HMAC 结构体.. ..27  
5.1.2 释放 HMAC 结构体.. ...27  
5.2 运算接口. ..27  
5.2.1 一步式消息认证... ....27  
5.2.2 三段式消息认证.. ....27  
5.2.2.1 三段式运算初始化.. ...27  
5.2.2.2 三段式读取数据.. ...28  
5.2.2.3 三段式完成消息认证.. ...28  
附录A （规范性附录） 错误代码定义. ..29  
附录B （规范性附录） 密钥管理接口调用流程.. ...31  
附录C （规范性附录） 非对称算法接口-签名验签调用流程.. ..32  
附录D （规范性附录） 非对称算法接口-加密解密调用流程.. ..33  
附录E （规范性附录） 对称算法接口-加密解密调用流程.. ..34  
附录F （规范性附录） 哈希算法接口调用流程.. ..35  
附录G （规范性附录） 消息认证码算法接口调用流程. .36

# 1 算法密钥管理

密钥管理接口如下：

表1-1 密钥管理接口  

<html><body><table><tr><td>接口名称</td><td>功能</td></tr><tr><td>实例化接口</td><td></td></tr><tr><td>WRP_KEY_CTX_neW</td><td>新建密钥</td></tr><tr><td>WRP_KEY_CTX_free</td><td>释放密钥</td></tr><tr><td>WRP_EC_GROUP_neW_sm2</td><td>新建sm2相关信息结构体（仅sm2/wbsm2算法需 要)</td></tr><tr><td>WRP_EC_GROUP_free</td><td>释放 sm2相关信息结构体（仅sm2/wbsm2 算法需 要）</td></tr><tr><td colspan="2">密钥管理接口</td></tr><tr><td>WRP_KEY_init</td><td>初始化密钥结构</td></tr><tr><td>WRP_KEY_genkey</td><td>生成相应算法密钥</td></tr><tr><td>WRP_KEY_get_key</td><td>导出密钥至字节数组</td></tr><tr><td>WRP_KEY_set_key</td><td>由字节数组装载密钥</td></tr><tr><td>WRP_KEY_export</td><td>导出密钥至文件</td></tr><tr><td>WRP_KEY_import</td><td>由文件装载密钥</td></tr><tr><td>WRP_KEY_ctrl</td><td>其他操作</td></tr></table></body></html>

表1-2 算法密钥信息接口  

<html><body><table><tr><td colspan="3">※接口名为”WRP_KEY_"与算法名拼接，如:WRP_KEY_rsa();</td></tr><tr><td>针对对称算法xts 模式为WRP_KEY_sm4_xts()形式 算法名</td><td>支持的密钥强度(bit)</td><td>功能</td></tr><tr><td>rsa</td><td>1024/2048/4096</td><td rowspan="9">作为初始化接口的参数</td></tr><tr><td>aes</td><td>128/192/256</td></tr><tr><td>wbrsa</td><td>1024/2048</td></tr><tr><td>wbaes</td><td>128/256</td></tr><tr><td>sm2</td><td>256</td></tr><tr><td>sm4</td><td>128</td></tr><tr><td>wbsm2</td><td>256</td></tr><tr><td>wbsm4</td><td>128</td></tr><tr><td>3des</td><td>128/192</td></tr></table></body></html>

表1-3 密钥导入导出模式  

<html><body><table><tr><td>类别</td><td>模式宏</td><td>功能</td><td>适配算法</td></tr><tr><td rowspan="2">通用</td><td>KEYMODE_GENERI C</td><td>默认格式，包括公钥 和私钥 (公钥在 前）；</td><td>sm2</td></tr><tr><td>KEYMODE_RAWBYT E</td><td>裸密钥(对称/白盒对 称)； KEYMODE_RAWBYTE 数值与</td><td>sm4/aes/3des; wbsm4/wbaes（该 模式仅适用于导入裸密</td></tr></table></body></html>

<html><body><table><tr><td></td><td></td><td>KEYMODE_GENER IC一样</td><td>钥，仅适用于 WRP_KEY_set_key ，不适用于 WRP_KEY_import)</td></tr><tr><td rowspan="2">对称</td><td>KEYMODE_ENCRYP T</td><td>加密密钥</td><td>wbsm4/wbaes (适 用于导入、导出密 钥）；</td></tr><tr><td>KEYMODE_DECRYP T</td><td>解密密钥</td><td>wbsm4/wbaes（适 用于导入、导出密钥)</td></tr><tr><td rowspan="2">非对称</td><td>KEYMODE_PRIVKE Y</td><td>私钥</td><td>sm2/rsa/wbsm2/ wbrsa/</td></tr><tr><td>KEYMODE_PUBKEY</td><td>公钥</td><td>sm2/rsa/wbsm2/ wbrsa/</td></tr></table></body></html>

表 1-4 WRP_KEY_ctrl 操作类型  

<html><body><table><tr><td>类别</td><td>类型宏</td><td>功能</td><td>备注</td></tr><tr><td>预计算参数加 载</td><td>WRP_KEY_CTRL_SET_EXT RA_PARM</td><td>所需预计算参数 的加载，目前仅 适用于sm2/ wbsm2</td><td>通过 WRP_KEY_ctrl设 置(datalen参数 内部不处理)</td></tr><tr><td rowspan="2">白盒密钥读写</td><td>WRP_KEY_CTRL_DEVICE_ BIND</td><td>配置设备绑定</td><td>调用import/ export前，通过 WRP_KEY_ctrl设置</td></tr><tr><td>WRP_KEY_CTRL_WB_SET_ PATH</td><td>(重新)指定密钥 读写路径</td><td>调用import/ export前，通过 WRP_KEY_ctrl设置</td></tr></table></body></html>

表1-5 算法对应密钥大小  

<html><body><table><tr><td>算法名</td><td colspan="2">密钥大小(字节)</td></tr><tr><td rowspan="2">rsa1024/2048/4096</td><td>私钥</td><td>580/1156/23 08</td></tr><tr><td>公钥</td><td>132/260/516</td></tr><tr><td>aes128/192/256</td><td colspan="2">16/24/32</td></tr><tr><td rowspan="2">wbrsa1024/2048</td><td>私钥</td><td>1668/1796</td></tr><tr><td>公钥</td><td>132/260</td></tr><tr><td>wbaes128/256</td><td colspan="2">368/432</td></tr><tr><td rowspan="2">sm2</td><td>私钥</td><td>32</td></tr><tr><td>公钥</td><td>64</td></tr><tr><td>sm4</td><td colspan="2">16</td></tr><tr><td rowspan="2">wbsm2</td><td>私钥</td><td>228</td></tr><tr><td>公钥</td><td>68</td></tr><tr><td>wbsm4</td><td colspan="2">324</td></tr><tr><td>3des128/192</td><td colspan="2">16/24</td></tr></table></body></html>

### 1.1 实例化接口

## 1.1.1 新建密钥

函数原型 WRP_KEY_CTX  \*WRP_KEY_CTX_new( void );  
功能描述 该接口将为密钥结构体分配内存，实例化结构体并返回其指针。  
参数 (空)返回值 密钥结构体的指针。  
备注 最后使用完毕，需调用WRP_KEY_CTX_free 以释放分配的内存。

## 1.1.2 释放密钥

函数原型 ERRNO WRP_KEY_CTX_free( WRP_KEY_CTX \*wrpkey );  
功能描述 该接口将释放指针所指向的密钥结构体内存。  
参数 wrpkey [IN] 待释放的密钥结构指针。  
返回值 ERRNO_OK： 成功。  
其他： 拒绝释放，将由加载它的运算结构体托管释放。  
备注 应与 WRP_KEY_CTX_new() 配对使用，如同 malloc 与 free。

## 1.1.3 新建SM2 相关信息结构体

函数原型 _EC_GROUP \*WRP_EC_GROUP_new_sm2();  
功能描述 该接口将为SM2 运算所需椭圆曲线固定参数及相关信息新建结构体并返回其指针。  
参数 (空)返回值 SM2 相关信息结构体的指针。  
备注 最后使用完毕，需调用WRP_EC_GROUP_free 以释放分配的内存。

## 1.1.4 释放SM2 相关信息结构体

函数原型 void WRP_EC_GROUP_free(_EC_GROUP \*group);  
功能描述 该接口将释放指针所指向的SM2 相关信息结构体内存。  
参数 ctx [IN] 待释放的SM2 相关信息结构指针。  
返回值 无备注 应与 WRP_EC_GROUP_new_sm2 () 配对使用，如同 malloc 与 free。

### 1.2 密钥管理接口

## 1.2.1 密钥初始化

函数原型 ERRNO  WRP_KEY_init(  WRP_KEY_CTX  \*wrpkey,  const  WRP_KEY\*keyalg );  
功能描述 该接口将算法信息keyalg，保存到密钥结构体wrpkey。  
参数 wrpkey [IN,OUT] 已实例化的密钥的指针。keyalg [IN] 选择并传入 WRP_KEY_(alg) () 返回的指针，见表 1-2。  
返回值 ERRNO_OK： 成功。其他： 错误码。  
备注 调用此接口前，应确保：(1) wrpkey 需由 WRP_KEY_CTX_new() 返回；

## 1.2.2 生成密钥

函数原型 ERRNO WRP_KEY_genkey ( WRP_KEY_CTX \*wrpkey, uint32_t bits );  
功能描述 该接口将根据 WRP_KEY_init 所指定的算法，生成 bits 强度的密钥并存储至结构体。  
参数 wrpkey [IN] 已初始化的密钥结构的指针。bits [IN] 密钥强度，见表1-2。  
返回值 ERRNO_OK： 成功。其他： 错误码。  
备注 (1) 该接口需在 WRP_KEY_init 之后调用；

## 1.2.3 导出密钥为字节数据

函数原型 ERRNO WRP_KEY_get_key( WRP_KEY_CTX \*wrpkey,uint8_t \*data, uint32_t \*data_len, uint32_t mode);  
功能描述 该接口将把密钥结构体内部的密钥，以二进制数组导出至指定缓冲区。  
参数 wrpkey [IN] 已装载密钥的密钥结构的指针。data [OUT] 输出密钥的缓冲区。datalen [IN,OUT] 传入缓冲区实际长度，输出密钥长度。mode [IN] 密钥导出模式，见表1-3。

返回值 ERRNO_OK： 成功。

其他： 错误码。

(1) 该接口需在 WRP_KEY_init 之后调用；  
(2) 该接口需在 WRP_KEY_genkey 之后、或将合法密钥 set_key/import 之后调用，即wrpkey 中首先应当“存在”所需导出的密钥，否则将无法导出并报错。

## 1.2.4 导入字节数据为密钥

函数原型 ERRNO WRP_KEY_set_key( WRP_KEY_CTX \*wrpkey,uint8_t \*data, uint32_t data_len, uint32_t mode);  
功能描述 该接口将把合法的二进制密钥数据，导入至密钥结构体。  
参数 wrpkey [IN] 已初始化的密钥结构的指针。data [IN] 待传入密钥数据的头指针。datalen [IN] 待传入密钥数据的长度。mode [IN] 密钥导入模式，见表1-3。  
返回值 ERRNO_OK： 成功。其他： 错误码。

注 (1) 该接口需在 WRP_KEY_init 之后调用；(2) 该接口传入的合法密钥数据，为WRP_KEY_get_key 所导出的密钥数据；(3) 若密钥数据的导出模式与该接口指定的导入模式不一致，将可能报错，如，RSA 密钥导出时指定为公钥导出，但导入时指定为私钥导入则无法导入。

## 1.2.5 导出密钥至文件

ERRNO WRP_KEY_export( WRP_KEY_CTX \*wrpkey, const char \*alias,  uint32_t mode );

功能描述参数

该接口将把密钥结构体内部的密钥，以指定名称导出为文件。

wrpkey [IN] 已装载密钥的密钥结构的指针。  
alias [IN] 密钥文件名(不包含路径，不包含文件后缀)。  
mode [IN] 密钥导出模式，见表1-3。

返回值 ERRNO_OK： 成功。

其他： 错误码。

备注

(1) 该接口需在 WRP_KEY_init 之后调用；  
(2) 该接口需在 WRP_KEY_genkey 之后、或将合法密钥 set_key/import 之后调用，即wrpkey 中首先应当“存在”所需导出的密钥，否则将无法导出并报错。  
(3) 若文件不存在，则新建文件；若文件存在则报错，文件已存在。  
(4) 仅适用于白盒算法。  
(5) 文件名字符合规则，文件名可以包含字母、数字和一些特殊字符，如_-.。但是，文件名中不应包含空格或其他特殊字符，如?: $\mathbf { < } >$ \*等，因为这些字符可能会导致文件操作出现问题。  
(6) 文件名长度，不同的操作系统对文件名长度有限制，通常在 255 个字符以内。因此，建议文件名不要过长，以免出现兼容性问题。

## 1.2.6 由文件导入密钥

型 ERRNO WRP_KEY_import( WRP_KEY_CTX \*wrpkey, const char \*alias,  uint32_t mode );

功能描述参数

该接口将把指定名称的合法密钥文件，导入至密钥结构体。

wrpkey [IN] 已初始化的密钥结构的指针。  
alias [IN] 密钥文件名(不包含路径)。  
mode [IN] 密钥导入模式，见表1-3。

返回值 ERRNO_OK： 成功。

其他： 错误码。

备注

(1) 该接口需在 WRP_KEY_init 之后调用；  
(2) 该接口能够导入的合法密钥文件，为WRP_KEY_export 所导出的密钥文件；  
(3) 若密钥文件的导出模式与该接口指定的导入模式不一致，将可能报错，如，RSA 密钥导出时指定为公钥导出，但导入时指定为私钥导入则无法导入。  
(4) 仅适用于白盒算法。  
(5) 文件名字符合规则，文件名可以包含字母、数字和一些特殊字符，如_-.。但是，文件名中不应包含空格或其他特殊字符，如?: $< > |$ \*等，因为这些字符可能会导致文件操作出现问题。

(6) 文件名长度，不同的操作系统对文件名长度有限制，通常在 255 个字符以内。因此，建议文件名不要过长，以免出现兼容性问题。

### 1.3 辅助接口

## 1.3.1 其他操作

函数原型 ERRNO WRP_KEY_ctrl( WRP_KEY_CTX \*ctx, uint32_t ctrl_flag,void \*data, uint32_t datalen);  
功能描述 该接口将以flag 指明的方式，利用参数进行操作，见表 $1 { - } 4 _ { \circ }$   
参数 ctx [IN,OUT] 已装载密钥的运算结构的指针。ctrl_fla [IN] 宏，指明操作类型。gdata [IN/OUT] 可选指针参数。datalen [IN/OUT] 可选数值参数。

返回值 ERRNO_OK： 成功。

其他： 错误码。

备注 (1) 该接口需在 WRP_KEY_init 之后调用；(2) 该接口在使用白盒的import/export 操作时，需注意以下事项：调用import/export 前，传入设备信息以设置文件的设备绑定；调用import/export 前，(重新)设置文件的读写路径；

# 2 非对称算法

非对称算法封装为通用接口，其中白盒运算只需密钥结构为相应白盒算法的密钥即可（参”算法密钥管理”一节）：

表2-1 非对称接口  
表 2-2 WRP_PUBCIPH_ctrl 操作类型  

<html><body><table><tr><td>接口名称</td><td>功能</td></tr><tr><td colspan="2">实例化接口</td></tr><tr><td>WRP_PUBCIPH_CTX_new</td><td>新建密钥运算结构体</td></tr><tr><td>WRP_PUBCIPH_CTX_free</td><td>释放密钥运算结构体</td></tr><tr><td colspan="2">运算接口</td></tr><tr><td>WRP_PUBCIPH_init</td><td>非对称运算初始化</td></tr><tr><td>WRP_PUBCIPH_DIGEST_doSig n</td><td>签名运算</td></tr><tr><td>WRP_PUBCIPH_DIGEST_doVer ify</td><td>验签运算</td></tr><tr><td>WRP_PUBCIPH_sign</td><td>签名运算-输入预处理数据</td></tr><tr><td>WRP PUBCIPH_verify</td><td>验签运算-输入预处理数据</td></tr><tr><td>WRP PUBCIPH_encrypt</td><td>加密运算</td></tr><tr><td>WRP PUBCIPH_decrypt</td><td>解密运算</td></tr><tr><td>WRP PUBCIPH_ctrl</td><td>其他操作</td></tr></table></body></html>

<html><body><table><tr><td>类别</td><td>类型宏</td><td>功能</td><td>备注</td></tr><tr><td>设置ID</td><td>WRP_PUBCIPH_CTRL_SM2 _set_id (发版1不涉及)</td><td>设置SM2签名 过程中的ID</td><td>通过 WRP_PUBCIPH_ctrl 设置</td></tr></table></body></html>

表 2-4 签名功能输入输出长度限制（WRP_PUBCIPH_DIGEST_doSign）  

<html><body><table><tr><td colspan="3">※接口名为”WRP_"与算法名拼接，如:WRP_sm2()</td></tr><tr><td>算法名</td><td>支持的密钥强度(bit)</td><td>功能</td></tr><tr><td>rsa</td><td>1024/2048/4096</td><td rowspan="3">作为初始化接口的参数</td></tr><tr><td>wbrsa</td><td>1024/2048</td></tr><tr><td>sm2</td><td>256</td></tr><tr><td>wbsm2</td><td>256</td><td></td></tr></table></body></html>

表2-3 算法信息接口  

<html><body><table><tr><td rowspan="2">签名</td><td colspan="3">输入 (字节)</td><td>输出 (字节)</td></tr><tr><td>公钥</td><td>待验签数据</td><td>签名值</td><td>验签结果</td></tr><tr><td>SHA256withRSA</td><td>132/260/516</td><td>不定长</td><td>128/256/512</td><td>返回错误码</td></tr><tr><td>SM3withSM2</td><td>64</td><td>不定长</td><td>64</td><td>返回错误码</td></tr><tr><td>SHA256withWBRSA</td><td>132/260</td><td>不定长</td><td>128/256</td><td>返回错误码</td></tr><tr><td>SM3withWBSM2</td><td>68</td><td>不定长</td><td>64</td><td>返回错误码</td></tr></table></body></html>

表 2-5 验签功能输入输出长度限制（WRP_PUBCIPH_DIGEST_doVerify）  

<html><body><table><tr><td rowspan="2">签名</td><td colspan="2">输入 (字节)</td><td>输出 (字节)</td></tr><tr><td>私钥</td><td>待签名数据</td><td>签名结果</td></tr><tr><td>SHA256withRSA</td><td>580/1156/2308</td><td>不定长</td><td>128/256/512</td></tr><tr><td>SM3withSM2</td><td>32</td><td>不定长</td><td>64</td></tr><tr><td>SHA256withWBRSA</td><td>1668/1796</td><td>不定长</td><td>128/256</td></tr><tr><td>SM3withWBSM2</td><td>228</td><td>不定长</td><td>64</td></tr></table></body></html>

表2-6 签名功能输入输出长度限制（WRP_PUBCIPH_sign）  

<html><body><table><tr><td rowspan="2">签名</td><td colspan="2">输入 (字节)</td><td>输出 (字节)</td></tr><tr><td>私钥</td><td>待签名数据</td><td>签名结果</td></tr><tr><td>rsa1024/2048/4096（默 认PKCS1.5填充)</td><td>580/1156/2308</td><td>20/32/48/64，见表4-2</td><td>128/256/512</td></tr><tr><td>wbrsa1024/2048（默认 PKCS1.5填充)</td><td>1668/1796</td><td>20/32/48/64，见表 4-2</td><td>128/256</td></tr><tr><td>sm2</td><td>32</td><td>20/32/48/64, 见表4-2</td><td>64</td></tr><tr><td>wbsm2</td><td>228</td><td>20/32/48/64, 见表4-2</td><td>64</td></tr></table></body></html>

表 2-7 验签功能输入输出长度限制（WRP_PUBCIPH_verify）  

<html><body><table><tr><td rowspan="2">签名</td><td colspan="3">输入 (字节)</td><td>输出 (字节)</td></tr><tr><td>公钥</td><td>待验签数据</td><td>签名值</td><td>验签结果</td></tr></table></body></html>

表2-8 加密功能输入输出长度限制  

<html><body><table><tr><td>rsa1024/2048/4096 （默认 PKCS1.5填 充)</td><td>132/260/516</td><td>20/32/48/64，见表4-2</td><td>128/256/512</td><td>返回错误码</td></tr><tr><td>wbrsa1024/2048</td><td>132/260</td><td>20/32/48/64, 见表4-2</td><td>128/256</td><td>返回错误码</td></tr><tr><td>sm2</td><td>64</td><td>20/32/48/64, 见表4-2</td><td>64</td><td>返回错误码</td></tr><tr><td>wbsm2</td><td>68</td><td>20/32/48/64, 见表4-2</td><td>64</td><td>返回错误码</td></tr></table></body></html>

<html><body><table><tr><td rowspan="2">加密</td><td>输入（字 节)</td><td></td><td>输出 (字节)</td></tr><tr><td>公钥</td><td>待加密数据</td><td>加密结果</td></tr><tr><td>rsa1024/2048/4096 （默认 PKCS1.5填 充)</td><td>132/260/516</td><td>小于等于 (128-11)/ (256-11)/(512-11)</td><td>128/256/512</td></tr><tr><td>wbrsa1024/2048</td><td>132/260</td><td>小于等于 (128-11)/ (256-11)</td><td>128/256</td></tr><tr><td>sm2</td><td>64</td><td>不定长</td><td>96+待加密数据长度</td></tr><tr><td>wbsm2</td><td>68</td><td>不定长</td><td>96+待加密数据长度</td></tr></table></body></html>

表2-9 解密功能输入输出长度限制  

<html><body><table><tr><td rowspan="2">解密</td><td colspan="2">输入 (字节)</td><td>输出 (字节)</td></tr><tr><td>私钼</td><td>待解密数据</td><td>解密结果</td></tr><tr><td>rsa1024/2048/4096（默 认PKCS1.5填充)</td><td>580/1156/2308</td><td>128/256/512</td><td>小于等于 (128-11)/ (256-11)/(512-11)</td></tr><tr><td>wbrsa1024/2048（默认 PKCS1.5填充)</td><td>1668/1796</td><td>128/256</td><td>小于等于 (128-11)/ (256-11)</td></tr><tr><td>sm2</td><td>32</td><td>大于96</td><td>不定长</td></tr><tr><td>wbsm2</td><td>228</td><td>大于96</td><td>不定长</td></tr></table></body></html>

### 2.1 实例化接口

## 2.1.1 新建密钥运算结构体

函数原型 WRP_PUBCIPH_CTX  \*WRP_PUBCIPH_CTX_new( void );  
功能描述 该接口将为密钥运算所需结构体分配内存，实例化结构体并返回其指针。  
参数 (空)返回值 运算结构体的指针。  
备注 最后使用完毕，需调用WRP_PUBCIPH_CTX_free 以释放分配的内存。

## 2.1.2 释放密钥运算结构体

函数原型 void  WRP_PUBCIPH_CTX_free( WRP_CIPHER_CTX \*ctx );  
功能描述 该接口将释放指针所指向的密钥运算结构体内存。  
参数 ctx [IN] 待释放的运算结构指针。  
返回值 无备注 应与 WRP_PUBCIPH_CTX_new() 配对使用，如同 malloc 与 free。

### 2.2 运算通用接口

## 2.2.1 非对称运算初始化

<html><body><table><tr><td>函数原型</td><td>ERRNOWRP_PUBCIPH_init( WRP_PUBCIPH_CTX *ctx, WRP_KEY_CTX *key );</td></tr><tr><td>功能描述 参数</td><td>该接口将密钥结构体key的引用，保存到非对称运算结构体ctx。 ctx [IN,OUT]已实例化的运算结构的指针。 key [IN］已装载相应算法密钥的密钥结构（参“算法密钥管理接口”</td></tr><tr><td>返回值</td><td>节）。 ERRNO_OK:成功。</td></tr><tr><td>备注</td><td>其他: 错误码。 调用此接口前，应确保:</td></tr><tr><td></td><td>(1)ctx 需由WRP_PUBCIPH_CTX_new() 返回;</td></tr><tr><td></td><td></td></tr><tr><td></td><td>(2)wrpkey 经WRP_KEY_init() 初始化，并且调用WRP_KEY_genkey()或 WRP_KEY_set_key()等接口装载密钥数据。</td></tr></table></body></html>

### 2.3 签名验签接口

## 2.3.1 签名运算

<html><body><table><tr><td>函数原型</td><td>ERRNO WRP_PUBCIPH_DIGEST_doSign( WRP_PUBCIPH_CTX *ctx, const WRP_DIGEST *digest, uint8_t *msg,uint32_t msglen, uint8_t *sign,uint32_t *siglen);</td></tr><tr><td>功能描述 参数 ctx</td><td>使用指定摘要算法digest，对已初始化的密钥结构，对输入的数据进行签名。 [IN]已初始化的运算结构指针。</td></tr><tr><td>digest</td><td>[IN]选择并传入WRP_(digalg)()返回的指针，见表4-2。</td></tr><tr><td>msg msglen</td><td>[IN]待签名数据。</td></tr><tr><td>sign signlen</td><td>[IN]待签名数据长度。</td></tr><tr><td></td><td>[IN,OUT]签名结果缓冲区。</td></tr><tr><td></td><td>[IN,OUT]输入签名结果缓冲区长度，输出结果长度。</td></tr><tr><td>返回值</td><td>ERRNO_OK: 成功。</td></tr><tr><td></td><td></td></tr><tr><td></td><td>其他: 错误码。</td></tr><tr><td></td><td></td></tr></table></body></html>

备注 (1) ctx 需已由 WRP_PUBCIPH_init 初始化；

## 2.3.2 验签运算

函数原型 ERRNO WRP_PUBCIPH_DIGEST_doVerify( WRP_PUBCIPH_CTX \*ctx, const WRP_DIGEST \*digest, uint8_t \*msg, uint32_t msglen, uint8_t \*sign, uint32_t siglen );

使用指定摘要算法digest，对已初始化的密钥结构，对输入的签名数据进行验证。

参数 ctx [IN] 已初始化的运算结构指针。digest [IN] 选择并传入 WRP_(digalg)() 返回的指针，见表 4-2。msg [IN] 被签名数据。msglen [IN] 被签名数据长度。sign [IN] 待验证的签名数据。signlen [IN] 待验证的签名数据长度。

## 2.3.3 签名运算-输入预处理数据

<html><body><table><tr><td>函数原型</td><td>ERRNO WRP_PUBCIPH_sign( WRP_PUBCIPH_CTX *ctx,</td></tr><tr><td></td><td>uint8_t *md,uint32_tmdlen, uint8_t *sign,uint32_t *signlen);</td></tr><tr><td>功能描述 ctx</td><td>使用已初始化和摘要初始化的密钥结构，对输入的数据进行签名。</td></tr><tr><td>参数</td><td>[IN]已初始化的运算结构指针。</td></tr><tr><td></td><td>md [IN]待签名数据经预处理后的摘要数据。摘要算法参考表4-2。</td></tr><tr><td></td><td>mdlen [IN]摘要数据长度。</td></tr><tr><td></td><td>sign [IN,OUT]签名结果缓冲区。 signlen [IN,OUT]输入签名结果缓冲区长度，输出结果长度。</td></tr><tr><td>返回值</td><td>ERRNO OK: 成功。</td></tr><tr><td></td><td></td></tr><tr><td>备注</td><td>其他: 错误码。</td></tr><tr><td></td><td>(1)ctx需已由WRP_PUBCIPH_init初始化;</td></tr></table></body></html>

## 2.3.4 验签运算-输入预处理数据

函数原型 ERRNO WRP_PUBCIPH_verify(WRP_PUBCIPH_CTX \*ctx,uint8_t \*md, uint32_t mdlen,uint8_t \*sign, uint32_t signlen);  
功能描述 使用已初始化和摘要初始化的密钥结构，对输入的签名数据进行验证。  
参数 ctx [IN] 已初始化的运算结构指针。md [IN] 被签名数据经预处理后的摘要数据。摘要算法参考表4-2。mdlen [IN] 摘要数据长度。sign [IN] 待验证的签名数据。signlen [IN] 待验证的签名数据长度。  
返回值 ERRNO_OK： 成功。其他： 错误码。  
备注 (1) ctx 需已由 WRP_PUBCIPH_init 初始化；(2) 若签名验证错误会返回相应算法的验证失败；

### 2.4 加解密接口

## 2.4.1 加密运算

<html><body><table><tr><td>函数原型</td><td>ERRNO WRP_PUBCIPH_encrypt( WRP_PUBCIPH_CTX *ctx, uint8_t *in,uint32_tinlen, uint8_t *out,uint32_t *outlen);</td></tr><tr><td>功能描述 参数</td><td>使用已初始化和摘要初始化的密钥结构，对输入的数据进行加密。 ctx [IN]已初始化的运算结构指针。</td></tr><tr><td></td><td>in [IN]待加密的数据。 inlen [IN]待加密的数据长度。</td></tr><tr><td></td><td>out [OUT]输出加密结果数据缓冲区。 outlen [IN,OUT]传入缓冲区实际长度，输出结果长度。</td></tr><tr><td>返回值</td><td>ERRNO OK: 成功。</td></tr><tr><td>备注</td><td>其他: 错误码。</td></tr></table></body></html>

## 2.4.2 解密运算

<html><body><table><tr><td>函数原型</td><td>ERRNO WRP_PUBCIPH_decrypt( WRP_PUBCIPH_CTX *ctx, uint8_t *in,uint32_t inlen, uint8 t *out,uint32 t *outlen);</td></tr><tr><td>功能描述 参数</td><td>使用已初始化和摘要初始化的密钥结构，对输入的数据进行解密。 ctx [IN]已初始化的运算结构指针。</td></tr><tr><td></td><td>in [IN]待解密的数据。 inlen [IN]待解密的数据长度。</td></tr><tr><td></td><td>out [OUT]输出解密结果数据缓冲区。 outlen [IN,OUT]传入缓冲区实际长度，输出结果长度。</td></tr><tr><td>返回值</td><td>ERRNO OK: 成功。</td></tr><tr><td>备注</td><td>其他: 错误码。</td></tr></table></body></html>

### 2.5 辅助接口

## 2.5.1 其他操作（迭代一不涉及）

函数原型 ERRNO WRP_PUBCIPH_ctrl( WRP_CIPHER_CTX \*ctx, uint32_t ctrl_flag,void \*data, uint32_t datalen);  
功能描述 该接口将以flag 指明的方式，利用参数进行操作，见表2-2。  
参数 ctx [IN,OUT] 已装载密钥的运算结构的指针。ctrl_fla [IN] 宏，指明操作类型。gdata [IN/OUT] 可选指针参数。datalen [IN/OUT] 可选数值参数。

返回值 ERRNO_OK： 成功。其他： 错误码。

注 (1) 该接口需在WRP_PUBCIPH_init 之后、签名/验签/加密/解密操作之前调用；(2) WRP_PUBCIPH_sign( 或 WRP_PUBCIPH_verify 或WRP_PUBCIPH_DIGEST_doSign 或 WRP_PUBCIPH_DIGEST_doVerify)之 前 不 可 设 置 WRP_PUBCIPH_CTRL_SM2_set_id 进 行 绑 定 用 户 ID ；WRP_PUBCIPH_DIGEST_init 之后，WRP_PUBCIPH_DIGEST_update 之前可以绑定用户ID；(3) 当前配套（白盒）sm2 的操作：绑定用户ID；

# 3 对称算法

对称算法封装为通用接口，其中白盒运算只需密钥结构为相应白盒算法的密钥即可（参考”算法密钥管理”一节）：

表3-1 对称接口  

<html><body><table><tr><td>接口名称</td><td>功能</td></tr><tr><td colspan="2">实例化接口</td></tr><tr><td>WRP_CIPHER_CTX_new</td><td>新建密钥运算结构体</td></tr><tr><td>WRP_CIPHER_CTX_free</td><td>释放密钥运算结构体</td></tr><tr><td colspan="2">运算接口</td></tr><tr><td>WRP_CIPHER_start</td><td>对称运算初始化</td></tr><tr><td>WRP_CIPHER_Encrypt_init</td><td>三段式-加密初始化</td></tr><tr><td>WRP CIPHER_Encrypt_update</td><td>三段式-带缓冲加密</td></tr><tr><td>WRP_CIPHER_Encrypt_doFinal</td><td>三段式-结束缓冲加密</td></tr><tr><td>WRP_CIPHER_Encrypt_doCiph er</td><td>三段式/两段式-无缓冲加密</td></tr><tr><td>WRP_CIPHER_Decrypt_init</td><td>三段式-解密初始化</td></tr><tr><td>WRP_CIPHER_Decrypt_update</td><td>三段式-带缓冲解密</td></tr><tr><td>WRP_CIPHER_Decrypt_doFinal</td><td>三段式-结束缓冲解密</td></tr><tr><td>WRP_CIPHER_Decrypt_doCiph er</td><td>三段式/两段式-无缓冲解密</td></tr><tr><td>WRP_CIPHER_ctrl</td><td>其他操作</td></tr></table></body></html>

表3-2 分组运算模式宏  

<html><body><table><tr><td>宏名</td><td>分组运算模式</td></tr><tr><td>WRP_MODE_ecb</td><td>ECB</td></tr><tr><td>WRP_MODE_cbc</td><td>CBC</td></tr><tr><td>WRP_MODE_ctr</td><td>CTR</td></tr><tr><td>WRP_MODE_ofb</td><td>OFB</td></tr><tr><td>WRP_MODE_gcm</td><td>GCM</td></tr><tr><td>WRP_MODE_cfb</td><td>cfb</td></tr><tr><td>WRP_MODE_ccm</td><td>CCM</td></tr><tr><td>WRP_MODE_ocb</td><td>OCB</td></tr><tr><td>WRP_MODE_xts</td><td>XTS128</td></tr><tr><td>WRP_MODE_bc</td><td>BC</td></tr></table></body></html>

表 3-3 WRP_CIPHER_ctrl 操作类型  

<html><body><table><tr><td>类别</td><td>类型宏</td><td>功能</td><td>备注</td></tr><tr><td>设</td><td>WRP_CIPH_CTRL_setiv</td><td>(重新)设置初始化向量</td><td></td></tr><tr><td>置 或 获</td><td>WRP_CTRL_TYPE_gcm_set aad</td><td>设置明文待认证数据(仅gcm)</td><td></td></tr><tr><td>取 密 码</td><td>WRP_CTRL_TYPE_gcm_get tag</td><td>输出认证数据(仅gcm)</td><td></td></tr><tr><td>数</td><td>WRP_CTRL_TYPE_gcm_ch ecktag</td><td>验证认证数据(仅gcm)</td><td>输入数据来源为 WRP_CTRL TYPE gC</td></tr><tr><td>据</td><td>WRP_CIPH_CTRL_gcm_set iv</td><td>设置初始化向量(仅gcm)</td><td></td></tr><tr><td></td><td>WRP_CIPH_CTRL_ccm_set aad</td><td>设置明文待认证数据(仅ccm)</td><td></td></tr><tr><td></td><td>WRP_CIPH_CTRL_ccm_get</td><td>输出认证数据(仅ccm)</td><td></td></tr><tr><td></td><td>tag WRP_CIPH_CTRL_ccm_ch</td><td>验证认证数据(仅ccm)</td><td></td></tr><tr><td></td><td>ecktag WRP_CIPH_CTRL_ccm_set iv</td><td>设置初始化向量(仅ccm)</td><td></td></tr><tr><td></td><td>WRP_CIPH_CTRL_ccm_set plainlen</td><td>设置待加密明文长度(仅ccm)</td><td>利用 WRP_CIPHER_ctrl （WRP_CIPHER_C TX *ctx,uint32_t ctrl_flag, void *data, uint32_t datalen) 设置明文长度，参数</td></tr><tr><td></td><td>WRP_CIPH_CTRL_ccm_set taglen</td><td>设置tag长度(仅ccm)</td><td>认传NULL即可; datalen 需传入 利用 WRP_CIPHER_ctrl （WRP_CIPHER_C TX *ctx, uint32_t</td></tr></table></body></html>

<html><body><table><tr><td></td><td></td><td>ctrl_flag, void *data, uint32_t datalen) 设置明文长度，参数 data为无用参数，默 认传NULL即可;</td></tr><tr><td>WRP_CIPH_CTRL_ocb_set aad</td><td>设置明文待认证数据(仅ocb)</td><td>datalen需传入</td></tr><tr><td>WRP_CIPH_CTRL_ocb_gett ag</td><td>输出认证数据(仅ocb)</td><td></td></tr><tr><td>WRP_CIPH_CTRL_ocb_che cktag</td><td>验证认证数据(仅ocb)</td><td></td></tr><tr><td>WRP_CIPH_CTRL_ocb_seti V</td><td>设置初始化向量(仅ocb)</td><td></td></tr><tr><td>WRP_CIPH_CTRL_ocb_sett aglen</td><td>设置输出认证数据长度(仅ocb)</td><td></td></tr></table></body></html>

表3-4 算法模式信息接口  

<html><body><table><tr><td colspan="3">※接口名为”WRP"+算法＋模式的拼接，中间用”_"隔开，如： WRP_aes_ctr()</td></tr><tr><td>算法</td><td>密钥强度(bit) (xts 模式*2)</td><td>支持模式：ecb/cbc/ctr/ofb/gcm/ cfb/ccm/ocb/xts/bc</td></tr><tr><td>sm4</td><td>128</td><td rowspan="9">作为初始化接口的参数</td></tr><tr><td rowspan="3">aes</td><td>128</td></tr><tr><td>192</td></tr><tr><td>256</td></tr><tr><td rowspan="2">3des</td><td>128</td></tr><tr><td>192</td></tr><tr><td>wbsm4</td><td>128</td></tr><tr><td rowspan="2">wbaes</td><td>128</td></tr><tr><td>256</td></tr></table></body></html>

表3-5 SM4/AES 加密功能输入输出长度限制  

<html><body><table><tr><td colspan="7">支持ECB/CBC/CTR/GCM/CCM/OFB/CFB/XTS/BC/OCB 模式</td></tr><tr><td rowspan="2"></td><td colspan="5">输入 (字节) 密钥</td></tr><tr><td></td><td>待加密数据 iv</td><td> aad</td><td>taglen</td><td>加密 结果</td><td>输出 (字节) tag</td></tr><tr><td>ECB/ CBR/ OFB/ CFB/BC</td><td>16(sm4); 16/2/32(aes</td><td>不定长</td><td>16 iv)</td><td>无</td><td>无</td><td>不定无 长</td><td></td></tr><tr><td>XTS</td><td>32(sm4); (32/48/64)</td><td>大于等于16</td><td>16</td><td>无</td><td>无</td><td>不定无 长</td><td></td></tr><tr><td>GCM/ CCM</td><td>16(sm4); 16/24/32(aes</td><td>不定长</td><td>大于等</td><td>不定长</td><td>数值类</td><td>不定</td><td>不定长</td></tr></table></body></html>

<html><body><table><tr><td></td><td>）</td><td></td><td>于1</td><td></td><td>型：长 unsigned int （小于等 于16)</td><td></td><td></td></tr><tr><td>OCB</td><td>16(sm4); 16/24/32(aes ）</td><td>不定长</td><td>1~15</td><td>不定长</td><td>数值类 型： unsigned int （小于等 于16)</td><td>不定 长</td><td>不定长</td></tr></table></body></html>

表3-6 SM4/AES 解密功能输入输出长度限制  

<html><body><table><tr><td colspan="7">支持 ECB/CBC/CTR/GCM/CCM/OFB/CFB/XTS/BC/OCB 模式</td></tr><tr><td rowspan="2"></td><td colspan="5">输入 (字节)</td><td colspan="2">输出 (字节)</td></tr><tr><td>密钥</td><td>待解密数据</td><td>iv</td><td> aad</td><td> tag</td><td>解密 结果</td><td>Tag 验 证结果</td></tr><tr><td>ECB/ CBC/ OFB/ CFB/BC</td><td>16(sm4); 16/24/2(aes</td><td>不定长</td><td>16 不EC iv)</td><td>无</td><td>无</td><td>不定 长</td><td>无</td></tr><tr><td>XTS</td><td>32(sm4); (32/48/64)</td><td>大于等于16</td><td>16</td><td>无</td><td>无</td><td>不定无 长</td><td></td></tr><tr><td>GCM/ CCM</td><td>16(sm4); 16/24/32(aes ）</td><td>不定长</td><td>大于等 于1</td><td>不定长</td><td>不定长 （小于等 于16)</td><td>不定 长</td><td>输出错 误码</td></tr><tr><td>OCB</td><td>16(sm4); 16/24/32(aes ）</td><td>不定长</td><td>1~15</td><td>不定长</td><td>不定长 （小于等 于16)</td><td>不定 长</td><td>输出错 误码</td></tr></table></body></html>

表3-7 3DES16/24 加密功能输入输出长度限制  

<html><body><table><tr><td rowspan="2">支持ECB/CBC/CTR/OFB/CFB模式</td><td colspan="3"></td><td></td></tr><tr><td>输入 (字节) 密钥</td><td>待加密数据</td><td></td><td>输出 (字节)</td></tr><tr><td>ECB/CBC/ CTR/OFB/CFB</td><td>16/24</td><td>不定长</td><td>iv 8 (ECB不需iv)</td><td>加密结果 不定长</td></tr></table></body></html>

表3-8 3DES16/24 解密功能输入输出长度限制  

<html><body><table><tr><td colspan="5">支持ECB/CBC/CTR/OFB/CFB模式</td></tr><tr><td rowspan="2"></td><td colspan="3">输入 (字节)</td><td>输出 (字节)</td></tr><tr><td>密钥</td><td>待解密数据</td><td>iv</td><td>解密结果</td></tr><tr><td>ECB/CBC/ CTR/OFB/CFB</td><td>16/24</td><td>不定长</td><td>8 (ECB不需iv)</td><td>不定长</td></tr></table></body></html>

表 3-9 WBSM4/WBAES128/256 加密功能输入输出长度限制  

<html><body><table><tr><td colspan="7">支持ECB/CBC/CTR/OFB模式</td><td colspan="2"></td></tr><tr><td rowspan="2"></td><td colspan="6">输入 (字节)</td><td colspan="2">输出 (字节)</td></tr><tr><td>密钥</td><td>待加密数据</td><td>iv</td><td> aad</td><td> taglen</td><td>结果</td><td>加密tag</td><td></td></tr><tr><td>ECB/ CBC/ CTR/ OFB</td><td>324(WBSM4); 368/432(WBAE S)</td><td>不定长</td><td>16 （ECB 不需 iv)</td><td>无</td><td>无</td><td>长</td><td>不定无</td><td></td></tr></table></body></html>

表 3-10 WBSM4/WBAES128/256 解密功能输入输出长度限制  

<html><body><table><tr><td colspan="7">支持ECB/CBC/CTR/OFB模式</td></tr><tr><td rowspan="2"></td><td colspan="5">输入 (字节)</td><td colspan="2">输出 (字节)</td></tr><tr><td>密钥</td><td>待解密数据</td><td>iv</td><td> aad</td><td>tag</td><td>解密 结果</td><td>Tag验 证结果</td></tr><tr><td>ECB/ CBC/ CTR/ OFB</td><td>324(WBSM4); 368/432(WBAE S)</td><td>不定长</td><td>16 （ECB 不需 iv)</td><td>无</td><td>无</td><td>不定 长</td><td>无</td></tr></table></body></html>

### 3.1 实例化接口

## 3.1.1 新建密钥运算结构体

函数原型 WRP_CIPHER_CTX  \*WRP_CIPHER_CTX_new( void );  
功能描述 该接口将为密钥运算所需结构体分配内存，实例化结构体并返回其指针。  
参数 (空)返回值 运算结构体的指针。  
备注 最后使用完毕，需调用WRP_CIPHER_CTX_free 以释放分配的内存。

## 3.1.2 释放密钥运算结构体

函数原型 void  WRP_CIPHER_CTX_free( WRP_CIPHER_CTX \*ctx );  
功能描述 该接口将释放指针所指向的密钥运算结构体内存。  
参数 ctx [IN] 待释放的运算结构指针。  
返回值 无备注 应与 WRP_CIPHER_CTX_new() 配对使用，如同 malloc 与 free。

### 3.2 运算接口

## 3.2.1 对称运算初始化

函数原型 ERRNO WRP_CIPHER_start( WRP_CIPHER_CTX \*ctx,uint32_t blk_mode, WRP_KEY_CTX \*key );  
功能描述 该接口将密钥结构体key 的引用，保存到运算结构体ctx，并指定分组模式。  
参数 ctx [IN,OUT] 已实例化的运算结构的指针。blk_mode [IN] 选择分组运算模式，见表3-2。key [IN] 已装载相应算法密钥的密钥结构（参“算法密钥管理接口”节）。

返回值 ERRNO_OK： 成功。其他： 错误码。

备注 调用此接口前，应确保：

(1) ctx 需由 WRP_CIPHER_CTX_new() 返回；  
(2) key 经 WRP_KEY_init() 初始化，并且调用 WRP_KEY_genkey() 或WRP_KEY_set_key() 等接口装载密钥数据。

## 3.2.2 加密运算

函数原型 ERRNO WRP_CIPHER_encrypt(WRP_CIPHER_CTX \*ctx, const uint8_t \*iv,const uint8_t \*input, uint32_t inlen, uint8_t \*output, uint32_t \*outlen);功能描述 使用经过加密初始化的运算结构，加密输入数据。

参数 ctx [IN,OUT] 已初始化并指定了工作模式的密钥结构指针。iv [IN] 在除ECB 外的工作模式下，需要传入的初始化向量数据。input [IN] 待加密数据。inlen [IN] 待加密数据长度。output [OUT] 输出运算结果数据的缓冲区。outlen [IN,OUT] 传入缓冲区实际长度，输出结果长度。  
返回值 ERRNO_OK： 成功。其他： 错误码。  
备注 (1) 本接口调用前，应已调用 WRP_CIPHER_start() 装载密钥，调用一次即可；

## 3.2.3 解密运算

型 ERRNO WRP_CIPHER_decrypt(WRP_CIPHER_CTX \*ctx, const uint8_t \*iv,const uint8_t \*input, uint32_t inlen, uint8_t \*output, uint32_t \*outlen);述 使用经过加密初始化的运算结构，加密输入数据。

ctx [IN,OUT] 已初始化并指定了工作模式的密钥结构指针。  
iv [IN] 在除ECB 外的工作模式下，需要传入的初始化向量数据。  
input [IN] 待解密数据。  
inlen [IN] 待解密数据长度。  
output [OUT] 输出运算结果数据的缓冲区。outlen [IN,OUT] 传入缓冲区实际长度，输出结果长度。  
返回值 ERRNO_OK： 成功。其他： 错误码。  
备注 (1) 本接口调用前，应已调用 WRP_CIPHER_start() 装载密钥，调用一次即可；

## 3.2.4 三段式加解密

### ******* 三段式加密初始化

<html><body><table><tr><td>函数原型</td><td>ERRNO WRP_CIPHER_Encrypt_init(WRP_CIPHER_CTX *ctx, const uint8 *iv );</td></tr><tr><td>功能描述</td><td>该接口以“加密模式”，初始化运算结构体ctx，并初始化iv。</td></tr><tr><td>参数</td><td>ctx [IN,OUT]已实例化的运算结构的指针。</td></tr><tr><td></td><td>iv [IN]在除ECB外的工作模式下，需要设置的初始化向量数据。</td></tr><tr><td>返回值</td><td>ERRNO_OK:成功。 其他: 错误码。</td></tr><tr><td>备注</td><td>调用此接口前，应确保:</td></tr><tr><td></td><td>(1）本接口调用前，应已调用WRP_CIPHER_start()装载密钥，调用一次即可;</td></tr></table></body></html>

### ******* 三段式带缓冲加密

函数原型 ERRNO WRP_CIPHER_Encrypt_update(WRP_CIPHER_CTX \*ctx,const uint8_t \*input, uint32_t inlen, uint8_t \*output, uint32_t \*outlen);  
功能描述 使用经过加密初始化的运算结构，加密输入数据，尾部数据写入缓冲不输出。  
参数 ctx [IN,OUT] 已初始化并指定了工作模式的密钥结构指针。input [IN] 待加密数据。inlen [IN] 待加密数据长度。output [OUT] 输出运算结果数据的缓冲区。outlen [IN,OUT] 传入缓冲区实际长度，输出结果长度。  
返回值 ERRNO_OK： 成功。其他： 错误码。  
备注 (1) 该接口需在 WRP_CIPHER_Encrypt_init 之后调用；(2) 输出结果长度，不包含存储于密钥结构缓冲区中的数据长度。

### ******* 三段式结束缓冲加密

<html><body><table><tr><td>函数原型</td><td>ERRNO WRP_CIPHER_Encrypt_doFinal(WRP_CIPHER_CTX*ctx, uint8_t * output,uint32_t *outlen);</td></tr><tr><td>功能描述</td><td>加密输出WRP_CIPHER_Encrypt_update 保留的尾部缓冲数据。</td></tr><tr><td>参数</td><td>ctx [IN]已初始化并指定了工作模式的密钥结构指针。</td></tr><tr><td></td><td>output [OUT]输出尾部数据的缓冲区。</td></tr><tr><td>返回值</td><td>outlen [IN,OUT]传入缓冲区实际长度，输出结果长度。</td></tr><tr><td></td><td>ERRNO_OK: 成功。 其他: 错误码。</td></tr></table></body></html>

注 (1) 该接口需在 WRP_CIPHER_Encrypt_Update 后调用；(2) CTR/GCM/CCM/OFB/CFB/XTS/OCB 返回尾部数据，16 字节或不足 16 字节；(3) ECB/CBC/BC 模式加密时返回 16 字节尾部密文数据。

### ******* 三段式 / 两段式无缓冲加密

函数原型 ERRNO WRP_CIPHER_Encrypt_doCipher (WRP_CIPHER_CTX \*ctx,const uint8_t \*input, uint32_t inlen, uint8_t \*output, uint32_t \*outlen);功能描述 使用经过加密初始化的运算结构，加密输入数据，尾部数据不缓冲直接输出。

ctx [IN,OUT] 已初始化并指定了工作模式的密钥结构input [IN] 待加密数据。  
inlen [IN] 待加密数据长度。  
output [OUT] 输出运算结果数据的缓冲区。  
outlen [IN,OUT] 传入缓冲区实际长度，输出结果长度。

返回值 ERRNO_OK： 成功。其他： 错误码。

备注 (1) 该接口需在 WRP_CIPHER_Encrypt_init 或者WRP_CIPHER_Encrypt_update 后调用；(2) 该接口等价于一次 WRP_CIPHER_Encrypt_update $+$ 一次WRP_CIPHER_Encrypt_doFinal 调用。

### ******* 三段式解密初始化

函数原型 ERRNO WRP_CIPHER_Decrypt_init( WRP_CIPHER_CTX \*ctx, const uint8_t\*iv );  
功能描述 该接口以“解密模式”，初始化运算结构体ctx，并初始化iv。  
参数 ctx [IN,OUT] 已实例化的运算结构的指针。iv [IN] 在除ECB 外的工作模式下，需要设置的初始化向量数据。  
返回值 ERRNO_OK： 成功。其他： 错误码。  
备注 调用此接口前，应确保：(1) 本接口调用前，应已调用 WRP_CIPHER_start() 装载密钥；

### ******* 三段式带缓冲解密

原型 ERRNO WRP_CIPHER_Decrypt_update(WRP_CIPHER_CTX \*ctx,const uint8_t \*input, uint32_t inlen, uint8_t \*output, uint32_t \*outlen);描述 使用经过解密初始化的运算结构，解密输入数据，尾部数据写入缓冲不输出。

ctx [IN,OUT] 已初始化并指定了工作模式的密钥结构指针input [IN] 待解密数据。  
inlen [IN] 待解密数据长度。  
output [OUT] 输出运算结果数据的缓冲区。  
outlen [IN,OUT] 传入缓冲区实际长度，输出结果长度。  
返回值 ERRNO_OK： 成功。其他： 错误码。  
备注 (1) 该接口需在 WRP_CIPHER_Decrypt_init 之后调用；(2) 输出结果长度，不包含存储于密钥结构缓冲区中的数据长度。

### ******* 三段式结束缓冲解密

<html><body><table><tr><td>函数原型</td><td>ERRNO WRP_CIPHER_Decrypt_doFinal(WRP_CIPHER_CTX*ctx, uint8_t *tailout,uint32_t *outlen);</td></tr><tr><td>功能描述</td><td>解密输出WRP_CIPHER_Decrypt_update 保留的尾部缓冲数据。</td></tr><tr><td>参数 ctx</td><td>[IN]已初始化并指定了工作模式的密钥结构指针。</td></tr><tr><td></td><td>tailout [OUT]输出尾部数据的缓冲区。</td></tr><tr><td></td><td>outlen [IN,OUT]传入缓冲区实际长度，输出结果长度。</td></tr></table></body></html>

返回值 ERRNO_OK： 成功。

其他： 错误码。

备注 (1) 该接口需在 WRP_CIPHER_Decrypt_Update 后调用；(2) CTR/GCM/CCM/OFB/CFB/XTS/OCB 返回尾部数据，16 字节或不足 16 字节；(3) ECB/CBC/BC 模式解密时返回不足16 字节或者0 字节尾部明文数据。

### ******* 三段式 / 两段式无缓冲解密

函数原型 ERRNO WRP_CIPHER_Decrypt_doCipher (WRP_CIPHER_CTX \*ctx,const uint8_t \*input, uint32_t inlen, uint8_t \*output, uint32_t \*outlen);功能描述 使用经过解密初始化的运算结构，解密输入数据，尾部数据不缓冲直接输出。

ctx [IN,OUT] 已初始化并指定了工作模式的密钥结构input [IN] 待解密数据。  
inlen [IN] 待解密数据长度。  
output [OUT] 输出运算结果数据的缓冲区。  
outlen [IN,OUT] 传入缓冲区实际长度，输出结果长度。

返回值 ERRNO_OK： 成功。

其他： 错误码。

备注 (1) 该接口需在 WRP_CIPHER_Decrypt_init 或者WRP_CIPHER_Decrypt_update 后调用；(2) 该接口等价于一次 WRP_CIPHER_Decrypt_update $+$ 一次WRP_CIPHER_Decrypt_doFinal 调用。

### 3.3 辅助接口

## 3.3.1 其他操作

函数原型 ERRNO WRP_CIPHER_ctrl( WRP_CIPHER_CTX \*ctx, uint32_t ctrl_flag, void \*data, uint32_t datalen);

功能描述参数

该接口将以flag 指明的方式，利用参数进行操作，见表3-3。ctx [IN,OUT] 已装载密钥的运算结构的指针。ctrl_fla [IN] 宏，指明操作类型。gdata [IN/OUT] 可选指针参数。datalen [IN/OUT] 可选数值参数。

返回值 ERRNO_OK： 成功。其他： 错误码。

(1) 关 于 表 3-3 ， 对 于 不 同 类 型 宏 ， 除 WRP_CTRL_TYPE_(gcm/ccm/ocb)_checktag 和 WRP_CTRL_TYPE_(gcm/ccm/ocb)_gettag 此类宏外，对于其他类型宏，该接口需在 WRP_CIPHER_En/Decrypt_init 之后、Update/doCipher 之前调用；  
(2) WRP_CTRL_TYPE_(gcm/ccm/ocb)_checktag 和WRP_CTRL_TYPE_(gcm/ccm/ocb)_gettag 此类宏可在 doCipher/doFinal 之后调用；  
(3) WRP_CIPHER_encrypt 之前不可调用；  
(4) 当前配套gcm 模式算法的操作：设置AAD、导出Tag、验证Tag 等；  
(5) 当前配套ccm 模式算法的操作：设置AAD、导出Tag、验证Tag 等；  
(6) 当前配套ocb 模式算法的操作：设置AAD、导出Tag、验证Tag 等；  
(7) 当前配套非(ecb/gcm/ccm/ocb)模式算法的操作：(重新)设置初始化向量IV；  
(8) 具体参考，表 3-3 。

# 4 摘要算法

摘要算法封装为通用接口，目前支持 SM3、SHA 1、SHA256、SHA384 及 SHA512：

表4-1 摘要接口  

<html><body><table><tr><td>接口名称</td><td>功能</td></tr><tr><td colspan="2">摘要结构体初始化设置接口</td></tr><tr><td>WRP_DIGEST_CTX_neW</td><td>新建摘要运算结构体</td></tr><tr><td>WRP_DIGEST_CTX_free</td><td>释放摘要运算结构体</td></tr><tr><td colspan="2">运算接口</td></tr><tr><td>WRP_DIGEST_doDigest</td><td>一步式摘要算法</td></tr><tr><td>WRP_DIGEST_init</td><td>摘要算法初始化</td></tr><tr><td>WRP_DIGEST_update</td><td>读取数据</td></tr><tr><td>WRP_DIGEST_doFinal</td><td>输出摘要</td></tr></table></body></html>

表4-2 算法指定接口  

<html><body><table><tr><td colspan="2">算法接口</td><td>输出长度 (字节)</td></tr><tr><td>WRP_sm3</td><td>返回SM3的算法信息</td><td>32</td></tr><tr><td>WRP_sha1</td><td>返回SHA1的算法信息</td><td>20</td></tr><tr><td>WRP_sha256</td><td>返回SHA256的算法信息</td><td>32</td></tr><tr><td>WRP_sha384</td><td>返回SHA384的算法信息</td><td>48</td></tr></table></body></html>

<html><body><table><tr><td>WRP_sha512</td><td>返回SHA512的算法信息</td><td>64</td></tr></table></body></html>

表4-3 哈希算法输入输出长度限制  

<html><body><table><tr><td rowspan="2"></td><td>输入 (字节)</td><td>输出 (字节)</td></tr><tr><td>待哈希数据</td><td>哈希结果</td></tr><tr><td>SM3</td><td>不定长</td><td>32</td></tr><tr><td>SHA1/256/384/512</td><td>不定长</td><td>20/32/48/64</td></tr></table></body></html>

### 4.1 初始化接口

## 4.1.1 新建摘要运算结构体

函数原型 WRP_DIGEST_CTX  \*WRP_DIGEST_CTX_new( void );  
功能描述 该接口将为摘要运算所需结构体分配内存，实例化结构体并返回其指针。  
参数 (空)返回值 运算结构体的指针。  
备注 最后使用完毕，需调用WRP_DIGEST_CTX_free 以释放分配的内存。

## 4.1.2 释放摘要运算结构体

函数原型 void  WRP_DIGEST_CTX_free( WRP_DIGEST_CTX \*ctx );  
功能描述 该接口将释放指针所指向的摘要运算结构体内存。  
参数 ctx [IN] 待释放的运算结构指针。  
返回值 无备注 应与 WRP_DIGEST_CTX_new() 配对使用，如同 malloc 与 free。

### 4.2 运算接口

## 4.2.1 一步式摘要

函数原型 ERRNO WRP_DIGEST_doDigest( const void \*data, uint32_t data_len,uint8_t \*md, uint32_t \*md_size, const WRP_DIGEST \*digest);功能描述 使用已实例化的摘要结构，读取数据并输出摘要值。

参数 data [IN] 待摘要数据。data_len [IN] 待摘要数据长度。md [OUT] 输出摘要的缓冲区。md_size [IN,OUT] 传入缓冲区实际长度，输出摘要长度。digest [IN] 选择并传入 WRP_(alg)() 返回的指针，见表 4-2。  
返回值 ERRNO_OK： 成功。其他： 错误码。  
备注 该接口无需新建/释放运算结构体。

## 4.2.2 三段式摘要

### 4.2.2.1 三段式摘要初始化

函数原型 ERRNO WRP_DIGEST _init( WRP_DIGEST_CTX \*ctx, const WRP_DIGES\*digest );  
功能描述 将算法初始参数digest 装载到摘要运算结构体ctx。  
参数 ctx [IN,OUT] 已实例化的摘要结构指针。digest [IN] 选择并传入 WRP_(alg)() 返回的指针，见表 4-2。  
返回值 ERRNO_OK： 成功。其他： 错误码。  
备注 调用此接口前，应确保：(1) ctx 需由 WRP_DIGEST_CTX_new() 返回；

### 4.2.2.2 三段式读取数据

函数原型 ERRNO  WRP_DIGEST_update(WRP_DIGEST_CTX \*ctx, void \*data,uint32_t data_len);  
功能描述 使用已初始化的摘要结构，做摘要运算。  
参数 ctx [IN,OUT] 已调用init 初始化的摘要结构指针。data [IN] 待摘要数据。data_len [IN] 待摘要数据长度。  
返回值 ERRNO_OK： 成功。其他： 错误码。  
备注 无

### 4.2.2.3 三段式完成摘要

<html><body><table><tr><td>函数原型</td><td>ERRNO WRP_DIGEST_doFinal(WRP_DIGEST_CTX *ctx， uint8_t *md, uint32_t *md_size);</td></tr><tr><td>功能描述</td><td>使用已初始化并读入数据的摘要结构，输出摘要值。</td></tr><tr><td>参数 ctx</td><td>[IN]已调用Updata读完全部数据的摘要结构指针。</td></tr><tr><td>md</td><td>[OUT]输出摘要的缓冲区。</td></tr><tr><td></td><td>md_size [IN,OUT]传入缓冲区实际长度，输出摘要长度。</td></tr><tr><td>返回值</td><td>ERRNO_OK:成功。</td></tr><tr><td>其他:</td><td>错误码。</td></tr><tr><td>备注 无</td><td></td></tr></table></body></html>

# 5 消息认证码算法

消息认证算法封装为通用接口，目前支持 SM3、SHA 1、SHA256、SHA384 及SHA512：

表5-1 消息认证码接口  

<html><body><table><tr><td>接口名称</td><td>功能</td></tr><tr><td colspan="2">摘要结构体初始化设置接口</td></tr><tr><td>WRP_HMAC_CTX_new</td><td>新建HMAC运算结构体</td></tr><tr><td>WRP_HMAC_CTX_free</td><td>释放HMAC运算结构体</td></tr><tr><td colspan="2">运算接口</td></tr><tr><td>WRP_HMAC_init</td><td>运算初始化</td></tr><tr><td>WRP_HMAC_update</td><td>读取数据</td></tr><tr><td>WRP_HMAC_doFInal</td><td>输出消息认证码</td></tr><tr><td>WRP_HMAC_doDigest</td><td>一步式输出消息认证码</td></tr><tr><td>WRP_HMAC_get_len</td><td>获取消息认证码长度</td></tr></table></body></html>

表5-2 算法指定接口  

<html><body><table><tr><td colspan="2">算法接口</td><td>输出长度 (字节)</td></tr><tr><td>WRP_sm3</td><td>返回SM3的算法信息</td><td>32</td></tr><tr><td>WRP_sha1</td><td>返回SHA1的算法信息</td><td>20</td></tr><tr><td>WRP_sha256</td><td>返回SHA256的算法信息</td><td>32</td></tr><tr><td>WRP_sha384</td><td>返回SHA384的算法信息</td><td>48</td></tr><tr><td>WRP_sha512</td><td>返回SHA512的算法信息</td><td>64</td></tr></table></body></html>

表5-3 HMAC 算法输入输出长度限制  

<html><body><table><tr><td rowspan="2"></td><td colspan="2">输入 (字节)</td><td>输出 (字节)</td></tr><tr><td>密钥</td><td>待HMAC的数据</td><td>HMAC结果</td></tr><tr><td>SM3</td><td>大于0</td><td>不定长</td><td>32</td></tr><tr><td>SHA1/256/384/512</td><td>大于0</td><td>不定长</td><td>20/32/48/64</td></tr></table></body></html>

### 5.1 初始化接口

## 5.1.1 新建HMAC 结构体

函数原型 WRP_HMAC_CTX  \*WRP_HMAC_CTX_new( void );  
功能描述 该接口将为HMAC 运算结构体分配内存，实例化结构体并返回其指针。参数 （空）  
返回值 运算结构体的指针  
备注 最后使用完毕，需调用WRP_HMAC_CTX_free 以释放分配的内存。

## 5.1.2 释放HMAC 结构体

函数原型 void  WRP_HMAC_CTX_free( WRP_HMAC_CTX \*ctx );功能描述 该接口将释放指针所指向的HMAC 运算结构体内存。  
参数 ctx [IN] 待释放的运算结构指针  
返回值 无  
备注 应与 WRP_HMAC_CTX_new() 配对使用，如同 malloc 与 free。

### 5.2 运算接口

## 5.2.1 一步式消息认证

ERRNO WRP_HMAC_doDigest(const void \*data, uint32_t data_len, constvoid  \*key,  uint32_t  len ， uint8_t  \*md ， uint32_t  \*md_size ， constWRP_DIGEST \*digest)

该接口使用已实例化的HMAC 结构，读取数据并输出消息认证值。

参数 data [IN] 待认证数据。data_len [IN] 待认证数据长度。key [IN] 密钥len [IN] 密钥长度md [OUT] 输出认证数据的缓冲区。md_size [IN,OUT] 传入缓冲区实际长度，输出认证数据长度。digest [IN] 选择并传入 WRP_(alg)() 返回的指针，见表 5-2。  
返回值 ERRNO_OK： 成功。其他： 错误码。  
备注 该接口无需新建/释放运算结构体。

## 5.2.2 三段式消息认证

### 5.2.2.1 三段式运算初始化

函数原型 ERRNO WRP_HMAC_init( WRP_HMAC_CTX \*ctx,const void \*key,uint32_tlen, const WRP_DIGEST \*digest );  
功能描述 该接口将初始参数digest 以及共享密钥装载到摘要运算结构体ctx。  
参数 ctx [IN,OUT] 已实例化的HMAC 结构指针。key [IN] 共享密钥len [IN] 密钥长度digest [IN] 选择并传入 WRP_(alg)() 返回的指针  
返回值 ERRNO_OK： 成功。其他： 错误码。  
备注 调用此接口前，应确保：(1) ctx 需由 WRP_HMAC_CTX_new() 返回；

### 5.2.2.2 三段式读取数据

数原型 ERRNO  WRP_HMAC_update(WRP_HMAC_CTX  \*ctx,  const  void  \*data,uint32_t data_len);  
能描述 该接口使用已初始化的HMAC 结构，做消息认证运算。  
数 ctx [IN,OUT] 已调用init 初始化的HMAC 结构指针。data [IN] 待认证数据。data_len [IN] 待认证数据长度。

返回值 ERRNO_OK： 成功其他： 错误码备注 无

### 5.2.2.3 三段式完成消息认证

函数原型 ERRNO WRP_HMAC_doFinal(WRP_HMAC_CTX \*ctx, uint8_t \*md, uint32_t\*md_size);  
功能描述 该接口使用已初始化并读入数据的消息认证结果，输出认证数据值  
参数 ctx [IN] 已调用Updata 读完全部数据的HMAC 结构指针。md [OUT] 输出认证数据的缓冲区。md_size [IN,OUT] 传入缓冲区实际长度，输出认证数据长度。  
返回值 ERRNO_OK： 成功。其他： 错误码。  
备注 无

附录A（规范性附录）错误代码定义

<html><body><table><tr><td colspan="3">错误代码标识</td></tr><tr><td>宏描述</td><td>预定义值</td><td>说明</td></tr><tr><td>ERRNO_OK</td><td>Ox00000000</td><td>操作成功</td></tr><tr><td></td><td></td><td></td></tr><tr><td>ERRNO_WRP_MASK</td><td>0x05000000</td><td>框架层通用错误码前 缀</td></tr><tr><td>ERRNO_WRP_NULL_CTX</td><td>ERRNO_WRP_MASK + 0x01</td><td>空结构体</td></tr><tr><td>ERRNO_WRP_NULL_PTR</td><td>ERRNO_WRP_MASK + 0x02</td><td>空指针</td></tr><tr><td>ERRNO_WRP_MALLOC</td><td>ERRNO_WRP_MASK +</td><td>内存分配失败</td></tr><tr><td>ERRNO_WRP_NO_INIT</td><td>0x03 ERRNO_WRP_MASK + 0x05</td><td>未执行初始化</td></tr><tr><td>ERRNO_WRP_LACK_METHOD</td><td>ERRNO_WRP_MASK +</td><td>内部错误</td></tr><tr><td>ERRNO_WRP_NOT_SUPPORT</td><td>0x06 ERRNO_WRP_MASK +</td><td>不支持的模式或算法</td></tr><tr><td>ERRNO_WRP_INVALID_SETLEN</td><td>0x07 ERRNO_WRP_MASK +</td><td>传入数据长度非法</td></tr><tr><td>ERRNO_WRP_INVALID_BUFLEN</td><td>0x0A ERRNO_WRP_MASK + 0x0B</td><td>传入缓冲区长度非法</td></tr><tr><td>ERRNO_WRP_INVALID_GETLEN</td><td>ERRNO_WRP_MASK+</td><td>指定传出长度非法</td></tr><tr><td></td><td>0x0C</td><td></td></tr><tr><td>ERRNO_WRP_KEY_MASK</td><td>0x07000000</td><td>密钥错误码前缀</td></tr><tr><td>ERRNO_WRP_KEY_BITS_NOT_SUP PORT</td><td>ERRNO_WRP_KEY_MASK +0x07</td><td>不支持的密钥强度</td></tr><tr><td>ERRNO_WRP_KEY_INVALID_KEY</td><td>ERRNO_WRP_KEY_MASK + 0x0A</td><td>密钥数据非法</td></tr><tr><td>ERRNO_WRP_KEY_MISMATCH</td><td>ERRNO_WRP_KEY_MASK +0x0B</td><td>密钥与算法不匹配</td></tr><tr><td>ERRNO_WRP_KEY_OPER_DENIED</td><td>ERRNO_WRP_KEY_MASK + 0x0C</td><td>拒绝操作</td></tr><tr><td>ERRNO_WRP_KEY_FILE_EXIST</td><td>ERRNO_WRP_KEY_MASK + 0xA0</td><td>输出文件已存在</td></tr><tr><td>ERRNO_WRP_KEY_IMPORT_NOT_ SUPPORT</td><td>ERRNO_WRP_KEY_MASK + 0xA1</td><td>密钥导入不支持</td></tr><tr><td>ERRNO_WRP_KEY_EXPORT_NOT_ SUPPORT</td><td>ERRNO_WRP_KEY_MASK + 0xA2</td><td>密钥导出不支持</td></tr><tr><td></td><td></td><td></td></tr><tr><td>ERRNO_WRP_CIPH_MASK</td><td>Ox08000000</td><td>对称算法错误码前缀</td></tr><tr><td>ERRNO_WRP_CIPH_DECRYPT</td><td>ERRNO_WRP_CIPH_MAS</td><td>解密失败</td></tr></table></body></html>

<html><body><table><tr><td></td><td>K+ 0x0A</td><td></td></tr><tr><td>ERRNO_WRP_CIPH_PAD_ALIGN</td><td>ERRNO_WRP_CIPH_MAS K+ 0x0B</td><td>数据长度不匹配填充 模式</td></tr><tr><td>ERRNO_WRP_CIPH_GCM_AUTH</td><td>ERRNO_WRP_CIPH_MAS K+ 0xAE</td><td>GCM模式解密认证失 败</td></tr><tr><td>ERRNO_WRP_CIPH_CCM_AUTH</td><td>ERRNO_WRP_CIPH_MAS K + OxAF</td><td>CCM模式解密认证失 败</td></tr><tr><td>ERRNO_WRP_CIPH_OCB_AUTH</td><td>ERRNO_WRP_CIPH_MAS K + 0xB0</td><td>OCB模式解密认证失 败</td></tr><tr><td>ERRNO_WRP_CIPH_IV_NO_INIT</td><td>ERRNO_WRP_NO_INIT + 0x10</td><td>GCM/CCM/OCB模 式未初始化IV</td></tr><tr><td>ERRNO_WRP_CIPH_AAD_NO_INIT</td><td>ERRNO_WRP_NO_INIT + 0x20</td><td>GCM/CCM/OCB模 式未初始化AAD</td></tr><tr><td>ERRNO_WRP_CIPH_CCM_PLAINL EN_NO_INIT</td><td>ERRNO_WRP_NO_INIT + 0x30</td><td>CCM模式未初始化明 文长度</td></tr><tr><td>ERRNO_WRP_CIPH_CCM_TAGLEN _NO_INIT</td><td>ERRNO_WRP_NO_INIT + 0x40</td><td>CCM模式未初始化 tag长度</td></tr><tr><td>ERRNO_WRP_CIPH_INIT_MISMAT CH</td><td>ERRNO_WRP_CIPH_MAS K+ 0xD1</td><td>加解密模式不匹配初 始化</td></tr><tr><td>ERRNO_WRP_PUB_MASK</td><td>0x0A000000</td><td>公钥算法错误码前缀</td></tr><tr><td>ERRNO_WRP_PUBCIPH_VERIFY</td><td>ERRNO_WRP_PUB_MASK</td><td>验签失败</td></tr><tr><td>ERRNO_WRP_PUBCIPH_BAD_PAR</td><td>+ 0x0A ERRNO_WRP_PUB_MASK</td><td>参数数据错误</td></tr><tr><td>M ERRNO_WRP_PUBCIPH_LACK_PA</td><td>+0x0B ERRNO_WRP_PUB_MASK</td><td>内部错误</td></tr><tr><td>RM ERRNO_WRP_PUBCIPH_DIGEST</td><td>+ 0x0C ERRNO_WRP_PUB_MASK</td><td>摘要错误</td></tr><tr><td>ERRNO_WRP_PUBCIPH_GET_RAN</td><td>+0x0D ERRNO_WRP_PUB_MASK</td><td>随机数生成错误</td></tr><tr><td>D ERRNO_WRP_PUBCIPH_DECRYPT</td><td>+0x0E ERRNO_WRP_PUB_MASK</td><td>解密失败</td></tr><tr><td></td><td>+ Ox0F</td><td></td></tr><tr><td>ERRNO_WRPIO_MASK</td><td>0x03000000</td><td>读写层通用错误码前</td></tr><tr><td>ERRNO_WRPIO_READ_ERR</td><td>ERRNO_WRPIO_MASK+</td><td>缀 文件读取错误</td></tr><tr><td>ERRNO_WRPIO_EOF_REACHED</td><td>0x03 ERRNO_WRPIO_MASK</td><td>文件总长小于读取长</td></tr><tr><td>ERRNO_WRPIO_WRITE_ERR</td><td>0x04 ERRNO_WRPIO_MASK+</td><td>度 文件写入错误</td></tr><tr><td>ERRNO_WRPIO_FILE_ERR</td><td>0x05 ERRNO_WRPIO_MASK +</td><td>文件打开失败</td></tr><tr><td></td><td>0x07</td><td></td></tr><tr><td>ERRNO_WRPIO_MAX_LENGTH_R EACHED</td><td>ERRNO_WRPIO_MASK + 0x0A</td><td>文件全路径名称过长</td></tr></table></body></html>

![](images/3f7c319a59c3b7376a18598ad442a641f7c1fccb766661a95995f0bc743d0cec.jpg)

### 附录B（规范性附录）密钥管理接口调用流程

![](images/3195cfd1a8ea62a4de640ae4ec43b4540ccdd3dedaa0e674f1bcd12f9e4051f0.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个流程图，描述了与WRP_KEY相关的操作步骤和条件。流程图从顶部开始，以红色文字说明了接口命名规则，即WRP_KEY_后跟算法名拼接，例如WRP_KEY_rsa()。

接下来的步骤包括：

1. WRP_KEY_CTX_new：创建一个新的上下文。
2. WRP_KEY_init：初始化上下文。

在WRP_KEY_init之后，流程分为两个分支：

- 针对SM2/WBSM2的必选操作：
  - WRP_EC_GROUP_new_sn2：创建一个新的椭圆曲线群。
  - WRP_KEY_ctrl + WRP_KEY_CTRL_SET_EXTRA_PARM：设置额外参数。

- 针对WRP_KEY_import的可选操作：
  - WRP_KEY_ctrl + WRP_KEY_CTRL_WB_SET_PATH或WRP_KEY_ctrl + WRP_KEY_CTRL_DEVICE_BIND：设置路径或设备绑定。

然后，流程继续到三个并行的操作：

- WRP_KEY_genkey：生成密钥。
- WRP_KEY_set_key：设置密钥。
- WRP_KEY_import：导入密钥。

在这些操作之后，针对WRP_KEY_export的可选操作：

- WRP_KEY_get_key：获取密钥。
- WRP_KEY_export：导出密钥。

最后，流程结束于WRP_KEY_CTX_free：释放上下文。

在流程图的右侧，有一个备注部分，解释了绿色虚线表示指定条件下操作，而红色虚线框表示多个操作可选择其一。
```


### 附录C（规范性附录）非对称算法接口-签名验签调用流程

![](images/c2eb2d183a9e1c502b5a020c9140707a44cba2c6a565a9f4c528f617a7ce5988.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个流程图，描述了在使用WRP_KEY系列函数进行密钥管理和公钥操作时的步骤和选项。流程图从顶部开始，以WRP_KEY_rsa()函数为例，展示了如何通过WRP_KEY_CIX_new、WRP_KEY_init等函数初始化密钥上下文，并根据需要选择不同的控制参数（如WRP_KEY_CTRL_SET_PATH或WRP_KEY_CTRL_DEVICE_BIND）来设置密钥路径或设备绑定。

接下来，流程图分为两个主要分支：一个针对SM2/WBSM2算法，另一个针对WRP_KEY_import操作。对于SM2/WBSM2算法，必须选择WRP_EC_GROUP_new_sn2+WRP_KEY_ctrl+WRP_KEY_CTRL_SET_EXTRA_PARAM参数；而对于WRP_KEY_import操作，则可以选择WRP_KEY_ctrl+WRP_KEY_CTRL_WB_SET_PATH或WRP_KEY_ctrl+WRP_KEY_CTRL_DEVICE_BIND参数。

在完成密钥生成（WRP_KEY_genkey）、密钥设置（WRP_KEY_set_key）或密钥导入（WRP_KEY_import）后，流程继续到WRP_PUBCPH_CIX_new和WRP_PUBCPH_init函数，用于初始化公钥上下文。之后，根据需要执行签名（WRP_PUBCPH_sign或WRP_PUBCPH_doSign）或验证（WRP_PUBCPH_verify或WRP_PUBCPH_doVerify）操作。

最后，流程图展示了如何通过WRP_KEY_CIX_free和WRP_PUBCPH_CIX_free函数释放密钥和公钥上下文资源，并在针对SM2/WBSM2算法的情况下，通过WRP_EC_GROUP_free函数释放椭圆曲线组资源。

备注部分指出，指定条件下的多个操作是可选的，具体取决于实际应用场景的需求。
```


![](images/bb39618a9ec8da2c3fbacae91b367052bc132d3fe90b845de624a98241216b37.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个流程图的示例，用于说明在编程或系统设计中如何处理条件操作和选择操作。图片分为两个主要部分：

1. **指定条件下操作**：
   - 这部分用蓝色背景表示，表示在特定条件下执行的操作。
   - 有一个绿色箭头指向右侧，表示流程的下一步。

2. **多个操作可选择其一**：
   - 这部分用白色背景表示，表示在多个操作中选择一个执行。
   - 有一个红色虚线框，表示可以选择的操作之一。

整体来看，这个流程图展示了在满足特定条件时执行某个操作，然后从多个操作中选择一个继续执行的逻辑。这种结构常见于编程中的条件语句和选择语句，例如if-else语句和switch-case语句。
```


### 附录D（规范性附录）非对称算法接口-加密解密调用流程

![](images/badf885577fa25cb4d28740046c8eb660fe293249feac969dd4f1daee26caa57.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个流程图，描述了在特定条件下进行密钥管理和公钥操作的步骤。流程图从顶部开始，以红色文字说明了接口命名规则，即WRP_KEY_与算法名拼接，例如WRP_KEY_rsa()。

接下来，流程图通过一系列蓝色矩形框和箭头展示了各个步骤：

1. WRP_KEY_CTX_new：创建一个新的密钥上下文。
2. WRP_KEY_init：初始化密钥。
3. 在这一步之后，流程图分为两个分支：
   - 针对SM2/WBSM2的必选操作：包括WRP_EC_GROUP_new_sm2、WRP_KEY_ctrl+WRP_KEY_CTRL_SET_EXTRA_PARAM等。
   - 针对WRP_KEY_import的可选操作：包括WRP_KEY_ctrl+WRP_KEY_CTRL_WEB_SET_PATH或WRP_KEY_ctrl+WRP_KEY_CTRL_DEVICE_BIND等。
4. 接下来是三个并列的操作：WRP_KEY_genkey（生成密钥）、WRP_KEY_set_key（设置密钥）和WRP_KEY_import（导入密钥），这些操作用红色虚线框起来，表示它们是多个操作中可选择其一。
5. 然后是WRP_PUBCPH_CTX_new和WRP_PUBCPH_init，分别用于创建和初始化公钥上下文。
6. 接着是WRP_PUBCPH_encrypt或WRP_PUBCPH_decrypt，用于加密或解密操作。
7. 最后是清理步骤，包括WRP_KEY_CTX_free、WRP_PUBCPH_CTX_free和WRP_EC_GROUP_free，其中WRP_EC_GROUP_free是针对SM2/WBSM2的必选操作。

在流程图的右下角，有一个备注，解释了绿色虚线表示指定条件下操作，而红色虚线表示多个操作可选择其一。
```


### 附录E（规范性附录）

![](images/73a097ad16915f3e0d851ef530b29d496e742beddb54b2f78298c343feb7fe57.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个对称算法接口的加密解密调用流程图。流程图详细描述了从创建密钥到释放密钥的整个过程，包括密钥生成、设置密钥、导入密钥、创建和启动加密/解密上下文、执行加密或解密操作以及最终释放资源等步骤。每个步骤都用矩形框表示，并通过箭头连接，展示了各个步骤之间的顺序关系。此外，流程图中还标注了一些特定条件下的操作和可选操作，以虚线和红色虚线框表示。
```


![](images/3c8380788073d032737283c233a7fb023cc1b9218b17a40bfa08e50e42c30bb8.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个哈希算法接口调用流程的规范性附录，具体步骤如下：

1. 判断是否为三段式：
   - 如果是三段式，则进入下一步。
   - 如果不是三段式，则直接调用 `WRP_DIGEST_digest+WRP_sm3` 接口。

2. 如果是三段式，则依次调用以下接口：
   - `WRP_DIGEST_CTX_new`
   - `WRP_DIGEST_init+WRP_sm3`（C接口名为 `WRP_` 与哈希算法名拼接，如：`WRP_sm3()`）
   - `WRP_DIGEST_update`
   - `WRP_DIGEST_doFinal`
   - `WRP_DIGEST_CTX_free`

这个流程图详细描述了在不同情况下如何调用哈希算法接口，确保了接口使用的规范性和一致性。
```


![](images/058faeb79600cc581d4865f3a74699fce7543f5da8a01d13676f1dd127adcb95.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片展示了一个消息认证码算法接口调用流程的规范性附录，具体步骤如下：

1. 判断是否为三段式：
   - 如果是三段式，则进入下一步。
   - 如果不是三段式，则直接调用`_HMAC_doDigest + WRP_sm3`（接口名为`a WRP_a`与哈希算法名拼接，如：`WRP_sm3()`）。

2. 如果是三段式，首先调用`WRP_HMAC_CTX_new`。

3. 然后调用`WRP_HMAC_init + WRP_sm3`（C接口名为`a WRP_a`与哈希算法名拼接，如：`WRP_sm3()`）。

4. 接着调用`WRP_HMAC_update`。

5. 再调用`WRP_HMAC_doFinal`。

6. 最后调用`WRP_HMAC_CTX_free`。

这个流程图详细描述了在不同情况下如何调用消息认证码算法接口，确保数据的安全性和完整性。
```
