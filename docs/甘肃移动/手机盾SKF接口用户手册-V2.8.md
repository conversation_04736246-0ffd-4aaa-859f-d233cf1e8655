# 手机盾SKF 接口用户手册

三未信安科技股份有限公司V2.3

SWXA-CMMI-SI-50

### 欢迎使用三未信安手机盾签名认证系统

Copyright (c) 2015 SanSec

版权所有

本出版物的内容将做不定期性的变动，且不会另行通知。更改的内容将不会补充到本出版物。且会在本手册发行新版本时予以付梓印刷。本公司不做任何明示或默许担保，其中包括本手册的内容的适售性或符合特定使用目的的默许担保。

本公司依中华人民共和国著作权法，享有及保留一切著作之专属权力，未经三未信安科技股份有限公司事先书面同意，不得对本手册有增删、改编、翻印、改造或仿制的行为。

三未信安科技股份有限公司

# 目录

### 手机盾SKF 接口用户手册.

第1 章 产品介绍.. 1  
1.1 产品简介... . 1  
1.2 产品功能... 1  
第2 章 终端集成....  
2.1 iOS 集成.. 1  
2.1.1 软件清单..... 1  
2.1.2 集成流程..  
2.1.3 Udid 获取接口.. 1  
2.1.4 调用文件改成 Object- $\cdot { \mathsf { C } } + +$ Source...... ... 1  
2.2 Android 集成.. 1  
2.2.1 软件清单... 1  
2.2.2 集成流程.. . 1  
2.2.3 权限.. 1  
2.2.4 预置数据.. 2  
2.2.5 配置文件及说明.. 2  
第3 章 接口集成.... . 2  
3.1 参考规范.. 2  
3.2 接口函数.. 2  
3.2.1 设备管理.. ... 2  
3.2.2 访问控制.. . 4  
3.2.3 应用管理.. 5  
3.2.4 容器管理.. .. 7  
3.2.5 密码服务.. . 9  
3.2.6 扩展服务.. . 24  
3.2.7 证书服务.. .. 24  
3.2.8 接口扩展.. .... 24  
附录A 错误代码定义和说明.. .. 26  
附录B 公司简介.. .29  
附录C 联系方式.. ... 30  
北京总部.. .. 30  
上海分公司... ..30  
广州分公司.. ..30  
济南研发中心... .30

修改记录：  

<html><body><table><tr><td>时间</td><td>内容</td><td>修改人</td></tr><tr><td>2023.09.27</td><td>修改SKF_ExportCertificate后两个接口参数说明</td><td>胡昊</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td></tr></table></body></html>

# 第1章 产品介绍

### 1.1 产品简介

该手册服务于手机盾签名认证系统SKF 运算接口库，指导内容包括运算库的接口使用说明和集成方式说明。

### 1.2 产品功能

该产品主要是在基于手机盾签名认证系统功能基础上，依据SKF 接口规范实现对应接口和功能。

# 第2章 终端集成

### 2.1 iOS 集成

## 2.1.1 软件清单

<html><body><table><tr><td>软件名称 版本</td><td colspan="2">作用</td></tr><tr><td colspan="3">手机盾签名认证系统SKF接口 V1.0</td></tr><tr><td>√ libMSSKF.a</td><td></td><td>手机盾SKF接口库</td></tr><tr><td>√ MSSKFTest</td><td></td><td>手机盾SKF调用</td></tr><tr><td>√ UDIDManager.h √ msskfapi.h</td><td>V1.0 V1.0</td><td>获取设备号头文件 SKF头文件</td></tr></table></body></html>

## 2.1.2 集成流程

把 libMSSKF.a、头文件 UDIDManager.h 和头文件 msskfapi.h 集成进工程。  
打开 Keychain Sharing,添加 Security.framework，完成。

2.1.3 Udid 获取接口 [UDIDManager getUDID];

2.1.4 调用文件改成 Object- $\cdot { \mathrm { C } } + +$ Source

### 2.2 Android 集成

## 2.2.1 软件清单

<html><body><table><tr><td>软件名称 版本</td><td></td><td>作用</td></tr><tr><td colspan="3">手机盾签名认证系统SKF接口 V1.0</td></tr><tr><td>√ CMakeLists.txt</td><td></td><td>cmake配置文件</td></tr><tr><td>√ jniLibs</td><td></td><td>为 SKF so 库</td></tr><tr><td>< native-lib.cpp</td><td></td><td>jni代码</td></tr><tr><td>√ SKFTest.cpp</td><td>V1.0</td><td>SKF接口简单调用</td></tr></table></body></html>

## 2.2.2 集成流程

示例demo 给出java 代码调用手机盾SKF 接口的过程。C 代码集成编译好的手机盾 so 库，然后将C 代码 jni 映射至 java 层（基于 android studio 自带 cmake），最后通过按钮触发调用。其中包含 arm64-v8a、armeabi-v7a 架构 so 库。具体 SKF 接口见 SKF 接口相关说明文档。

## 2.2.3 权限

手机盾SKF 接口需要对终端进行文件读写及与服务端进行网络交互，故需要 app 拥有文件读写权限及网络权限。

## 2.2.4 预置数据

App 在调用SKF 接口前，由于SKF 接口无法采集设备信息，故需要app 采集设备序列号保存以供接口调用使用。（采集设备号后，在 /sdcard 下创建文件夹 msskfkeystore 并在 msskfkeystore 下创建文件ANID.Data，将设备序列号写入该文件），此操作为必须操作。

## 2.2.5 配置文件及说明

SKF 相关配置文件应当在初始化时一并初始化至/sdcard/msskfkeystore  下，配置文件见下方<msskf.ini>。在没有msskf.ini 文件或配置项错误，程序会读取程序相关默认值，

应用名为手机盾系统用户名，用户设备号为预置数据 ANID.Data 中的值，手机盾系统以用户名及设备号构成唯一键对应唯一的一对密钥。同一用户下不同的设备号对应不同的密钥，同一设备下不同的用户对应不同的密钥。

测试程序中SKFTest.cpp Line:962 中使用测试用户名,可对该值进行修改测试。

# 第3章 接口集成

### 3.1 参考规范

《智能IC 卡及智能密码钥匙密码应用接口规范》

### 3.2 接口函数

## 3.2.1 设备管理

### 3.2.1.1 概述

设备管理主要完成设备的枚举设备、连接设备、断开连接、获取设备信息等操作。设备管理系列函数如表16 所示：

表16 设备管理系列函数  

<html><body><table><tr><td>函数名称</td><td>功能</td></tr><tr><td>SKF_EnumDev</td><td>枚举设备</td></tr><tr><td>SKF_ConnectDev</td><td>连接设备</td></tr><tr><td>SKF_DisconnectDev</td><td>断开连接</td></tr><tr><td>SKF_GetDevInfo</td><td>获取设备信息</td></tr></table></body></html>

### 3.2.1.2 枚举设备

函数原型 ULONG DEVAPI SKF_EnumDev(BOOL bPresent, LPSTR szNameList, ULONG \*pulSize)功能描述 获得当前系统中的设备列表。

<html><body><table><tr><td rowspan="3">参数</td><td>bPresent</td><td>[IN]为TRUE表示取当前设备状态为存在的设备列表。为FALSE表示</td></tr><tr><td>szNameList</td><td>取当前驱动支持的设备列表。 [OUT]设备名称列表。如果该参数为NULL，将由puISize返回所需要 的内存空间大小。每个设备的名称以单个‘\0'结束，以双‘\0'表示</td></tr><tr><td>pulSize</td><td>列表的结束。 [IN，OUT]输入时表示设备名称列表的缓冲区长度，输出时表示 szNameList所占用的空间大小。</td></tr><tr><td></td><td>返回值 SAR_OK：成功。</td><td></td></tr></table></body></html>

SAR_NOTINITIALIZEERR：传入的参数指针为空  
SAR_FILE_NOT_EXIST： 设备不存在  
SAR_DEVICE_REMOVED：当前存放配置文件与设备号的目录不存在、写入 ANID.Data 或  
RAMDOM.DATA 失败  
SAR_BUFFER_TOO_SMALL：读取设备号的缓冲区太小  
SAR_WRITEFILEERR：写入配置文件错误

### 3.2.1.3 连接设备

函数原型 ULONG DEVAPI SKF_ConnectDev (LPSTR szName, DEVHANDLE \*phDev)  
功能描述 通过设备名称连接设备，返回设备的句柄。  
参数 szName [IN] 设备名称。phDev [OUT] 返回设备操作句柄。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：传入的参数指针为空SAR_MEMORYERR：分配内存失败SKF_EnumDev 错误码：继承 SKF_EnumDev 错误码。

### 3.2.1.4 断开连接

函数原型 ULONG DEVAPI SKF_DisConnectDev (DEVHANDLE hDev)  
功能描述 断开一个已经连接的设备，并释放句柄。  
参数 hDev [IN] 连接设备时返回的设备句柄。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR： 传入的参数指针为空或设备未连接  
备注 如果该设备已被锁定，函数应首先解锁该设备。断开连接操作并不影响设备的权限状态。

### 3.2.1.5 获取设备信息

函数原型 ULONG DEVAPI SKF_GetDevInfo (DEVHANDLE hDev, DEVINFO \*pDevInfo)  
功能描述 获取设备的一些特征信息，包括设备标签、厂商信息、支持的算法等。  
参数 hDev [IN] 连接设备时返回的设备句柄。pDevInfo [OUT] 返回设备信息。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR： 传入的参数指针为空或设备未连接

### 3.2.1.6 锁定设备

函数原型 ULONG DEVAPI SKF_LockDev (DEVHANDLE hDev, ULONG ulTimeOut)  
功能描述 获得设备的独占使用权。  
参数 hDev [IN] 连接设备时返回的设备句柄。ulTimeOut [IN] 超时时间，单位为毫秒。如果为 0xFFFFFFFF 表示无限等待。（无意义）  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR： 传入的参数指针为空或设备未连接SWKR_APPLICATION_LOCKED：设备已被锁定

### 3.2.1.7 解锁设备

函数原型 ULONG DEVAPI SKF_UnlockDev (DEVHANDLE hDev)功能描述 释放对设备的独占使用权。参数 hDev [IN] 连接设备时返回的设备句柄。

返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR： 传入的参数指针为空或设备未连接

## 3.2.2 访问控制

### 3.2.2.1 概述

访问控制主要完成设备认证、PIN 码管理和安全状态管理等操作。访问控制系列函数如表所17：

表17 访问控制系列函数  

<html><body><table><tr><td>函数名称</td><td>功能</td></tr><tr><td>SKF_DevAuth</td><td>设备认证</td></tr><tr><td>SKF_ChangePIN</td><td>修改PIN</td></tr><tr><td>SKF_VerifyPIN</td><td>校验PIN</td></tr><tr><td>SKF_UnblockPIN</td><td>解锁PIN</td></tr></table></body></html>

### 3.2.2.2 设备认证

函数原型 ULONG DEVAPI SKF_DevAuth (DEVHANDLE hDev, BYTE \*pbAuthData，ULONG ulLen)  
功能描述 设备认证是设备对应用程序的认证。认证过程参见 $8 . 2 . 3 _ { \circ }$   
参数 hDev [IN] 连接时返回的设备句柄。pbAuthData [IN] 认证数据。ulLen [IN] 认证数据的长度。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：应用句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_INDATALENERR：认证数据长度错误SAR_DECRYPTPADERR：认证错误SAR_INVALIDPARAMERR：没有调用 SKF_GetRandom

### 3.2.2.3 修改 PIN

函数原型 ULONG  DEVAPI  SKF_ChangePIN  (HAPPLICATION  hApplication,  ULONG  ulPINType,  LPSTRszOldPin, LPSTR szNewPin, ULONG \*pulRetryCount)  
功能描述 调用该函数可以修改 Administrator PIN 和 User PIN 的值。如果原PIN 码错误导致验证失败，该函数会返回相应PIN 码的剩余重试次数，当剩余次数为0 时，表示PIN 已经被锁死。  
参数 hApplication [IN] 应用句柄。ulPINType [IN] PIN 类型，可为 ADMIN_TYPE 或 USER_TYPE。szOldPin [IN] 原 PIN 值。szNewPin [IN] 新 PIN 值。pulRetryCount [OUT] 出错后重试次数。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：应用句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_NAMELENERR：PIN 码长度错误SAR_USER_TYPE_INVALID：身份错误，非用户或管理员SAR_PIN_LEN_RANGE：验证 PIN 错误SAR_FILE_NOT_EXIST 文件不存在SAR_WRITEFILEERR 刷新 PIN 错误

### ******* 校验 PIN

<html><body><table><tr><td>函数原型</td><td>ULONG DEVAPI SKF_VerifyPIN (HAPPLICATION hApplication, ULONG uIPINType,LPSTR szPIN, ULONG *pulRetryCount)</td></tr><tr><td>功能描述</td><td>校验PIN码。校验成功后，会获得相应的权限，如果PIN码错误，会返回PIN码的重试 次数，当重试次数为0时表示PIN码已经锁死。</td></tr><tr><td>参数</td><td>hApplication [IN]应用句柄。 ulPINType</td></tr><tr><td></td><td>[IN] PIN类型。 SzPIN [IN] PIN值。</td></tr><tr><td></td><td>pulRetryCount [OUT]出错后返回的重试次数。</td></tr><tr><td>返回值</td><td>SAR_OK: 成功。</td></tr><tr><td></td><td>SAR_NOTINITIALIZEERR：应用句柄未初始化</td></tr><tr><td></td><td></td></tr><tr><td></td><td>SAR_INVALIDPARAMERR：传入的参数指针为空</td></tr><tr><td></td><td>SAR_NAMELENERR：PIN码长度错误</td></tr><tr><td></td><td>SAR_USER_TYPE_INVALID：身份错误，非用户或管理员</td></tr><tr><td></td><td>SAR_APPLICATION_NAME_INVALID：应用未存在</td></tr><tr><td></td><td>SAR_READFILEERR：读文件错误</td></tr><tr><td></td><td>SAR_PIN_INCORRECT：PIN不正确</td></tr><tr><td></td><td>SAR_FILE_NOT_EXIST文件不存在</td></tr><tr><td></td><td>SAR_WRITEFILEERR 刷新PIN错误</td></tr><tr><td></td><td>SKF_CreateApplication 错误码</td></tr></table></body></html>

### 3.2.2.5 解锁 PIN

<html><body><table><tr><td>函数原型</td><td>ULONG DEVAPI SKF_UnblockPIN (HAPPLICATION hApplication，LPSTR szAdminPIN，LPSTR szNewUserPIN，ULONG*pulRetryCount)</td></tr><tr><td>功能描述 参数</td><td>当用户的PIN码锁死后，通过调用该函数来解锁用户PIN码。 解锁后，用户PIN码被设置成新值，用户PIN码的重试次数也恢复到原值。 hApplication [IN]应用句柄。 szAdminPIN [IN]管理员PIN码。</td></tr><tr><td>返回值</td><td>szNewUserPIN [IN]新的用户PIN码。 pulRetryCount [OUT]管理员PIN码错误时，返回剩余重试次数。 SAR_OK: 成功。 SAR_NOTINITIALIZEERR：应用句柄未初始化 SAR_INVALIDPARAMERR：传入的参数指针为空 SAR_NAMELENERR：PIN码长度错误</td></tr><tr><td>备注</td><td>SKF_VerifyPIN错误码 验证完管理员PIN才能够解锁用户PIN码，如果输入的AdministratorPIN不正确或者已</td></tr></table></body></html>

经锁死，会调用失败，并返回Administrator PIN 的重试次数。

## 3.2.3 应用管理

### 3.2.3.1 概述

应用管理主要完成应用的创建、枚举、删除、打开、关闭等操作。应用管理系列函数如表18 所示：

表18 应用管理系列函数  

<html><body><table><tr><td>函数名称</td><td>功能</td></tr><tr><td>SKF_CreateApplication</td><td>创建应用</td></tr><tr><td>SKF_EnumApplication</td><td>枚举应用</td></tr><tr><td>SKF_DeleteApplication</td><td>删除应用</td></tr><tr><td>SKF_OpenApplication</td><td>打开应用</td></tr></table></body></html>

<html><body><table><tr><td>SKF_CloseApplication</td><td>关闭应用</td></tr></table></body></html>

### 3.2.3.2 创建应用

<html><body><table><tr><td>函数原型</td><td>szAdminPin, DWORD</td><td>ULONG DEVAPI SKF_CreateApplication(DEVHANDLE hDev， LPSTR SzAppName， LPSTR dwAdminPinRetryCount,LPSTR dwUserPinRetryCount,DWORD dwCreateFileRights, HAPPLICATION *phApplication)</td><td>szUserPin,</td><td>DWORD</td></tr><tr><td>功能描述</td><td colspan="4">创建一个应用。</td></tr><tr><td>参数</td><td>hDev</td><td>[IN]连接设备时返回的设备句柄。</td><td></td><td></td></tr><tr><td></td><td>szAppName</td><td>[IN]应用名称。</td><td></td><td></td></tr><tr><td></td><td>szAdminPin</td><td>[IN]管理员PIN。</td><td></td><td></td></tr><tr><td></td><td>dwAdminPinRetryCount</td><td>[IN]管理员PIN最大重试次数。</td><td></td><td></td></tr><tr><td></td><td>szUserPin</td><td>[IN]用户PIN。</td><td></td><td></td></tr><tr><td></td><td>dwUserPinRetryCount</td><td>[IN]用户PIN最大重试次数。</td><td></td><td></td></tr><tr><td></td><td>dwCreateFileRights</td><td>[IN]在该应用下创建文件和容器的权限，参见6.4.12权限类</td><td></td><td></td></tr><tr><td>返回值</td><td>phApplication</td><td>型。为各种权限的或值。 [OUT]应用的句柄。</td><td></td><td></td></tr><tr><td></td><td>SAR_OK: 成功。</td><td></td><td></td><td></td></tr><tr><td></td><td>SAR_NOTINITIALIZEERR：应用句柄未初始化</td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td>SAR_INVALIDPARAMERR：传入的参数指针为空</td><td></td><td></td><td></td></tr><tr><td></td><td>SAR_NAMELENERR：应用名长度错误</td><td></td><td></td><td></td></tr><tr><td></td><td>SAR_USER_ALREADY_LOGGED_IN用户未登录</td><td></td><td></td><td></td></tr><tr><td></td><td>SAR_FILE_NOT_EXIST、SAR_WRITEFILEERR：生成配置文件错误</td><td></td><td></td><td></td></tr><tr><td>备注</td><td>SKF_OpenApplication 错误码 权限要求：需要设备权限。</td><td></td><td></td><td></td></tr></table></body></html>

### 3.2.3.3 枚举应用

函数原型 ULONG DEVAPI SKF_EnumApplication(DEVHANDLE hDev, LPSTR szAppName,ULONG \*pulSize)  
功能描述 枚举设备中存在的所有应用。  
参数 hDev [IN] 连接设备时返回的设备句柄。szAppName [OUT] 返回应用名称列表, 如果该参数为空，将由pulSize 返回所需要的内存空间大小。每个应用的名称以单个‘ \0’结束，以双‘\0’表示列表的结束。pulSize [IN，OUT] 输入时表示应用名称的缓冲区长度，输出时返回szAppName 所占用的空间大小。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR： 传入的参数指针为空或设备未连接或SAR_APPLICATION_NOT_EXISTS：不存在应用

### 3.2.3.4 删除应用

函数原型 ULONG DEVAPI SKF_DeleteApplication(DEVHANDLE hDev, LPSTR szAppName)  
功能描述 删除指定的应用。  
参数 hDev [IN] 连接设备时返回的设备句柄。szAppName [IN] 应用名称。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：应用句柄未初始化

SAR_INVALIDPARAMERR：传入的参数指针为空SAR_USER_NOT_LOGGED_IN：没有经过认证SAR_FILE_NOT_EXIST、SAR_FILEERR：删除应用文件夹错误备注 权限要求：需要设备权限。

### 3.2.3.5 打开应用

函数原型 ULONG DEVAPI SKF_OpenApplication(DEVHANDLE hDev, LPSTR szAppName, HAPPLICATION\*phApplication)  
功能描述 打开指定的应用。  
参数 hDev [IN] 连接设备时返回的设备句柄。szAppName [IN] 应用名称。phApplication [OUT] 应用的句柄。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：应用句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_NAMELENERR：应用名长度错误SAR_APPLICATION_NOT_EXISTS：应用不存在SWKR_FILE_PERMISSION_ERROR：文件读写权限错误SWKR_STR_TO_HEX_ERROR：配置文件读取转换错误

### 3.2.3.6 关闭应用

函数原型 ULONG DEVAPI SKF_CloseApplication(HAPPLICATION hApplication)  
功能描述 关闭应用并释放应用句柄。  
参数 hApplication [IN]应用句柄。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：应用句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空  
备注 此函数不影响应用安全状态。

## 3.2.4 容器管理

3.2.4.1 概述

本规范提供的应用管理用于满足各种不同应用的管理，包括创建、删除、枚举、打开和关闭容器的操作。容器管理系列函数如表20 所示：

表20 容器管理系列函数  

<html><body><table><tr><td>函数名称</td><td>功能</td></tr><tr><td>SKF_CreateContainer</td><td>创建容器</td></tr><tr><td>SKF_DeleteContainer</td><td>删除容器</td></tr><tr><td>SKF_EnumContainer</td><td>枚举容器</td></tr><tr><td>SKF_OpenContainer</td><td>打开容器</td></tr><tr><td>SKF_CloseContainer</td><td>关闭容器</td></tr><tr><td>SKF_GetContainerType</td><td>获得容器类型</td></tr></table></body></html>

### 3.2.4.2 创建容器

函数原型 ULONG DEVAPI SKF_CreateContainer (HAPPLICATION hApplication, LPSTR szContainerName,HCONTAINER \*phContainer)  
功能描述 在应用下建立指定名称的容器并返回容器句柄。  
参数 hApplication [IN] 应用句柄。szContainerName [IN] ASCII 字符串，表示所建立容器的名称，容器名称的最大长度不能超过64 字节。

phContainer [OUT] 返回所建立容器的容器句柄。返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_USER_NOT_LOGGED_IN：非用户权限SWKR_CONTAINER_NAME_INVALID：输入容器名错误SWKR_FILE_DIR_ERROR：创建容器错误SKF_OpenContainer 错误码备注 权限要求：需要用户权限。

### 3.2.4.3 删除容器

函数原型 ULONG DEVAPI SKF_DeleteContainer(HAPPLICATION hApplication, LPSTR szContainerName)  
功能描述 在应用下删除指定名称的容器并释放容器相关的资源。  
参数 hApplication [IN] 应用句柄。szContainerName [IN] 指向删除容器的名称。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR： 传入的参数指针为空或应用句柄未初始化SAR_USER_NOT_LOGGED_IN：非用户权限SAR_FAIL：删除文件错误  
备注 权限要求：需要用户权限。

### 3.2.4.4 打开容器

函数原型 ULONG DEVAPI SKF_OpenContainer(HAPPLICATION hApplication,LPSTszContainerName,HCONTAINER \*phContainer)  
功能描述 获取容器句柄。  
参数 hApplication [IN] 应用句柄。szContainerName [IN] 容器的名称。phContainer [OUT] 返回所打开容器的句柄。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_USER_NOT_LOGGED_IN：非用户权限SWKR_CONTAINER_NAME_INVALID：输入容器名错误SWKR_CONTAINER_NOT_EXIST：不存在容器其他： 错误码。

### 3.2.4.5 关闭容器

函数原型 ULONG DEVAPI SKF_CloseContainer(HCONTAINER hCon  
功能描述 关闭容器句柄，并释放容器句柄相关资源。  
参数 hContainer [IN] 容器句柄。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空其他： 错误码。

### 3.2.4.6 枚举容器

函数原型 ULONG DEVAPI SKF_EnumContainer (HAPPLICATION hApplication,LPSTRszContainerName,ULONG \*pulSize)  
功能描述 枚举应用下的所有容器并返回容器名称列表。  
参数 hApplication [IN] 应用句柄。

szContainerName [OUT] 指向容器名称列表缓冲区，如果此参数为NULL 时，pulSize表示返回数据所需要缓冲区的长度，如果此参数不为 NULL 时，返回容器名称列表，每个容器名以单个‘\0’为结束，列表以双‘\0’结束。pulSize [IN，OUT] 输入时表示 szContainerName 缓冲区的长度，输出时表示容器名称列表的长度。返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空

### 3.2.4.7 获得容器类型

函数原型 ULONG DEVAPI SKF_GetContainerType(HCONTAINER hContainer, ULONG \*pulContainerType)  
功能描述 获取容器的类型  
参数 hContainer [IN] 容器句柄。pulContainerType [OUT] 获得的容器类型。指针指向的值为0 表示未定、尚未分配类型或者为空容器，为1 表示为RSA 容器，为2 表示为ECC容器。

返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空

## 3.2.5 密码服务

### 3.2.5.1 概述

密码服务函数提供对称算法运算、非对称算法运算、密码杂凑运算、密钥管理、消息鉴别码计算等功能。密码服务系列函数如表21 所示：

表21 密码服务系列函数  

<html><body><table><tr><td>SKF_GenRandom</td><td>生成随机数</td></tr><tr><td>SKF_GenExtRSAKey</td><td>生成外部 RSA 密钥对</td></tr><tr><td>SKF_GenRSAKeyPair</td><td>生成RSA签名密钥对</td></tr><tr><td>SKF_ImportRSAKeyPair</td><td>导入RSA加密密钥对</td></tr><tr><td>SKF_RSASignData</td><td>RSA签名</td></tr><tr><td>SKF_RSAVerify</td><td>RSA验签</td></tr><tr><td>SKF_ExtRSAPubKeyOperation</td><td>RSA 外来公钥运算</td></tr><tr><td>SKF_ExtRSAPriKeyOperation</td><td>RSA外来私钥运算</td></tr><tr><td>SKF_GenECCKeyPair</td><td>生成 ECC签名密钥对</td></tr><tr><td>SKF_ImportECCKeyPair</td><td>导入ECC加密密钥对</td></tr><tr><td>SKF_ECCSignData</td><td>ECC签名</td></tr><tr><td>SKF_ECCVerify</td><td>ECC验签</td></tr><tr><td>SKF_ExtECCEncrypt</td><td>ECC 外来公钥加密</td></tr><tr><td>SKF_ExtECCDecrypt</td><td>ECC 外来私钥解密</td></tr><tr><td>SKF_ExtECCSign</td><td>ECC 外来私钥签名</td></tr><tr><td>SKF_ExtECCVerify</td><td>ECC 外来公钥验签</td></tr><tr><td>SKF_GenerateAgreementDataWithECC</td><td>ECC 生成密钥协商参数并输出</td></tr><tr><td>SKF_GenerateKeyWithECC</td><td>ECC 计算会话密钥</td></tr></table></body></html>

<html><body><table><tr><td>SKF_GenerateAgreementDataAndKeyWithECC</td><td>ECC产生协商数据并计算会话密钥</td></tr><tr><td>SKF_ExportPublicKey</td><td>导出公钥</td></tr><tr><td>SKF_ImportSessionKey</td><td>导入会话密钥</td></tr><tr><td>SKF_SetSymmKey</td><td>明文导入会话密钥</td></tr><tr><td>SKF_EncryptInit</td><td>加密初始化</td></tr><tr><td>SKF_Encrypt</td><td>单组数据加密</td></tr><tr><td>SKF_EncryptUpdate</td><td>多组数据加密</td></tr><tr><td>SKF_EncryptFinal</td><td>结束加密</td></tr><tr><td>SKF_DecryptInit</td><td>解密初始化</td></tr><tr><td>SKF_Decrypt</td><td>单组数据解密</td></tr><tr><td>SKF_DecryptUpdate</td><td>多组数据解密</td></tr><tr><td>SKF_DecryptFinal</td><td>结束解密</td></tr><tr><td>SKF_DigestInit</td><td>密码杂凑初始化</td></tr><tr><td>SKF_Digest</td><td>单组数据密码杂凑</td></tr><tr><td>SKF_DigestUpdate</td><td>多组数据密码杂凑</td></tr><tr><td>SKF_DigestFinal</td><td>结束密码杂凑</td></tr><tr><td>SKF_Maclnit</td><td>消息鉴别码运算初始化</td></tr><tr><td>SKF_Mac</td><td>单组数据消息鉴别码运算</td></tr><tr><td>SKF_MacUpdate</td><td>多组数据消息鉴别码运算</td></tr><tr><td>SKF_MacFinal</td><td>结束消息鉴别码运算</td></tr><tr><td>SKF_CloseHandle</td><td>关闭密码对象句柄</td></tr></table></body></html>

### 3.2.5.2 生成随机数

函数原型 ULONG DEVAPI SKF_GenRandom (DEVHANDLE hDev, BYTE \*pbRandom, ULONG ulRandomLen)

功能描述 产生指定长度的随机数。

参数 hDev [IN] 设备句柄。pbRandom [OUT]返回的随机数。ulRandomLen [IN] 随机数长度。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：传入的参数指针为空或设备未连接SAR_GENRANDERR：生成随机数错误

3.2.5.3 生成外部RSA 密钥对

函数原型 ULONG DEVAPI SKF_GenExtRSAKey (DEVHANDLE hDev, ULONG ulBitsLen,RSAPRIVATEKEYBLOB \*pBlob)

功能描述 由设备生成RSA 密钥对并明文输出。

参数 hDev [IN]设备句柄。ulBitsLen [IN] 密钥模长。pBlob [OUT] 返回的私

返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：传入的参数指针为空或设备未连接SAR_GENRSAKEYERR：初始化 RSA 密钥错误  
备注： 生成的私钥只用于输出，接口内不做保留和计算。

### 3.2.5.4 生成RSA 签名密钥对

函数原型 ULONG DEVAPI SKF_GenRSAKeyPair (HCONTAINER hContainer, ULONG ulBitsLen, RSAPUBLICKEYBLOB \*pBlob)  
功能描述 生成RSA 签名密钥对并输出签名公钥。  
参数 hContainer [IN] 容器句柄。ulBitsLen [IN] 密钥模长。pBlob [OUT] 返回的RSA 公钥数据结构。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR： 传入的参数指针为空或应用句柄未初始化SAR_USER_NOT_LOGGED_IN：非用户权限SAR_MODULUSLENERR：RSA 模长非 1024 或 2048SAR_INDATAERR 、 SAR_OBJERR 、 SAR_PIN_LOCKED 、 SAR_USER_NOT_LOGGED_IN 、SAR_TIMEOUTERR：服务端注册密钥错误SAR_WRITEFILEERR：保存私钥或公钥文件错误SWKR_BASE64_DECODE_ERROR：生成的密钥错误SAR_KEYINFOTYPEERR：容器类型错误，非 RSA 类容器  
备注 权限要求：需要用户权限。

### 3.2.5.5 导入RSA 加密密钥对

函数原型 ULONG DEVAPI SKF_ImportRSAKeyPair (HCONTAINER hContainer, ULONG ulSymAlgId,BYTE \*pbWrappedKey, ULONG ulWrappedKeyLen,BYTE \*pbEncryptedData, ULONG ulEncryptedDataLen)  
功能描述 导入RSA 加密公私钥对。  
参数 hContainer [IN] 容器句柄。ulSymAlgId [IN] 对称算法密钥标识。pbWrappedKey [IN] 使用该容器内签名公钥保护的对称算法密钥。ulWrappedKeyLen [IN] 保护的对称算法密钥长度。pbEncryptedData [IN] 对称算法密钥保护的 RSA 加密私钥。私钥的格式遵循 PKCS\#1 v2.1: RSA Cryptography Standard 中的私钥格式定义。ulEncryptedDataLen [IN] 对称算法密钥保护的RSA 加密公私钥对长度。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_USER_NOT_LOGGED_IN：非用户权限SAR_KEYUSAGEERR：容器类型错误，非 RSA 类容器SAR_KEYINFOTYPEERR：对称算法标识错误，非 SM4SKF_RSADecrypt ， SKF_SetSymmKey ， SKF_DecryptInit ， SKF_DecryptUpdate ，SKF_DecryptFinal 错误码SAR_FILE_NOT_EXIST 文件不存在SAR_WRITEFILEERR 写入文件错误其他： 错误码。  
备注 权限要求：需要用户权限。

3.2.5.6 RSA 签名

函数原型 ULONG DEVAPI SKF_RSASignData(HCONTAINER hContainer, BYTE \*pbData, ULONG  ulDataLen,BYTE \*pbSignature, ULONG \*pulSignLen)  
功能描述 使用hContainer 指定容器的签名私钥，对指定数据 pbData 进行数字签名。签名后的结果存放到pbSignature 缓冲区，设置pulSignLen 为签名的长度。既可以本地运算也可以协同运算。

<html><body><table><tr><td rowspan="6">参数 返回值</td><td rowspan="5">hContainer pbData ulDataLen</td><td>[IN]用来签名的私钥所在容器句柄。</td></tr><tr><td>[IN]被签名的数据。</td></tr><tr><td>[IN]签名数据长度，应不大于RSA 密钥模长-11。</td></tr><tr><td>pbSignature [OUT]存放签名结果的缓冲区指针，如果值为NULL，用于取得签名结果</td></tr><tr><td>长度。</td></tr><tr><td rowspan="2">pulSignLen SAR_OK:</td><td>[IN，OUT]输入时表示签名结果缓冲区大小，输出时表示签名结果长</td></tr><tr><td></td></tr><tr><td rowspan="9"></td><td rowspan="9">SAR_NOTINITIALIZEERR：传入的参数指针为空或应用句柄未初始化</td><td>度。</td></tr><tr><td>成功。</td></tr><tr><td>SAR_USER_NOT_LOGGED_IN：非用户权限</td></tr><tr><td>SAR_KEYUSAGEERR：容器类型错误，非RSA类容器</td></tr><tr><td>SWKR_RSA_SIGB_ERROR：RSA白盒签名错误</td></tr><tr><td>SWKR_BASE64_DECODE_ERROR:BaSe64 解码错误</td></tr><tr><td>SAR_RSAMODULUSLENERR：RSA模长错误</td></tr><tr><td>SAR_OBJERR：设备号信息错误</td></tr><tr><td colspan="2">SAR_KEYNOTFOUNDERR：读取RSA密钥对错误</td></tr><tr><td colspan="2">SAR_RSAENCERR：RSA协同过程错误 备注 权限要求：需要用户权限。</td><td></td></tr></table></body></html>

3.2.5.7 RSA 验签

函数原型 ULONG DEVAPI SKF_RSAVerify (DEVHANDLE hDev , RSAPUBLICKEYBLOB\* pRSAPubKeyBlob,BYTE \*pbData, ULONG  ulDataLen, BYTE \*pbSignature, ULONG ulSignLen)  
功能描述 验证RSA 签名。用pRSAPubKeyBlob 内的公钥值对待验签数据进行验签。  
参数 hDev [IN] 设备句柄。pRSAPubKeyBlob [IN] RSA 公钥数据结构。pbData [IN] 待验证签名的数据。ulDataLen [IN] 数据长度，应不大于公钥模长-11。pbSignature [IN] 待验证的签名值。ulSignLen [IN] 签名值长度，必须为公钥模长。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR： 传入的参数指针为空或应用句柄未初始化SAR_USER_NOT_LOGGED_IN：非用户权限SAR_INDATALENERR：签名值长度与公钥模长不匹配SAR_RSAMODULUSLENERR：PKCS1 填充错误SWKR_RSA_VERIFY_ERROR：RSA 验签错误SAR_RSADECERR、SAR_DECRYPTPADERR：RSA 运算错误SAR_KEYINFOTYPEERR：应用类型错误，非 RSA

### 3.2.5.8 RSA 外来公钥运算

函数原型 ULONG DEVAPI SKF_ExtRSAPubKeyOperation (DEVHANDLE hDev, RSAPUBLICKEYBLOB\*pRSAPubKeyBlob,BYTE\* pbInput, ULONG ulInputLen, BYTE\* pbOutput, ULONG\*pulOutputLen)  
功能描述 使用外部传入的RSA 公钥对输入数据做公钥运算并输出结果。  
参数 hDev [IN] 设备句柄。pRSAPubKeyBlob [IN] RSA 公钥数据结构。pbInput [IN] 指向待运算的原始数据缓冲区。ulInputLen [IN] 待运算原始数据的长度，必须为公钥模长。pbOutput [OUT] 指向RSA 公钥运算结果缓冲区，如果该参数为NULL，则由pulOutputLen 返回运算结果的实际长度。

<html><body><table><tr><td></td><td>pulOutputLen</td><td>[IN，OUT]输入时表示pbOutput缓冲区的长度，输出时表示 RSA公钥运算结果的实际长度。</td></tr><tr><td rowspan="3">返回值</td><td colspan="2">SAR_OK: 成功。</td></tr><tr><td>SAR_NOTINITIALIZEERR：传入的参数指针为空或应用句柄未初始化</td><td></td></tr><tr><td colspan="2">SAR_INDATALENERR：待运算长度与公钥模长不匹配</td></tr></table></body></html>

<html><body><table><tr><td>SAR_NOTINITIALIZEERR：传入的参数指针为空或应用句柄未初始化 3.2.5.9RSA外来私钥运算 函数原型 hDev</td><td colspan="2">SAR_INDATALENERR：待运算长度与公钥模长不匹配 SAR_RSADECERR：RSA运算错误</td></tr><tr><td>功能描述</td><td colspan="2">ULONG DEVAPI SKF_ExtRSAPriKeyOperation (DEVHANDLE hDev, RSAPRIVATEKEYBLOB* pRSAPriKeyBlob,BYTE* pblnput, ULONG ulInputLen,BYTE* pbOutput, ULONG* pulOutputLen) 直接使用外部传入的RSA私钥对输入数据做私钥运算并输出结果。 [IN]设备句柄。</td></tr><tr><td>参数</td><td colspan="2">pRSAPriKeyBlob [IN] RSA私钥数据结构。</td></tr><tr><td></td><td colspan="2">pblnput [IN]指向待运算数据缓冲区。 ullnputLen [IN]待运算数据的长度，必须为公钥模长。 pbOutput [OUT]RSA私钥运算结果，如果该参数为NULL，则由</td></tr><tr><td></td><td colspan="2">pulOutputLen返回运算结果的实际长度。 pulOutputLen [IN，OUT]输入时表示pbOutput缓冲区的长度，输出时表示</td></tr><tr><td>返回值</td><td colspan="2">RSA 私钥运算结果的实际长度。</td></tr><tr><td></td><td colspan="2">SAR_OK: 成功。 SAR_NOTINITIALIZEERR：传入的参数指针为空或应用句柄未初始化</td></tr><tr><td></td><td colspan="2">SAR_INDATALENERR：待运算长度与公钥模长不匹配</td></tr><tr><td>备注</td><td colspan="2">SAR_RSADECERR：RSA运算错误</td></tr><tr><td>3.2.5.10 生成ECC 签名密钥对</td><td colspan="2">本函数仅用于测试和调试，不建议用于实际的密码服务。</td></tr><tr><td>函数原型</td><td colspan="2"></td></tr><tr><td></td><td colspan="2">ULONG DEVAPI SKF_GenECCKeyPair (HCONTAINER hContainer, ULONG uIAlgld, ECCPUBLICKEYBLOB *pBlob)</td></tr><tr><td>功能描述</td><td colspan="2">生成 ECC签名密钥对并输出签名公钥。</td></tr><tr><td>参数</td><td colspan="2">hContainer [IN]密钥容器句柄。</td></tr><tr><td>返回值</td><td colspan="2">ulAlgld [IN]算法标识，只支持 SGD_SM2_1算法。 pBlob [OUT]返回 ECC 公钥数据结构。 SAR_OK: 成功。</td></tr><tr><td></td><td colspan="2"></td></tr><tr><td></td><td colspan="2"></td></tr><tr><td></td><td colspan="2">SAR_NOTINITIALIZEERR：容器句柄未初始化 SAR_INVALIDPARAMERR：传入的参数指针为空</td></tr><tr><td></td><td colspan="2"></td></tr><tr><td></td><td colspan="2">SAR_USER_NOT_LOGGED_IN：非用户权限</td></tr><tr><td></td><td colspan="2">SAR_KEYINFOTYPEERR：非 SM2类算法</td></tr><tr><td></td><td colspan="2">SWKR_ECC_SPLIT_KEY_ERROR：分割密钥计算错误</td></tr><tr><td></td><td colspan="2">SWKR_BASE64_DECODE_ERROR：公钥生成错误</td></tr><tr><td></td><td colspan="2"></td></tr><tr><td></td><td colspan="2">SAR_FILE_NOT_EXIST文件不存在</td></tr><tr><td></td><td colspan="2">SAR_WRITEFILEERR写入SM2密钥错误</td></tr><tr><td>备注</td><td colspan="2">权限要求：需要用户权限。</td></tr><tr><td>3.2.5.11 导入ECC 加密密钥对</td><td colspan="2"></td></tr><tr><td></td><td colspan="2"></td></tr><tr><td>函数原型</td><td colspan="2">ULONG DEVAPI SKF_ImportECCKeyPair（</td></tr><tr><td></td><td colspan="2"></td></tr><tr><td>功能描述</td><td colspan="2">HCONTAINER hContainer, PENVELOPEDKEYBLOB pEnvelopedKeyBlob) 导入ECC公私钥对。</td></tr></table></body></html>

参数 hContainer [IN] 密钥容器句柄。pEnvelopedKeyBlob [IN] 受保护的加密密钥对。

返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_USER_NOT_LOGGED_IN：非用户权限SAR_KEYINFOTYPEERR 容器句柄非 SM2 或对称加密算法标识符非 SM4_ECBSKF_ECCDecrypt 错误码SKF_SetSymmKey 错误码SKF_DecryptInit 错误码SKF_DecryptUpdate 错误码SKF_DecryptFinal 错误码SAR_FILE_NOT_EXIST 文件不存在SAR_WRITEFILEERR 写入文件错误  
备注 权限要求：需要用户权限。

3.2.5.12 ECC 签名

函数原型 ULONG DEVAPI SKF_ECCSignData (HCONTAINER hContainer, BYTE \*pbData, ULONGulDataLen, PECCSIGNATUREBLOB pSignature)  
功能描述 ECC 数字签名。采用ECC 算法和指定私钥hKey，对指定数据pbData 进行数字签名。签名后的结果存放到 pSignature 中。  
参数 hContainer [IN] 密钥容器句柄。pbData [IN] 待签名的数据。ulDataLen [IN] 待签名数据长度，必须小于密钥模长。pSignature [OUT] 签名值。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_USER_NOT_LOGGED_IN：非用户权限SAR_KEYINFOTYPEERR：容器类型非 SM2 类SAR_READFILEERR：读取私钥文件错误SWKR_ECC_SPLIT_SIGN_ERROR：分割签名计算错误SWKR_ECC_COMBINE_SIGN_ERROR：联合签名计算错误

备注 权限要求：需要用户权限。输入数据为待签数据的杂凑值。当使用SM2 算法时，该输入数据为待签数据经过SM2签名预处理的结果，预处理过程遵循《公钥密码基础设施应用技术体系 SM2 算法密码使用规范》。

3.2.5.13 ECC 验签

函数原型 ULONG DEVAPI SKF_ECCVerify (DEVHANDLE hDev , ECCPUBLICKEYBLOB\* pECCPubKeyBlob,BYTE \*pbData, ULONG  ulDataLen, PECCSIGNATUREBLOB pSignature)  
功能描述 用ECC 公钥对数据进行验签。  
参数 hDev [IN] 设备句柄。pECCPubKeyBlob [IN] ECC 公钥数据结构。pbData [IN] 待验证签名的数据。ulDataLen [IN] 数据长度。pSignature [IN] 待验证签名值。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化

SAR_INVALIDPARAMERR：传入的参数指针为空SAR_INDATALENERR：签名值长度非 32 字节SAR_RSAENCERR：验签失败备注 输入数据为待签数据的杂凑值。当使用SM2 算法时，该输入数据为待签数据经过SM2签名预处理的结果，预处理过程遵循《公钥密码基础设施应用技术体系 SM2 算法密码使用规范》。

### ******** ECC 外来公钥加密

函数原型 ULONG DEVAPI SKF_ExtECCEncrypt (DEVHANDLE hDev, ECCPUBLICKEYBLOB\*pECCPubKeyBlob,BYTE\* pbPlainText, ULONG ulPlainTextLen, PECCCIPHERBLOB pCipherText)  
功能描述 使用外部传入的ECC 公钥对输入数据做加密运算并输出结果。  
参数 hDev [IN] 设备句柄。pECCPubKeyBlob [IN] ECC 公钥数据结构。pbPlainText [IN] 待加密的明文数据。ulPlainTextLen [IN] 待加密明文数据的长度。pCipherText [OUT] 密文数据。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR： 传入的参数指针为空或设备句柄未初始化SKF_ExtECCEncrypt：申请内存失败SWKR_ECCDECERR：ECC 加密错误

### ******** ECC 外来私钥解密

函数原型 ULONG DEVAPI SKF_ExtECCDecrypt (DEVHANDLE hDev, ECCPRIVATEKEYBLOB\*pECCPriKeyBlob, PECCCIPHERBLOB pCipherText, BYTE\* pbPlainText, ULONG\*pulPlainTextLen)  
功能描述 使用外部传入的ECC 私钥对输入数据做解密运算并输出结果。  
参数 hDev [IN] 设备句柄。pECCPriKeyBlob [IN] ECC 私钥数据结构。pCipherText [IN] 待解密的密文数据。pbPlainText [OUT]  返 回 明 文 数 据 ， 如 果 该 参 数 为 NULL ， 则 由pulPlainTextLen 返回明文数据的实际长度。pulPlainTextLen [IN，OUT] 输入时表示 pbPlainText 缓冲区的长度，输出时表示明文数据的实际长度。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR： 传入的参数指针为空或设备句柄未初始化SWKR_ECCDECERR：解密时错误  
备注 本函数仅用于测试和调试，不建议用于实际的密码服务。

### ******** ECC 外来私钥签名

函数原型 ULONG DEVAPI SKF_ExtECCSign (DEVHANDLE hDev, ECCPRIVATEKEYBLOB\*  pECCPriKeyBlob,BYTE\* pbData, ULONG ulDataLen, PECCSIGNATUREBLOB pSignature)  
功能描述 使用外部传入的ECC 私钥对输入数据做签名运算并输出结果。  
参数 hDev [IN] 设备句柄。pECCPriKeyBlob [IN] ECC 私钥数据结构。pbData [IN] 待签名数据。ulDataLen [IN] 待签名数据的长度。pSignature [OUT]签名值。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR： 传入的参数指针为空或设备句柄未初始化SWKR_ECCENCERR：ECC 签名错误  
备注： 输入数据为待签数据的杂凑值。当使用 SM2 算法时，该输入数据为待签数据经过 SM2签名预处理的结果，预处理过程遵循《公钥密码基础设施应用技术体系 SM2 算法密码使用规范》。  
备注 本函数仅用于测试和调试，不建议用于实际的密码服务。

### 3.2.5.17 ECC 外来公钥验签

<html><body><table><tr><td>函数原型</td><td colspan="2">ULONG DEVAPI SKF_ExtECCVerify(DEVHANDLE hDev,ECCPUBLICKEYBLOB* pECCPubKeyBlob,BYTE* pbData, ULONG uIDataLen,PECCSIGNATUREBLOB pSignature)</td></tr><tr><td>功能描述</td><td colspan="2">外部使用传入的ECC公钥做签名验证。</td></tr><tr><td rowspan="6">参数</td><td>hDev</td><td>[IN]设备句柄。</td></tr><tr><td>pECCPubKeyBlob</td><td>[IN]ECC公钥数据结构。</td></tr><tr><td>pbData</td><td>[IN]待验证数据。</td></tr><tr><td>ulDataLen</td><td>[IN]待验证数据的长度。</td></tr><tr><td>pSignature</td><td></td></tr><tr><td>成功。</td><td>[IN]签名值。</td></tr><tr><td rowspan="4">返回值</td><td>SAR_OK:</td><td>SAR_NOTINITIALIZEERR：传入的参数指针为空或设备句柄未初始化</td></tr><tr><td>SAR_INDATALENERR：输入数据长度错误</td><td></td></tr><tr><td>SWKR_ECCDECERR：ECC验签错误</td><td></td></tr><tr><td>输入数据为待签数据的杂凑值。当使用SM2算法时，该输入数据为待签数据经过 SM</td><td></td></tr><tr><td>备注:</td><td>签名预处理的结果，预处理过程遵循《公钥密码基础设施应用技术体系SM2算法密石 使用规范》。</td><td></td></tr></table></body></html>

3.2.5.18 ECC 生成密钥协商参数并输出

函 数 原 ULONG DEVAPI SKF_GenerateAgreementDataWithECC (HCONTAINER hContainer, ULONG型： ulAlgId,ECCPUBLICKEYBLOB\*  pTempECCPubKeyBlob,BYTE\* pbID, ULONG ulIDLen,HANDLE\*phAgreementHandle)

功 能 描 使用ECC 密钥协商算法，为计算会话密钥而产生协商参数，返回临时 ECC 密钥对的公述： 钥及协商句柄。

参数： hContainer [IN] 容器句柄。（必须由创建容器或打开容器返回）ulAlgId [IN] 会话密钥算法标识。pTempECCPubKeyBlob [OUT] 发起方临时 ECC 公钥。pbID [IN] 发起方的 ID。ulIDLen [IN] 发起方ID 的长度，不大于32。phAgreementHandle [OUT] 返回的密钥协商句柄。

返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_USER_NOT_LOGGED_IN：非用户权限SAR_KEYUSAGEERR：密码用途错误SAR_MEMORYERR：内存分配错误SKF_ExportPublicKey 错误码SWKR_GENECCKEYERR：生成 SM2 密钥对错误备注： 为协商会话密钥，协商的发起方应首先调用本函数。

3.2.5.19 ECC 产生协商数据并计算会话密钥  
函 数 原 ULONG DEVAPI SKF_GenerateAgreementDataAndKeyWithECC(  
型： HANDLE hContainer, ULONG ulAlgId,  
ECCPUBLICKEYBLOB\*  pSponsorECCPubKeyBlob,  
ECCPUBLICKEYBLOB\*  pSponsorTempECCPubKeyBlob,  
ECCPUBLICKEYBLOB\*  pTempECCPubKeyBlob,  
BYTE\* pbID, ULONG ulIDLen, BYTE \*pbSponsorID, ULONG ulSponsorIDLen,  
HANDLE \*phKeyHandle)

功 能 描 使用ECC 密钥协商算法，产生协商参数并计算会话密钥，输出临时 ECC 密钥对公钥，述： 并返回产生的密钥句柄。

参数：

hContainer [IN] 容器句柄。  
ulAlgId [IN] 会话密钥算法标识。  
pSponsorECCPubKeyBlob [IN] 发起方的 ECC 公钥。  
pSponsorTempECCPubKeyBlob [IN] 发起方的临时ECC 公钥。  
pTempECCPubKeyBlob [OUT] 响应方的临时 ECC 公钥。  
pbID [IN] 响应方的 ID。  
ulIDLen [IN] 响应方ID 的长度，不大于32。  
pbSponsorID [IN] 发起方的 ID。  
ulSponsorIDLen [IN] 发起方ID 的长度，不大于32。  
phKeyHandle [OUT] 返回的对称算法密钥句柄。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_USER_NOT_LOGGED_IN：非用户权限SAR_MEMORYERR：分配内存错误SKF_ExportPublicKey 错误码SAR_READFILEERR：读取私钥文件错误SWKR_ECC_EXCHANGE_CAL_ERROR：密钥协商过程计算错误SWKR_ECCDECERR：协同解密错误SWKR_GENECCKEYERR：生成 SM2 密钥对错误SKF_SetSymmKey 错误码  
备注： 本函数由响应方调用。

### 3.2.5.20 ECC 计算会话密钥

函 数 原 ULONG DEVAPI SKF_GenerateKeyWithECC (HANDLE hAgreementHandle,  
型： ECCPUBLICKEYBLOB\*  pECCPubKeyBlob,ECCPUBLICKEYBLOB\*  pTempECCPubKeyBlob,BYTE\* pbID, ULONG ulIDLen, HANDLE \*phKeyHandle)

功 能 描 使用ECC 密钥协商算法，使用自身协商句柄和响应方的协商参数计算会话密钥，同时述： 返回会话密钥句柄。

参数： hAgreementHandle [IN] 密钥协商句柄。pECCPubKeyBlob [IN] 外部输入的响应方ECC 公钥。pTempECCPubKeyBlob [IN] 外部输入的响应方临时ECC 公钥。pbID [IN] 响应方的 ID。ulIDLen [IN] 响应方ID 的长度，不大于32。phKeyHandle [OUT] 返回的密钥句柄。

返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空 SAR_KEYUSAGEERR：密码用途错误SAR_READFILEERR：读取私钥文件错误

SWKR_ECC_EXCHANGE_CAL_ERROR：密钥协商过程计算错误  
SWKR_ECCDECERR：协同解密错误  
SKF_SetSymmKey 错误码

协商的发起方获得响应方的协商参数后调用本函数，计算会话密钥。计算过程遵循《公钥密码基础设施应用技术体系 SM2 算法密码使用规范》。

### 3.2.5.21 导出公钥

函数原型 ULONG DEVAPI SKF_ExportPublicKey (HCONTAINER hContainer, BOOL bSignFlag ， BYTE\*pbBlob, ULONG\* pulBlobLen)  
功能描述 导出容器中的签名公钥或者加密公钥。  
参数 hContainer [IN] 密钥容器句柄。bSignFlag [IN] TRUE 表示导出签名公钥，FALSE 表示导出加密公钥。pbBlob [OUT] 指向 RSA 公钥结构（RSAPUBLICKEYBLOB）或者 ECC 公钥结构（ECCPUBLICKEYBLOB），如果此参数为 NULL 时，由 pulBlobLen 返回pbBlob 的长度。pulBlobLen [IN，OUT] 输入时表示pbBlob 缓冲区的长度，输出时表示导出公钥结构的大小。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：应用句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_FILE_NOT_EXIST：密钥不存在SAR_BUFFER_TOO_SMALL：存储密钥的缓冲区太小SAR_READFILEERR：读取密钥错误SWKR_ECC_KEY_FORMAT_ERROR：读取 SM2 公钥长度不正确，可能是密钥文件被修改SAR_RSAMODULUSLENERR：读取 RSA 公钥长度不正确SWKR_BASE64_DECODE_ERROR：Base64 解码错误

### ******** 导入会话密钥

函数原型 ULONG DEVAPI SKF_ImportSessionKey (HCONTAINER hContainer, ULONG ulAlgId,BYTE\*pbWrapedData,ULONG ulWrapedLen，HANDLE \*phKey)  
功能描述 导入会话密钥密文，使用容器中的加密私钥解密得到会话密钥。  
参数 hContainer [IN] 容器句柄。ulAlgId [IN] 会话密钥算法标识。pbWrapedData [IN]  要导入的会话密钥密文。当容器为 ECC 类型时，此参数为ECCCIPHERBLOB 密文数据，当容器为 RSA 类型时，此参数为 RSA 公钥加密后的数据。ulWrapedLen [IN] 会话密钥密文长度。phKey [OUT] 返回会话密钥句柄。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR： 传入的参数指针为空或容器句柄未初始化SAR_INDATALENERR：输入数据长度错误SAR_KEYUSAGEERR：密钥用途错误SAR_KEYINFOTYPEERR：密钥类型错误SAR_DECRYPTPADERR：解密时做补丁错误  
备注 权限要求：需要用户权限。

### ******** 明文导入会话密钥

函数原型 ULONG DEVAPI SKF_SetSymmKey (DEVHANDLE hDev, BYTE\* pbKey, ULONG ulAlgID, HANDLE\* phKey)

功能描述 设置明文对称密钥，返回密钥句柄。

hDev [IN] 设备句柄。  
pbKey [IN] 指向会话密钥值的缓冲区。  
ulAlgID [IN] 会话密钥算法标识。  
phKey [OUT] 返回会话密钥句柄。  
SAR_NOTINITIALIZEERR： 传入的参数指针为空或设备句柄未初始化  
SAR_MEMORYERR：系统内存分配错误  
SAR_KEYINFOTYPEERR：密钥类型错误  
SWKR_SYM_CTX_INIT_ERROR：初始化对称上下文错误，详情见日志错误码

备注 本函数仅用于测试和调试，不建议用于实际的密码服务。

### ******** 加密初始化

函数原型 ULONG DEVAPI SKF_EncryptInit (HANDLE hKey, BLOCKCIPHERPARAM EncryptParam)  
功能描述 数据加密初始化。设置数据加密的算法相关参数。  
参数 hKey [IN] 加密密钥句柄。EncryptParam [IN] 分组密码算法相关参数：初始向量、初始向量长度、填充方法、反馈值的位长度。

返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：密钥句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空或SWKR_SYM_PARAM_PADDING_ERROR：SM4 参数填充类型错误，当前不支持填充或非指定类型SWKR_SYM_CIPHER_INIT_ERROR：初始化加密上下文错误

### ******** 单组数据加密

函数原型 ULONG DEVAPI SKF_Encrypt(HANDLE hKey, BYTE \* pbData, ULONG ulDataLen, BYTE\*pbEncryptedData, ULONG \*pulEncryptedLen)  
功能描述 单一分组数据的加密操作。用指定加密密钥对指定数据进行加密，被加密的数据只包含一个分组，加密后的密文保存到指定的缓冲区中。SKF_Encrypt 只对单个分组数据进行 加 密 ， 在 调 用 SKF_Encrypt 之 前 ， 必 须 调 用 SKF_EncryptInit 初 始 化 加 密 操 作 。SKF_Encypt 等价于先调用 SKF_EncryptUpdate 再调用 SKF_EncryptFinal。  
参数 hKey [IN] 加密密钥句柄。pbData [IN] 待加密数据。ulDataLen [IN] 待加密数据长度。pbEncryptedData [OUT] 加密后的数据缓冲区指针，可以为NULL，用于获得加密后数据长度。pulEncryptedLen [IN，OUT] 输入时表示结果数据缓冲区长度，输出时表示结果数据实际长度。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：密钥句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SWKR_SYM_PLAIN_LEN_ERROR：不填充且明文长度不为 16 倍数SWKR_SYM_CIPHER_UPDATE_ERROR：分组更新错误，详情见日志错误码SWKR_SYM_CIPHER_FINAL_ERROR：结束加密错误，详情见日志错误码

### ******** 多组数据加密

函数原型 ULONG DEVAPI SKF_EncryptUpdate(HANDLE hKey, BYTE \* pbData, ULONG ulDataLen, BYTE\*pbEncryptedData, ULONG \*pulEncryptedLen)

功能描述 多个分组数据的加密操作。用指定加密密钥对指定数据进行加密，被加密的数据包含多个分组，加密后的密文保存到指定的缓冲区中。SKF_EncryptUpdate 对多个分组数据进行加密，在调用 SKF_EncryptUpdate 之前，必须调用 SKF_EncryptInit 初始化加密操作；在调用 SKF_EncryptUpdate 之后，必须调用 SKF_EncryptFinal 结束加密操作。  
参数 hKey [IN] 加密密钥句柄。pbData [IN] 待加密数据。ulDataLen [IN] 待加密数据长度。pbEncryptedData [OUT] 加密后的数据缓冲区指针。pulEncryptedLen [OUT] 返回加密后的数据长度。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：密钥句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SWKR_SYM_CIPHER_UPDATE_ERROR：分组更新错误，详情见日志错误码

### ******** 结束加密

函数原型 ULONG  DEVAPI  SKF_EncryptFinal  (HANDLE  hKey,  BYTE  \*pbEncryptedData,  ULONG\*ulEncryptedDataLen )  
功能描述 结束多个分组数据的加密，返回剩余加密结果。先调用 SKF_EncryptInit 初始化加密操作，再调用 SKF_EncryptUpdate 对多个分组数据进行加密，最后调用 SKF_EncryptFinal结束多个分组数据的加密。  
参数 hKey [IN] 加密密钥句柄。pbEncyptedData [OUT] 加密结果的缓冲区。ulEncyptedDataLen [OUT] 加密结果的长度。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR： 传入的参数指针为空或密钥句柄未初始化SWKR_SYM_CIPHER_FINAL_ERROR：最终加密错误，详情见日志错误码

### ******** 解密初始化

<html><body><table><tr><td>函数原型</td><td>ULONG DEVAPI SKF_DecryptInit (HANDLE hKey, BLOCKCIPHERPARAM DecryptParam)</td></tr><tr><td>功能描述</td><td>数据解密初始化，设置解密密钥相关参数。调用 SKF_DecryptInit之后，可以调用 SKF_Decrypt对单个分组数据进行解密，也可以多次调用 SKF_DecryptUpdate之后再调用</td></tr><tr><td>参数</td><td>SKF_DecryptFinal完成对多个分组数据的解密。 hKey [IN]解密密钥句柄。 DecryptParam [IN]分组密码算法相关参数：初始向量、初始向量长度、填充方</td></tr><tr><td>返回值</td><td>法、反馈值的位长度。 SAR_OK:成功。 SAR_NOTINITIALIZEERR：密钥句柄未初始化 SAR_INVALIDPARAMERR：传入的参数指针为空</td></tr></table></body></html>

### ******** 单组数据解密

函数原型 ULONG DEVAPI SKF_Decrypt(HANDLE hKey, BYTE \* pbEncryptedData, ULONG ulEncryptedLen,BYTE \* pbData, ULONG \* pulDataLen)  
功能描述 单个分组数据的解密操作。用指定解密密钥对指定数据进行解密，被解密的数据只包含一个分组，解密后的明文保存到指定的缓冲区中。SKF_Decrypt 只对单个分组数据进行 解 密 ， 在 调 用 SKF_Decrypt 之 前 ， 必 须 调 用 SKF_DecryptInit 初 始 化 解 密 操 作 。SKF_Decypt 等价于先调用 SKF_DecryptUpdate 再调用 SKF_DecryptFinal。  
参数 hKey [IN] 解密密钥句柄。

<html><body><table><tr><td rowspan="6"></td><td>pbEncryptedData</td><td>[IN]待解密数据。</td></tr><tr><td>ulEncryptedLen</td><td>[IN]待解密数据长度。</td></tr><tr><td>pbData</td><td>[OUT]指向解密后的数据缓冲区指针，当为NULL时可获得解密后的</td></tr><tr><td rowspan="2">pulDataLen</td><td>数据长度。</td></tr><tr><td>[IN，OUT]输入时表示结果数据缓冲区长度，输出时表示结果数据实 际长度。</td></tr><tr><td colspan="2">SAR_OK: 成功。</td></tr><tr><td rowspan="6">返回值</td><td colspan="2"></td></tr><tr><td colspan="2">SAR_NOTINITIALIZEERR：密钥句柄未初始化 SARINVALIDPARAMERR：传入的参数指针为空</td></tr><tr><td colspan="2">SWKR_SYM_PLAIN_LEN_ERROR：密文长度不为16倍数</td></tr><tr><td colspan="2">SWKR_SYM_CIPHER_UPDATE_ERROR：更新加解密上下文错误</td></tr><tr><td colspan="2"></td></tr><tr><td colspan="2">SWKR_SYM_CIPHER_FINAL_ERROR：结束解密错误，详情见日志错误码</td></tr></table></body></html>

### ******** 多组数据解密

函数原型 ULONG  DEVAPI  SKF_DecryptUpdate(HANDLE  hKey,  BYTE  \*  pbEncryptedData,  ULONGulEncryptedLen, BYTE \* pbData, ULONG \* pulDataLen)  
功能描述 多个分组数据的解密操作。用指定解密密钥对指定数据进行解密，被解密的数据包含多个分组，解密后的明文保存到指定的缓冲区中。SKF_DecryptUpdate 对多个分组数据进行解密，在调用 SKF_DecryptUpdate 之前，必须调用 SKF_DecryptInit 初始化解密操作；在调用 SKF_DecryptUpdate 之后，必须调用 SKF_DecryptFinal 结束解密操作。  
参数 hKey [IN] 解密密钥句柄。pbEncryptedData [IN] 待解密数据。ulEncryptedLen [IN] 待解密数据长度。pbData [OUT] 指向解密后的数据缓冲区指针。pulDataLen [IN，OUT] 输入时表示结果数据缓冲区长度，输出时表示结果数据实际长度。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：密钥句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SWKR_SYM_CIPHER_UPDATE_ERROR：更新加解密上下文错误

### ******** 结束解密

函数原型 ULONG  DEVAPI  SKF_DecryptFinal  (HANDLE  hKey,  BYTE  \*pbDecryptedData,  ULONG\*pulDecryptedDataLen)  
功能描述 结束多个分组数据的解密。先调用 SKF_DecryptInit 初始化解密操作，再调用SKF_DecryptUpdate 对多个分组数据进行解密，最后调用 SKF_DecryptFinal 结束多个分组数据的解密。  
参数 hKey [IN] 解密密钥句柄。pbDecryptedData [OUT] 指向解密结果的缓冲区，如果此参数为 NULL 时，由pulDecryptedDataLen 返回解密结果的长度。pulDecryptedDataLen [IN，OUT] 输入时表示 pbDecryptedData 缓冲区的长度，输出时表示解密结果的长度。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：密钥句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SWKR_SYM_CIPHER_FINAL_ERROR：结束解密错误，详情见日志错误码

### ******** 密码杂凑初始化

函数原型 ULONG DEVAPI SKF_DigestInit(DEVHANDLE hDev, ULONG ulAlgID,  ECCPUBLICKEYBLOB \*pPubKey,unsigned char \*pucID, ULONG ulIDLen, HANDLE \*phHash)

功能描述 初始化密码杂凑计算操作，指定计算密码杂凑的算法。

<html><body><table><tr><td>参数 返回值</td><td>hDev [IN]连接设备时返回的设备句柄。 uIAlgID [IN]密码杂凑算法标识。 pPubKey [IN]签名者公钥。当alAIgID为SGD_SM3时有效。 pucID [IN]签名者的ID值，当alAIgID为SGD_SM3时有效。 ulIDLen [IN]签名者ID的长度，当aIAIgID为SGD_SM3时有效。 phHash [OUT]密码杂凑对象句柄。 SAR_OK: 成功。 SAR_NOTINITIALIZEERR：设备句柄未初始化</td></tr><tr><td>备注</td><td>SWKR_HASH_CTX_INIT_ERROR：初始化密码杂凑对象错误 SAR_KEYINFOTYPEERR：无效的参数，算法表示不支持 当ulAlgID为 SGD_SM3且ulIDLen不为0的情况下pPubKey、pucID 有效，执行SM2算法签名 处理1操作。计算过程遵循《公钥密码基础设施应用技术体系SM2算法密码使用规范》</td></tr></table></body></html>

### 3.2.5.33 单组数据密码杂凑

函数原型 ULONG DEVAPI SKF_Digest (HANDLE hHash, BYTE \*pbData, ULONG ulDataLen, BYTE\*pbHashData, ULONG \*pulHashLen)  
功能描述 对单一分组的消息进行密码杂凑计算。调用 SKF_Digest 之前，必须调用SKF_DigestInit 初 始 化 密 码 杂 凑 计 算 操 作 。 SKF_Digest 等 价 于 多 次 调 用SKF_DigestUpdate 之后再调用 SKF_DigestFinal。  
参数 hHash [IN] 密码杂凑对象句柄。pbData [IN] 指向消息数据的缓冲区。ulDataLen [IN] 消息数据的长度。pbHashData [OUT]  密码杂凑数据缓冲区指针，当此参数为 NULL 时，由pulHashLen 返回密码杂凑结果的长度。pulHashLen [IN，OUT] 输入时表示结果数据缓冲区长度，输出时表示结果数据实际长度。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：设备句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SWKR_HASH_UPDATE_ERROR：更新摘要错误SWKR_HASH_FINAL_ERROR：结束摘要计算错误

### 3.2.5.34 多组数据密码杂凑

函数原型 ULONG DEVAPI SKF_DigestUpdate (HANDLE hHash, BYTE \*pbData, ULONG  ulDataLen)  
功能描述 对多个分组的消息进行密码杂凑计算。调用 SKF_DigestUpdate 之前，必须调用SKF_DigestInit 初始化密码杂凑计算操作；调用 SKF_DigestUpdate 之后，必须调用SKF_DigestFinal 结束密码杂凑计算操作。  
参数 hHash [IN] 密码杂凑对象句柄。pbData [IN] 指向消息数据的缓冲区。ulDataLen [IN] 消息数据的长度。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：设备句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SWKR_HASH_UPDATE_ERROR：更新摘要错误

### 3.2.5.35 结束密码杂凑

函数原型 ULONG  DEVAPI  SKF_DigestFinal  (HANDLE  hHash,  BYTE  \*pHashData,  ULONG\*pulHashLen)  
功能描述 结束多个分组消息的密码杂凑计算操作，将密码杂凑结果保存到指定的缓冲区。  
参数 hHash [IN] 密码杂凑对象句柄。pHashData [OUT] 返回的密码杂凑结果缓冲区指针，如果此参数 NULL 时，由pulHashLen 返回杂凑结果的长度。pulHashLen [IN，OUT] 输入时表示杂凑结果缓冲区的长度，输出时表示密码杂凑结果的长度。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：设备句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SWKR_HASH_FINAL_ERROR：结束摘要计算错误  
备注 SKF_DigestFinal 必须用于 SKF_DigestUpdate 之后。

### ******** 消息鉴别码运算初始化

函数原型 ULONG DEVAPI SKF_MacInit (HANDLE hKey, BLOCKCIPHERPARAM\* pMacParam, HANDLE\*phMac)  
功能描述 初始化消息鉴别码计算操作，设置计算消息鉴别码的所需参数，并返回消息鉴别码句柄。  
参数 hKey [IN] 计算消息鉴别码的密钥句柄。pMacParam [IN] 消息认证计算相关参数，包括初始向量、初始向量长度、填充方法等。phMac [OUT] 消息鉴别码对象句柄。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：对称密钥句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_USER_NOT_LOGGED_IN：非用户权限SAR_KEYINFOTYPEERR 不是 SM4 CBC 模式SWKR_SM4IV_ERROR：IV 格式错误SWKR_SYM_PARAM_PADDING_ERROR：不支持填充SAR_MEMORYERR：内存分配错误其他： 错误码。  
备注 消息鉴别码计算采用分组加密算法的CBC 模式，将加密结果的最后一块作为计算结果。待计算数据的长度必须是分组加密算法块长的倍数，接口内部不作数据填充。

### ******** 单组数据消息鉴别码运算

函数原型 ULONG  DEVAPI  SKF_Mac(HANDLE  hMac,  BYTE\*  pbData,  ULONG  ulDataLen,  BYTE\*pbMacData, ULONG \*pulMacLen)  
功能描述 SKF_Mac 计算单一分组数据的消息鉴别码。  
参数 hMac [IN] 消息鉴别码句柄。pbData [IN] 指向待计算数据的缓冲区。ulDataLen [IN] 待计算数据的长度。pbMacData [OUT]  指向计算后的 Mac 结果，如果此参数为 NULL 时，由pulMacLen 返回计算后Mac 结果的长度。pulMacLen [IN，OUT] 输入时表示 pbMacData 缓冲区的长度，输出时表示 Mac结果的长度。  
返回值 SAR_OK： 成功。

SAR_NOTINITIALIZEERR：消息鉴别码句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_INDATALENERR：带计算明文长度不为16 倍数或结果缓冲区长度不足16 字节SKF_EncryptInit、SKF_Encrypt 错误码备注 调用 SKF_Mac 之前，必须调用 SKF_MacInit 初始化消息鉴别码计算操作。SKF_Mac等价于多次调用 SKF_MacUpdate 之后再调用 SKF_MacFinal。

### 3.2.5.39 多组数据消息鉴别码运算

函数原型 ULONG DEVAPI SKF_MacUpdate(HANDLE hMac, BYTE \* pbData, ULONG ulDataLen)  
功能描述 计算多个分组数据的消息鉴别码。  
参数 hMac [IN] 消息鉴别码句柄。pbData [IN] 指向待计算数据的缓冲区。plDataLen [IN] 待计算数据的长度。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：消息鉴别码句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_INDATALENERR：带计算明文长度不为 16 倍数  
备注 调用 SKF_MacUpdate 之前，必须调用 SKF_MacInit 初始化消息鉴别码计算操作；调用 SKF_MacUpdate 之后，必须调用 SKF_MacFinal 结束多个分组数据的消息鉴别码计算操作。

### 3.2.5.40 结束消息鉴别码运算

函数原型 ULONG DEVAPI  SKF_MacFinal (HANDLE  hMac, BYTE \*pbMacData,  ULONG\*pulMacDataLen)  
功能描述 结束多个分组数据的消息鉴别码计算操作。  
参数 hMac [IN] 消息鉴别码句柄。pbMacData [OUT]  指向消息鉴别码的缓冲区，当此参数为 NULL 时，由pulMacDataLen 返回消息鉴别码返回的长度。pulMacDataLen [OUT] 调用时表示消息鉴别码缓冲区的最大长度，返回消息鉴别码的长度。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：消息鉴别码句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_INDATALENERR：带计算明文长度不为16 倍数或结果缓冲区长度不足16 字节SKF_EncryptInit、SKF_Encrypt 错误码  
备注 SKF_MacFinal 必须用于 SKF_MacUpdate 之后。

### ******** 关闭密码对象句柄

函数原型 ULONG DEVAPI SKF_CloseHandle(HANDLE hHandle)  
功能描述 关闭会话密钥、密码杂凑对象、消息鉴别码对象、ECC 密钥协商等句柄。  
参数 hHandle [IN] 要关闭的对象句柄。  
返回值 SAR_OK： 成功。其他： 错误码。

## 3.2.6 扩展服务

******* ECC 协同解密

函数原型 ULONG DEVAPI SKF_ECCDecrypt(HCONTAINER hContainer,PECCCIPHERBLOB

<html><body><table><tr><td rowspan="3">功能描述 参数</td><td colspan="2">pCipherText,BYTE*pbPlainText,ULONG*pulPlainTextLen) 使用容器中的加密私钥进行协同运算对输入数据做解密运算并输出结果。</td></tr><tr><td>hDev</td><td>[IN]设备句柄。</td></tr><tr><td>pCipherText pbPlainText</td><td>[IN]待解密的密文数据。</td></tr><tr><td rowspan="3">返回值</td><td></td><td>[OUT]返回明文数据，如果该参数为NULL，则由 pulPlainTextLen返回明文数据的实际长度。 [IN，OUT]输入时表示pbPlainText 缓冲区的长度，输出时表示</td></tr><tr><td>pulPlainTextLen SAR_OK: 成功。</td><td>明文数据的实际长度。</td></tr><tr><td>SAR_NOTINITIALIZEERR：容器句柄未初始化</td><td></td></tr><tr><td rowspan="8"></td><td></td><td></td></tr><tr><td>SAR_USER_NOT_LOGGED_IN：非用户权限</td><td>SAR_INVALIDPARAMERR：传入的参数指针为空</td></tr><tr><td>SAR_READFILEERR：读取私钥错误</td><td></td></tr><tr><td>SWKR_ECC_SPLIT_SIGN_ERROR：分割签名计算错误</td><td></td></tr><tr><td>SWKR_ECC_COMBINE_SIGN_ERROR：联合签名计算错误</td><td></td></tr><tr><td>SAR_KEYINFOTYPEERR：容器类型错误，非SM2类</td><td></td></tr><tr><td>其他: 错误码。</td><td></td></tr><tr><td></td><td></td></tr></table></body></html>

### 3.2.6.2 RSA 协同解密

<html><body><table><tr><td>函数原型</td><td colspan="2">ULONG DEVAPI SKF_RSADecrypt(HCONTAINER hContainer, BYTE* pbData, ULONG uIDataLen, BYTE* pbSignature,ULONG* pulSignLen)</td></tr><tr><td rowspan="4">功能描述 参数</td><td colspan="2">使用容器中的加密私钥进行协同运算对输入数据做解密运算并输出结果。 [IN]容器句柄。</td></tr><tr><td colspan="2">hContainer pbData [IN]待解密的密文数据。</td></tr><tr><td colspan="2">ulDataLen [IN]密文长度</td></tr><tr><td colspan="2">pbSignature</td></tr><tr><td colspan="2">pulSignLen</td></tr><tr><td colspan="2">返回值 SAR_OK: SAR_NOTINITIALIZEERR：容器句柄未初始化</td></tr></table></body></html>

### 3.2.7 证书服务

### 3.2.7.1 ECC 协同签名生成证书请求

函数原型 ULONG DEVAPI SKF_GenerateEccCSR(HCONTAINER hContainer, LPSTR szDn, LPSTRszCSRCertRequest)  
功能描述 使用容器中的ECC 签名私钥生成证书请求。

<html><body><table><tr><td>参数</td><td>hContainer szDn szCSRCertRequest</td><td>[IN]容器句柄。 [IN]设备名ANSI格式 [OUT]P10内容ANSI格式。</td></tr><tr><td>返回值</td><td>SAR_OK: 成功。 SAR_NOTINITIALIZEERR：容器句柄未初始化 SARINVALIDPARAMERR：传入的参数指针为空 SAR_USER_NOT_LOGGED_IN：非用户权限</td><td></td></tr><tr><td></td><td colspan="2">SAR_NAMELENERR：设备名称过长 SWKR_CONTAINER_NAME_INVALID：容器名无效 SWKR_ECC_GEN_REQ_ERROR：生成请求错误</td></tr><tr><td colspan="2"></td><td>SAR_KEYNOTFOUNDERR：公钥不存在</td></tr></table></body></html>

### 3.2.7.2 RSA 协同签名生成证书请求

<html><body><table><tr><td>函数原型</td><td colspan="2">SKF_GenerateRsaCSR(HCONTAINER hContainer, LPSTR szDn,LPSTR szCSRCertReques ULONG nLen);</td></tr><tr><td rowspan="4">功能描述 参数 返回值</td><td>使用容器中的RSA签名私钥生成证书请求。 hContainer</td><td>[IN]容器句柄。</td></tr><tr><td>szDn</td><td></td></tr><tr><td>szCSRCertRequest</td><td>[IN]设备名ANSI格式</td></tr><tr><td>nLen</td><td>[OUT]P10 内容ANSI格式。 [IN]RSA强度(1024与2048)</td></tr><tr><td></td><td>SAR_OK: SAR_NOTINITIALIZEERR：容器句柄未初始化 SAR_INVALIDPARAMERR：传入的参数指针为空 SAR_USER_NOT_LOGGED_IN：非用户权限 SAR_NAMELENERR：设备名称过长</td><td>成功。</td></tr></table></body></html>

3.2.7.3 导入用户证书

<html><body><table><tr><td>函数原型</td><td>ULONG DEVAPI SKF_ImportCertificate(HCONTAINER hContainer, BOOL bSignFlag，BYT pbCert,ULONGulCertLen);</td></tr><tr><td>功能描述</td><td>导入格式为单行Base64编码的证书到hContainer容器中</td></tr><tr><td>参数</td><td>hContainer [IN]容器句柄。</td></tr><tr><td></td><td>bSignFlag [IN]1为签名证书，0为加密证书</td></tr><tr><td></td><td>pbCert [IN]格式为单行Base64编码的证书字符串</td></tr><tr><td></td><td>ulCertLen [IN]证书长度</td></tr><tr><td>返回值</td><td>SAR_OK: 成功。</td></tr><tr><td></td><td>SAR_NOTINITIALIZEERR：传入的参数指针为空或应用句柄未初始化</td></tr><tr><td></td><td>SAR_INDATALENERR：传入证书长度错误</td></tr><tr><td></td><td>SAR_FILE_NOT_EXIST文件不存在</td></tr></table></body></html>

SAR_WRITEFILEERR 写入证书错误

### 3.2.7.4 导出用户证书

函数原型 ULONG DEVAPI SKF_ExportCertificate(HCONTAINER hContainer, BOOL bSignFlag,  BYTE\*pbCert, ULONG \*pulCertLen);  
功能描述 导出 hContainer 容器中的证书  
参数 hContainer [IN] 容器句柄。bSignFlag [IN] 1 为签名证书，0 为加密证书pbCert [OUT] 证书字符串ulCertLen [IN，OUT] 输入时表示pbCert 缓冲区的长度，输出时表示明文数据的实际长度。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_FILE_NOT_EXIST：密钥不存在SAR_BUFFER_TOO_SMALL：存储密钥的缓冲区太小SAR_READFILEERR：读取密钥错误SWKR_CRER_USERCERT_FORMAT_ERR：用户证书格式错误

### 3.2.7.5 验证证书

数原型 ULONG DEVAPI SKF_VerifyCertificate(HCONTAINER hContainer)  
能描述 使用该容器中的密钥对与根证书验证该容器中的证书。  
数 hContainer [IN] 容器句柄。  
回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR： 传入的参数指针为空或应用句柄未初始化SAR_USER_NOT_LOGGED_IN：非用户权限SAR_KEYINFOTYPEERR：容器类型错误SWKR_CRER_ROOT_CERT_LOAD_ERR：加载根证书失败SWKR_CRER_USERCERT_FORMAT_ERR：用户证书格式错误SWKR_CRER_ROOTCERT_PUBLICKEY_ERR：加载根证书中的公钥失败SWKR_CRER_ROOT_SIGNATURE_VERIFY_ERR：根证书签名验证失败SWKR_CRER_ROOT_SIGNATURE_NOT_MATCH_CERT：根证书签名验证不匹配SKF_ExportCertificate 错误码CRER_ROOT_CERT_LOAD_ERR：加载根证书失败CRER_USERCERT_PUBLICKEY_ERR：加载用户证书中的公钥失败CRER_USER_SIGNATURE_VERIFY_ERR：用户证书签名验证失败CRER_USER_SIGNATURE_NOT_MATCH_CERT：用户证书签名验证不匹配CRER_USERCERT_TIME_ERR：用户证书时间无效CRER_USERCERT_PUBLICKEY_NOT_MATCH：用户证书公钥与本地公钥不匹配

## 3.2.8 接口扩展

### 3.2.8.1 释放对称加密密钥

函数原型 ULONG DEVAPI SKF_FreeSymmKey(HANDLE\* phKey);功能描述 释放对称加密密钥句柄参数 PhKey [IN] 对称密钥对象句柄。

返回值 SAR_OK： 成功。其他： 错误码。  
备注 用于 SKF_SetSymmKey 后释放对称密钥对象句柄

### 3.2.8.2 释放摘要句柄

函数原型 ULONG DEVAPI SKF_FreeHandleDigest(HANDLE\* hDigestHandle);功能描述 释放摘要句柄  
参数 hDigestHandle [IN] 对称密钥对象句柄。  
返回值 SAR_OK： 成功。  
其他： 错误码。  
备注 用于 SKF_DigestInit 后释放摘要对象句柄

### 3.2.8.3 释放消息鉴别码句柄

函数原型 ULONG DEVAPI SKF_FreeHandleMac(HANDLE\* hMacHandle);功能描述 释放消息鉴别码句柄  
参数 hMacHandle [IN] 对称密钥对象句柄。  
返回值 SAR_OK： 成功。  
其他： 错误码。  
备注 用于SKF_MactInit 后释放消息鉴别码对象句柄

## 3.2.9 协签新标准接口

3.2.9.1 生成ECC 签名密钥对

函数原型 ULONG DEVAPI SKF_GenECCKeyPair (HCONTAINER hContainer, ULONG ulAlgId，ECCPUBLICKEYBLOB \*pBlob)  
功能描述 生成ECC 签名密钥对并输出签名公钥。  
参数 hContainer [IN] 密钥容器句柄。ulAlgId [IN] 算法标识，只支持 SGD_SM2_1 算法。pBlob [OUT] 返回 ECC 公钥数据结构。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_USER_NOT_LOGGED_IN：非用户权限SAR_KEYINFOTYPEERR：非 SM2 类算法SWKR_ECC_SPLIT_KEY_ERROR：分割密钥计算错误SWKR_BASE64_DECODE_ERROR：公钥生成错误SAR_FILE_NOT_EXIST 文件不存在SAR_WRITEFILEERR 写入 SM2 密钥错误  
备注 权限要求：需要用户权限。

3.2.9.2 ECC 签名

函数原型 ULONG DEVAPI SKF_ECCSignData (HCONTAINER hContainer, BYTE \*pbData, ULONGulDataLen, PECCSIGNATUREBLOB pSignature)  
功能描述 ECC 数字签名。采用ECC 算法和指定私钥hKey，对指定数据pbData 进行数字签名。签名后的结果存放到 pSignature 中。  
参数 hContainer [IN] 密钥容器句柄。pbData [IN] 待签名的数据。ulDataLen [IN] 待签名数据长度，必须小于密钥模长。pSignature [OUT] 签名值。

返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_USER_NOT_LOGGED_IN：非用户权限SAR_KEYINFOTYPEERR：容器类型非 SM2 类SAR_READFILEERR：读取私钥文件错误SWKR_ECC_SPLIT_SIGN_ERROR：分割签名计算错误SWKR_ECC_COMBINE_SIGN_ERROR：联合签名计算错误备注 权限要求：需要用户权限。输入数据为待签数据的杂凑值。当使用SM2 算法时，该输入数据为待签数据经过SM2签名预处理的结果，预处理过程遵循《公钥密码基础设施应用技术体系 SM2 算法密码使用规范》。

3.2.9.3 ECC 签名

函数原型 ULONG DEVAPI SKF_ECCSignData (HCONTAINER hContainer, BYTE \*pbData, ULONGulDataLen, PECCSIGNATUREBLOB pSignature)  
功能描述 ECC 数字签名。采用ECC 算法和指定私钥hKey，对指定数据pbData 进行数字签名。签名后的结果存放到 pSignature 中。  
参数 hContainer [IN] 密钥容器句柄。pbData [IN] 待签名的数据。ulDataLen [IN] 待签名数据长度，必须小于密钥模长。pSignature [OUT] 签名值。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_USER_NOT_LOGGED_IN：非用户权限SAR_KEYINFOTYPEERR：容器类型非 SM2 类SAR_READFILEERR：读取私钥文件错误SWKR_ECC_SPLIT_SIGN_ERROR：分割签名计算错误SWKR_ECC_COMBINE_SIGN_ERROR：联合签名计算错误

备注 权限要求：需要用户权限。输入数据为待签数据的杂凑值。当使用SM2 算法时，该输入数据为待签数据经过SM2签名预处理的结果，预处理过程遵循《公钥密码基础设施应用技术体系 SM2 算法密码使用规范》。

3.2.9.4 ECC 签名

函数原型 ULONG DEVAPI SKF_ECCSignData (HCONTAINER hContainer, BYTE \*pbData, ULONGulDataLen, PECCSIGNATUREBLOB pSignature)  
功能描述 ECC 数字签名。采用ECC 算法和指定私钥hKey，对指定数据pbData 进行数字签名。签名后的结果存放到 pSignature 中。  
参数 hContainer [IN] 密钥容器句柄。pbData [IN] 待签名的数据。ulDataLen [IN] 待签名数据长度，必须小于密钥模长。pSignature [OUT] 签名值。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空

SAR_USER_NOT_LOGGED_IN：非用户权限SAR_KEYINFOTYPEERR：容器类型非 SM2 类SAR_READFILEERR：读取私钥文件错误SWKR_ECC_SPLIT_SIGN_ERROR：分割签名计算错误SWKR_ECC_COMBINE_SIGN_ERROR：联合签名计算错误备注 权限要求：需要用户权限。输入数据为待签数据的杂凑值。当使用SM2 算法时，该输入数据为待签数据经过SM2签名预处理的结果，预处理过程遵循《公钥密码基础设施应用技术体系 SM2 算法密码使用规范》。

### ******* ECC 协同解密

函数原型 ULONG DEVAPI SKF_ECCDecrypt(HCONTAINER hContainer,PECCCIPHERBLOBpCipherText,BYTE \*pbPlainText,ULONG \*pulPlainTextLen)

功能描述 使用容器中的加密私钥进行协同运算对输入数据做解密运算并输出结果。

参数 hDev [IN] 设备句柄。pCipherText [IN] 待解密的密文数据。pbPlainText [OUT]  返 回 明 文 数 据 ， 如 果 该 参 数 为 NULL ， 则 由pulPlainTextLen 返回明文数据的实际长度。pulPlainTextLen [IN，OUT] 输入时表示 pbPlainText 缓冲区的长度，输出时表示明文数据的实际长度。

返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_USER_NOT_LOGGED_IN：非用户权限SAR_READFILEERR：读取私钥错误SWKR_ECC_SPLIT_SIGN_ERROR：分割签名计算错误SWKR_ECC_COMBINE_SIGN_ERROR：联合签名计算错误SAR_KEYINFOTYPEERR：容器类型错误，非 SM2 类其他： 错误码。

### ******* ECC 协同签名生成证书请求

函数原型 ULONG DEVAPI SKF_GenerateEccCSR(HCONTAINER hContainer, LPSTR szDn, LPSTRszCSRCertRequest)  
功能描述 使用容器中的ECC 签名私钥生成证书请求。  
参数 hContainer [IN] 容器句柄。szDn [IN] 设备名 ANSI 格式szCSRCertRequest [OUT] P10 内容 ANSI 格式。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_USER_NOT_LOGGED_IN：非用户权限SAR_NAMELENERR：设备名称过长SWKR_CONTAINER_NAME_INVALID：容器名无效SWKR_ECC_GEN_REQ_ERROR：生成请求错误SAR_KEYNOTFOUNDERR：公钥不存在SAR_INVALIDPARAMERR：读取的公钥格式错误或产生的 X509 请求格式错误CRER_SIGNDATA_TO_ANS1_ERROR：签名值转 ANS1 错误SKF_ECCSignData 错误码

其他： 错误码。

3.2.9.7 ECC 协同签名生成证书请求

函数原型 ULONG DEVAPI SKF_GenerateEccCSR(HCONTAINER hContainer, LPSTR szDn, LPSTRszCSRCertRequest)  
功能描述 使用容器中的ECC 签名私钥生成证书请求。  
参数 hContainer [IN] 容器句柄。szDn [IN] 设备名 ANSI 格式szCSRCertRequest [OUT] P10 内容 ANSI 格式。  
返回值 SAR_OK： 成功。SAR_NOTINITIALIZEERR：容器句柄未初始化SAR_INVALIDPARAMERR：传入的参数指针为空SAR_USER_NOT_LOGGED_IN：非用户权限SAR_NAMELENERR：设备名称过长SWKR_CONTAINER_NAME_INVALID：容器名无效SWKR_ECC_GEN_REQ_ERROR：生成请求错误SAR_KEYNOTFOUNDERR：公钥不存在SAR_INVALIDPARAMERR：读取的公钥格式错误或产生的 X509 请求格式错误CRER_SIGNDATA_TO_ANS1_ERROR：签名值转 ANS1 错误SKF_ECCSignData 错误码其他： 错误码。

# 附录A 错误代码定义和说明

<html><body><table><tr><td>宏描述</td><td>预定义值</td><td>说明</td></tr><tr><td>SAR_OK</td><td>Ox00000000</td><td>成功</td></tr><tr><td> SAR_FAIL</td><td>0x0A000001</td><td>失败</td></tr><tr><td>SAR_UNKNOWNERR</td><td>0x0A000002</td><td>异常错误</td></tr><tr><td>SAR_NOTSUPPORTYETERR</td><td>0x0A000003</td><td>不支持的服务</td></tr><tr><td>SAR_FILEERR</td><td>0x0A000004</td><td>文件操作错误</td></tr><tr><td>SAR_INVALIDHANDLEERR</td><td>Ox0A000005</td><td>无效的句柄</td></tr><tr><td>SAR_INVALIDPARAMERR</td><td>Ox0A000006</td><td>无效的参数</td></tr><tr><td>SAR_READFILEERR</td><td>Ox0A000007</td><td>读文件错误</td></tr><tr><td>SAR_WRITEFILEERR</td><td>Ox0A000008</td><td>写文件错误</td></tr><tr><td>SAR_NAMELENERR</td><td>0x0A000009</td><td>名称长度错误</td></tr><tr><td>SAR_KEYUSAGEERR</td><td>0x0A00000A</td><td></td></tr><tr><td>SAR_MODULUSLENERR</td><td>0x0A00000B</td><td>密钥用途错误</td></tr><tr><td>SAR_NOTINITIALIZEERR</td><td>Ox0A00000C</td><td>模的长度错误 未初始化</td></tr><tr><td>SAR_OBJERR</td><td>0x0A00000D</td><td>对象错误</td></tr><tr><td>SAR_MEMORYERR</td><td>Ox0A00000E</td><td>内存错误</td></tr><tr><td>SAR_TIMEOUTERR</td><td>0x0A00000F</td><td>超时</td></tr><tr><td>SAR_INDATALENERR</td><td>0x0A000010</td><td>输入数据长度错误</td></tr><tr><td>SAR_INDATAERR</td><td>0x0A000011</td><td>输入数据错误</td></tr><tr><td>SAR_GENRANDERR</td><td>0x0A000012</td><td>生成随机数错误</td></tr><tr><td>SAR_HASHOBJERR</td><td>0x0A000013</td><td>HASH对象错</td></tr><tr><td>SAR_HASHERR</td><td>0x0A000014</td><td>HASH运算错误</td></tr><tr><td>SAR_GENRSAKEYERR</td><td>0x0A000015</td><td>产生RSA密钥错</td></tr><tr><td>SAR_RSAMODULUSLENERR</td><td>0x0A000016</td><td>RSA 密钥模长错误</td></tr><tr><td>SAR_CSPIMPRTPUBKEYERR</td><td>0x0A000017</td><td>CSP 服务导入公钥错误</td></tr><tr><td>SAR_RSAENCERR</td><td>0x0A000018</td><td>RSA 加密错误</td></tr><tr><td>SAR_RSADECERR</td><td>0x0A000019</td><td>RSA 解密错误</td></tr><tr><td>SAR_HASHNOTEQUALERR</td><td>0x0A00001A</td><td>HASH值不相等</td></tr><tr><td>SAR_KEYNOTFOUNTERR</td><td>Ox0A00001B</td><td>密钥未发现</td></tr><tr><td>SAR_CERTNOTFOUNTERR</td><td>0x0A00001C</td><td>证书未发现</td></tr><tr><td>SAR_NOTEXPORTERR</td><td>0x0A00001D</td><td>对象未导出</td></tr><tr><td>SAR_DECRYPTPADERR</td><td>Ox0A00001E</td><td>解密时做补丁错误</td></tr><tr><td>SAR_MACLENERR</td><td>0x0A00001F</td><td>MAC长度错误</td></tr><tr><td>SAR_BUFFER_TOO_SMALL</td><td>0x0A000020</td><td>缓冲区不足</td></tr><tr><td>SAR_KEYINFOTYPEERR</td><td>Ox0A000021</td><td>密钥类型错误</td></tr><tr><td></td><td></td><td></td></tr><tr><td>SAR_NOT_EVENTERR</td><td>0x0A000022</td><td>无事件错误</td></tr><tr><td>SAR_DEVICE_REMOVED</td><td>Ox0A000023</td><td>设备已移除</td></tr><tr><td>SAR_PIN_INCORRECT</td><td>0x0A000024</td><td>PIN不正确</td></tr></table></body></html>

<html><body><table><tr><td colspan="3"></td></tr><tr><td>SAR_PIN_LOCKED</td><td>0x0A00O025</td><td>PIN被锁死</td></tr><tr><td>SAR_PIN_INVALID</td><td>0x0A000026</td><td>PIN无效</td></tr><tr><td>SAR_PIN_LEN_RANGE</td><td>Ox0A0O0027</td><td>PIN 长度错误</td></tr><tr><td>SAR_USER_ALREADY_LOGGED_IN</td><td>Ox0A000028</td><td>用户已经登录</td></tr><tr><td>SAR_USER_PIN_NOT_INITIALIZED</td><td>Ox0A000029</td><td>没有初始化用户口令</td></tr><tr><td>SAR_USER_TYPE_INVALID</td><td>0x0A00002A</td><td>PIN类型错误</td></tr><tr><td>SAR_APPLICATION_NAME_INVALID</td><td>0x0A00002B</td><td>应用名称无效</td></tr><tr><td>SAR_APPLICATION_EXISTS</td><td>Ox0A00O02C</td><td>应用已经存在</td></tr><tr><td>SAR_USER_NOT_LOGGED_IN</td><td>0x0A00002D</td><td>用户没有登录</td></tr><tr><td>SAR_APPLICATION_NOT_EXISTS</td><td>Ox0A00002E</td><td>应用不存在</td></tr><tr><td>SAR_FILE_ALREADY_EXIST</td><td>0x0A00002F</td><td>文件已经存在</td></tr><tr><td> SAR_NO_ROOM</td><td>Ox0A000030</td><td>空间不足</td></tr><tr><td>SAR_FILE_NOT_EXIST</td><td>Ox0A000031</td><td>文件不存在</td></tr><tr><td>SAR_REACH_MAX_CONTAINER_COUNT</td><td>0x0A000032</td><td>已达到最大可管理容器数</td></tr><tr><td></td><td></td><td></td></tr><tr><td>SAR_BASE</td><td>0x0A000000</td><td>基础扩展错误码 通用情况</td></tr><tr><td>SWKR_BASE</td><td>Ox0A100OO0</td><td>设备未认证</td></tr><tr><td>SWKR_INNER_ERROR</td><td>Ox0A100001</td><td>内部错误，详情看日志提示</td></tr><tr><td>SWKR_BASE64_DECODE_ERROR</td><td>Ox0A100002</td><td>base64解码错误</td></tr><tr><td>SWKR_BASE64_ENCODE_ERROR</td><td>0x0A100003</td><td>base64转码错误</td></tr><tr><td>SWKR_PADDING_ERROR</td><td>Ox0A100004</td><td>Padding错误</td></tr><tr><td>SWKR_UNPADDING_ERROR</td><td>0x0A100005</td><td>UnPadding错误</td></tr><tr><td>SWKR_CHK_PADDING_ERROR</td><td>Ox0A100006</td><td>检查Padding错误</td></tr><tr><td>SWKR_STR_TO_HEX_ERROR</td><td>Ox0A100007</td><td>字符串转16进制字符串错误</td></tr><tr><td>SWKR_FILE_DIR_ERROR</td><td>Ox0A100008</td><td>文件夹操作错误</td></tr><tr><td></td><td></td><td></td></tr><tr><td>SWKR_STRUCT_BASE</td><td>0x0A200000</td><td>接口扩展错误码 设备、应</td></tr><tr><td>SWKR_DEVICE_NOT_AUTH</td><td>Ox0A20OO01</td><td>用、容器层错误 设备未认证</td></tr><tr><td>SWKR_APPLICATION_MAX</td><td>Ox0A200002</td><td>应用数目已达到最大值</td></tr><tr><td>SWKR_APPLICATION_LOCKED</td><td>Ox0A20O003</td><td>设备已锁定</td></tr><tr><td>SWKR_CONTAINER_NAME_INVALI</td><td>0x0A200004</td><td>容器名无效</td></tr><tr><td>SWKR_CONTAINER_EXIST</td><td>0x0A200O05</td><td>容器重名</td></tr><tr><td>SWKR_CONTAINER_MAX</td><td>0x0A200006</td><td>容器达到最大值，不能再创建</td></tr><tr><td>SWKR_CONTAINER_NOT_EXIST</td><td>Ox0A200O07</td><td>指定名称的容器不存在</td></tr><tr><td>SWKR_FILE_PERMISSION_ERROR</td><td>0x0A200008</td><td>文件容器增删权限错误</td></tr><tr><td></td><td></td><td></td></tr><tr><td>SWKR_HASH_BASE</td><td>0x0A300O00</td><td>摘要扩展错误码</td></tr><tr><td>SWKR_HASH_CTX_INIT_ERROR</td><td>0x0A300001</td><td>摘要上下文初始化错误</td></tr><tr><td>SWKR_HASH_UPDATE_ERROR</td><td>Ox0A300002</td><td>更新摘要错误</td></tr><tr><td>SWKR_HASH_FINAL_ERROR</td><td>Ox0A300003</td><td></td></tr><tr><td></td><td></td><td>结束摘要计算错误</td></tr></table></body></html>

<html><body><table><tr><td colspan="3"></td></tr><tr><td>SWKR_SYM_BASE</td><td>0x0A400000</td><td>对称加密扩展错误码</td></tr><tr><td>SWKR_SYM_CTX_INIT_ERROR</td><td>0x0A400001</td><td>SM4初始化上下文错误</td></tr><tr><td>SWKR_SYM_CIPHER_INIT_ERROR</td><td>0x0A400002</td><td>SM4初始化加密上下文错误</td></tr><tr><td>SWKR_SYM_CIPHER_UPDATE_ERROR</td><td>0x0A400003</td><td>SM4 加密上下文更新错误</td></tr><tr><td>SWKR_SYM_CIPHER_FINAL_ERROR</td><td>0x0A400004</td><td>SM4结束加密错误</td></tr><tr><td>SWKR_SYM_PARAM_PADDING_ERROR</td><td>0x0A400005</td><td>SM4 参数填充类型错误，当前 不支持填充或非指定类型</td></tr><tr><td>SWKR_SYM_PLAIN_LEN_ERROR</td><td>0x0A400006</td><td>对称加密明文长度错误</td></tr><tr><td>SWKR_SM4IV_ERROR</td><td>Ox0A400007</td><td>sm4-IV错误不存在或长度有误</td></tr><tr><td></td><td></td><td></td></tr><tr><td>SWKR_ASY_BASE</td><td>0x0A000000</td><td>非对称扩展错误码</td></tr><tr><td>SWKR_ECCENCERR</td><td>Ox0A500001</td><td>ECC 加密错误</td></tr><tr><td>SWKR_ECCDECERR</td><td>Ox0A500002</td><td>ECC解密错误</td></tr><tr><td>SWKR_ECC_KEY_FORMAT_ERROR</td><td>Ox0A500003</td><td>密钥格式错误</td></tr><tr><td>SWKR_ECC_SPLIT_SIGN_ERROR</td><td>0x0A500004</td><td>密钥协商计算错误</td></tr><tr><td>SWKR_ECC_COMBINE_SIGN_ERROR</td><td>Ox0A500005</td><td>密钥协商计算错误</td></tr><tr><td>SWKR_ECC_SPLIT_KEY_ERROR</td><td>Ox0A500006</td><td>SM2密钥分割错误</td></tr><tr><td>SWKR_ECC_EXCHANGE_CAL_ERROR</td><td>Ox0A500007</td><td>密钥协商计算错误*</td></tr><tr><td>SWKR_GENECCKEYERR</td><td>0x0A500008</td><td>生成SM2密钥对错误</td></tr><tr><td>SWKR_ECC_GEN_REQ_ERROR</td><td>Ox0A500009</td><td>生成 SM2请求错误</td></tr><tr><td>SWKR_RSA_VERIFY_ERROR</td><td>0x0A50000A</td><td>RSA验签错误</td></tr><tr><td>SWKR_RSA_SIGB_ERROR</td><td>0x0A50000B</td><td>RSA签名错误</td></tr><tr><td></td><td></td><td></td></tr><tr><td>SWKR_CRER_BASE</td><td>0x0A000000</td><td>证书扩展错误码</td></tr><tr><td>SWKR_CRER_ROOT_CERT_LOAD_ERR</td><td>Ox0A600001</td><td>加载根证书失败</td></tr><tr><td>SWKR_CRER_ROOTCERT_PUBLICKEY_ERR</td><td>Ox0A600002</td><td>加载根证书中的公钥失败</td></tr><tr><td>SWKR_CRER_ROOT_SIGNATURE_VERIFY_ERR</td><td>0x0A600003</td><td>根证书签名验证失败</td></tr><tr><td>SWKR_CRER_ROOT_SIGNATURE_NOT_MATCH_CER T</td><td>0x0A600004</td><td>根证书签名验证不匹配</td></tr><tr><td>SWKR_CRER_USERCERT_FORMAT_ERR</td><td>0x0A600005</td><td>用户证书格式错误</td></tr><tr><td>SWKR_CRER_USERCERT_PUBLICKEY_ERR</td><td>Ox0A600006</td><td>加载用户证书中的公钥失败</td></tr><tr><td>SWKR_CRER_USER_SIGNATURE_VERIFY_ERR</td><td>Ox0A600007</td><td>用户证书签名验证失败</td></tr><tr><td>SWKR_CRER_USER_SIGNATURE_NOT_MATCH_CERT</td><td>Ox0A600008</td><td>用户证书签名验证不匹配</td></tr><tr><td>SWKR_CRER_USERCERT_TIME_ERR</td><td>Ox0A600009</td><td>用户证书时间无效</td></tr><tr><td>SWKR_CRER_USERCERT_PUBLICKEY_NOT_MATCH</td><td>0x0A60000A</td><td>用户证书公钥与本地公钥不匹</td></tr><tr><td>SWKR_CRER_SIGNDATA_TO_ANS1_ERROR</td><td>0x0A60000B</td><td>配 签名值转ANS1错误</td></tr><tr><td></td><td></td><td></td></tr></table></body></html>

### 附录B 公司简介

三未信安科技股份有限公司（以下简称“三未信安”）成立于 2008 年8 月，注册资金2353 万元人民币。总部位于北京市朝阳区，公司在济南设有研发中心，在上海、广州设有分公司，在多地设有办事处。

三未信安专注于基于密码技术的产品和解决方案的研发、销售，是国家密码管理局批准的商用密码产品生产定点单位和商用密码产品销售许可单位，是经过国家级认证的高新技术企业。

三未信安通过了ISO9001 质量保证体系认证、ISO27001 信息安全管理体系认证、CMMI 3 级软件能力成熟度模型认证等。

三未信安的创始技术团队来源于山东大学，山东大学在密码理论和技术方面在中国处于领先地位。公司核心研发团队由中国较早从事商用密码技术和产品研发的专家、资深技术人员组成。公司是全国信息安全标准化技术委员会成员单位和密码行业标准化技术委员会成员单位，有多位专家参与中国信息安全相关技术标准、规范的制定工作。

三未信安注重研发投入、技术创新，公司积极进行新技术和产品的研发。公司还与山东大学成立了联合实验室，共同进行新技术、新产品的研发。2011 年，密码机、密码卡产品获得 “密码科技进步三等奖”（省部级）。2013 年，签名验证服务器获得“密码科技进步三等奖（省部级）”。同年，取得北京产品评价中心颁发的“产品质量创新贡献奖-创新成果奖”。由于技术先进、产品优秀，三未信安还获得国家科技部、国家发改委、以及北京市、朝阳区的多次项目资金资助。

三未信安是中国主要的商用密码设备供应商，已经推出了适用于各种应用场景的商密产品，包括：密码卡、SD 密码卡、服务器密码机、金融数据密码机、签名验证服务器、安全认证网关、安全存储网关等硬件产品，以及手机盾、身份认证系统、密钥管理系统、数字版权管理系统、统一用户管理及单点登录系统等软件系统。针对云安全需求，三未信安开发了高速加密卡、云密码机/云密码服务平台、云密钥管理系统以及云存储加密系列产品等。、

三未信安的产品已广泛应用于金融、证券、电力、电信、石油、铁路、交通等行业，以及海关、公安、税务、水利、质检、国家政务外网等政府部门。公司注重对用户的售后服务，我们认为优异的产品配合完善的服务才能形成良好的应用。

三未信安的目标是成为国际领先的密码产品和解决方案供应商。

### 附录C 联系方式

## 北京总部

m 目 □ M .

电话： +86-10-5978 5977   
传真： +86-10-5978 5937   
邮箱： <EMAIL>   
地址： 北京市朝阳区广顺北大街16 号院2 号楼华彩大厦16 层 [100102]   
网址： www.sansec.com.cn

## 上海分公司

电话： +86-21-2023 8100  
传真： +86-21-2023 8100  
地址： 上海市浦东新区金科路2966 号创智空间北楼6A [201204]

## 广州分公司

m 电话： +86-20-38939962 传真： +86-20-38939962 地址： 广州市天河区天河路 490 号壬丰大厦东厅 2204 [510630]

## 济南研发中心

曲 电话： +86-531-8898 8936/7  
员 传真： +86-531-8898 8936-8080  
0 地址： 济南市高新区新泺大街 1299 号鑫盛大厦 2 号楼 18 层 [250101]