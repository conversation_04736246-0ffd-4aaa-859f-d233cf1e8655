协同签名服务用户手册

三未信安科技股份有限公司   www.sansec.com.cn

# 版权声明

版权所有 $\circledcirc$ 三未信安科技股份有限公司 2025 保留一切权利（包括但不限于修订、最终解释权）。

本文档由三未信安科技股份有限公司编写，仅用于用户和合作伙伴阅读。本公司依中华人民共和国著作权法，享有及保留一切著作之专属权利。未经本公司书面许可，任何单位和个人不得以任何方式或形式对文档内任何部分或全部内容进行擅自摘抄、增删、复制、仿制、备份和修改，并不得以任何形式传播。

# 特别提示

由于产品版本升级或其他原因，本文档内容会不定期更新，更新的内容会在本文档发行新版本时予以印刷。本文档仅用于为最终用户提供信息或使用指导，文档中的陈述、信息和建议不构成任何明示或暗示的担保。您所购买产品的硬件配置、功能、特性或服务等应受本公司商业合同和条款约束。本文档中描述的硬件配置、功能、特性或服务可能不在您的购买或使用范围之内。任何情况下，本公司均不对（包括但不限于）最终用户或任何第三方因使用本文档而造成的直接和间接损失或损害负责。

# 联系我们

感谢您使用我们的产品，如果您对我们的产品有什么意见和建议，可以通过电话、传真或电子邮件等方式向我们反馈。

电话：+86-10-5978 5977  
传真：+86-10-5978 5937  
邮箱：<EMAIL>  
网址：www.sansec.com.cn  
各地分公司与办事处地址请前往官方网站查阅。

# 目录

1. 产品简介. 4  
1.1 协同签名. 4  
1.1.1 产品介绍. 4  
1.1.2 关键技术.  
2. 服务管理. 5  
2.1 租户操作员. 5  
2.1.1 用户管理. 5  
2.1.2 密钥管理. 8  
2.1.3 业务信息. 12  
公司介绍. . 14

### 外部公开

# 1. 产品简介

## 1.1 协同签名

### 1.1.1 产品介绍

协同签名系统是针对移动互联网设计的一种能够提供可信数字签名的产品。该系统在理论创新的基础上，依托于密钥分组、分布式数字签名的专利技术，通过手机端与服务端独立存储密钥分量，双方协作签名，该系统能够有效解决移动互联网中安全的数字签名的问题，可以在用户端无需任何额外硬件介质的条件下，为用户提供出安全合规的数字签名。

### 1.1.2 关键技术

#### 签名密钥分割

协同签名系统的安全设计依托于密钥分割专利技术，通过将传统的密钥进行分割为客户端密钥因子与服务器端密钥因子两部分，手机与服务器分别存储各自的密钥因子，以此保证密钥的安全存储。通过注册时交换临时公钥信息，计算出完整的用户公钥，通过 CA机构颁发的数字证书对外发布。

#### 无硬件介质依赖

协同签名系统中签名密钥采用分割原理，在用户端与服务器端都只出现部分密钥，完整的签名密钥在密钥周期的任何时刻都不会出现，这杜绝了用户签名私钥暴露的风险，因此系统中无需额外的硬件介质进行密钥的存储，降低了成本的同时，极大的提高了数字签名在移动端的应用场景。

#### 密钥因子安全存储

用户手机端的密钥因子采用加密存储，且与设备强相关，用户进行设备变更需要用户提供可信的身份证明；服务器端密钥因子存储采用硬件级加密，密钥因子不会明文出现在硬件之外；在签名业务过程中，密钥因子的调用都要求用户的授权许可，充分保证了密钥因子的安全。

#### 多方参与，协作签名

针对业务系统的签名请求，客户端与服务端分别独自计算各自的签名结果，双方各自的签名结果作为中间结果，通过中间结果无法推导任何签名信息，服务器端将中间结果传送给客户端，由客户端最终完成数字签名的合成。签名结果的验证则由服务器端通过用户证书按照传统的验签方式完成。

# 2. 服务管理

## 2.1 租户操作员

### 2.1.1 用户管理

租户操作员在密码服务管理平台登录后，可在子业务系统中管理协同签名服务，用户管理功能下主要有已审核用户和业务信息查看，如下图：

![](images/7d490caef9bbdd1dbde57787a9374af142160465114eb7ad2e73486f153c476b.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张密码服务管理平台的截图，显示了“已审核用户”页面的内容。页面左侧有一个导航栏，列出了多个功能模块，包括首页、密码产品、应用管理、密码资源、计量分析、密评管理、数据加解密、签名验签、密钥管理、文件加密、时间戳、协同签名、已审核用户、业务信息、资源信息、动态令牌、数据库加密和电子签章等。

在页面的主要部分，有一个表格列出了已审核用户的详细信息。表格的列标题包括用户名、创建日期、用户状态和操作。每一行代表一个用户，显示了他们的用户名、创建日期和用户状态（通过开关按钮表示是否启用）。在操作列中，每个用户都有“查看密钥”和“更多”选项，可以进行进一步的操作。

此外，在表格上方还有一些功能按钮，如新增、批量导入用户、导出用户、导出全部未发证用户P10和导入证书等。用户可以通过这些按钮执行相应的操作。

页面右上角显示了当前登录的租户操作员信息，包括租户ID和操作员名称。
```


在已审核用户中可查看用户、维护用户状态、新增用户、批量导入用户、导出为发证用户P10 和导入证书，操作如下：

修改用户状态：

![](images/88279dcaa38eeff211ead54b20a950b3bf4b1043c958d2daddedc7fd89cc5cc6.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户管理界面，分为上下两个部分。上半部分是一个弹出的提示框，询问是否确定要将用户状态改为不可用，背景是用户列表，列出了多个用户的用户名、创建日期和用户状态等信息。下半部分是另一个用户列表，显示了操作成功的信息，并列出了与上半部分相似的用户信息，但这里的用户状态都是可用的。整个界面包含了新增、批量导入用户、导出用户、导出全部未发证用户P10和导入证书等功能按钮。
```
  
前往 页

协同签名 用户手册

![](images/cedd79568eccc7d989971fcebc9a4c15da6575d241ed73ad9bfe27a6fb10a157.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户管理界面，其中包含一个弹出窗口用于新增用户。弹出窗口中有一个输入框，用户正在输入用户名“tttttt”。下方有一个表格，列出了已有的用户信息，包括用户名、创建日期、用户状态和操作选项。表格中的用户状态可以通过开关按钮进行启用或禁用。此外，还有一个绿色的提示框，显示“新增成功”，表明刚刚添加的用户已经成功创建。
```
  
新增用户：用户名

![](images/057843070e7433158c22fec5c08d3ae9f44521fb64fb5464c51921d7bdb703bd.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户管理界面，其中有一个弹出窗口用于导入用户。弹出窗口的标题是“导入用户”，并提供了上传文件的功能，只允许上传xls和xlsx格式的文件，且文件大小不能超过10MB。弹出窗口中有两个按钮：“取消”和“提交”。背景中可以看到一个用户列表，列出了多个用户的用户名、注册时间、用户状态和操作选项。用户状态可以通过开关进行控制，操作选项包括查看密钥和更多操作。
```
  
批量导入用户：

按照固定用户格式进行导入，格式如下：

<html><body><table><tr><td></td><td>A</td><td>B</td><td></td></tr><tr><td>1</td><td>用户名</td><td></td><td></td></tr><tr><td>2</td><td>1211211111</td><td></td><td></td></tr><tr><td>3</td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr></table></body></html>

导出用户：导出用户信息为excel 表格

![](images/5bbec485330bf841f152247832f0e30745a69c4f77ff0171a50d7fb79e8516ed.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个用户管理界面，界面上方有一个搜索栏和一些操作按钮，包括“新增”、“批量导入用户”、“导出用户”、“导出全部未发证用户P10”和“导入证书”。在搜索栏中，有一个提示信息“导出中...”。下方是一个用户列表，列出了用户的用户名、创建日期、用户状态和操作选项。用户状态通过开关按钮显示，开启表示用户处于活动状态，关闭表示用户被禁用。每个用户后面都有“查看密码”和“更多”选项，可以进行进一步的操作。列表中共有10条记录，每页显示20条，当前是第1页。
```
  
协同签名 用户手册

![](images/eebb550b5b32ae70c6b3ed24a5116834208d59662a56ccdab95abd9f7c6ef002.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户管理界面，其中包含一个弹出窗口，标题为“导出P10”。在弹出窗口中，有一个下拉菜单，当前选择的选项是“SM2”，这可能是指一种加密算法。弹出窗口底部有两个按钮：“取消”和“提交”。

背景中的用户管理界面列出了多个用户的详细信息，包括用户名、创建日期、用户状态和操作选项。用户状态一栏显示了每个用户的激活状态，通过开关图标表示。操作一栏提供了查看密钥和更多选项的功能。

此外，界面顶部有一些功能按钮，如“新增”、“批量导入用户”和“导出用户”，以及一个搜索框和重置按钮。整体来看，这个界面用于管理和操作用户数据，特别是与加密相关的功能。
```
  
导出P10：所有用户下存在的所有P10 文件都会导出来

![](images/4a52dbc83383a6d6e46d631ae88bc0cd2a342166539a2234b861f0f37d25596e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户管理界面，其中包含一个弹出窗口，用于导入证书。弹出窗口的标题是“导入证书”，并且有以下内容：

1. **密钥算法**：选择框中显示了“SM2”。
2. **上传文件**：有一个按钮，上面写着“点击上传”，提示用户可以上传文件，文件大小不超过10MB。
3. **取消和提交按钮**：在弹出窗口的底部有两个按钮，一个是“取消”，另一个是蓝色的“提交”按钮。

背景中可以看到用户管理界面的部分内容，包括用户名列表、创建日期、用户状态和操作选项。用户名列表中有一些示例用户名，如“ttttt”、“test123”等。每个用户的右侧都有一个开关按钮，表示用户的状态（开启或关闭），以及“查看密钥”和“更多”选项。

这个界面看起来是一个用于管理和导入用户证书的系统界面。
```
  
导入证书：用户下证后按照相应格式进行导入（证书名必须要和P10 的名字一致）

更多操作里可以删除用户：

协同签名 用户手册

<html><body><table><tr><td>+新增</td><td>批量导入用户</td><td>导出用户</td><td>②导出全部未发证用户P10</td><td>导入证书</td><td></td><td></td></tr><tr><td>用户名</td><td></td><td></td><td>创建日期</td><td></td><td>用户状态</td><td>操作</td></tr><tr><td>test+=/1</td><td></td><td></td><td></td><td>2025-04-2119:54:18</td><td>O</td><td>查看密钥更多∨</td></tr><tr><td>11112</td><td></td><td></td><td></td><td>2025-04-21 19:54:18</td><td>0</td><td>查 密钥归档</td></tr><tr><td>111221</td><td></td><td></td><td></td><td>2025-04-2119:54:18</td><td>0</td><td>查 删除</td></tr><tr><td>15689732275</td><td></td><td></td><td>2025-04-21 19:54:18</td><td></td><td>0</td><td>查看密钥 史多</td></tr><tr><td></td><td>1er456781234567812345678123456781er45678123456781234567812345678</td><td></td><td>2025-04-21 19:54:18</td><td></td><td>0</td><td>查看密钥更多∨</td></tr><tr><td>aX111=+_aal</td><td></td><td></td><td>2025-04-21 19:54:18</td><td></td><td>0</td><td>查看密钥 更多∨</td></tr><tr><td>test</td><td></td><td></td><td>2025-04-21 19:54:18</td><td></td><td>0</td><td>查看密钥更多∨</td></tr><tr><td>11111</td><td></td><td></td><td>2025-04-2119:54:18</td><td></td><td>0</td><td>查看密钥更多∨</td></tr><tr><td>test+=/</td><td></td><td></td><td>2025-04-21 19:54:17</td><td></td><td>0</td><td>查看密钥更多</td></tr></table></body></html>

### 提示

![](images/2462d7dc9a6f9571dc50a2c53686665c1d6aebc5c46b4dc98ef95578d0d6743c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个黄色的圆形图标，中间有一个白色的感叹号。这种图标通常用来表示警告或注意的信息。在不同的上下文中，它可能意味着需要用户注意某些重要的信息或者提醒用户可能存在潜在的问题或风险。例如，在软件应用中，这样的图标可能会出现在错误消息旁边，提示用户发生了某种问题；在安全标志中，它可能被用来提醒人们注意危险区域或行为。总之，这个图标的主要目的是引起人们的注意，并传达出需要特别关注的信息。
```


确定要删除该用户吗？

### 2.1.2 密钥管理

![](images/5e65593fd6df349bf975720457317c9b8f46882494f1806798733aafccc12a69.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户管理界面，列出了多个用户的详细信息。表格中有三列：用户名、创建日期和用户状态。每个用户的状态可以通过旁边的开关进行激活或停用。此外，每行还包含一个“查看密钥 更多”的操作选项，允许进一步的操作或查看用户的密钥信息。
```
  
在操作功能栏可查看密钥信息和更多操作，如下图所示：

更多操作中会对当前用户进行密钥归档操作：

![](images/3fea2a3a61d830e46d687d9d6486a2d393115fdbee35ff3702e1191dc3d3f217.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示用户管理界面的截图，界面上列出了多个用户的详细信息。每行代表一个用户，包含用户名、创建日期和用户状态等信息。用户状态通过开关按钮表示，开启状态为蓝色，关闭状态为灰色。在操作列中，有“查看密钥 更多”选项，点击后会显示“密钥归档”和“删除”两个操作选项。此外，界面顶部有四个功能按钮，分别是“新增”、“批量导入用户”、“导出用户”、“导出全部未发证用户P10”和“导入证书”。
```
  
前往

![](images/7b8a6c46f2aaf784ab7f14f165062e958fb049d46d6eac09ab5e919fb139032b.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户管理界面，其中包含一个弹出的提示框。提示框的内容是“确定要对该用户密钥进行归档吗？”，并提供了“取消”和“确定”两个选项按钮。背景中可以看到一个用户列表，列出了多个用户的用户名、创建日期、用户状态以及操作选项。用户状态通过开关按钮表示，操作选项包括查看密钥和更多操作。
```
  
协同签名 用户手册

查看密钥可以对用户密钥进行查看，主要内容有用户公钥信息，是否发证，设备号等相关信息，如下图：

$$ 查看用户密钥信息

![](images/b2ba99e76781c74a8a75837936dc1208cba7b1f3fe275b5f2296e67c1b12183f.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户管理界面，列出了多个用户的详细信息。每行代表一个用户，包含以下字段：用户名、签名公钥、加密公钥、签名证书、加密证书、数字信封、设备ID、PIN次数、创建时间、设备信息和HMAC。所有用户的用户名都是“test”，签名公钥各不相同，而加密公钥、签名证书、加密证书和数字信封都显示为“无”。设备ID都是“SDAXAS1W...”，除了第二行显示“被锁定”。PIN次数都是5，创建时间都是2024-09-09 1...，设备信息都是“iPhone Simul...”，HMAC都是“无”。在操作列中，每个用户都有一个“查看证书 更多”的选项。
```


![](images/0382d706d685d37c97efdc87a54bd32b69ed94f2469473ebeb1518becf8bf7b6.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个查看用户密钥信息的界面，具体是一个证书信息的弹窗。弹窗中包含了以下内容：

- 使用者：O=swxa,CN=user,OU=development,L=jn,ST=sd,C=CN,STREET=xxx
- 证书开始时间：2022-05-17 20:17:40
- 证书结束时间：2022-08-25 20:17:40
- 证书序号：1318429325730897348
- 证书类型：1.2.156.10197.1.501

背景中可以看到一个表格，列出了多个用户的签名公钥信息，以及设备信息和操作选项。
```
  
查看证书功能（用户已发证）可查看证书详情：

更多操作可以对用户密钥和设备进行解绑，解绑会删除用户密钥信息

协同签名 用户手册

<html><body><table><tr><td>密钥算法 SM2</td><td>√</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>查询</td><td>重置</td></tr><tr><td colspan="11"></td></tr><tr><td>用户名</td><td>签名公钼</td><td>加密公钥</td><td>签名证书</td><td>加密证书</td><td>数字信封</td><td>设备ID</td><td>PIN次数</td><td>创建时问</td><td>设备信</td><td>操作</td></tr><tr><td>test</td><td>RE75hsi8VIHoYLZq..</td><td>无</td><td>无</td><td>无</td><td>无</td><td>SDAXAS1W..</td><td>5</td><td>2024-09-09 1.</td><td>iPhone</td><td>查看证书更多</td></tr><tr><td>test</td><td>Hh+BjMjT/4s0Zex62..</td><td>无</td><td>无</td><td>无</td><td>无</td><td>SDAXAS1W..</td><td>被锁定</td><td>2024-09-09 1..</td><td>iPhone</td><td>查看证书更多</td></tr><tr><td>test</td><td>QrIUYGlowRSndArlO..</td><td>无</td><td>无</td><td>无</td><td>无</td><td>SDAXAS1W...</td><td>5</td><td>2024-09-09 1..</td><td>iPhone</td><td></td></tr><tr><td>test</td><td>5u/HgmengTNYORS..</td><td>无</td><td>无</td><td>无</td><td>无</td><td> SDAXAS1W..</td><td>5</td><td>2024-09-09 1..</td><td>iPhone</td><td>校验完整性 解冻</td></tr><tr><td>test</td><td>+R88GR9JdTgavQq..</td><td>无</td><td>无</td><td>无</td><td>无</td><td>SDAXAS1W.</td><td>5</td><td>2024-09-09 1..</td><td>iPhone</td><td>解绑</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>查看证书更多</td></tr><tr><td>test</td><td>Ulo7RfMPnkw1ZMn+.</td><td>无 无</td><td></td><td>无</td><td>无</td><td>SDAXAS1W.. 5</td><td></td><td>2024-09-09 1. iPhone</td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

<html><body><table><tr><td>用户名</td><td>签名公钥</td><td>加密公钥</td><td>签名证书</td><td>加密证书</td><td>数字信封</td><td>设备ID</td><td>PIN次数</td><td>创建时间</td><td>设备信</td><td>操作</td></tr><tr><td>test</td><td>RE75hsi8VIHoYLZq..</td><td>无</td><td>无</td><td>无</td><td>无</td><td>SDAXAS1W..</td><td>5</td><td>2024-09-09 1..</td><td>iPhone</td><td>查看证书更多</td></tr><tr><td>test</td><td>Hh+BjMjT/4s0Zex62.</td><td>无</td><td>无</td><td>无</td><td>无</td><td>SDAXAS1W.</td><td>被锁定</td><td>2024-09-09 1.</td><td>iPhone</td><td>查看证书更多</td></tr><tr><td>test</td><td>QrIUYGlowRSndArlO.</td><td>无</td><td>无 提示</td><td></td><td></td><td>1W.</td><td>5</td><td>2024-09-09 1.</td><td>iPhone</td><td>查看证书更多</td></tr><tr><td>test</td><td>5u/HgmengTNYORS..</td><td>无</td><td colspan="2">无</td><td></td><td>× 1W..</td><td>5</td><td>2024-09-09 1.</td><td>iPhone</td><td>查看证书更多∨</td></tr><tr><td>test</td><td>+R88GR9JdTgavQq..</td><td>无</td><td>无</td><td>确定要解绑吗？</td><td></td><td>1W..</td><td>5</td><td>2024-09-09 1.</td><td>iPhone</td><td>查看证书更多</td></tr><tr><td>test</td><td>Ulo7RfMPnkw1ZMn+..</td><td>无</td><td>无</td><td></td><td>取消</td><td>确定 1W..</td><td>5</td><td>2024-09-09 1..</td><td>iPhone</td><td>查看证书更多</td></tr></table></body></html>

当PIN 码次数（默认5 次）被锁定（0 次）时可进行解冻。

<html><body><table><tr><td>用户名</td><td>签名公钥</td><td>加密公钥</td><td>签名证书</td><td>加密证书</td><td>数字信封</td><td>设备ID</td><td>PIN次数</td><td>创建时间</td><td>设备信</td><td>操作</td></tr><tr><td>test</td><td>RE75hsi8VIHoYLZq..</td><td>无</td><td>无</td><td>无</td><td>无</td><td>SDAXAS1W..</td><td>5</td><td>2024-09-09 1..</td><td>iPhone</td><td>查看证书更多</td></tr><tr><td>test</td><td>Hh+BjMjT/4s0Zex62..</td><td>无</td><td>无</td><td>无</td><td>无</td><td>SDAXAS1W..</td><td>被锁定</td><td>2024-09-09 1..</td><td>iPhone</td><td>查看证书更多</td></tr><tr><td>test</td><td>QrIUYGlowRSndArlO..</td><td>无</td><td>无</td><td>无</td><td>无</td><td>SDAXAS1W...</td><td>5</td><td>2024-09-09 1...</td><td>iPhone</td><td>校验完整性</td></tr><tr><td>test</td><td>5u/HgmengTNYORS.</td><td>无</td><td>无</td><td>无</td><td>无</td><td>SDAXAS1W..</td><td>5</td><td>2024-09-09 1..</td><td>iPhone</td><td>解冻</td></tr><tr><td>test</td><td>+R88GR9JdTgavQq.</td><td>无</td><td>无</td><td>无</td><td>无</td><td>SDAXAS1W.</td><td>5</td><td>2024-09-09 1..</td><td>iPhone</td><td>解绑</td></tr><tr><td>test</td><td>Ulo7RfMPnkw1ZMn+..</td><td>无</td><td>无</td><td>无</td><td>无</td><td>SDAXAS1W..</td><td>5</td><td>2024-09-09 1..</td><td>iPhone</td><td>查看证书更多</td></tr></table></body></html>

协同签名 用户手册

![](images/f2d63d606df4f73dd212f19d6dffa9788c57ec1d01b34338f2249560a2419b2b.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个用户界面，其中包含一个弹出的提示框和一个表格。表格列出了多个用户的详细信息，包括用户名、签名公钥、加密公钥、签名证书、加密证书、数字信封、设备ID、PIN次数、创建时间、设备信息以及操作选项。所有列出的用户都使用了相同的用户名“test”，并且他们的签名公钥各不相同，但加密公钥、签名证书、加密证书和数字信封都显示为“无”。设备ID部分显示了部分信息，PIN次数均为5次，创建时间都是2024年9月9日，设备信息一栏显示为iPhone。在操作列中，每个用户都有“查看证书”和“更多”两个选项。

弹出的提示框位于表格的中央，背景为半透明灰色，框内有一个黄色感叹号图标和文字“确定要解冻吗？”，下方有两个按钮：“取消”和“确定”。这表明用户可能正在尝试对某个被锁定的账户进行解冻操作，并且系统要求确认这一操作。
```


如果HMAC 有值，可以进行校验完整性操作

![](images/56eb0c4753e3794cd23f0caf101bd683ad035b5b484246977e072ff080df233b.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个设备管理界面，列出了多个设备的详细信息。每一行代表一个设备，包含以下字段：加密公钥、加密密钥、签名证书、加密证书、数字信封、设备ID、PIN次数、创建时间、设备信息和HMAC。所有设备的加密公钥、加密密钥、签名证书、加密证书和数字信封都显示为“无”。设备ID都是“SDAXAS1W...”，PIN次数大部分为5，其中一个被锁定。创建时间都是2023年10月27日，设备信息显示为“iPhoneSimul...”。HMAC字段大部分为空，最后一个设备的HMAC值为“+UQVhzWA...”。每行右侧有一个“查看证书 更多”的链接，可以查看更多详细信息。页面底部有分页导航，当前显示第1页，共6条记录，每页显示20条。右下角有三个按钮：“校验完整性”、“解冻”和“解绑”。
```


### 提示

![](images/97ae6f2691a8e8cb166719deb065b8d66db7268e67f55a9a7e670e8c1090a8fa.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个用户密钥信息查看界面，界面上方有一个下拉菜单选择密钥算法为SM2，右侧有两个按钮分别是“查询”和“重置”。界面主体部分是一个表格，列出了多个用户的密钥信息，包括加密公钥、加密密钥、签名证书、加密证书、数字信封、设备ID、PIN次数、创建时间、设备信息、HMAC以及操作选项。表格中的数据大部分显示为“无”，只有设备ID和创建时间有具体信息，设备信息一栏显示为“iPhoneSimulator”。在表格的右下角，有一个弹出的提示框，内容是“确定要校验该密钥的完整性吗？”，提示框内有一个黄色的感叹号图标，下方有两个按钮，分别是“取消”和“确定”。
```


协同签名 用户手册

![](images/f33ef7ba1f59bf9e7195cac98e747277a2c712f743484024c01967aedd0733f9.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示用户密钥信息的网页截图。页面顶部有一个红色警告框，提示“校验完整性失败”。在警告框下方，有一个下拉菜单，当前选择的是“SM2”密钥算法。页面右侧有两个按钮，分别是“查询”和“重置”。

中间部分是一个表格，列出了用户的密钥信息。表格的列标题包括：密钥公钥、加密密钥、签名证书、加密证书、数字信封、设备ID、PIN次数、创建时间、设备信息、HMAC和操作。每一行代表一个用户的密钥信息。

从表格内容来看，所有用户的密钥公钥、加密密钥、签名证书、加密证书和数字信封都显示为“无”。设备ID都是“SDAXAS1W...”，除了第二行显示为“被锁定”。PIN次数都是5。创建时间都是“2023-10-27 1...”。设备信息都是“iPhoneSimul...”。HMAC大部分显示为“无”，只有最后一行显示为“+UQVhzWA...”。

在操作列中，每行都有一个蓝色链接“查看证书 更多”，点击可以查看更多详细信息。
```


### 2.1.3 业务信息

业务信息栏可查看并审计协同签名系统中业务信息，如下图：

![](images/05e593dada76d6f53982bb42a7d87501c9aa85563022aa1b694f05149f225a79.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个操作日志或审计记录的界面。界面上方有三个搜索框，分别用于输入用户名、操作类型和操作详情进行筛选查询，右侧有两个按钮，分别是“查询”和“重置”。下方是一个表格，列出了用户的操作记录，包括用户名、操作类型、操作详情、操作结果、审计结果、操作时间和操作选项。

具体到表格中的内容，有一条记录显示用户名为“test2”，操作类型和操作详情都是“sm2签名”，操作结果是“成功”，审计结果目前显示为“未审计”，操作时间是“2023-06-07 20:02:28”，在操作列有一个蓝色的“审计”链接，可能用于进一步的审计操作。

表格底部显示了当前页面的记录总数为1条，每页显示20条记录，当前页码为第1页，以及前往指定页码的输入框。整体来看，这个界面可能是某个系统中用于管理和审计用户操作记录的部分。
```


# 公司介绍

三未信安科技股份有限公司（股票代码：688489）成立于2008 年，是国内主要的密码基础设施提供商，专注于密码技术的研究创新和核心产品的开发、销售及服务，为用户提供全面的商用密码产品和整体解决方案。

三未信安具备从密码芯片、密码板卡、密码整机到密码系统的完整密码产品体系和信创密码建设能力，当前已有五十多款产品取得了国家商用密码产品认证证书，是商用密码产品种类最齐全的公司之一。典型产品包括密码芯片、PCI-E 接口密码卡、服务器密码机、金融数据密码机、签名验签服务器、云密码机、数据库加密机、SSL VPN、IPSec VPN、密钥管理系统、密码服务平台、身份认证系统等，全面支持 SM1、SM2、SM3、SM4、SM7、SM9、ZUC 等国产密码算法和RSA、ECC、AES、SHA 等国际密码算法，为关键信息基础设施和重要信息系统提供安全的密码运算和完善的密钥管理机制。三未信安在云计算、大数据、物联网、车联网、人工智能、区块链、隐私保护计算、数字货币等新兴技术领域进行了积极的技术创新，并推出了一系列新型密码解决方案。

经过十几年的市场开拓，三未信安的产品和服务赢得了客户和市场的认可，产品已广泛应用于金融、证券、能源、电信、交通、电子商务等行业，以及海关、公安、税务、水利、医疗保障等政府部门。

三未信安是国家级高新技术企业、国家级专精特新重点“小巨人”企业，公司研发了国内首款安全三级密码板卡和首款安全三级密码机，公司的密码机通过了 FIPS 140-2Level3（美国联邦信息处理标准3 级）认证，荣获五次国家密码科技进步奖。公司是全国信息安全标准化技术委员会和密码行业标准化技术委员会成员单位，牵头和参与制定了二十余项密码领域国家标准或行业标准。

三未信安坚持 “做客户信赖的公司，做有核心技术的公司，做员工热爱的公司”的发展理念，恪守“让生活更美好，做对社会真正有价值的事情”的价值追求，以“用密码技术守护数字世界”为使命，凝聚人才、锐意进取，立志为我国的网络信息安全事业贡献自己的力量！