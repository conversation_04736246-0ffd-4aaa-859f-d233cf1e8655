import os
import re
import time
from playwright.sync_api import sync_playwright
from tqdm import tqdm

HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8" />
    <title>Mermaid 图表</title>
    <script src="mermaid.min.js"></script>
</head>
<body>
    <div id="chart" class="mermaid">
        {}
    </div>
</body>
</html>
"""

def generate_mermaid_jpg(input_str, output_path):
    html_content = HTML_TEMPLATE.format(input_str)
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    html_file = f"{time.time()}.html"
    with open(html_file, "w", encoding="utf-8") as f:
        f.write(html_content)
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            page.goto(f"file://{os.path.abspath(html_file)}")
            page.wait_for_selector(".mermaid")
            page.wait_for_timeout(1000)
            chart_element = page.locator("svg") # 原来是.mermaid，发现会有问题（右侧有大片空白），所以改成svg
            chart_element.screenshot(path=output_path)
            browser.close()
    except Exception as e:
        print(e)
        print(html_file)
    finally:
        os.remove(html_file)
        pass

def replace_mermaid_with_images(input_md_file):
    """将markdown文件中的mermaid代码块替换为图片引用"""
    # 读取md文件
    output_dir = os.path.dirname(input_md_file)
    file_name = os.path.basename(input_md_file)

    with open(input_md_file, 'r', encoding='utf-8') as file:
        md_content = file.read()

    # 提取所有mermaid代码块
    mermaid_blocks = re.findall(r'```mermaid\n(.*?)\n```', md_content, re.DOTALL)
    # mermaid_blocks = re.findall(r'<div class="mermaid">\n(.*?)\n</div>', md_content, re.DOTALL)
    # 依次处理每个mermaid代码块
    for i, mermaid_code in tqdm(enumerate(mermaid_blocks), total=len(mermaid_blocks), desc="处理mermaid代码块"):
        # 生成图片
        img_filename = f"mermaid_{i+1}.png"
        img_path = f"{output_dir}/images-{file_name}/{img_filename}"
        generate_mermaid_jpg(mermaid_code, img_path)
        print(f"成功生成图片: {img_path}")
        
        # 替换markdown中的mermaid代码块为图片引用
        mermaid_block = f"```mermaid\n{mermaid_code}\n```"
        # mermaid_block = f'<div class="mermaid">\n{mermaid_code}\n</div>'
        img_reference = f"![mermaid_{i+1}](images-{file_name}/{img_filename})"
        md_content = md_content.replace(mermaid_block, img_reference)
            
    # 保存替换后的markdown文件
    output_md_path = input_md_file.replace('.md', '_with_images.md')
    with open(output_md_path, 'w', encoding='utf-8') as file:
        file.write(md_content)
    process_file
    process_file(output_md_path)
    
    print(f"总共处理了 {len(mermaid_blocks)} 个 mermaid 代码块")
    print(f"替换后的markdown文件已保存到: {output_md_path}")

    try:
        cmd = f"cd {output_dir} && pandoc {file_name.replace('.md', '_with_images.md')} -o {file_name.replace('.md', '.docx')} --reference-doc=reference.docx"
        os.system(cmd)
        print(f"pandoc 转换完成: {output_md_path} -> {file_name.replace('.md', '.docx')}")
    except Exception as e:
        print(f"cd {output_dir} && pandoc {output_md_path} -o {file_name.replace('.md', '.docx')}")
        print(e)
        print(f"pandoc 转换失败: {output_md_path}")

def remove_title_numbers(text):
    """
    去掉Markdown标题后的数字
    
    Args:
        text (str): 输入的文本内容
        
    Returns:
        str: 处理后的文本内容
    """
    # 匹配Markdown标题的正则表达式
    # 匹配 # 到 ###### 开头的行，后面跟着数字和点，然后是标题内容
    pattern = r'^(#{1,6})\s+(\d+\.)*\d+\s*(.+)$'
    
    lines = text.split('\n')
    processed_lines = []
    
    for line in lines:
        match = re.match(pattern, line)
        if match:
            # 提取标题级别和标题内容
            title_level = match.group(1)
            title_content = match.group(3)
            # 重新组合标题，去掉数字部分
            new_line = f"{title_level} {title_content}"
            processed_lines.append(new_line)
        else:
            # 不是标题行，保持原样
            processed_lines.append(line)
    
    return '\n'.join(processed_lines)


def process_file(input_file, output_file=None):
    try:
        # 读取文件内容
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 处理内容
        processed_content = remove_title_numbers(content)
        
        # 确定输出文件路径
        if output_file is None:
            output_file = input_file.replace('.md', '_processed.md')
        
        # 写入处理后的内容
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(processed_content)
        
        print(f"✅ 文件处理完成: {input_file} -> {output_file}")
        
    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {input_file}")
    except Exception as e:
        print(f"❌ 处理文件时出错: {e}")

if __name__ == "__main__":
    input_md_file = "results/XX研发项目COSMIC评估功能点拆解模板-0729_功能需求文档.md"  # 输入的markdown文件路径
    replace_mermaid_with_images(input_md_file)

# uv pip install playwright
# playwright install chromium
# playwright install-deps
