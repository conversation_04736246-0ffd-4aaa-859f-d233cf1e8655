#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提示词管理模块

提供提示词文件的版本管理功能：
1. 自动版本号管理
2. 提示词文件的读取和保存
3. 版本历史查询
"""

import os
import re
import glob
from datetime import datetime
from typing import List, Dict, Optional, Tuple

class PromptManager:
    """提示词管理器"""
    
    def __init__(self, data_dir: str = "data"):
        """
        初始化提示词管理器
        
        Args:
            data_dir: 提示词文件存储目录
        """
        self.data_dir = data_dir
        self.ensure_data_dir()
    
    def ensure_data_dir(self):
        """确保数据目录存在"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def get_prompt_types(self) -> List[str]:
        """获取支持的提示词类型"""
        return ["功能拆解", "文档生成"]
    
    def get_latest_version(self, prompt_type: str) -> int:
        """
        获取指定类型提示词的最新版本号
        
        Args:
            prompt_type: 提示词类型（功能拆解/文档生成）
            
        Returns:
            最新版本号，如果没有文件则返回0
        """
        pattern = os.path.join(self.data_dir, f"{prompt_type}_提示词.*.md")
        files = glob.glob(pattern)
        
        if not files:
            return 0
        
        versions = []
        for file in files:
            match = re.search(rf"{prompt_type}_提示词\.(\d+)\.md$", file)
            if match:
                versions.append(int(match.group(1)))
        
        return max(versions) if versions else 0
    
    def get_next_version(self, prompt_type: str) -> int:
        """
        获取指定类型提示词的下一个版本号
        
        Args:
            prompt_type: 提示词类型
            
        Returns:
            下一个版本号
        """
        return self.get_latest_version(prompt_type) + 1
    
    def get_prompt_file_path(self, prompt_type: str, version: Optional[int] = None) -> str:
        """
        获取提示词文件路径
        
        Args:
            prompt_type: 提示词类型
            version: 版本号，如果为None则使用最新版本
            
        Returns:
            文件路径
        """
        if version is None:
            version = self.get_latest_version(prompt_type)
            if version == 0:
                version = 1  # 如果没有文件，使用版本1
        
        filename = f"{prompt_type}_提示词.{version}.md"
        return os.path.join(self.data_dir, filename)
    
    def load_prompt(self, prompt_type: str, version: Optional[int] = None) -> str:
        """
        加载提示词内容
        
        Args:
            prompt_type: 提示词类型
            version: 版本号，如果为None则使用最新版本
            
        Returns:
            提示词内容，如果文件不存在则返回默认内容
        """
        file_path = self.get_prompt_file_path(prompt_type, version)
        
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        else:
            # 返回默认提示词内容
            return self.get_default_prompt(prompt_type)
    
    def save_prompt(self, prompt_type: str, content: str, create_new_version: bool = True) -> str:
        """
        保存提示词内容
        
        Args:
            prompt_type: 提示词类型
            content: 提示词内容
            create_new_version: 是否创建新版本
            
        Returns:
            保存的文件路径
        """
        if create_new_version:
            version = self.get_next_version(prompt_type)
        else:
            version = self.get_latest_version(prompt_type)
            if version == 0:
                version = 1
        
        file_path = self.get_prompt_file_path(prompt_type, version)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return file_path
    
    def get_version_history(self, prompt_type: str) -> List[Dict]:
        """
        获取指定类型提示词的版本历史
        
        Args:
            prompt_type: 提示词类型
            
        Returns:
            版本历史列表，包含版本号、文件路径、修改时间等信息
        """
        pattern = os.path.join(self.data_dir, f"{prompt_type}_提示词.*.md")
        files = glob.glob(pattern)
        
        history = []
        for file in files:
            match = re.search(rf"{prompt_type}_提示词\.(\d+)\.md$", file)
            if match:
                version = int(match.group(1))
                stat = os.stat(file)
                history.append({
                    'version': version,
                    'file_path': file,
                    'file_name': os.path.basename(file),
                    'size': stat.st_size,
                    'modified_time': stat.st_mtime,
                    'modified_time_str': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                })
        
        # 按版本号降序排列
        history.sort(key=lambda x: x['version'], reverse=True)
        return history
    
    def get_default_prompt(self, prompt_type: str) -> str:
        """
        获取默认提示词内容
        
        Args:
            prompt_type: 提示词类型
            
        Returns:
            默认提示词内容
        """
        if prompt_type == "功能拆解":
            return """# 功能拆解提示词

## 任务描述
根据提供的功能需求，将其拆解为COSMIC功能点。

## 拆解原则
1. 每个功能过程必须包含至少1个E（输入）和1个X（输出）
2. 将关联数据属性合并为最小单元数据组
3. R/W仅针对边界内持久存储
4. 避免同一数据组在同一功能过程中重复计数

## 输出格式
请按照以下格式输出：
- 功能过程名称
- 子过程描述
- 数据移动类型（E/X/R/W）
- 数据组
- 数据属性

## 注意事项
- 确保拆解的完整性和准确性
- 遵循COSMIC标准规范
"""
        elif prompt_type == "文档生成":
            return """# 文档生成提示词

## 任务描述
根据COSMIC功能拆解数据生成功能需求文档。

## 文档结构
1. 项目概述
2. 功能模块说明
3. 详细功能描述
4. 数据流程图
5. 技术要求

## 输出要求
- 使用Markdown格式
- 包含Mermaid时序图
- 结构清晰，层次分明
- 内容完整，描述准确

## 注意事项
- 确保文档的可读性和专业性
- 包含必要的技术细节
- 遵循文档编写规范
"""
        else:
            return f"# {prompt_type}提示词\n\n请在此处编写{prompt_type}相关的提示词内容。"
    
    def delete_version(self, prompt_type: str, version: int) -> bool:
        """
        删除指定版本的提示词文件
        
        Args:
            prompt_type: 提示词类型
            version: 版本号
            
        Returns:
            是否删除成功
        """
        file_path = self.get_prompt_file_path(prompt_type, version)
        
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                return True
            except Exception:
                return False
        
        return False
    
    def copy_version(self, prompt_type: str, from_version: int, to_version: Optional[int] = None) -> str:
        """
        复制版本
        
        Args:
            prompt_type: 提示词类型
            from_version: 源版本号
            to_version: 目标版本号，如果为None则自动生成新版本号
            
        Returns:
            新文件路径
        """
        if to_version is None:
            to_version = self.get_next_version(prompt_type)
        
        source_path = self.get_prompt_file_path(prompt_type, from_version)
        target_path = self.get_prompt_file_path(prompt_type, to_version)
        
        if os.path.exists(source_path):
            with open(source_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            with open(target_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return target_path
        
        raise FileNotFoundError(f"源文件不存在: {source_path}")
