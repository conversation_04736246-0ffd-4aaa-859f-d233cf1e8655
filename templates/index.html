<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>COSMIC功能需求文档生成器</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.9.1/dist/mermaid.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.44.0/min/vs/loader.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            height: 100vh;
        }

        /* 左侧菜单栏 */
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-header h1 {
            font-size: 1.2em;
            font-weight: 300;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 0.8em;
            opacity: 0.7;
        }

        .sidebar-menu {
            flex: 1;
            padding: 20px 0;
        }

        .menu-item {
            display: block;
            padding: 15px 20px;
            color: white;
            text-decoration: none;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .menu-item:hover {
            background: rgba(255,255,255,0.1);
            padding-left: 30px;
        }

        .menu-item.active {
            background: rgba(52, 152, 219, 0.3);
            border-right: 3px solid #3498db;
        }

        .menu-item i {
            margin-right: 10px;
            width: 16px;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .content-header {
            background: white;
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .content-header h2 {
            color: #2c3e50;
            margin: 0;
        }

        .content-body {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        /* 内容面板 */
        .content-panel {
            display: none;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .content-panel.active {
            display: block;
        }
        
        /* 文件选择器样式 */
        .file-selector {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .file-selector h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        /* 编辑器样式 */
        .editor-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .editor-toolbar {
            background: #f8f9fa;
            padding: 10px 15px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .editor-toolbar h4 {
            margin: 0;
            color: #2c3e50;
        }

        .version-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .version-selector select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        #editor {
            height: 400px;
        }

        /* 配置管理样式 */
        .config-group {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .config-group h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        .config-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            gap: 15px;
        }

        .config-item label {
            flex: 0 0 200px;
            font-weight: 500;
            color: #495057;
        }

        .config-item input,
        .config-item select,
        .config-item textarea {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .config-item textarea {
            min-height: 80px;
            resize: vertical;
        }

        .config-item .config-description {
            flex: 0 0 250px;
            font-size: 12px;
            color: #6c757d;
            font-style: italic;
        }
        
        /* 保留原有的文件选择器样式，但调整一些参数 */
        .original-file-selector {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .original-file-selector h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .file-dropdown {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            background: white;
            margin-bottom: 20px;
        }
        
        .file-dropdown:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .file-info {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            display: none;
        }
        
        .file-info.show {
            display: block;
        }
        
        .file-info h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .file-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .detail-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
        }
        
        .detail-label {
            font-weight: bold;
            color: #495057;
            font-size: 0.9em;
        }
        
        .detail-value {
            color: #2c3e50;
            margin-top: 5px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: bold;
        }
        
        .status-exists {
            background: #d4edda;
            color: #155724;
        }
        
        .status-missing {
            background: #f8d7da;
            color: #721c24;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-warning:hover {
            background: #e67e22;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #95a5a6;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .message {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
        }
        
        .document-viewer {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            border: 1px solid #e9ecef;
        }

        .viewer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .viewer-header h3 {
            color: #2c3e50;
            margin: 0;
        }

        .document-content {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-height: 600px;
            overflow-y: auto;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
        }

        .document-content h1, .document-content h2, .document-content h3,
        .document-content h4, .document-content h5, .document-content h6 {
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.8em;
        }

        .document-content h1 { border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        .document-content h2 { border-bottom: 2px solid #e74c3c; padding-bottom: 8px; }
        .document-content h3 { border-bottom: 1px solid #f39c12; padding-bottom: 5px; }

        .document-content .mermaid {
            text-align: center;
            margin: 20px 0;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }

        .document-content pre {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
        }

        .document-content code {
            background-color: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .main-content {
                padding: 20px;
            }

            .action-buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }

            .document-content {
                padding: 15px;
                max-height: 400px;
            }

            .viewer-header {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 左侧菜单栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h1>COSMIC生成器</h1>
                <p>功能需求文档生成工具</p>
            </div>
            <div class="sidebar-menu">
                <button class="menu-item active" onclick="showPanel('document-generation')">
                    <i>📄</i>文档生成
                </button>
                <button class="menu-item" onclick="showPanel('cosmic-decompose')">
                    <i>🔧</i>功能拆解
                </button>
                <button class="menu-item" onclick="showPanel('prompt-engineering')">
                    <i>✏️</i>提示词工程
                </button>
                <button class="menu-item" onclick="showPanel('system-config')">
                    <i>⚙️</i>系统配置
                </button>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="content-header">
                <h2 id="content-title">文档生成</h2>
            </div>
            <div class="content-body">
                <!-- 文档生成面板 -->
                <div id="document-generation" class="content-panel active">
                    <div class="file-selector">
                        <h3>📁 选择数据文件</h3>
                        <select id="docFileSelect" class="file-dropdown">
                            <option value="">请选择一个xlsx或csv文件...</option>
                            {% for file in files %}
                            <option value="{{ file.path }}"
                                    data-name="{{ file.name }}"
                                    data-size="{{ file.size }}"
                                    data-modified="{{ file.modified }}"
                                    data-has-markdown="{{ file.has_markdown }}"
                                    data-markdown-path="{{ file.markdown_path }}">
                                {{ file.name }} ({{ "%.1f"|format(file.size/1024) }} KB)
                            </option>
                            {% endfor %}
                        </select>

                        <div class="file-selector">
                            <h3>📝 选择文档生成提示词</h3>
                            <select id="docPromptSelect" class="file-dropdown">
                                <option value="">使用默认提示词</option>
                            </select>
                        </div>
                    </div>

                    <div id="docFileInfo" class="file-info">
                        <h3>📄 文件信息</h3>
                        <div class="file-details">
                            <div class="detail-item">
                                <div class="detail-label">文件名</div>
                                <div class="detail-value" id="docFileName">-</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">文件大小</div>
                                <div class="detail-value" id="docFileSize">-</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">修改时间</div>
                                <div class="detail-value" id="docFileModified">-</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Markdown状态</div>
                                <div class="detail-value" id="docMarkdownStatus">-</div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button id="docViewBtn" class="btn btn-success" onclick="viewDocument()" style="display: none;">
                                👁️ 查看文档
                            </button>
                            <button id="docViewInPageBtn" class="btn btn-success" onclick="viewDocumentInPage()" style="display: none;">
                                📄 页面内查看
                            </button>
                            <button id="docGenerateBtn" class="btn btn-primary" onclick="generateDocument()">
                                🚀 生成文档
                            </button>
                            <button id="docDownloadBtn" class="btn btn-secondary" onclick="downloadDocument()" style="display: none;">
                                💾 下载文档
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 功能拆解面板 -->
                <div id="cosmic-decompose" class="content-panel">
                    <div class="file-selector">
                        <h3>📁 选择Excel文件</h3>
                        <select id="cosmicFileSelect" class="file-dropdown">
                            <option value="">请选择一个xlsx文件...</option>
                            {% for file in files %}
                            {% if file.extension == '.xlsx' %}
                            <option value="{{ file.path }}">{{ file.name }}</option>
                            {% endif %}
                            {% endfor %}
                        </select>
                    </div>

                    <div class="file-selector">
                        <h3>📝 选择功能拆解提示词</h3>
                        <select id="cosmicPromptSelect" class="file-dropdown">
                            <option value="">使用默认提示词</option>
                        </select>
                    </div>

                    <div class="action-buttons">
                        <button id="cosmicDecomposeBtn" class="btn btn-primary" onclick="cosmicDecompose()">
                            🔧 开始拆解
                        </button>
                    </div>
                </div>

                <!-- 提示词工程面板 -->
                <div id="prompt-engineering" class="content-panel">
                    <div class="file-selector">
                        <h3>📝 选择提示词类型</h3>
                        <select id="promptTypeSelect" class="file-dropdown" onchange="loadPromptForEdit()">
                            <option value="">请选择提示词类型...</option>
                            <option value="功能拆解">功能拆解提示词</option>
                            <option value="文档生成">文档生成提示词</option>
                        </select>
                    </div>

                    <div id="promptEditor" style="display: none;">
                        <div class="editor-container">
                            <div class="editor-toolbar">
                                <h4 id="promptEditorTitle">提示词编辑器</h4>
                                <div class="version-selector">
                                    <label>版本:</label>
                                    <select id="promptVersionSelect" onchange="loadPromptVersion()">
                                    </select>
                                    <button class="btn btn-secondary" onclick="createNewVersion()">新建版本</button>
                                </div>
                            </div>
                            <div id="editor"></div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="savePrompt()">💾 保存</button>
                            <button class="btn btn-warning" onclick="savePromptAsNew()">📄 另存为新版本</button>
                            <button class="btn btn-secondary" onclick="previewPrompt()">👁️ 预览</button>
                        </div>
                    </div>
                </div>

                <!-- 系统配置面板 -->
                <div id="system-config" class="content-panel">
                    <div id="configContainer">
                        <!-- 配置项将通过JavaScript动态加载 -->
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="saveConfig()">💾 保存配置</button>
                        <button class="btn btn-secondary" onclick="exportConfig()">📤 导出配置</button>
                        <button class="btn btn-secondary" onclick="importConfig()">📥 导入配置</button>
                        <button class="btn btn-warning" onclick="resetConfig()">🔄 重置为默认</button>
                    </div>
                </div>

                <div id="loading" class="loading">
                    <div class="spinner"></div>
                    <p>正在处理，请稍候...</p>
                </div>

                <div id="message" class="message"></div>

                <!-- 页面内文档渲染区域 -->
                <div id="documentViewer" class="document-viewer" style="display: none;">
                    <div class="viewer-header">
                        <h3>📄 文档预览</h3>
                        <button class="btn btn-secondary" onclick="hideDocumentViewer()">✖️ 关闭</button>
                    </div>
                    <div id="documentContent" class="document-content">
                        <!-- 文档内容将在这里渲染 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedFile = null;
        let markdownPath = null;
        let currentPromptType = null;
        let currentPromptVersion = null;
        let monacoEditor = null;

        // 面板切换功能
        function showPanel(panelId) {
            // 隐藏所有面板
            const panels = document.querySelectorAll('.content-panel');
            panels.forEach(panel => panel.classList.remove('active'));

            // 显示选中的面板
            document.getElementById(panelId).classList.add('active');

            // 更新菜单状态
            const menuItems = document.querySelectorAll('.menu-item');
            menuItems.forEach(item => item.classList.remove('active'));
            event.target.classList.add('active');

            // 更新标题
            const titles = {
                'document-generation': '文档生成',
                'cosmic-decompose': '功能拆解',
                'prompt-engineering': '提示词工程',
                'system-config': '系统配置'
            };
            document.getElementById('content-title').textContent = titles[panelId];

            // 根据面板类型执行特定初始化
            if (panelId === 'prompt-engineering') {
                initPromptEditor();
            } else if (panelId === 'system-config') {
                loadSystemConfig();
            }
        }

        // 文档生成 - 文件选择事件
        document.getElementById('docFileSelect').addEventListener('change', function() {
            const select = this;
            const option = select.options[select.selectedIndex];

            if (option.value) {
                selectedFile = option.value;
                const hasMarkdown = option.dataset.hasMarkdown === 'True';
                markdownPath = option.dataset.markdownPath;

                // 显示文件信息
                document.getElementById('docFileName').textContent = option.dataset.name;
                document.getElementById('docFileSize').textContent = formatFileSize(parseInt(option.dataset.size));
                document.getElementById('docFileModified').textContent = formatDate(parseFloat(option.dataset.modified));

                const statusElement = document.getElementById('docMarkdownStatus');
                if (hasMarkdown) {
                    statusElement.innerHTML = '<span class="status-badge status-exists">✅ 已生成</span>';
                    document.getElementById('docViewBtn').style.display = 'inline-block';
                    document.getElementById('docViewInPageBtn').style.display = 'inline-block';
                    document.getElementById('docDownloadBtn').style.display = 'inline-block';
                } else {
                    statusElement.innerHTML = '<span class="status-badge status-missing">❌ 未生成</span>';
                    document.getElementById('docViewBtn').style.display = 'none';
                    document.getElementById('docViewInPageBtn').style.display = 'none';
                    document.getElementById('docDownloadBtn').style.display = 'none';
                }

                document.getElementById('docFileInfo').classList.add('show');
            } else {
                document.getElementById('docFileInfo').classList.remove('show');
                selectedFile = null;
                markdownPath = null;
            }
        });

        // 生成文档
        function generateDocument() {
            if (!selectedFile) {
                showMessage('请先选择一个文件', 'error');
                return;
            }
            
            showLoading(true);
            hideMessage();
            
            fetch('/api/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    file_path: selectedFile,
                    force_regenerate: false
                })
            })
            .then(response => response.json())
            .then(data => {
                showLoading(false);
                if (data.success) {
                    showMessage(data.message, 'success');
                    markdownPath = data.markdown_path;
                    updateUIAfterGeneration();
                } else {
                    showMessage(data.error || '生成失败', 'error');
                }
            })
            .catch(error => {
                showLoading(false);
                showMessage('网络错误: ' + error.message, 'error');
            });
        }

        // 重新生成文档
        function regenerateDocument() {
            if (!selectedFile) {
                showMessage('请先选择一个文件', 'error');
                return;
            }
            
            if (!confirm('确定要重新生成文档吗？这将覆盖现有的文档。')) {
                return;
            }
            
            showLoading(true);
            hideMessage();
            
            fetch('/api/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    file_path: selectedFile,
                    force_regenerate: true
                })
            })
            .then(response => response.json())
            .then(data => {
                showLoading(false);
                if (data.success) {
                    showMessage('文档重新生成成功', 'success');
                    markdownPath = data.markdown_path;
                } else {
                    showMessage(data.error || '重新生成失败', 'error');
                }
            })
            .catch(error => {
                showLoading(false);
                showMessage('网络错误: ' + error.message, 'error');
            });
        }

        // 查看文档（新标签页）
        function viewDocument() {
            if (!markdownPath) {
                showMessage('没有可查看的文档', 'error');
                return;
            }

            window.open('/api/render/' + encodeURIComponent(markdownPath), '_blank');
        }

        // 页面内查看文档
        function viewDocumentInPage() {
            if (!markdownPath) {
                showMessage('没有可查看的文档', 'error');
                return;
            }

            showLoading(true);
            hideMessage();

            fetch('/api/render/' + encodeURIComponent(markdownPath))
                .then(response => response.text())
                .then(html => {
                    showLoading(false);

                    // 提取HTML内容中的主体部分
                    const contentMatch = html.match(/<div class="container">([\s\S]*?)<\/div>\s*<script>/);
                    if (contentMatch && contentMatch[1]) {
                        const content = contentMatch[1];
                        document.getElementById('documentContent').innerHTML = content;
                        document.getElementById('documentViewer').style.display = 'block';

                        // 滚动到文档查看区域
                        document.getElementById('documentViewer').scrollIntoView({ behavior: 'smooth' });

                        // 重新初始化Mermaid
                        if (window.mermaid) {
                            try {
                                window.mermaid.init(undefined, document.querySelectorAll('.mermaid'));
                            } catch (e) {
                                console.error('Mermaid初始化失败:', e);
                            }
                        }
                    } else {
                        showMessage('文档内容解析失败', 'error');
                    }
                })
                .catch(error => {
                    showLoading(false);
                    showMessage('加载文档失败: ' + error.message, 'error');
                });
        }

        // 隐藏文档查看器
        function hideDocumentViewer() {
            document.getElementById('documentViewer').style.display = 'none';
        }

        // 下载文档
        function downloadDocument() {
            if (!markdownPath) {
                showMessage('没有可下载的文档', 'error');
                return;
            }
            
            window.open('/download/' + encodeURIComponent(markdownPath), '_blank');
        }

        // 更新UI状态
        function updateUIAfterGeneration() {
            const statusElement = document.getElementById('docMarkdownStatus');
            statusElement.innerHTML = '<span class="status-badge status-exists">✅ 已生成</span>';
            document.getElementById('docViewBtn').style.display = 'inline-block';
            document.getElementById('docViewInPageBtn').style.display = 'inline-block';
            document.getElementById('docDownloadBtn').style.display = 'inline-block';
        }

        // 功能拆解相关函数
        function cosmicDecompose() {
            const fileSelect = document.getElementById('cosmicFileSelect');
            const promptSelect = document.getElementById('cosmicPromptSelect');

            if (!fileSelect.value) {
                showMessage('请先选择Excel文件', 'error');
                return;
            }

            showLoading(true);
            hideMessage();

            fetch('/api/cosmic/decompose', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    excel_file: fileSelect.value,
                    prompt_type: '功能拆解',
                    prompt_version: promptSelect.value || null
                })
            })
            .then(response => response.json())
            .then(data => {
                showLoading(false);
                if (data.success) {
                    showMessage(data.message + '，输出文件: ' + data.output_file, 'success');
                } else {
                    showMessage(data.error || '功能拆解失败', 'error');
                }
            })
            .catch(error => {
                showLoading(false);
                showMessage('网络错误: ' + error.message, 'error');
            });
        }

        // 提示词工程相关函数
        function initPromptEditor() {
            // 初始化Monaco编辑器
            if (!monacoEditor) {
                require.config({ paths: { 'vs': 'https://cdn.jsdelivr.net/npm/monaco-editor@0.44.0/min/vs' }});
                require(['vs/editor/editor.main'], function () {
                    monacoEditor = monaco.editor.create(document.getElementById('editor'), {
                        value: '',
                        language: 'markdown',
                        theme: 'vs',
                        automaticLayout: true,
                        wordWrap: 'on',
                        minimap: { enabled: false }
                    });
                });
            }

            // 加载提示词选项
            loadPromptOptions();
        }

        function loadPromptOptions() {
            fetch('/api/prompts')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        showMessage(data.error, 'error');
                        return;
                    }

                    // 更新文档生成和功能拆解的提示词选择器
                    updatePromptSelectors(data);
                })
                .catch(error => {
                    showMessage('加载提示词选项失败: ' + error.message, 'error');
                });
        }

        function updatePromptSelectors(promptData) {
            const docPromptSelect = document.getElementById('docPromptSelect');
            const cosmicPromptSelect = document.getElementById('cosmicPromptSelect');

            // 清空现有选项
            docPromptSelect.innerHTML = '<option value="">使用默认提示词</option>';
            cosmicPromptSelect.innerHTML = '<option value="">使用默认提示词</option>';

            // 添加文档生成提示词选项
            if (promptData['文档生成'] && promptData['文档生成'].history) {
                promptData['文档生成'].history.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.version;
                    option.textContent = `v${item.version} (${item.modified_time_str})`;
                    docPromptSelect.appendChild(option);
                });
            }

            // 添加功能拆解提示词选项
            if (promptData['功能拆解'] && promptData['功能拆解'].history) {
                promptData['功能拆解'].history.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.version;
                    option.textContent = `v${item.version} (${item.modified_time_str})`;
                    cosmicPromptSelect.appendChild(option);
                });
            }
        }

        function loadPromptForEdit() {
            const promptType = document.getElementById('promptTypeSelect').value;
            if (!promptType) {
                document.getElementById('promptEditor').style.display = 'none';
                return;
            }

            currentPromptType = promptType;
            document.getElementById('promptEditor').style.display = 'block';
            document.getElementById('promptEditorTitle').textContent = promptType + '提示词编辑器';

            // 加载版本历史
            fetch(`/api/editor/prompt/${promptType}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 更新版本选择器
                        const versionSelect = document.getElementById('promptVersionSelect');
                        versionSelect.innerHTML = '';

                        data.history.forEach(item => {
                            const option = document.createElement('option');
                            option.value = item.version;
                            option.textContent = `v${item.version} (${item.modified_time_str})`;
                            if (item.version === data.version) {
                                option.selected = true;
                            }
                            versionSelect.appendChild(option);
                        });

                        // 加载内容到编辑器
                        if (monacoEditor) {
                            monacoEditor.setValue(data.content);
                        }
                        currentPromptVersion = data.version;
                    } else {
                        showMessage(data.error, 'error');
                    }
                })
                .catch(error => {
                    showMessage('加载提示词失败: ' + error.message, 'error');
                });
        }

        function loadPromptVersion() {
            const version = document.getElementById('promptVersionSelect').value;
            if (!currentPromptType || !version) return;

            fetch(`/api/editor/prompt/${currentPromptType}?version=${version}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (monacoEditor) {
                            monacoEditor.setValue(data.content);
                        }
                        currentPromptVersion = data.version;
                    } else {
                        showMessage(data.error, 'error');
                    }
                })
                .catch(error => {
                    showMessage('加载提示词版本失败: ' + error.message, 'error');
                });
        }

        function savePrompt() {
            if (!currentPromptType || !monacoEditor) return;

            const content = monacoEditor.getValue();

            fetch(`/api/editor/prompt/${currentPromptType}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content: content,
                    create_new_version: false
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    // 刷新版本历史
                    loadPromptForEdit();
                } else {
                    showMessage(data.error, 'error');
                }
            })
            .catch(error => {
                showMessage('保存失败: ' + error.message, 'error');
            });
        }

        function savePromptAsNew() {
            if (!currentPromptType || !monacoEditor) return;

            const content = monacoEditor.getValue();

            fetch(`/api/editor/prompt/${currentPromptType}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content: content,
                    create_new_version: true
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    // 刷新版本历史
                    loadPromptForEdit();
                } else {
                    showMessage(data.error, 'error');
                }
            })
            .catch(error => {
                showMessage('保存失败: ' + error.message, 'error');
            });
        }

        function createNewVersion() {
            if (!currentPromptType) return;

            const content = monacoEditor ? monacoEditor.getValue() : '';

            fetch(`/api/editor/prompt/${currentPromptType}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content: content,
                    create_new_version: true
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('新版本创建成功', 'success');
                    loadPromptForEdit();
                } else {
                    showMessage(data.error, 'error');
                }
            })
            .catch(error => {
                showMessage('创建新版本失败: ' + error.message, 'error');
            });
        }

        function previewPrompt() {
            if (!monacoEditor) return;

            const content = monacoEditor.getValue();
            const previewWindow = window.open('', '_blank');
            previewWindow.document.write(`
                <html>
                <head>
                    <title>提示词预览</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
                        pre { background: #f4f4f4; padding: 15px; border-radius: 5px; overflow-x: auto; }
                    </style>
                </head>
                <body>
                    <h1>提示词预览</h1>
                    <pre>${content}</pre>
                </body>
                </html>
            `);
        }

        // 系统配置管理相关函数
        function loadSystemConfig() {
            fetch('/api/config')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderConfigForm(data.config_groups);
                    } else {
                        showMessage(data.error, 'error');
                    }
                })
                .catch(error => {
                    showMessage('加载配置失败: ' + error.message, 'error');
                });
        }

        function renderConfigForm(configGroups) {
            const container = document.getElementById('configContainer');
            container.innerHTML = '';

            Object.keys(configGroups).forEach(groupName => {
                const group = configGroups[groupName];
                const groupDiv = document.createElement('div');
                groupDiv.className = 'config-group';

                const groupTitle = document.createElement('h4');
                groupTitle.textContent = groupName;
                groupDiv.appendChild(groupTitle);

                Object.keys(group).forEach(configKey => {
                    const config = group[configKey];
                    const itemDiv = document.createElement('div');
                    itemDiv.className = 'config-item';

                    const label = document.createElement('label');
                    label.textContent = configKey;
                    itemDiv.appendChild(label);

                    let input;
                    if (config.type === 'boolean') {
                        input = document.createElement('select');
                        input.innerHTML = '<option value="true">是</option><option value="false">否</option>';
                        input.value = config.value ? 'true' : 'false';
                    } else if (config.type === 'number') {
                        input = document.createElement('input');
                        input.type = 'number';
                        input.value = config.value;
                        if (config.min !== undefined) input.min = config.min;
                        if (config.max !== undefined) input.max = config.max;
                        if (config.step !== undefined) input.step = config.step;
                    } else if (config.type === 'textarea') {
                        input = document.createElement('textarea');
                        input.value = config.value;
                    } else if (config.type === 'password') {
                        input = document.createElement('input');
                        input.type = 'password';
                        input.value = config.value;
                    } else {
                        input = document.createElement('input');
                        input.type = 'text';
                        input.value = config.value;
                    }

                    input.id = 'config_' + configKey;
                    input.dataset.configKey = configKey;
                    itemDiv.appendChild(input);

                    const description = document.createElement('div');
                    description.className = 'config-description';
                    description.textContent = config.description;
                    itemDiv.appendChild(description);

                    groupDiv.appendChild(itemDiv);
                });

                container.appendChild(groupDiv);
            });
        }

        function saveConfig() {
            const configInputs = document.querySelectorAll('[data-config-key]');
            const configs = {};

            configInputs.forEach(input => {
                const key = input.dataset.configKey;
                let value = input.value;

                // 类型转换
                if (input.type === 'number') {
                    value = parseFloat(value);
                } else if (input.tagName === 'SELECT' && (value === 'true' || value === 'false')) {
                    value = value === 'true';
                }

                configs[key] = value;
            });

            showLoading(true);

            fetch('/api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ configs: configs })
            })
            .then(response => response.json())
            .then(data => {
                showLoading(false);
                if (data.success) {
                    showMessage(data.message, 'success');
                } else {
                    showMessage(data.error, 'error');
                    if (data.validation_errors) {
                        Object.keys(data.validation_errors).forEach(key => {
                            const input = document.getElementById('config_' + key);
                            if (input) {
                                input.style.borderColor = 'red';
                                input.title = data.validation_errors[key];
                            }
                        });
                    }
                }
            })
            .catch(error => {
                showLoading(false);
                showMessage('保存配置失败: ' + error.message, 'error');
            });
        }

        function exportConfig() {
            fetch('/api/config/export')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const blob = new Blob([data.content], { type: 'text/plain' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'config.env';
                        a.click();
                        URL.revokeObjectURL(url);
                        showMessage('配置导出成功', 'success');
                    } else {
                        showMessage(data.error, 'error');
                    }
                })
                .catch(error => {
                    showMessage('导出配置失败: ' + error.message, 'error');
                });
        }

        function importConfig() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.env';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const content = e.target.result;

                        fetch('/api/config/import', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ content: content })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                showMessage(data.message, 'success');
                                loadSystemConfig(); // 重新加载配置
                            } else {
                                showMessage(data.error, 'error');
                            }
                        })
                        .catch(error => {
                            showMessage('导入配置失败: ' + error.message, 'error');
                        });
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        function resetConfig() {
            if (!confirm('确定要重置所有配置为默认值吗？此操作不可撤销。')) {
                return;
            }

            showMessage('重置功能暂未实现', 'error');
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => btn.disabled = show);
        }

        // 显示消息
        function showMessage(text, type) {
            const messageEl = document.getElementById('message');
            messageEl.textContent = text;
            messageEl.className = 'message ' + type;
            messageEl.style.display = 'block';
            
            // 3秒后自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    hideMessage();
                }, 3000);
            }
        }

        // 隐藏消息
        function hideMessage() {
            document.getElementById('message').style.display = 'none';
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // 格式化日期
        function formatDate(timestamp) {
            const date = new Date(timestamp * 1000);
            return date.toLocaleString('zh-CN');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化Mermaid
            if (window.mermaid) {
                mermaid.initialize({
                    startOnLoad: true,
                    theme: 'default',
                    securityLevel: 'loose',
                    sequence: {
                        diagramMarginX: 50,
                        diagramMarginY: 10,
                        actorMargin: 50,
                        width: 150,
                        height: 65,
                        boxMargin: 10,
                        boxTextMargin: 5,
                        noteMargin: 10,
                        messageMargin: 35,
                        mirrorActors: true,
                        bottomMarginAdj: 1,
                        useMaxWidth: true,
                        rightAngles: false,
                        showSequenceNumbers: false
                    },
                    flowchart: {
                        useMaxWidth: true,
                        htmlLabels: true
                    }
                });
            }

            // 初始化提示词选项
            loadPromptOptions();
        });
    </script>
</body>
</html>
