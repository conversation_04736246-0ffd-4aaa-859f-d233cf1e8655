<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能需求文档</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.9.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 2em;
            margin-bottom: 1em;
        }
        h1 { border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { border-bottom: 2px solid #e74c3c; padding-bottom: 8px; }
        h3 { border-bottom: 1px solid #f39c12; padding-bottom: 5px; }
        code {
            background-color: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
        }
        .mermaid {
            text-align: center;
            margin: 20px 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .back-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
        }
        .back-button:hover {
            background: #2980b9;
        }

        /* 流程图样式 */
        .diagram-container {
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }

        .flowchart-container {
            position: relative;
            min-height: 400px;
            background: #f9f9f9;
            padding: 20px;
            overflow: auto;
        }

        .participant-box {
            position: absolute;
            top: 20px;
            width: 120px;
            height: 60px;
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border: 2px solid #2196f3;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #1976d2;
            text-align: center;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .interaction {
            position: absolute;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .arrow {
            position: relative;
            width: 100%;
            height: 2px;
            background: #333;
        }

        .arrow-right::after {
            content: '';
            position: absolute;
            right: -8px;
            top: -4px;
            width: 0;
            height: 0;
            border-left: 8px solid #333;
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
        }

        .arrow-left::after {
            content: '';
            position: absolute;
            left: -8px;
            top: -4px;
            width: 0;
            height: 0;
            border-right: 8px solid #333;
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
        }

        .dashed-arrow {
            background: repeating-linear-gradient(
                to right,
                #333 0px,
                #333 5px,
                transparent 5px,
                transparent 10px
            );
        }

        .dashed-arrow.arrow-right::after {
            border-left-color: #333;
        }

        .dashed-arrow.arrow-left::after {
            border-right-color: #333;
        }

        .message {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .diagram-toggle {
            text-align: center;
            padding: 10px;
            background: #f5f5f5;
            border-top: 1px solid #ddd;
        }

        .toggle-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .toggle-btn:hover {
            background: #2980b9;
        }

        .fallback-flowchart {
            background: white;
        }
    </style>
</head>
<body>
    <div class="container">
        {{html_content}}
    </div>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35,
                mirrorActors: true,
                bottomMarginAdj: 1,
                useMaxWidth: true,
                rightAngles: false,
                showSequenceNumbers: false
            },
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });

        // 图表切换函数
        function toggleDiagram(button) {
            const container = button.closest('.diagram-container');
            const mermaidDiagram = container.querySelector('.mermaid-diagram');
            const fallbackFlowchart = container.querySelector('.fallback-flowchart');

            if (mermaidDiagram.style.display === 'none') {
                mermaidDiagram.style.display = 'block';
                fallbackFlowchart.style.display = 'none';
                button.textContent = '切换到流程图';
            } else {
                mermaidDiagram.style.display = 'none';
                fallbackFlowchart.style.display = 'block';
                button.textContent = '切换到时序图';
            }
        }

        // Mermaid错误处理
        mermaid.parseError = function(err, hash) {
            console.error('Mermaid解析错误:', err);
            // 自动切换到流程图
            const containers = document.querySelectorAll('.diagram-container');
            containers.forEach(container => {
                const mermaidDiagram = container.querySelector('.mermaid-diagram');
                const fallbackFlowchart = container.querySelector('.fallback-flowchart');
                const toggleBtn = container.querySelector('.toggle-btn');

                if (mermaidDiagram && fallbackFlowchart) {
                    mermaidDiagram.style.display = 'none';
                    fallbackFlowchart.style.display = 'block';
                    if (toggleBtn) {
                        toggleBtn.textContent = '切换到时序图';
                    }
                }
            });
        };

        // 确保在DOM加载完成后重新渲染
        document.addEventListener('DOMContentLoaded', function() {
            try {
                mermaid.init();
            } catch (error) {
                console.error('Mermaid初始化失败:', error);
                // 如果Mermaid初始化失败，自动显示流程图
                const containers = document.querySelectorAll('.diagram-container');
                containers.forEach(container => {
                    const mermaidDiagram = container.querySelector('.mermaid-diagram');
                    const fallbackFlowchart = container.querySelector('.fallback-flowchart');
                    const toggleBtn = container.querySelector('.toggle-btn');

                    if (mermaidDiagram && fallbackFlowchart) {
                        mermaidDiagram.style.display = 'none';
                        fallbackFlowchart.style.display = 'block';
                        if (toggleBtn) {
                            toggleBtn.textContent = '切换到时序图';
                        }
                    }
                });
            }
        });
    </script>
</body>
</html>
